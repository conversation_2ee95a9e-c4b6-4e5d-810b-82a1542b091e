/**
 * ChatArea Component
 * Component khu vực chat chính với header, message list và chat input
 */

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Icon, Button, Image } from '@/shared/components/common';
import MessageList from './MessageList';
import ChatInput from './ChatInput';
import { useMessages } from '../../../hooks/zalo/useZaloChat';
import type { Contact, ZaloAccount } from '../../../types/zalo-chat.types';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';

interface ChatAreaProps {
  selectedAccount?: ZaloAccount;
  selectedContact?: Contact;
  conversationId?: string;
  onAddTag?: (contact: Contact) => void;
  isMobile?: boolean;
  onBackToSidebar?: () => void;
  className?: string;
}

/**
 * Component header của chat area
 */
const ChatHeader: React.FC<{
  contact: Contact;
  onAddTag?: (contact: Contact) => void;
  onContactInfo?: (contact: Contact) => void;
  isMobile?: boolean;
  onBackToSidebar?: () => void;
}> = ({ contact, onAddTag, onContactInfo, isMobile, onBackToSidebar }) => {
  const { t } = useTranslation(['marketing', 'common']);

  const formatLastSeen = (timestamp?: string): string => {
    if (!timestamp) return '';
    
    const lastSeenDate = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - lastSeenDate.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) {
      return t('marketing:zalo.chat.justNow');
    } else if (diffInMinutes < 60) {
      return t('marketing:zalo.chat.minutesAgo', { count: diffInMinutes });
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return t('marketing:zalo.chat.hoursAgo', { count: hours });
    } else {
      return format(lastSeenDate, 'dd/MM/yyyy HH:mm', { locale: vi });
    }
  };

  return (
    <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
      {/* Left side - Mobile back button + Contact info */}
      <div className="flex items-center space-x-3">
        {/* Mobile back to list button */}
        {isMobile && onBackToSidebar && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onBackToSidebar}
            className="p-2 lg:hidden"
            title={t('marketing:zalo.chat.backToList')}
          >
            <Icon name="list" size="lg" />
          </Button>
        )}

        {/* Avatar */}
        <div className="relative">
          {contact.avatar ? (
            <Image
              src={contact.avatar}
              alt={contact.name}
              className="w-10 h-10 rounded-full object-cover"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
              <Icon name="user" size="lg" className="text-gray-600 dark:text-gray-300" />
            </div>
          )}

          {/* Online status */}
          {contact.isOnline && (
            <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
          )}
        </div>

        {/* Name and status */}
        <div className="flex-1 min-w-0">
          <Typography variant="body1" className="font-medium text-gray-900 dark:text-gray-100 truncate">
            {contact.name}
          </Typography>
          <Typography variant="caption" className="text-gray-500">
            {contact.isOnline
              ? t('marketing:zalo.chat.online')
              : contact.lastMessageTime
              ? t('marketing:zalo.chat.lastSeen', { time: formatLastSeen(contact.lastMessageTime) })
              : t('marketing:zalo.chat.offline')
            }
          </Typography>
        </div>
      </div>

      {/* Action buttons */}
      <div className="flex items-center space-x-2">
        {/* Add tag button */}
        {onAddTag && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onAddTag(contact)}
            className="p-2"
            title={t('marketing:zalo.chat.addTag')}
          >
            <Icon name="tag" size="sm" />
          </Button>
        )}

        {/* Contact info button */}
        {onContactInfo && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onContactInfo(contact)}
            className="p-2"
            title={t('marketing:zalo.chat.contactInfo')}
          >
            <Icon name="info" size="sm" />
          </Button>
        )}

        {/* More actions */}
        <Button
          variant="ghost"
          size="sm"
          className="p-2"
          title={t('common:moreActions')}
        >
          <Icon name="more-vertical" size="sm" />
        </Button>
      </div>
    </div>
  );
};

/**
 * Component empty state khi chưa chọn contact
 */
const EmptyState: React.FC<{
  selectedAccount?: ZaloAccount;
}> = ({ selectedAccount }) => {
  const { t } = useTranslation(['marketing', 'common']);

  return (
    <div className="flex flex-col items-center justify-center h-full px-8 bg-gray-50 dark:bg-gray-900">
      <div className="text-center">
        <Icon name="message-circle" size="2xl" className="text-gray-300 dark:text-gray-600 mb-6" />
        
        <Typography variant="h5" className="text-gray-600 dark:text-gray-400 mb-2">
          {selectedAccount 
            ? t('marketing:zalo.chat.selectContactToStart')
            : t('marketing:zalo.chat.selectAccountFirst')
          }
        </Typography>
        
        <Typography variant="body2" className="text-gray-500 dark:text-gray-500 max-w-md">
          {selectedAccount
            ? t('marketing:zalo.chat.selectContactDescription')
            : t('marketing:zalo.chat.selectAccountDescription')
          }
        </Typography>

        {selectedAccount && (
          <div className="mt-6">
            <Button variant="primary" size="sm">
              <Icon name="plus" size="sm" className="mr-2" />
              {t('marketing:zalo.chat.newConversation')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Component ChatArea chính
 */
const ChatArea: React.FC<ChatAreaProps> = ({
  selectedAccount,
  selectedContact,
  conversationId,
  onAddTag,
  isMobile,
  onBackToSidebar,
  className = '',
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [showContactInfo, setShowContactInfo] = useState(false);

  // Load messages for the selected conversation
  const {
    messages,
    isLoading,
    isLoadingMore,
    hasMore,
    loadMore,
  } = useMessages(conversationId || '');

  // Handle contact info modal
  const handleContactInfo = (contact: Contact) => {
    setShowContactInfo(true);
    // TODO: Implement contact info modal
    console.log('Show contact info for:', contact);
  };

  // Handle message read
  const handleMessageRead = (messageIds: string[]) => {
    // TODO: Implement mark as read functionality
    console.log('Mark messages as read:', messageIds);
  };

  // If no contact is selected, show empty state
  if (!selectedContact || !conversationId) {
    return (
      <div className={`flex-1 ${className}`}>
        <EmptyState selectedAccount={selectedAccount} />
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full bg-white dark:bg-gray-800 ${className}`}>
      {/* Header */}
      <div className="flex-shrink-0">
        <ChatHeader
          contact={selectedContact}
          onAddTag={onAddTag}
          onContactInfo={handleContactInfo}
          isMobile={isMobile}
          onBackToSidebar={onBackToSidebar}
        />
      </div>

      {/* Messages area */}
      <div className="flex-1 relative overflow-hidden">
        <MessageList
          messages={messages}
          contact={selectedContact}
          isLoading={isLoading}
          isLoadingMore={isLoadingMore}
          hasMore={hasMore}
          onLoadMore={loadMore}
          onMessageRead={handleMessageRead}
          className="h-full"
        />
      </div>

      {/* Chat input */}
      <div className="flex-shrink-0">
        <ChatInput
          conversationId={conversationId}
          placeholder={t('marketing:zalo.chat.typeMessageTo', { name: selectedContact.name })}
          className="border-t border-gray-200 dark:border-gray-700"
        />
      </div>

      {/* Contact info modal */}
      {showContactInfo && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <Typography variant="h6" className="text-gray-900 dark:text-gray-100">
                {t('marketing:zalo.chat.contactInfo')}
              </Typography>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowContactInfo(false)}
                className="p-1"
              >
                <Icon name="x" size="lg" />
              </Button>
            </div>

            {/* Contact details */}
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                {selectedContact.avatar ? (
                  <Image
                    src={selectedContact.avatar}
                    alt={selectedContact.name}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-16 h-16 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                    <Icon name="user" size="2lg" className="text-gray-600 dark:text-gray-300" />
                  </div>
                )}
                <div>
                  <Typography variant="h6" className="text-gray-900 dark:text-gray-100">
                    {selectedContact.name}
                  </Typography>
                  {selectedContact.phone && (
                    <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                      {selectedContact.phone}
                    </Typography>
                  )}
                </div>
              </div>

              {/* Tags */}
              {selectedContact.tags && selectedContact.tags.length > 0 && (
                <div>
                  <Typography variant="body2" className="text-gray-600 dark:text-gray-400 mb-2">
                    {t('marketing:zalo.chat.tags')}:
                  </Typography>
                  <div className="flex flex-wrap gap-2">
                    {selectedContact.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-block px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Notes */}
              {selectedContact.notes && (
                <div>
                  <Typography variant="body2" className="text-gray-600 dark:text-gray-400 mb-2">
                    {t('marketing:zalo.chat.notes')}:
                  </Typography>
                  <Typography variant="body2" className="text-gray-900 dark:text-gray-100">
                    {selectedContact.notes}
                  </Typography>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatArea;
