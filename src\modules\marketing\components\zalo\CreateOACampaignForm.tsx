import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Icon, Button, Checkbox } from '@/shared/components/common';
import { Card, ResponsiveGrid } from '@/shared';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { useInviteAudiencesToZaloGroups } from '../../hooks/useZaloGroups';
import type { InviteAudiencesToZaloGroupsDto } from '../../types/zalo-groups.types';
import { MarketingPlatform } from '../../types/marketing-audiences.types';
import SendOAMessageForm from './SendOAMessageForm';
import PersonalMessageForm from './PersonalMessageForm';
import BroadcastTargetMessageForm from './BroadcastTargetMessageForm';

// Campaign types
enum OACampaignType {
  ADD_MEMBER_TO_GROUP = 'ADD_MEMBER_TO_GROUP',
  SEND_OA_MESSAGE = 'SEND_OA_MESSAGE',
  SEND_PERSONAL_MESSAGE = 'SEND_PERSONAL_MESSAGE',
  SEND_BROADCAST_TARGET_MESSAGE = 'SEND_BROADCAST_TARGET_MESSAGE',
}

const OA_CAMPAIGN_TYPE_LABELS = {
  [OACampaignType.ADD_MEMBER_TO_GROUP]: 'Mời thành viên vào group',
  [OACampaignType.SEND_OA_MESSAGE]: 'Gửi tin nhắn truyền thông',
  [OACampaignType.SEND_PERSONAL_MESSAGE]: 'Tin nhắn truyền thông cá nhân',
  [OACampaignType.SEND_BROADCAST_TARGET_MESSAGE]: 'Truyền thông Broadcast theo target',
} as const;

interface CreateOACampaignFormProps {
  onSuccess?: () => void;
  onCancel: () => void;
}

const CreateOACampaignForm: React.FC<CreateOACampaignFormProps> = ({ onSuccess, onCancel }) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [selectedType, setSelectedType] = useState<OACampaignType | null>(null);

  // Danh sách các loại chiến dịch với icon và màu tương ứng
  const campaignTypes = [
    {
      type: OACampaignType.ADD_MEMBER_TO_GROUP,
      label: OA_CAMPAIGN_TYPE_LABELS[OACampaignType.ADD_MEMBER_TO_GROUP],
      icon: 'users' as const,
      color: 'bg-blue-500',
    },
    {
      type: OACampaignType.SEND_OA_MESSAGE,
      label: OA_CAMPAIGN_TYPE_LABELS[OACampaignType.SEND_OA_MESSAGE],
      icon: 'message-circle' as const,
      color: 'bg-green-500',
    },
    {
      type: OACampaignType.SEND_PERSONAL_MESSAGE,
      label: OA_CAMPAIGN_TYPE_LABELS[OACampaignType.SEND_PERSONAL_MESSAGE],
      icon: 'user' as const,
      color: 'bg-purple-500',
    },
    {
      type: OACampaignType.SEND_BROADCAST_TARGET_MESSAGE,
      label: OA_CAMPAIGN_TYPE_LABELS[OACampaignType.SEND_BROADCAST_TARGET_MESSAGE],
      icon: 'zap' as const,
      color: 'bg-orange-500',
    },
  ];

  const handleTypeSelect = (type: OACampaignType) => {
    setSelectedType(type);
  };

  const handleBackToSelection = () => {
    setSelectedType(null);
  };

  const handleFormSubmit = (data: any) => {
    console.log('Form submitted:', data);
    // Success notification is handled by the individual form components
    // Just call onSuccess to close the form
    onSuccess?.();
  };

  // Hiển thị form chọn loại chiến dịch
  if (!selectedType) {
    return (
      <div className="w-full bg-background text-foreground">
        {/* Header */}
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="h5" className="font-semibold">
                {t('marketing:zalo.oaCampaigns.create.title', 'Tạo chiến dịch OA mới')}
              </Typography>
            </div>
            <Button variant="ghost" onClick={onCancel}>
              {t('common:cancel', 'Hủy')}
            </Button>
          </div>
        </div>

        {/* Campaign Type Selection */}
        <div className="px-6 pb-6">
          <ResponsiveGrid maxColumns={{ xs: 1, md: 2, lg: 2 }} gap={3}>
            {campaignTypes.map(campaignType => (
              <Card
                key={campaignType.type}
                className="p-4 cursor-pointer hover:shadow-md transition-shadow border border-border hover:border-primary/20"
                onClick={() => handleTypeSelect(campaignType.type)}
              >
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0">
                    <Icon name={campaignType.icon} size="lg" className="text-primary" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <Typography variant="body1" className="font-medium text-foreground">
                      {campaignType.label}
                    </Typography>
                    <Typography variant="caption" className="text-muted mt-1">
                      {campaignType.description}
                    </Typography>
                  </div>
                </div>
              </Card>
            ))}
          </ResponsiveGrid>
        </div>
      </div>
    );
  }

  // Hiển thị form cụ thể cho loại chiến dịch đã chọn
  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <div>
            <Typography variant="h5" className="font-semibold">
              {OA_CAMPAIGN_TYPE_LABELS[selectedType]}
            </Typography>
          </div>
          <Button variant="ghost" onClick={handleBackToSelection}>
            {t('common:back', 'Quay lại')}
          </Button>
        </div>
      </div>

      {/* Form Content */}
      <div>
        {selectedType === OACampaignType.ADD_MEMBER_TO_GROUP && (
          <AddMemberToGroupForm onSubmit={handleFormSubmit} />
        )}

        {selectedType === OACampaignType.SEND_OA_MESSAGE && (
          <SendOAMessageForm onSubmit={handleFormSubmit} />
        )}

        {selectedType === OACampaignType.SEND_PERSONAL_MESSAGE && (
          <PersonalMessageForm onSubmit={handleFormSubmit} />
        )}

        {selectedType === OACampaignType.SEND_BROADCAST_TARGET_MESSAGE && (
          <BroadcastTargetMessageForm onSubmit={handleFormSubmit} />
        )}
      </div>
    </div>
  );
};

// Component form cho "Thêm thành viên vào group"
interface AddMemberToGroupFormProps {
  onSubmit: (data: any) => void;
}

interface FormData {
  integrationId: string;
  selectedGroups: string[];
  selectedAudiences: number[];
  inviteAllAudiences: boolean;
  inviteToAllGroups: boolean;
  inviteAllOAs: boolean;
}

const AddMemberToGroupForm: React.FC<AddMemberToGroupFormProps> = ({ onSubmit }) => {
  const { t } = useTranslation(['marketing', 'common']);
  const inviteMutation = useInviteAudiencesToZaloGroups();

  const [formData, setFormData] = useState<FormData>({
    integrationId: '',
    selectedGroups: [],
    selectedAudiences: [],
    inviteAllAudiences: false,
    inviteToAllGroups: false,
    inviteAllOAs: false,
  });

  // Load options cho OA/Integration select
  const loadIntegrationOptions = async ({
    search,
    page = 1,
    limit = 10,
  }: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      // Sử dụng service trực tiếp để có thể await
      const { ZaloService } = await import('../../services/zalo.service');
      const response = await ZaloService.getPaginatedAccounts({ search, page, limit });
      const accounts = response.result?.items || [];

      return {
        items: accounts.map((account: any) => ({
          value: account.id || account.oaId,
          label: account.name || account.displayName || `OA ${account.id}`,
          icon: account.avatarUrl ? (
            <img
              src={account.avatarUrl}
              alt={account.name || 'OA Avatar'}
              className="w-6 h-6 rounded-full object-cover"
            />
          ) : (
            <div className="w-6 h-6 rounded-full bg-muted flex items-center justify-center text-xs">
              OA
            </div>
          ),
          data: account, // Lưu toàn bộ data để sử dụng sau
        })),
        totalItems: response.result?.meta?.totalItems || 0,
        totalPages: response.result?.meta?.totalPages || 1,
        currentPage: response.result?.meta?.currentPage || page,
        hasMore:
          (response.result?.meta?.currentPage || page) < (response.result?.meta?.totalPages || 1),
      };
    } catch (error) {
      console.error('Error loading OA accounts:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: page,
        hasMore: false,
      };
    }
  };

  // Load options cho Groups select
  const loadGroupOptions = async ({
    search,
    page = 1,
    limit = 10,
  }: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      // Sử dụng service trực tiếp để có thể await
      const { ZaloGroupsService } = await import('../../services/zalo-groups.service');
      const response = await ZaloGroupsService.getZaloGroups({
        integrationId: formData.inviteAllOAs ? '' : formData.integrationId, // Filter theo integrationId đã chọn
        search,
        page,
        limit,
      });
      const groups = response.result?.items || [];

      return {
        items: groups.map((group: any) => ({
          value: group.id, // Sử dụng id (UUID - khóa chính) thay vì groupId (Zalo group ID)
          label: group.groupName || group.name || `Group ${group.id}`,
          icon: group.avatarUrl ? (
            <img
              src={group.avatarUrl}
              alt={group.groupName || 'Group Avatar'}
              className="w-6 h-6 rounded-full object-cover"
            />
          ) : (
            <div className="w-6 h-6 rounded-full bg-muted flex items-center justify-center text-xs">
              GR
            </div>
          ),
          data: group, // Lưu toàn bộ data để sử dụng sau
        })),
        totalItems: response.result?.meta?.totalItems || 0,
        totalPages: response.result?.meta?.totalPages || 1,
        currentPage: response.result?.meta?.currentPage || page,
        hasMore:
          (response.result?.meta?.currentPage || page) < (response.result?.meta?.totalPages || 1),
      };
    } catch (error) {
      console.error('Error loading groups:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: page,
        hasMore: false,
      };
    }
  };

  // Load options cho Audiences select
  const loadAudienceOptions = async ({
    search,
    page = 1,
    limit = 10,
  }: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      // Sử dụng service trực tiếp để có thể await
      const { MarketingAudiencesService } = await import(
        '../../services/marketing-audiences.service'
      );
      const response = await MarketingAudiencesService.getMarketingAudiences({
        search,
        page,
        limit,
        platform: MarketingPlatform.ZALO,
        integrationId: formData.inviteAllOAs ? undefined : formData.integrationId, // Filter theo integrationId đã chọn
      });
      const audiences = response.result?.data || [];

      return {
        items: audiences.map((audience: any) => ({
          value: parseInt(audience.id),
          label: audience.name || audience.email || `Audience ${audience.id}`,
          icon: audience.avatarUrl ? (
            <img
              src={audience.avatarUrl}
              alt={audience.name || 'Audience Avatar'}
              className="w-6 h-6 rounded-full object-cover"
            />
          ) : (
            <div className="w-6 h-6 rounded-full bg-muted flex items-center justify-center text-xs">
              AU
            </div>
          ),
          data: audience, // Lưu toàn bộ data để sử dụng sau
        })),
        totalItems: response.result?.meta?.total || 0,
        totalPages: response.result?.meta?.totalPages || 1,
        currentPage: response.result?.meta?.page || page,
        hasMore: (response.result?.meta?.page || page) < (response.result?.meta?.totalPages || 1),
      };
    } catch (error) {
      console.error('Error loading audiences:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: page,
        hasMore: false,
      };
    }
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Validation
    if (!formData.inviteAllOAs && !formData.integrationId) {
      alert('Vui lòng chọn OA hoặc chọn "Tất cả OA"');
      return;
    }
    if (!formData.inviteToAllGroups && formData.selectedGroups.length === 0) {
      alert('Vui lòng chọn ít nhất một nhóm hoặc chọn "Tất cả nhóm"');
      return;
    }
    if (!formData.inviteAllAudiences && formData.selectedAudiences.length === 0) {
      alert('Vui lòng chọn ít nhất một audience hoặc chọn "Tất cả audience"');
      return;
    }

    try {
      const payload: InviteAudiencesToZaloGroupsDto = {
        inviteAllAudiences: formData.inviteAllAudiences,
        audienceIds: formData.inviteAllAudiences ? undefined : formData.selectedAudiences,
        inviteToAllGroups: formData.inviteToAllGroups,
        groupIds: formData.inviteToAllGroups ? undefined : formData.selectedGroups, // Truyền id (UUID) chứ không phải groupId
        integrationId: formData.inviteAllOAs ? '' : formData.integrationId, // Empty string when inviting to all OAs
      };

      await inviteMutation.mutateAsync(payload);
      onSubmit({
        type: OACampaignType.ADD_MEMBER_TO_GROUP,
        payload,
        formData, // Include form data for reference
      });
    } catch (error) {
      console.error('Error inviting audiences to groups:', error);
    }
  };

  return (
    <Card>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* OA Selection */}
        <div className="space-y-3">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-foreground">
              Chọn OA <span className="text-red-500">*</span>
            </label>
            <AsyncSelectWithPagination
              value={formData.integrationId}
              onChange={value =>
                setFormData({
                  ...formData,
                  integrationId: value as string,
                  // Reset groups và audiences khi thay đổi OA
                  selectedGroups: [],
                  selectedAudiences: [],
                })
              }
              loadOptions={loadIntegrationOptions}
              placeholder="Chọn Zalo Official Account"
              fullWidth
              disabled={formData.inviteAllOAs}
            />
          </div>

          <Checkbox
            checked={formData.inviteAllOAs}
            onChange={checked =>
              setFormData({
                ...formData,
                inviteAllOAs: checked,
                integrationId: checked ? '' : formData.integrationId,
                // Reset groups và audiences khi thay đổi OA selection
                selectedGroups: [],
                selectedAudiences: [],
              })
            }
            label="Sử dụng tất cả OA hiện có"
          />
        </div>

        {/* Groups Selection */}
        <div className="space-y-3">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-foreground">Chọn nhóm</label>
            <AsyncSelectWithPagination
              value={formData.selectedGroups}
              onChange={value => setFormData({ ...formData, selectedGroups: value as string[] })}
              loadOptions={loadGroupOptions}
              placeholder="Chọn nhóm Zalo"
              multiple
              fullWidth
              disabled={
                formData.inviteToAllGroups || (!formData.inviteAllOAs && !formData.integrationId)
              }
              parentLoading={!formData.inviteAllOAs && !formData.integrationId}
            />
          </div>

          <Checkbox
            checked={formData.inviteToAllGroups}
            onChange={checked =>
              setFormData({
                ...formData,
                inviteToAllGroups: checked,
                selectedGroups: checked ? [] : formData.selectedGroups,
              })
            }
            label="Mời vào tất cả nhóm"
          />
        </div>

        {/* Audiences Selection */}
        <div className="space-y-3">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-foreground">Chọn audience</label>
            <AsyncSelectWithPagination
              value={formData.selectedAudiences}
              onChange={value => setFormData({ ...formData, selectedAudiences: value as number[] })}
              loadOptions={loadAudienceOptions}
              placeholder="Chọn audience"
              multiple
              fullWidth
              disabled={
                formData.inviteAllAudiences || (!formData.inviteAllOAs && !formData.integrationId)
              }
              parentLoading={!formData.inviteAllOAs && !formData.integrationId}
              autoLoadInitial={false} // Chỉ gọi API khi mở dropdown
            />
          </div>

          <Checkbox
            checked={formData.inviteAllAudiences}
            onChange={checked =>
              setFormData({
                ...formData,
                inviteAllAudiences: checked,
                selectedAudiences: checked ? [] : formData.selectedAudiences,
              })
            }
            label="Mời tất cả audience"
          />
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button
            type="submit"
            variant="primary"
            isLoading={inviteMutation.isPending}
            disabled={inviteMutation.isPending}
          >
            {t('marketing:zalo.oaCampaigns.create.createButton', 'Tạo chiến dịch')}
          </Button>
        </div>
      </form>
    </Card>
  );
};

export default CreateOACampaignForm;
