import React, { useState, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Icon, Button, Checkbox } from '@/shared/components/common';
import { Card, ResponsiveGrid } from '@/shared';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { useInviteAudiencesToZaloGroups } from '../../hooks/useZaloGroups';
import type { InviteAudiencesToZaloGroupsDto } from '../../types/zalo-groups.types';
import { MarketingPlatform } from '../../types/marketing-audiences.types';
import SendOAMessageForm from './SendOAMessageForm';
import PersonalMessageForm from './PersonalMessageForm';
import BroadcastTargetMessageForm from './BroadcastTargetMessageForm';
import { useCreateZaloOACampaign } from '../../hooks/zalo/useZaloOACampaigns';
import { CreateZaloOACampaignDto } from '../../types/zalo-oa-campaigns.types';
import { Form, FormItem, Input, TagsInput, Table } from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import MessageChainEditor, { MessageChainItem } from './MessageChainEditor';

// Campaign types
enum OACampaignType {
  ADD_MEMBER_TO_GROUP = 'ADD_MEMBER_TO_GROUP',
  SEND_OA_MESSAGE = 'SEND_OA_MESSAGE',
  SEND_PERSONAL_MESSAGE = 'SEND_PERSONAL_MESSAGE',
  SEND_BROADCAST_TARGET_MESSAGE = 'SEND_BROADCAST_TARGET_MESSAGE',
  SEND_GROUP_MESSAGE = 'SEND_GROUP_MESSAGE',
}

const OA_CAMPAIGN_TYPE_LABELS = {
  [OACampaignType.ADD_MEMBER_TO_GROUP]: 'Mời thành viên vào group',
  [OACampaignType.SEND_OA_MESSAGE]: 'Gửi tin nhắn truyền thông',
  [OACampaignType.SEND_PERSONAL_MESSAGE]: 'Tin nhắn truyền thông cá nhân',
  [OACampaignType.SEND_BROADCAST_TARGET_MESSAGE]: 'Truyền thông Broadcast theo target',
  [OACampaignType.SEND_GROUP_MESSAGE]: 'Gửi tin nhắn hàng loạt',
} as const;

interface CreateOACampaignFormProps {
  onSuccess?: () => void;
  onCancel: () => void;
}

const CreateOACampaignForm: React.FC<CreateOACampaignFormProps> = ({ onSuccess, onCancel }) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [selectedType, setSelectedType] = useState<OACampaignType | null>(null);

  // Danh sách các loại chiến dịch với icon và màu tương ứng
  const campaignTypes = [
    {
      type: OACampaignType.ADD_MEMBER_TO_GROUP,
      label: OA_CAMPAIGN_TYPE_LABELS[OACampaignType.ADD_MEMBER_TO_GROUP],
      icon: 'users' as const,
      color: 'bg-blue-500',
    },
    {
      type: OACampaignType.SEND_OA_MESSAGE,
      label: OA_CAMPAIGN_TYPE_LABELS[OACampaignType.SEND_OA_MESSAGE],
      icon: 'message-circle' as const,
      color: 'bg-green-500',
    },
    {
      type: OACampaignType.SEND_PERSONAL_MESSAGE,
      label: OA_CAMPAIGN_TYPE_LABELS[OACampaignType.SEND_PERSONAL_MESSAGE],
      icon: 'user' as const,
      color: 'bg-purple-500',
    },
    {
      type: OACampaignType.SEND_BROADCAST_TARGET_MESSAGE,
      label: OA_CAMPAIGN_TYPE_LABELS[OACampaignType.SEND_BROADCAST_TARGET_MESSAGE],
      icon: 'zap' as const,
      color: 'bg-orange-500',
    },
    {
      type: OACampaignType.SEND_GROUP_MESSAGE,
      label: OA_CAMPAIGN_TYPE_LABELS[OACampaignType.SEND_GROUP_MESSAGE],
      icon: 'send' as const,
      color: 'bg-red-500',
    },
  ];

  const handleTypeSelect = (type: OACampaignType) => {
    setSelectedType(type);
  };

  const handleBackToSelection = () => {
    setSelectedType(null);
  };

  const handleFormSubmit = (data: any) => {
    console.log('Form submitted:', data);
    // Success notification is handled by the individual form components
    // Just call onSuccess to close the form
    onSuccess?.();
  };

  // Hiển thị form chọn loại chiến dịch
  if (!selectedType) {
    return (
      <div className="w-full bg-background text-foreground">
        {/* Header */}
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="h5" className="font-semibold">
                {t('marketing:zalo.oaCampaigns.create.title', 'Tạo chiến dịch OA mới')}
              </Typography>
            </div>
            <Button variant="ghost" onClick={onCancel}>
              {t('common:cancel', 'Hủy')}
            </Button>
          </div>
        </div>

        {/* Campaign Type Selection */}
        <div className="px-6 pb-6">
          <ResponsiveGrid maxColumns={{ xs: 1, md: 2, lg: 2 }} gap={3}>
            {campaignTypes.map(campaignType => (
              <Card
                key={campaignType.type}
                className="p-4 cursor-pointer hover:shadow-md transition-shadow border border-border hover:border-primary/20"
                onClick={() => handleTypeSelect(campaignType.type)}
              >
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0">
                    <Icon name={campaignType.icon} size="lg" className="text-primary" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <Typography variant="body1" className="font-medium text-foreground">
                      {campaignType.label}
                    </Typography>
                  </div>
                </div>
              </Card>
            ))}
          </ResponsiveGrid>
        </div>
      </div>
    );
  }

  // Hiển thị form cụ thể cho loại chiến dịch đã chọn
  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <div>
            <Typography variant="h5" className="font-semibold">
              {OA_CAMPAIGN_TYPE_LABELS[selectedType]}
            </Typography>
          </div>
          <Button variant="ghost" onClick={handleBackToSelection}>
            {t('common:back', 'Quay lại')}
          </Button>
        </div>
      </div>

      {/* Form Content */}
      <div>
        {selectedType === OACampaignType.ADD_MEMBER_TO_GROUP && (
          <AddMemberToGroupForm onSubmit={handleFormSubmit} />
        )}

        {selectedType === OACampaignType.SEND_OA_MESSAGE && (
          <SendOAMessageForm onSubmit={handleFormSubmit} />
        )}

        {selectedType === OACampaignType.SEND_PERSONAL_MESSAGE && (
          <PersonalMessageForm onSubmit={handleFormSubmit} />
        )}

        {selectedType === OACampaignType.SEND_BROADCAST_TARGET_MESSAGE && (
          <BroadcastTargetMessageForm onSubmit={handleFormSubmit} />
        )}

        {selectedType === OACampaignType.SEND_GROUP_MESSAGE && (
          <GroupMessageForm onSubmit={handleFormSubmit} />
        )}
      </div>
    </div>
  );
};

// Component form cho "Thêm thành viên vào group"
interface AddMemberToGroupFormProps {
  onSubmit: (data: any) => void;
}

interface FormData {
  integrationId: string;
  selectedGroups: string[];
  selectedAudiences: number[];
  inviteAllAudiences: boolean;
  inviteToAllGroups: boolean;
  inviteAllOAs: boolean;
}

const AddMemberToGroupForm: React.FC<AddMemberToGroupFormProps> = ({ onSubmit }) => {
  const { t } = useTranslation(['marketing', 'common']);
  const inviteMutation = useInviteAudiencesToZaloGroups();

  const [formData, setFormData] = useState<FormData>({
    integrationId: '',
    selectedGroups: [],
    selectedAudiences: [],
    inviteAllAudiences: false,
    inviteToAllGroups: false,
    inviteAllOAs: false,
  });

  // Load options cho OA/Integration select
  const loadIntegrationOptions = async ({
    search,
    page = 1,
    limit = 10,
  }: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      // Sử dụng service trực tiếp để có thể await
      const { ZaloService } = await import('../../services/zalo.service');
      const response = await ZaloService.getPaginatedAccounts({ search, page, limit });
      const accounts = response.result?.items || [];

      return {
        items: accounts.map((account: any) => ({
          value: account.id || account.oaId,
          label: account.name || account.displayName || `OA ${account.id}`,
          icon: account.avatarUrl ? (
            <img
              src={account.avatarUrl}
              alt={account.name || 'OA Avatar'}
              className="w-6 h-6 rounded-full object-cover"
            />
          ) : (
            <div className="w-6 h-6 rounded-full bg-muted flex items-center justify-center text-xs">
              OA
            </div>
          ),
          data: account, // Lưu toàn bộ data để sử dụng sau
        })),
        totalItems: response.result?.meta?.totalItems || 0,
        totalPages: response.result?.meta?.totalPages || 1,
        currentPage: response.result?.meta?.currentPage || page,
        hasMore:
          (response.result?.meta?.currentPage || page) < (response.result?.meta?.totalPages || 1),
      };
    } catch (error) {
      console.error('Error loading OA accounts:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: page,
        hasMore: false,
      };
    }
  };

  // Load options cho Groups select
  const loadGroupOptions = async ({
    search,
    page = 1,
    limit = 10,
  }: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      // Sử dụng service trực tiếp để có thể await
      const { ZaloGroupsService } = await import('../../services/zalo-groups.service');
      const response = await ZaloGroupsService.getZaloGroups({
        integrationId: formData.inviteAllOAs ? '' : formData.integrationId, // Filter theo integrationId đã chọn
        search,
        page,
        limit,
      });
      const groups = response.result?.items || [];

      return {
        items: groups.map((group: any) => ({
          value: group.id, // Sử dụng id (UUID - khóa chính) thay vì groupId (Zalo group ID)
          label: group.groupName || group.name || `Group ${group.id}`,
          icon: group.avatarUrl ? (
            <img
              src={group.avatarUrl}
              alt={group.groupName || 'Group Avatar'}
              className="w-6 h-6 rounded-full object-cover"
            />
          ) : (
            <div className="w-6 h-6 rounded-full bg-muted flex items-center justify-center text-xs">
              GR
            </div>
          ),
          data: group, // Lưu toàn bộ data để sử dụng sau
        })),
        totalItems: response.result?.meta?.totalItems || 0,
        totalPages: response.result?.meta?.totalPages || 1,
        currentPage: response.result?.meta?.currentPage || page,
        hasMore:
          (response.result?.meta?.currentPage || page) < (response.result?.meta?.totalPages || 1),
      };
    } catch (error) {
      console.error('Error loading groups:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: page,
        hasMore: false,
      };
    }
  };

  // Load options cho Audiences select
  const loadAudienceOptions = async ({
    search,
    page = 1,
    limit = 10,
  }: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      // Sử dụng service trực tiếp để có thể await
      const { MarketingAudiencesService } = await import(
        '../../services/marketing-audiences.service'
      );
      const response = await MarketingAudiencesService.getMarketingAudiences({
        search,
        page,
        limit,
        platform: MarketingPlatform.ZALO,
        integrationId: formData.inviteAllOAs ? undefined : formData.integrationId, // Filter theo integrationId đã chọn
      });
      const audiences = response.result?.data || [];

      return {
        items: audiences.map((audience: any) => ({
          value: parseInt(audience.id),
          label: audience.name || audience.email || `Audience ${audience.id}`,
          icon: audience.avatarUrl ? (
            <img
              src={audience.avatarUrl}
              alt={audience.name || 'Audience Avatar'}
              className="w-6 h-6 rounded-full object-cover"
            />
          ) : (
            <div className="w-6 h-6 rounded-full bg-muted flex items-center justify-center text-xs">
              AU
            </div>
          ),
          data: audience, // Lưu toàn bộ data để sử dụng sau
        })),
        totalItems: response.result?.meta?.total || 0,
        totalPages: response.result?.meta?.totalPages || 1,
        currentPage: response.result?.meta?.page || page,
        hasMore: (response.result?.meta?.page || page) < (response.result?.meta?.totalPages || 1),
      };
    } catch (error) {
      console.error('Error loading audiences:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: page,
        hasMore: false,
      };
    }
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Validation
    if (!formData.inviteAllOAs && !formData.integrationId) {
      alert('Vui lòng chọn OA hoặc chọn "Tất cả OA"');
      return;
    }
    if (!formData.inviteToAllGroups && formData.selectedGroups.length === 0) {
      alert('Vui lòng chọn ít nhất một nhóm hoặc chọn "Tất cả nhóm"');
      return;
    }
    if (!formData.inviteAllAudiences && formData.selectedAudiences.length === 0) {
      alert('Vui lòng chọn ít nhất một audience hoặc chọn "Tất cả audience"');
      return;
    }

    try {
      const payload: InviteAudiencesToZaloGroupsDto = {
        inviteAllAudiences: formData.inviteAllAudiences,
        audienceIds: formData.inviteAllAudiences ? undefined : formData.selectedAudiences,
        inviteToAllGroups: formData.inviteToAllGroups,
        groupIds: formData.inviteToAllGroups ? undefined : formData.selectedGroups, // Truyền id (UUID) chứ không phải groupId
        integrationId: formData.inviteAllOAs ? '' : formData.integrationId, // Empty string when inviting to all OAs
      };

      await inviteMutation.mutateAsync(payload);
      onSubmit({
        type: OACampaignType.ADD_MEMBER_TO_GROUP,
        payload,
        formData, // Include form data for reference
      });
    } catch (error) {
      console.error('Error inviting audiences to groups:', error);
    }
  };

  return (
    <Card>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* OA Selection */}
        <div className="space-y-3">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-foreground">
              Chọn OA <span className="text-red-500">*</span>
            </label>
            <AsyncSelectWithPagination
              value={formData.integrationId}
              onChange={value =>
                setFormData({
                  ...formData,
                  integrationId: value as string,
                  // Reset groups và audiences khi thay đổi OA
                  selectedGroups: [],
                  selectedAudiences: [],
                })
              }
              loadOptions={loadIntegrationOptions}
              placeholder="Chọn Zalo Official Account"
              fullWidth
              disabled={formData.inviteAllOAs}
            />
          </div>

          <Checkbox
            checked={formData.inviteAllOAs}
            onChange={checked =>
              setFormData({
                ...formData,
                inviteAllOAs: checked,
                integrationId: checked ? '' : formData.integrationId,
                // Reset groups và audiences khi thay đổi OA selection
                selectedGroups: [],
                selectedAudiences: [],
              })
            }
            label="Sử dụng tất cả OA hiện có"
          />
        </div>

        {/* Groups Selection */}
        <div className="space-y-3">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-foreground">Chọn nhóm</label>
            <AsyncSelectWithPagination
              value={formData.selectedGroups}
              onChange={value => setFormData({ ...formData, selectedGroups: value as string[] })}
              loadOptions={loadGroupOptions}
              placeholder="Chọn nhóm Zalo"
              multiple
              fullWidth
              disabled={
                formData.inviteToAllGroups || (!formData.inviteAllOAs && !formData.integrationId)
              }
              parentLoading={!formData.inviteAllOAs && !formData.integrationId}
            />
          </div>

          <Checkbox
            checked={formData.inviteToAllGroups}
            onChange={checked =>
              setFormData({
                ...formData,
                inviteToAllGroups: checked,
                selectedGroups: checked ? [] : formData.selectedGroups,
              })
            }
            label="Mời vào tất cả nhóm"
          />
        </div>

        {/* Audiences Selection */}
        <div className="space-y-3">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-foreground">Chọn audience</label>
            <AsyncSelectWithPagination
              value={formData.selectedAudiences}
              onChange={value => setFormData({ ...formData, selectedAudiences: value as number[] })}
              loadOptions={loadAudienceOptions}
              placeholder="Chọn audience"
              multiple
              fullWidth
              disabled={
                formData.inviteAllAudiences || (!formData.inviteAllOAs && !formData.integrationId)
              }
              parentLoading={!formData.inviteAllOAs && !formData.integrationId}
              autoLoadInitial={false} // Chỉ gọi API khi mở dropdown
            />
          </div>

          <Checkbox
            checked={formData.inviteAllAudiences}
            onChange={checked =>
              setFormData({
                ...formData,
                inviteAllAudiences: checked,
                selectedAudiences: checked ? [] : formData.selectedAudiences,
              })
            }
            label="Mời tất cả audience"
          />
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button
            type="submit"
            variant="primary"
            isLoading={inviteMutation.isPending}
            disabled={inviteMutation.isPending}
          >
            {t('marketing:zalo.oaCampaigns.create.createButton', 'Tạo chiến dịch')}
          </Button>
        </div>
      </form>
    </Card>
  );
};

// Component form cho "Gửi tin nhắn hàng loạt"
interface GroupMessageFormProps {
  onSubmit: (data: any) => void;
}

interface GroupMessageFormData {
  integrationId: string;
  campaignName: string;
  description: string;
  tags: string[];
  selectedGroups: string[];
  messageChain: MessageChainItem[];
  scheduledAt?: number;
}

const GroupMessageForm: React.FC<GroupMessageFormProps> = ({ onSubmit }) => {
  const { t } = useTranslation(['marketing', 'common']);
  const { formRef, setFormErrors } = useFormErrors();
  const createCampaignMutation = useCreateZaloOACampaign();

  const [formData, setFormData] = useState<GroupMessageFormData>({
    integrationId: '',
    campaignName: '',
    description: '',
    tags: [],
    selectedGroups: [],
    messageChain: [],
  });

  const [selectedGroupsList, setSelectedGroupsList] = useState<any[]>([]);
  const [groupDataMap, setGroupDataMap] = useState<Map<string, any>>(new Map());

  // Columns for selected groups table
  const groupColumns = useMemo(
    () => [
      {
        key: 'groupName',
        title: t('marketing:zalo.groups.name', 'Tên nhóm'),
        render: (_: unknown, record: any) => (
          <Typography variant="body2">
            {record.groupName || record.name || 'Không có tên'}
          </Typography>
        ),
      },
      {
        key: 'groupId',
        title: t('marketing:zalo.groups.groupId', 'Group ID'),
        render: (_: unknown, record: any) => (
          <Typography variant="body2" className="text-muted-foreground">
            {record.groupId || 'Không có Group ID'}
          </Typography>
        ),
      },
      {
        key: 'memberCount',
        title: t('marketing:zalo.groups.memberCount', 'Số thành viên'),
        render: (_: unknown, record: any) => (
          <Typography variant="body2" className="text-muted-foreground">
            {record.memberCount || 0}
          </Typography>
        ),
      },
    ],
    [t]
  );

  const dataTable = useDataTable(
    useDataTableConfig({
      columns: groupColumns,
      createQueryParams: () => ({}),
    })
  );

  // Normalize group data to consistent format
  const normalizeGroupData = (group: any): any => ({
    id: group.id,
    groupId: group.groupId,
    groupName: group.groupName || group.name,
    avatarUrl: group.avatarUrl,
    memberCount: group.memberCount || 0,
    integrationId: group.integrationId,
    createdAt: group.createdAt,
    updatedAt: group.updatedAt,
    ...group,
  });

  // Update selectedGroupsList when formData.selectedGroups or groupDataMap changes
  useEffect(() => {
    const selectedData = formData.selectedGroups
      .map(id => {
        const data = groupDataMap.get(id);
        return data;
      })
      .filter(Boolean);

    setSelectedGroupsList(selectedData);
  }, [formData.selectedGroups, groupDataMap]);

  // Load options for Integration select
  const loadIntegrationOptions = async ({
    search,
    page = 1,
    limit = 10,
  }: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const { ZaloService } = await import('../../services/zalo.service');
      const response = await ZaloService.getPaginatedAccounts({ search, page, limit });
      const accounts = response.result?.items || [];

      return {
        items: accounts.map((account: any) => ({
          value: account.id || account.oaId,
          label: account.name || account.displayName || `OA ${account.id}`,
          icon: account.avatarUrl ? (
            <img
              src={account.avatarUrl}
              alt={account.name || 'OA Avatar'}
              className="w-6 h-6 rounded-full object-cover"
            />
          ) : (
            <div className="w-6 h-6 rounded-full bg-muted flex items-center justify-center text-xs">
              OA
            </div>
          ),
          data: account,
        })),
        totalItems: response.result?.meta?.totalItems || 0,
        totalPages: response.result?.meta?.totalPages || 1,
        currentPage: response.result?.meta?.currentPage || page,
        hasMore:
          (response.result?.meta?.currentPage || page) < (response.result?.meta?.totalPages || 1),
      };
    } catch (error) {
      console.error('Error loading OA accounts:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: page,
        hasMore: false,
      };
    }
  };

  // Load options for Groups select
  const loadGroupOptions = async ({
    search,
    page = 1,
    limit = 10,
  }: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const { ZaloGroupsService } = await import('../../services/zalo-groups.service');
      const response = await ZaloGroupsService.getZaloGroups({
        integrationId: formData.integrationId,
        search,
        page,
        limit,
      });

      // Response structure: { result: { items: [...], meta: {...} } }
      const groups = response.result?.items || [];

      // Save data to map and create options
      const normalizedGroups = groups.map(group => normalizeGroupData(group));

      setGroupDataMap(prev => {
        const newMap = new Map(prev);
        normalizedGroups.forEach(normalized => {
          newMap.set(normalized.id, normalized);
        });
        return newMap;
      });

      const items = normalizedGroups.map((normalized: any) => ({
        value: normalized.id,
        label: normalized.groupName,
        icon: normalized.avatarUrl ? (
          <img
            src={normalized.avatarUrl}
            alt={normalized.groupName}
            className="w-6 h-6 rounded-full object-cover"
          />
        ) : (
          <div className="w-6 h-6 rounded-full bg-muted flex items-center justify-center text-xs">
            GR
          </div>
        ),
        data: normalized,
      }));

      return {
        items,
        totalItems: response.result?.meta?.totalItems || 0,
        totalPages: response.result?.meta?.totalPages || 1,
        currentPage: response.result?.meta?.currentPage || page,
        hasMore:
          (response.result?.meta?.currentPage || page) < (response.result?.meta?.totalPages || 1),
      };
    } catch (error) {
      console.error('Error loading groups:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: page,
        hasMore: false,
      };
    }
  };

  const handleGroupChange = (selectedIds: string | number | string[] | number[] | undefined) => {
    let ids: string[] = [];
    if (Array.isArray(selectedIds)) {
      ids = selectedIds.map(id => String(id));
    } else if (selectedIds !== undefined) {
      ids = [String(selectedIds)];
    }

    setFormData(prev => ({ ...prev, selectedGroups: ids }));
  };

  const handleSubmit = async (data: any) => {
    console.log('🚀 Group Message Form submitted with data:', data);
    console.log('🚀 Current formData:', formData);

    // Use current form data for validation
    const currentData = { ...formData, ...data };

    // Validation
    const errors: Record<string, string> = {};
    if (!currentData.campaignName?.trim()) {
      errors.campaignName = 'Vui lòng nhập tên chiến dịch';
    }
    if (!currentData.integrationId?.trim()) {
      errors.integrationId = 'Vui lòng chọn Zalo OA';
    }
    if (!currentData.selectedGroups?.length) {
      errors.selectedGroups = 'Vui lòng chọn ít nhất một nhóm';
    }
    if (!currentData.messageChain?.length) {
      errors.messageChain = 'Vui lòng thêm ít nhất một tin nhắn vào chuỗi';
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      // Get selected groups' groupIds for API
      const groupIds = currentData.selectedGroups
        .map((id: string) => {
          const group = groupDataMap.get(id);
          return group?.groupId;
        })
        .filter(Boolean);

      // Transform messageChain to API format
      const transformedMessages = currentData.messageChain.map(
        (message: MessageChainItem, index: number) => {
          const baseMessage = {
            messageType: message.type.toLowerCase(),
            delaySeconds: index * 2, // 2 seconds delay between messages
          };

          switch (message.type) {
            case 'TEXT':
              return {
                ...baseMessage,
                content: message.content,
              };
            case 'IMAGE':
              return {
                ...baseMessage,
                imageUrl: message.metadata?.imageUrl || message.content,
                caption: message.metadata?.imageMessage || '',
              };
            default:
              return {
                ...baseMessage,
                content: message.content,
              };
          }
        }
      );

      // Prepare API payload according to the structure you provided
      const campaignPayload = {
        integrationId: currentData.integrationId,
        name: currentData.campaignName,
        description:
          currentData.description || `Gửi tin nhắn hàng loạt đến ${groupIds.length} nhóm`,
        tags: currentData.tags || [],
        type: 'message' as const, // Use 'message' type as supported by API
        scheduledAt: currentData.scheduledAt || Date.now(),
        messageContent: {
          type: 'group_message',
          groupIds: groupIds,
          messages: transformedMessages,
        },
      };

      console.log('🚀 Group Message Campaign Payload:', JSON.stringify(campaignPayload, null, 2));

      // Call API
      await createCampaignMutation.mutateAsync(campaignPayload as CreateZaloOACampaignDto);

      // Call parent onSubmit for additional handling
      onSubmit({
        type: 'GROUP_MESSAGE',
        formData: currentData,
        apiPayload: campaignPayload,
      });
    } catch (error) {
      console.error('Error creating group message campaign:', error);
    }
  };

  return (
    <Card>
      <Form ref={formRef} onSubmit={handleSubmit} defaultValues={formData} className="space-y-6">
        {/* Campaign Name */}
        <FormItem label="Tên chiến dịch" name="campaignName" required>
          <Input
            value={formData.campaignName}
            onChange={e => setFormData(prev => ({ ...prev, campaignName: e.target.value }))}
            placeholder="Nhập tên chiến dịch"
            fullWidth
          />
        </FormItem>

        {/* Description */}
        <FormItem label="Mô tả chiến dịch" name="description">
          <Input
            value={formData.description}
            onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Nhập mô tả chiến dịch"
            fullWidth
          />
        </FormItem>

        {/* Zalo OA Selection */}
        <FormItem label="Tài khoản OA" name="integrationId" required>
          <AsyncSelectWithPagination
            value={formData.integrationId || ''}
            onChange={(value: string | string[] | number | number[] | undefined) =>
              setFormData(prev => ({
                ...prev,
                integrationId: (value as string) || '',
                selectedGroups: [], // Reset groups when OA changes
              }))
            }
            loadOptions={loadIntegrationOptions}
            placeholder="Chọn Zalo Official Account"
            debounceTime={300}
            itemsPerPage={20}
            autoLoadInitial={true}
            searchOnEnter={true}
            fullWidth
          />
        </FormItem>

        {/* Tags */}
        <FormItem label="Tag" name="tags">
          <TagsInput
            fieldName="tags"
            formRef={formRef}
            placeholder="Nhập tag và nhấn Enter"
            initialValue={formData.tags}
            onChange={tags => setFormData(prev => ({ ...prev, tags }))}
          />
        </FormItem>

        {/* Groups Selection */}
        <FormItem label="Danh sách nhóm Zalo" name="selectedGroups" required>
          <AsyncSelectWithPagination
            value={formData.selectedGroups}
            onChange={handleGroupChange}
            loadOptions={loadGroupOptions}
            placeholder="Chọn nhóm"
            multiple
            fullWidth
            disabled={!formData.integrationId}
            parentLoading={!formData.integrationId}
            autoLoadInitial={false}
            searchOnEnter={false}
          />
        </FormItem>

        {/* Selected Groups Table */}
        {formData.selectedGroups.length > 0 && (
          <div className="mt-4">
            <Typography variant="h6" className="mb-4">
              Nhóm đã chọn ({formData.selectedGroups.length})
            </Typography>
            <Table
              columns={dataTable.columnVisibility.visibleTableColumns}
              data={selectedGroupsList}
              loading={false}
            />
          </div>
        )}

        {/* Message Content Section */}
        <div className="space-y-4">
          <Typography variant="h6">Phần soạn thảo nội dung tin nhắn</Typography>
          <MessageChainEditor
            messageChain={formData.messageChain}
            onChange={messageChain => setFormData(prev => ({ ...prev, messageChain }))}
            integrationId={formData.integrationId}
          />
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button
            onClick={() => {
              console.log('🚀 Current form data before submit:', formData);
              handleSubmit(formData);
            }}
            variant="primary"
            isLoading={createCampaignMutation.isPending}
            disabled={createCampaignMutation.isPending}
          >
            {t('marketing:zalo.oaCampaigns.create.createButton', 'Tạo chiến dịch')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default CreateOACampaignForm;
