/**
 * ChatInput Component
 * Component input chat với upload button, emoji picker và send button
 */

import React, { useState, useRef, useCallback, KeyboardEvent } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Icon, Typography } from '@/shared/components/common';
import { useSendMessage } from '../../../hooks/zalo/useZaloChat';
import type { SendMessageRequest } from '../../../types/zalo-chat.types';
import { FILE_UPLOAD_LIMITS } from '../../../constants/zalo-chat.constants';

interface ChatInputProps {
  conversationId?: string;
  onSendMessage?: (message: SendMessageRequest) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

/**
 * Component hiển thị file preview
 */
const FilePreview: React.FC<{
  files: File[];
  onRemove: (index: number) => void;
}> = ({ files, onRemove }) => {
  const { t } = useTranslation(['marketing', 'common']);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const isImageFile = (file: File): boolean => {
    return (FILE_UPLOAD_LIMITS.ALLOWED_IMAGE_TYPES as readonly string[]).includes(file.type);
  };

  if (files.length === 0) return null;

  return (
    <div className="border-t border-gray-200 dark:border-gray-700 p-3 bg-gray-50 dark:bg-gray-800">
      <Typography variant="caption" className="text-gray-600 dark:text-gray-400 mb-2 block">
        {t('marketing:zalo.chat.attachedFiles')} ({files.length})
      </Typography>
      <div className="flex flex-wrap gap-2">
        {files.map((file, index) => (
          <div
            key={index}
            className="relative flex items-center space-x-2 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-2 max-w-xs"
          >
            {/* File icon or image preview */}
            <div className="flex-shrink-0">
              {isImageFile(file) ? (
                <img
                  src={URL.createObjectURL(file)}
                  alt={file.name}
                  className="w-8 h-8 object-cover rounded"
                />
              ) : (
                <Icon name="file" size="sm" className="text-gray-500" />
              )}
            </div>

            {/* File info */}
            <div className="flex-1 min-w-0">
              <Typography variant="caption" className="font-medium text-gray-900 dark:text-gray-100 truncate block">
                {file.name}
              </Typography>
              <Typography variant="caption" className="text-gray-500">
                {formatFileSize(file.size)}
              </Typography>
            </div>

            {/* Remove button */}
            <button
              onClick={() => onRemove(index)}
              className="flex-shrink-0 p-1 text-gray-400 hover:text-red-500 transition-colors"
            >
              <Icon name="x" size="xs" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * Component ChatInput chính
 */
const ChatInput: React.FC<ChatInputProps> = ({
  conversationId,
  onSendMessage,
  disabled = false,
  placeholder,
  className = '',
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [message, setMessage] = useState('');
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { sendMessage, isSending } = useSendMessage();
  // const { uploadFile } = useFileUpload();

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  }, []);

  // Handle message change
  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    adjustTextareaHeight();
  };

  // Handle key press
  const handleKeyPress = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    
    // Validate file count
    if (attachedFiles.length + files.length > FILE_UPLOAD_LIMITS.MAX_FILES) {
      alert(t('marketing:zalo.chat.errors.tooManyFiles', { max: FILE_UPLOAD_LIMITS.MAX_FILES }));
      return;
    }

    // Validate file size and type
    const validFiles: File[] = [];
    for (const file of files) {
      if (file.size > FILE_UPLOAD_LIMITS.MAX_SIZE) {
        alert(t('marketing:zalo.chat.errors.fileTooLarge', { 
          name: file.name, 
          max: FILE_UPLOAD_LIMITS.MAX_SIZE / 1024 / 1024 
        }));
        continue;
      }

      const isValidType = [
        ...(FILE_UPLOAD_LIMITS.ALLOWED_IMAGE_TYPES as readonly string[]),
        ...(FILE_UPLOAD_LIMITS.ALLOWED_FILE_TYPES as readonly string[]),
      ].includes(file.type);

      if (!isValidType) {
        alert(t('marketing:zalo.chat.errors.invalidFileType', { name: file.name }));
        continue;
      }

      validFiles.push(file);
    }

    setAttachedFiles(prev => [...prev, ...validFiles]);
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Remove attached file
  const handleRemoveFile = (index: number) => {
    setAttachedFiles(prev => prev.filter((_, i) => i !== index));
  };

  // Handle send message
  const handleSendMessage = async () => {
    if (!conversationId) return;
    
    const trimmedMessage = message.trim();
    if (!trimmedMessage && attachedFiles.length === 0) return;

    const messageData: SendMessageRequest = {
      conversationId,
      type: attachedFiles.length > 0 ? 'file' : 'text',
      content: trimmedMessage,
      attachments: attachedFiles.length > 0 ? attachedFiles : undefined,
    };

    try {
      if (onSendMessage) {
        onSendMessage(messageData);
      } else {
        await sendMessage(messageData);
      }

      // Clear input after successful send
      setMessage('');
      setAttachedFiles([]);
      adjustTextareaHeight();
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  // Handle emoji picker (placeholder for now)
  const handleEmojiClick = () => {
    // TODO: Implement emoji picker
    console.log('Emoji picker clicked');
  };

  const canSend = (message.trim() || attachedFiles.length > 0) && !disabled && !isSending;

  return (
    <div className={`bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 ${className}`}>
      {/* File preview */}
      <FilePreview files={attachedFiles} onRemove={handleRemoveFile} />

      {/* Input area */}
      <div className="p-4">
        <div className="flex items-end space-x-2">
          {/* File upload button */}
          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled || attachedFiles.length >= FILE_UPLOAD_LIMITS.MAX_FILES}
            className="flex-shrink-0 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title={t('marketing:zalo.chat.attachFile')}
          >
            <Icon name="paperclip" size="lg" />
          </button>

          {/* Hidden file input */}
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={[
              ...(FILE_UPLOAD_LIMITS.ALLOWED_IMAGE_TYPES as readonly string[]),
              ...(FILE_UPLOAD_LIMITS.ALLOWED_FILE_TYPES as readonly string[]),
            ].join(',')}
            onChange={handleFileSelect}
            className="hidden"
          />

          {/* Message input */}
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={handleMessageChange}
              onKeyPress={handleKeyPress}
              placeholder={placeholder || t('marketing:zalo.chat.typeMessage')}
              disabled={disabled}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-full bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
              rows={1}
              style={{ minHeight: '40px', maxHeight: '120px' }}
            />
          </div>

          {/* Emoji button */}
          <button
            onClick={handleEmojiClick}
            disabled={disabled}
            className="flex-shrink-0 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title={t('marketing:zalo.chat.emoji')}
          >
            <Icon name="smile" size="lg" />
          </button>

          {/* Send button */}
          <Button
            onClick={handleSendMessage}
            disabled={!canSend}
            isLoading={isSending}
            variant="primary"
            size="sm"
            className="flex-shrink-0 rounded-full w-10 h-10 p-0 flex items-center justify-center"
            title={t('marketing:zalo.chat.sendMessage')}
          >
            <Icon name="send" size="sm" />
          </Button>
        </div>

        {/* Character count */}
        {message.length > 1800 && (
          <div className="mt-2 text-right">
            <Typography
              variant="caption"
              className={message.length > 2000 ? 'text-red-500' : 'text-gray-500'}
            >
              {message.length}/2000
            </Typography>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatInput;
