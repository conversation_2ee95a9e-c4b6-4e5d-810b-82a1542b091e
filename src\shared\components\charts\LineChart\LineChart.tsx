import React, { useMemo } from 'react';
import {
  LineChart as Recha<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Label,
  TooltipProps,
} from 'recharts';
import { LineChartProps } from './LineChart.types';
import { useTheme } from '@/shared/contexts/theme';

/**
 * Custom Tooltip component với theme support
 */
const CustomTooltip: React.FC<TooltipProps<any, any>> = ({ active, payload, label }) => {
  const { currentTheme } = useTheme();

  if (!active || !payload || !payload.length) {
    return null;
  }

  return (
    <div
      className="rounded-lg shadow-lg border p-3 min-w-[120px] relative"
      style={{
        backgroundColor: currentTheme?.semanticColors?.card || '#FFFFFF',
        borderColor: currentTheme?.semanticColors?.border || '#E5E7EB',
        color: currentTheme?.semanticColors?.foreground || '#111827',
        zIndex: 9999, // <PERSON><PERSON><PERSON> bảo tooltip nổi lên trên
        boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      }}
    >
      {/* Label (thời gian) */}
      <div
        className="text-sm font-medium mb-2"
        style={{ color: currentTheme?.semanticColors?.foreground || '#111827' }}
      >
        {formatPeriodLabel(label)}
      </div>

      {/* Data items */}
      {payload.map((entry, index) => (
        <div key={index} className="flex items-center gap-2 text-sm">
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: entry.color }}
          />
          <span
            className="font-medium"
            style={{ color: currentTheme?.semanticColors?.foreground || '#111827' }}
          >
            {entry.name}:
          </span>
          <span
            className="font-semibold"
            style={{ color: currentTheme?.semanticColors?.foreground || '#111827' }}
          >
            {typeof entry.value === 'number' ? entry.value.toLocaleString() : entry.value}
          </span>
        </div>
      ))}
    </div>
  );
};

/**
 * Tính ngày đầu tuần (thứ 2) từ năm và số tuần ISO
 */
const getWeekStartDate = (year: number, week: number): Date => {
  // Ngày 4 tháng 1 luôn thuộc tuần đầu tiên của năm theo ISO
  const jan4 = new Date(year, 0, 4);

  // Tìm thứ 2 của tuần đầu tiên
  const firstMonday = new Date(jan4);
  firstMonday.setDate(jan4.getDate() - ((jan4.getDay() + 6) % 7));

  // Tính ngày đầu tuần cần tìm
  const targetWeekStart = new Date(firstMonday);
  targetWeekStart.setDate(firstMonday.getDate() + (week - 1) * 7);

  return targetWeekStart;
};

/**
 * Format period label từ API
 * - 2025-W30 -> 21/07 - 27/07/2025 (thứ 2 đến chủ nhật)
 * - 2025-03 -> Tháng 3, 2025
 * - 2025-03-15 -> 15/03/2025
 */
const formatPeriodLabel = (label: string): string => {
  if (!label) return '';

  // Xử lý format tuần: 2025-W30
  if (label.includes('-W')) {
    const [year, weekPart] = label.split('-W');
    const weekNumber = parseInt(weekPart);
    const yearNum = parseInt(year);

    // Tính ngày đầu tuần (thứ 2) và cuối tuần (chủ nhật)
    const weekStart = getWeekStartDate(yearNum, weekNumber);
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);

    // Format: 21/07 - 27/07/2025
    const startStr = weekStart.toLocaleDateString('vi-VN', { day: '2-digit', month: '2-digit' });
    const endStr = weekEnd.toLocaleDateString('vi-VN', { day: '2-digit', month: '2-digit' });

    return `${startStr} - ${endStr}/${yearNum}`;
  }

  // Xử lý format tháng: 2025-03
  if (/^\d{4}-\d{2}$/.test(label)) {
    const [year, month] = label.split('-');
    const monthNames = [
      'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
      'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
    ];
    return `${monthNames[parseInt(month) - 1]}, ${year}`;
  }

  // Xử lý format ngày: 2025-03-15
  if (/^\d{4}-\d{2}-\d{2}$/.test(label)) {
    const date = new Date(label);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  // Trả về label gốc nếu không match format nào
  return label;
};

/**
 * LineChart component hiển thị dữ liệu dạng đường
 *
 * @example
 * ```tsx
 * <LineChart
 *   data={[
 *     { month: 'Jan', sales: 100, profit: 50 },
 *     { month: 'Feb', sales: 200, profit: 100 },
 *     { month: 'Mar', sales: 150, profit: 75 },
 *   ]}
 *   xAxisKey="month"
 *   lines={[
 *     { dataKey: 'sales', name: 'Sales', color: '#FF3333' },
 *     { dataKey: 'profit', name: 'Profit', color: '#FFCC99' },
 *   ]}
 *   height={300}
 *   showGrid
 *   showTooltip
 *   showLegend
 * />
 * ```
 */
const LineChart: React.FC<LineChartProps> = ({
  data,
  xAxisKey,
  lines,
  height = 300,
  width = '100%',
  showGrid = true,
  showTooltip = true,
  showLegend = true,
  legendPosition = 'bottom',
  // customLegend không được sử dụng do vấn đề tương thích kiểu
  margin = { top: 10, right: 30, left: 0, bottom: 0 },
  xAxisFormatter,
  yAxisFormatter,
  xAxisLabel,
  yAxisLabel,
  className = '',
  animated = true,
}) => {
  const { currentTheme } = useTheme();

  // Lấy màu từ theme
  const gridColor = useMemo(
    () => currentTheme?.semanticColors?.border || '#E5E7EB',
    [currentTheme]
  );
  const textColor = useMemo(
    () => currentTheme?.semanticColors?.foreground || '#111827',
    [currentTheme]
  );

  // Tạo các đường
  const renderLines = useMemo(() => {
    return lines.map((line, index) => (
      <Line
        key={`line-${index}`}
        type={line.type || 'monotone'}
        dataKey={line.dataKey}
        name={line.name || line.dataKey}
        stroke={line.color || '#FF3333'}
        strokeWidth={line.strokeWidth || 2}
        dot={line.showDot !== false ? { r: line.dotSize || 4 } : false}
        activeDot={{ r: (line.dotSize || 4) + 2 }}
        connectNulls={line.connectNulls !== false}
        isAnimationActive={animated}
      />
    ));
  }, [lines, animated]);

  // Tạo component chính
  const chart = (
    <RechartsLineChart data={data} margin={margin} className={className}>
      {showGrid && <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />}

      <XAxis dataKey={xAxisKey} tick={{ fill: textColor }} {...(xAxisFormatter && { tickFormatter: xAxisFormatter })}>
        {xAxisLabel && (
          <Label value={xAxisLabel} offset={-5} position="insideBottom" style={{ textAnchor: 'middle', fill: textColor }} />
        )}
      </XAxis>

      <YAxis tick={{ fill: textColor }} {...(yAxisFormatter && { tickFormatter: yAxisFormatter })}>
        {yAxisLabel && (
          <Label value={yAxisLabel} angle={-90} position="insideLeft" style={{ textAnchor: 'middle', fill: textColor }} />
        )}
      </YAxis>

      {showTooltip && <Tooltip content={<CustomTooltip />} />}

      {showLegend && (
        <Legend
          verticalAlign={
            legendPosition === 'top' || legendPosition === 'bottom' ? legendPosition : 'middle'
          }
          align={
            legendPosition === 'left' || legendPosition === 'right' ? legendPosition : 'center'
          }
        />
      )}

      {renderLines}
    </RechartsLineChart>
  );

  // Đảm bảo dữ liệu không rỗng
  if (!data || data.length === 0) {
    return <div>Không có dữ liệu</div>;
  }

  // Sử dụng div với kích thước cố định và ResponsiveContainer
  return (
    <div style={{
      width,
      height,
      position: 'relative',
      minHeight: '300px',
      overflow: 'visible', // Cho phép tooltip hiển thị ra ngoài
      zIndex: 1 // Đảm bảo container có z-index thấp hơn tooltip
    }}>
      {/* Sử dụng aspect ratio để đảm bảo biểu đồ luôn hiển thị */}
      <ResponsiveContainer width="100%" height="100%" aspect={16 / 9}>
        {chart}
      </ResponsiveContainer>
    </div>
  );
};

export default LineChart;
