import React from 'react';
import { type WidgetConfig, type WidgetRegistry, type RegisterWidgetOptions } from '../types';
import { type WidgetType } from '../constants';

/**
 * Widget Registry - Quản lý đăng ký và tạo widgets
 * Hỗ trợ lazy loading, type safety và auto-registration
 */
class WidgetRegistryManager {
  private registry: WidgetRegistry = {};
  private loadedWidgets: Set<string> = new Set();
  private loadingPromises: Map<string, Promise<React.ComponentType<Record<string, unknown>>>> = new Map();

  /**
   * Đăng ký một widget vào registry
   */
  register(config: WidgetConfig, options: RegisterWidgetOptions = {}): void {
    const { override = false } = options;

    console.log(`🔄 WidgetRegistry.register: ${config.type}`, { config, options });

    // Kiểm tra widget đã tồn tại
    if (this.registry[config.type] && !override) {
      console.warn(`Widget type "${config.type}" already registered. Use override: true to replace.`);
      return;
    }

    try {
      // Validate config
      this.validateConfig(config);

      // Đăng ký widget
      this.registry[config.type] = {
        ...config,
        component: options.lazy ? this.createLazyComponent(config) : config.component,
      };

      console.log(`✅ WidgetRegistry.register success: ${config.type}`);
    } catch (error) {
      console.error(`❌ WidgetRegistry.register failed: ${config.type}`, error);
      throw error;
    }
  }

  /**
   * Đăng ký nhiều widgets cùng lúc
   */
  registerBatch(configs: WidgetConfig[], options: RegisterWidgetOptions = {}): void {
    configs.forEach(config => this.register(config, options));
  }

  /**
   * Lấy widget config theo type
   */
  getConfig(type: WidgetType): WidgetConfig | undefined {
    return this.registry[type];
  }

  /**
   * Lấy widget component theo type
   */
  getComponent(type: WidgetType): React.ComponentType<Record<string, unknown>> | undefined {
    const config = this.registry[type];
    return config?.component;
  }

  /**
   * Kiểm tra widget có tồn tại không
   */
  hasWidget(type: WidgetType): boolean {
    return type in this.registry;
  }

  /**
   * Lấy tất cả widgets đã đăng ký
   */
  getAllWidgets(): WidgetRegistry {
    return { ...this.registry };
  }

  /**
   * Lấy widgets theo category
   */
  getWidgetsByCategory(category: string): WidgetConfig[] {
    return Object.values(this.registry).filter(config => config.category === category);
  }

  /**
   * Xóa widget khỏi registry
   */
  unregister(type: WidgetType): boolean {
    if (this.registry[type]) {
      delete this.registry[type];
      this.loadedWidgets.delete(type);
      this.loadingPromises.delete(type);
      return true;
    }
    return false;
  }

  /**
   * Clear toàn bộ registry
   */
  clear(): void {
    this.registry = {};
    this.loadedWidgets.clear();
    this.loadingPromises.clear();
  }

  /**
   * Preload widgets
   */
  async preloadWidgets(types: WidgetType[]): Promise<void> {
    const promises = types.map(type => this.loadWidget(type));
    await Promise.allSettled(promises);
  }

  /**
   * Load widget component (for lazy loading)
   */
  private async loadWidget(type: WidgetType): Promise<React.ComponentType<Record<string, unknown>> | undefined> {
    if (this.loadedWidgets.has(type)) {
      return this.getComponent(type);
    }

    if (this.loadingPromises.has(type)) {
      return this.loadingPromises.get(type);
    }

    const config = this.registry[type];
    if (!config) {
      throw new Error(`Widget type "${type}" not found in registry`);
    }

    try {
      const component = config.component;
      this.loadedWidgets.add(type);
      return component;
    } catch (error) {
      console.error(`Failed to load widget "${type}":`, error);
      throw error;
    }
  }

  /**
   * Tạo lazy component wrapper
   */
  private createLazyComponent(config: WidgetConfig): React.ComponentType<Record<string, unknown>> {
    return React.lazy(async () => {
      const component = await this.loadWidget(config.type);
      return { default: component || (() => null) };
    });
  }

  /**
   * Validate widget config
   */
  private validateConfig(config: WidgetConfig): void {
    const required = ['id', 'type', 'category', 'title', 'component', 'defaultSize'];
    const missing = required.filter(field => !(field in config) || config[field as keyof WidgetConfig] === undefined);
    
    if (missing.length > 0) {
      throw new Error(`Widget config validation failed. Missing fields: ${missing.join(', ')}`);
    }

    if (!config.defaultSize.w || !config.defaultSize.h) {
      throw new Error('Widget defaultSize must have valid w and h values');
    }

    // Validate component
    if (!config.component) {
      throw new Error('Widget component is required');
    }

    // Accept both regular components (function) and lazy components (object)
    const isFunction = typeof config.component === 'function';
    const isLazyComponent = typeof config.component === 'object' &&
                           config.component !== null &&
                           '$$typeof' in config.component;

    if (!isFunction && !isLazyComponent) {
      console.error('Invalid component:', config.component, {
        type: typeof config.component,
        isFunction,
        isLazyComponent,
        hasSymbol: config.component && '$$typeof' in config.component
      });
      throw new Error(`Widget component must be a React component or lazy component. Got: ${typeof config.component}`);
    }

    console.log(`✅ Component validation passed for "${config.type}":`, {
      isFunction,
      isLazyComponent,
      componentType: typeof config.component
    });
  }

  /**
   * Get registry statistics
   */
  getStats(): {
    total: number;
    loaded: number;
    categories: Record<string, number>;
  } {
    const configs = Object.values(this.registry);
    const categories: Record<string, number> = {};
    
    configs.forEach(config => {
      categories[config.category] = (categories[config.category] || 0) + 1;
    });

    return {
      total: configs.length,
      loaded: this.loadedWidgets.size,
      categories,
    };
  }
}

// Singleton instance
export const widgetRegistry = new WidgetRegistryManager();

// Export types
export type { WidgetRegistryManager };
export default widgetRegistry;
