/**
 * MessageBubble Component
 * Component hiển thị tin nhắn dạng bubble với avatar, nội dung và thời gian
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Image, Icon } from '@/shared/components/common';
import type { Message, MessageAttachment } from '../../../types/zalo-chat.types';
import { TIME_FORMATS } from '../../../constants/zalo-chat.constants';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';

interface MessageBubbleProps {
  message: Message;
  senderName?: string;
  senderAvatar?: string;
  showAvatar?: boolean;
  showTime?: boolean;
  isConsecutive?: boolean; // Tin nhắn liên tiếp từ cùng người gửi
  className?: string;
}

/**
 * Component hiển thị file đính kèm
 */
const AttachmentRenderer: React.FC<{ attachment: MessageAttachment }> = ({ attachment }) => {
  const { t } = useTranslation(['marketing', 'common']);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (attachment.type === 'image') {
    return (
      <div className="mt-2">
        <Image
          src={attachment.url}
          alt={attachment.name}
          className="max-w-xs rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
          onClick={() => window.open(attachment.url, '_blank')}
        />
        {attachment.name && (
          <Typography variant="caption" className="text-gray-500 mt-1 block">
            {attachment.name}
          </Typography>
        )}
      </div>
    );
  }

  return (
    <div className="mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 max-w-xs">
      <div className="flex items-center space-x-2">
        <Icon 
          name={attachment.type === 'video' ? 'play' : 'file'} 
          className="text-gray-500 flex-shrink-0" 
          size="sm"
        />
        <div className="flex-1 min-w-0">
          <Typography 
            variant="body2" 
            className="font-medium text-gray-900 dark:text-gray-100 truncate"
          >
            {attachment.name}
          </Typography>
          <Typography variant="caption" className="text-gray-500">
            {formatFileSize(attachment.size)}
          </Typography>
        </div>
      </div>
      <button
        onClick={() => window.open(attachment.url, '_blank')}
        className="mt-2 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
      >
        {t('common:download')}
      </button>
    </div>
  );
};

/**
 * Component MessageBubble
 */
const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  senderName,
  senderAvatar,
  showAvatar = true,
  showTime = true,
  isConsecutive = false,
  className = '',
}) => {
  const { t } = useTranslation(['marketing', 'common']);

  const isFromUser = message.senderType === 'user';
  const isFromContact = message.senderType === 'contact';

  // Format thời gian
  const formatMessageTime = (timestamp: string): string => {
    const messageDate = new Date(timestamp);
    const now = new Date();
    const isToday = messageDate.toDateString() === now.toDateString();
    
    if (isToday) {
      return format(messageDate, TIME_FORMATS.MESSAGE_TIME);
    } else {
      return format(messageDate, TIME_FORMATS.FULL_DATETIME, { locale: vi });
    }
  };

  // Xác định màu sắc bubble
  const getBubbleClasses = (): string => {
    if (isFromUser) {
      return 'bg-blue-500 text-white ml-auto';
    } else {
      return 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 mr-auto';
    }
  };

  // Xác định alignment
  const getContainerClasses = (): string => {
    return isFromUser ? 'flex-row-reverse' : 'flex-row';
  };

  return (
    <div className={`flex items-end space-x-2 mb-4 ${getContainerClasses()} ${className}`}>
      {/* Avatar */}
      {showAvatar && !isConsecutive && (
        <div className="flex-shrink-0">
          {senderAvatar ? (
            <Image
              src={senderAvatar}
              alt={senderName || t('marketing:zalo.chat.unknownSender')}
              className="w-8 h-8 rounded-full"
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
              <Icon name="user" size="sm" className="text-gray-600 dark:text-gray-300" />
            </div>
          )}
        </div>
      )}

      {/* Spacer khi không hiển thị avatar */}
      {showAvatar && isConsecutive && <div className="w-8" />}

      {/* Message content */}
      <div className="flex-1 max-w-[70%]">
        {/* Sender name (chỉ hiển thị cho tin nhắn từ contact và không consecutive) */}
        {isFromContact && !isConsecutive && senderName && (
          <Typography 
            variant="caption" 
            className="text-gray-500 mb-1 block"
          >
            {senderName}
          </Typography>
        )}

        {/* Message bubble */}
        <div className={`
          inline-block px-4 py-2 rounded-2xl max-w-full break-words
          ${getBubbleClasses()}
          ${isFromUser ? 'rounded-br-md' : 'rounded-bl-md'}
        `}>
          {/* Text content */}
          {message.content && (
            <Typography 
              variant="body2" 
              className={isFromUser ? 'text-white' : 'text-gray-900 dark:text-gray-100'}
            >
              {message.content}
            </Typography>
          )}

          {/* Attachments */}
          {message.attachments && message.attachments.length > 0 && (
            <div className="space-y-2">
              {message.attachments.map((attachment, index) => (
                <AttachmentRenderer key={index} attachment={attachment} />
              ))}
            </div>
          )}

          {/* Reply indicator */}
          {message.replyTo && (
            <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
              <Typography 
                variant="caption" 
                className={`italic ${isFromUser ? 'text-blue-100' : 'text-gray-500'}`}
              >
                {t('marketing:zalo.chat.replyTo')}
              </Typography>
            </div>
          )}
        </div>

        {/* Message status and time */}
        {showTime && (
          <div className={`flex items-center mt-1 space-x-1 ${isFromUser ? 'justify-end' : 'justify-start'}`}>
            <Typography variant="caption" className="text-gray-500">
              {formatMessageTime(message.timestamp)}
            </Typography>
            
            {/* Message status (chỉ hiển thị cho tin nhắn từ user) */}
            {isFromUser && (
              <div className="flex items-center">
                {message.status === 'sending' && (
                  <Icon name="clock" size="xs" className="text-gray-400" />
                )}
                {message.status === 'sent' && (
                  <Icon name="check" size="xs" className="text-gray-400" />
                )}
                {message.status === 'delivered' && (
                  <div className="flex">
                    <Icon name="check" size="xs" className="text-gray-400" />
                    <Icon name="check" size="xs" className="text-gray-400 -ml-1" />
                  </div>
                )}
                {message.status === 'read' && (
                  <div className="flex">
                    <Icon name="check" size="xs" className="text-blue-500" />
                    <Icon name="check" size="xs" className="text-blue-500 -ml-1" />
                  </div>
                )}
                {message.status === 'failed' && (
                  <Icon name="alert-circle" size="xs" className="text-red-500" />
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageBubble;
