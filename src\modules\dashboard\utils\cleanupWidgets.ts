import { widgetRegistry } from '../registry';
import { DashboardTabsState, DashboardWidget } from '../types';

/**
 * Utility để clean up các widget không hợp lệ khỏi dashboard
 */

const STORAGE_KEY = 'dashboard-tabs';

/**
 * Kiểm tra xem widget type có hợp lệ không
 */
export const isValidWidgetType = (type: string): boolean => {
  return widgetRegistry.hasWidget(type as any);
};

/**
 * Lọc ra các widget hợp lệ
 */
export const filterValidWidgets = (widgets: DashboardWidget[]): DashboardWidget[] => {
  return widgets.filter(widget => {
    const isValid = isValidWidgetType(widget.type);
    if (!isValid) {
      console.warn(`🗑️ Removing invalid widget: ${widget.type} (${widget.title})`);
    }
    return isValid;
  });
};

/**
 * Clean up dashboard tabs - xóa các widget không hợp lệ
 */
export const cleanupDashboardTabs = (): boolean => {
  try {
    const saved = localStorage.getItem(STORAGE_KEY);
    if (!saved) {
      console.log('✅ No dashboard data to clean up');
      return false;
    }

    const tabsState = JSON.parse(saved) as DashboardTabsState;
    let hasChanges = false;

    // Clean up widgets trong mỗi tab
    const cleanedTabs = tabsState.tabs.map(tab => {
      const originalCount = tab.widgets.length;
      const validWidgets = filterValidWidgets(tab.widgets);
      
      if (validWidgets.length !== originalCount) {
        hasChanges = true;
        console.log(`🧹 Cleaned tab "${tab.name}": ${originalCount} → ${validWidgets.length} widgets`);
      }

      return {
        ...tab,
        widgets: validWidgets,
        updatedAt: hasChanges ? new Date().toISOString() : tab.updatedAt,
      };
    });

    if (hasChanges) {
      const cleanedState: DashboardTabsState = {
        ...tabsState,
        tabs: cleanedTabs,
      };

      localStorage.setItem(STORAGE_KEY, JSON.stringify(cleanedState));
      console.log('✅ Dashboard cleanup completed');
      return true;
    } else {
      console.log('✅ No invalid widgets found');
      return false;
    }
  } catch (error) {
    console.error('❌ Error during dashboard cleanup:', error);
    return false;
  }
};

/**
 * Lấy danh sách các widget type không hợp lệ trong dashboard
 */
export const getInvalidWidgetTypes = (): string[] => {
  try {
    const saved = localStorage.getItem(STORAGE_KEY);
    if (!saved) return [];

    const tabsState = JSON.parse(saved) as DashboardTabsState;
    const invalidTypes = new Set<string>();

    tabsState.tabs.forEach(tab => {
      tab.widgets.forEach(widget => {
        if (!isValidWidgetType(widget.type)) {
          invalidTypes.add(widget.type);
        }
      });
    });

    return Array.from(invalidTypes);
  } catch (error) {
    console.error('❌ Error getting invalid widget types:', error);
    return [];
  }
};

/**
 * Reset dashboard về trạng thái mặc định
 */
export const resetDashboard = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEY);
    console.log('🔄 Dashboard reset to default state');
    
    // Reload trang để áp dụng thay đổi
    window.location.reload();
  } catch (error) {
    console.error('❌ Error resetting dashboard:', error);
  }
};

/**
 * Debug function - hiển thị thông tin về widgets trong dashboard
 */
export const debugDashboardWidgets = (): void => {
  try {
    const saved = localStorage.getItem(STORAGE_KEY);
    if (!saved) {
      console.log('📊 No dashboard data found');
      return;
    }

    const tabsState = JSON.parse(saved) as DashboardTabsState;
    
    console.log('📊 Dashboard Debug Info:');
    console.log(`📋 Total tabs: ${tabsState.tabs.length}`);
    console.log(`🎯 Current tab: ${tabsState.currentTabId}`);
    
    tabsState.tabs.forEach((tab, index) => {
      console.log(`\n📁 Tab ${index + 1}: "${tab.name}" (${tab.id})`);
      console.log(`   📦 Widgets: ${tab.widgets.length}`);
      
      tab.widgets.forEach((widget, widgetIndex) => {
        const isValid = isValidWidgetType(widget.type);
        const status = isValid ? '✅' : '❌';
        console.log(`   ${status} ${widgetIndex + 1}. ${widget.title} (${widget.type})`);
      });
    });

    const invalidTypes = getInvalidWidgetTypes();
    if (invalidTypes.length > 0) {
      console.log(`\n❌ Invalid widget types found: ${invalidTypes.join(', ')}`);
      console.log('💡 Run cleanupDashboardTabs() to fix this');
    }
  } catch (error) {
    console.error('❌ Error debugging dashboard widgets:', error);
  }
};

// Expose functions to window for debugging
if (typeof window !== 'undefined') {
  (window as any).dashboardCleanup = {
    cleanup: cleanupDashboardTabs,
    debug: debugDashboardWidgets,
    reset: resetDashboard,
    getInvalidTypes: getInvalidWidgetTypes,
  };
}

export default {
  cleanupDashboardTabs,
  debugDashboardWidgets,
  resetDashboard,
  getInvalidWidgetTypes,
  isValidWidgetType,
  filterValidWidgets,
};
