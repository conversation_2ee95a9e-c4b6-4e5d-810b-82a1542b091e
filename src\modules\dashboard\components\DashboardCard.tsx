import React from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Icon, IconCard } from '@/shared/components/common';
import { DashboardWidget } from '../types';
import { WIDGET_TYPES } from '../constants/widget-types';
import { renderWidgetContent } from './DashboardCard.utils';
import EditableWidgetTitle from './EditableWidgetTitle';
import { Move } from 'lucide-react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ResponsiveGridLayout = WidthProvider(Responsive);

// Layout type for react-grid-layout
interface Layout {
  i: string;
  x: number;
  y: number;
  w: number;
  h: number;
  minW?: number;
  minH?: number;
  maxW?: number;
  maxH?: number;
}

interface DashboardCardProps {
  widgets: DashboardWidget[];
  onLayoutChange?: (layout: Layout[], layouts: { [key: string]: Layout[] }) => void;
  onRemoveWidget?: (widgetId: string) => void;
  onWidgetTitleChange?: (widgetId: string, newTitle: string) => void;
  onWidgetPropsChange?: (widgetId: string, newProps: Record<string, unknown>) => void;
  isDraggable?: boolean;
  isResizable?: boolean;
  className?: string;
  enablePanZoom?: boolean;
  mode?: 'view' | 'edit'; // Current dashboard mode
  displayMode?: 'normal' | 'minimal' | 'ultra-minimal'; // Display mode for card appearance
  smartLayoutMode?: boolean; // Smart layout mode for auto-compacting
  autoHeightMode?: boolean; // Auto height mode for widgets
}

interface WidgetCardProps {
  widget: DashboardWidget;
  children?: React.ReactNode;
  autoHeight?: boolean; // Cho phép card tự động điều chỉnh chiều cao
  onRemove?: (widgetId: string) => void; // Callback để remove widget
  onTitleChange?: (widgetId: string, newTitle: string) => void; // Callback để change title
  onPropsChange?: (widgetId: string, newProps: Record<string, unknown>) => void; // Callback để change props
  mode?: 'view' | 'edit'; // Current dashboard mode
  displayMode?: 'normal' | 'minimal' | 'ultra-minimal'; // Display mode for card appearance
}

const WidgetCard: React.FC<WidgetCardProps> = ({
  widget,
  children,
  autoHeight = false,
  onRemove,
  onTitleChange,
  mode = 'edit',
  displayMode = 'normal'
}) => {
  const { t } = useTranslation(['common', 'dashboard']);

  const handleTitleChange = (newTitle: string) => {
    if (onTitleChange) {
      onTitleChange(widget.id, newTitle);
    }
  };

  // Ultra-minimal mode: absolutely no wrapper, no styling, pure content only
  if (displayMode === 'ultra-minimal') {
    // If widget is empty, return null
    if (widget.isEmpty) {
      return null;
    }

    // Return absolutely pure content with drag handle overlay
    const content = children || renderWidgetContent(widget, displayMode);

    // If content is a React element, clone it and remove all padding/margin classes
    if (React.isValidElement(content)) {
      return (
        <div className="relative h-full">
          {/* Drag Handle for ultra-minimal mode - Only show in edit mode */}
          {mode === 'edit' && (
            <div
              className="widget-drag-handle absolute top-1 left-1 z-20 cursor-grab active:cursor-grabbing p-1 rounded bg-background/90 hover:bg-muted/90 transition-colors shadow-md"
              title={t('dashboard:drag_to_move')}
            >
              <Move size={14} className="text-muted-foreground" />
            </div>
          )}
          <div
            className="widget-no-drag h-full"
            onMouseDown={(e) => {
              // Ngăn grid layout bắt sự kiện drag từ content
              e.stopPropagation();
            }}
            onDragStart={(e) => {
              // Ngăn drag event từ content
              e.preventDefault();
              e.stopPropagation();
            }}
          >
            {React.cloneElement(content as React.ReactElement<any>, {
              className: (content.props.className || '').replace(/p-\d+|m-\d+|px-\d+|py-\d+|mx-\d+|my-\d+/g, '').trim(),
              style: { ...content.props.style, padding: 0, margin: 0 }
            })}
          </div>
        </div>
      );
    }

    return (
      <div className="relative h-full">
        {/* Drag Handle for ultra-minimal mode - Only show in edit mode */}
        {mode === 'edit' && (
          <div
            className="widget-drag-handle absolute top-1 left-1 z-20 cursor-grab active:cursor-grabbing p-1 rounded bg-background/90 hover:bg-muted/90 transition-colors shadow-md"
            title={t('dashboard:drag_to_move')}
          >
            <Move size={14} className="text-muted-foreground" />
          </div>
        )}
        <div
          className="widget-no-drag h-full"
          onMouseDown={(e) => {
            // Ngăn grid layout bắt sự kiện drag từ content
            e.stopPropagation();
          }}
          onDragStart={(e) => {
            // Ngăn drag event từ content
            e.preventDefault();
            e.stopPropagation();
          }}
        >
          {content}
        </div>
      </div>
    );
  }

  // Minimal mode: only pure content, no wrapper, no padding, no placeholders
  if (displayMode === 'minimal') {
    // If widget is empty, return null (no placeholder in minimal mode)
    if (widget.isEmpty) {
      return null;
    }

    // Return content with drag handle for minimal mode
    return (
      <div className="relative h-full">
        {/* Drag Handle for minimal mode - Only show in edit mode */}
        {mode === 'edit' && (
          <div
            className="widget-drag-handle absolute top-2 left-2 z-10 cursor-grab active:cursor-grabbing p-2 rounded bg-background/80 hover:bg-muted/80 transition-colors shadow-sm"
            title={t('dashboard:drag_to_move')}
          >
            <Move size={16} className="text-muted-foreground" />
          </div>
        )}
        <div
          className="widget-no-drag h-full"
          onMouseDown={(e) => {
            // Ngăn grid layout bắt sự kiện drag từ content
            e.stopPropagation();
          }}
          onDragStart={(e) => {
            // Ngăn drag event từ content
            e.preventDefault();
            e.stopPropagation();
          }}
        >
          {children || renderWidgetContent(widget, displayMode)}
        </div>
      </div>
    );
  }

  // Normal mode: full card with title and background
  return (
    <Card className={`${autoHeight ? 'h-auto' : 'h-full'} flex flex-col`}>
      {/* Widget Header - NOT draggable */}
      <div
        className="flex items-center px-3 py-2 border-b border-border widget-header"
        onMouseDown={(e) => {
          // Chỉ ngăn drag nếu KHÔNG phải từ drag handle
          if (!(e.target as HTMLElement).closest('.widget-drag-handle')) {
            e.stopPropagation();
          }
        }}
        onDragStart={(e) => {
          // Chỉ ngăn drag nếu KHÔNG phải từ drag handle
          if (!(e.target as HTMLElement).closest('.widget-drag-handle')) {
            e.preventDefault();
            e.stopPropagation();
          }
        }}
      >
        {/* Drag Handle - Only show in edit mode */}
        {mode === 'edit' && (
          <div
            className="widget-drag-handle mr-3 flex-shrink-0 cursor-grab active:cursor-grabbing p-2 rounded hover:bg-muted/50 transition-colors"
            title={t('dashboard:drag_to_move')}
          >
            <Move size={20} className="text-muted-foreground" />
          </div>
        )}

        <div className="mr-2 max-w-full overflow-hidden">
          <EditableWidgetTitle
            title={widget.title}
            onTitleChange={handleTitleChange}
            isEditMode={mode === 'edit'}
          />
        </div>
        <div className="flex items-center gap-1 widget-no-drag flex-shrink-0 ml-auto">
          {/* Only show remove button in edit mode */}
          {onRemove && mode === 'edit' && (
            <div
              className="widget-no-drag"
              onMouseDown={(e) => {
                // Ngăn grid layout bắt sự kiện drag
                e.stopPropagation();
              }}
              onClick={(e) => {
                // Ngăn sự kiện bubble up và xung đột với drag/drop
                e.stopPropagation();
                e.preventDefault();
                onRemove(widget.id);
              }}
            >
              <IconCard
                icon="x"
                variant="ghost"
                size="sm"
                className="text-muted-foreground hover:text-destructive hover:bg-destructive/10 cursor-pointer widget-no-drag"
                title={t('dashboard:remove_widget')}
              />
            </div>
          )}
        </div>
      </div>
      {/* Widget Content - NOT draggable */}
      <div
        className={`${autoHeight ? 'p-2' : 'flex-1 p-2'} widget-no-drag`}
        onMouseDown={(e) => {
          // Ngăn grid layout bắt sự kiện drag từ content
          e.stopPropagation();
        }}
        onDragStart={(e) => {
          // Ngăn drag event từ content
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        {widget.isEmpty ? (
          <div className={`${autoHeight ? 'py-4' : 'h-full'} flex flex-col items-center justify-center text-center`}>
            <div className="w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-3">
              <Icon name="bar-chart-3" className="w-6 h-6 text-muted-foreground" />
            </div>
            <Typography variant="body2" className="text-muted-foreground mb-1">
              {t('dashboard:no_data')}
            </Typography>
            <Typography variant="body2" className="text-xs text-muted-foreground">
              {t('dashboard:data_will_display')}
            </Typography>
          </div>
        ) : (
          children || (
            <div className={`${autoHeight ? 'py-4' : 'h-full'} flex items-center justify-center`}>
              <Typography variant="body2" className="text-muted-foreground">
                {t('dashboard:widget_content')}
              </Typography>
            </div>
          )
        )}
      </div>
    </Card>
  );
};

const DashboardCard: React.FC<DashboardCardProps> = ({
  widgets,
  onLayoutChange,
  onRemoveWidget,
  onWidgetTitleChange,
  onWidgetPropsChange,
  isDraggable = true,
  isResizable = true,
  className = '',
  mode = 'edit',
  displayMode = 'normal',
  smartLayoutMode = false,
  autoHeightMode = true,
}) => {
  // Enable drag/resize based on props (controlled by parent component)
  const canDrag = isDraggable;
  const canResize = isResizable;

  // Function to calculate auto height for widgets
  const calculateAutoHeight = (widget: DashboardWidget): number => {
    if (!autoHeightMode) return widget.h;

    // Base height calculation based on widget type
    switch (widget.type) {
      case WIDGET_TYPES.CUSTOM_FIELD_FORM:
        return 12; // Form widgets need more height
      case WIDGET_TYPES.PRODUCT_TYPE_PIE_CHART:
      case WIDGET_TYPES.CUSTOMERS_CHART:
        return 8; // Chart widgets need medium height
      case WIDGET_TYPES.BUSINESS_MULTI_LINE_CHART:
      case WIDGET_TYPES.SALES_LINE_CHART:
      case WIDGET_TYPES.ORDERS_LINE_CHART:
      case WIDGET_TYPES.UNIVERSAL_LINE_CHART:
        return 7; // Line charts need medium height
      case WIDGET_TYPES.ORDER_STATS:
      case WIDGET_TYPES.BUSINESS_OVERVIEW:
        return 4; // Stats widgets need less height
      case WIDGET_TYPES.MARKETING_OVERVIEW:
      case WIDGET_TYPES.CAMPAIGN_PERFORMANCE:
      case WIDGET_TYPES.AGENT_OVERVIEW:
      case WIDGET_TYPES.AGENT_PERFORMANCE:
        return 6; // Medium height for overview widgets
      case WIDGET_TYPES.DATA_COUNT:
      case WIDGET_TYPES.DATA_STORAGE:
        return 3; // Small height for data widgets
      case WIDGET_TYPES.TEXT_WIDGET:
        return 4; // Medium height for text content
      case WIDGET_TYPES.IMAGE_WIDGET:
        return 6; // Square aspect ratio for images
      case WIDGET_TYPES.CLOCK_WIDGET:
        return 3; // Compact height for clock
      case WIDGET_TYPES.QUOTE_WIDGET:
        return 4; // Medium height for quotes
      case WIDGET_TYPES.HTML_WIDGET:
      case WIDGET_TYPES.IFRAME_WIDGET:
        return 8; // Larger height for embedded content
      case WIDGET_TYPES.VIDEO_WIDGET:
        return 6; // Video aspect ratio
      case WIDGET_TYPES.COUNTER_WIDGET:
        return 3; // Compact height for counter
      case WIDGET_TYPES.WEATHER_WIDGET:
        return 5; // Medium height for weather info
      case WIDGET_TYPES.PROGRESS_WIDGET:
        return 4; // Medium height for progress bars
      case WIDGET_TYPES.CALENDAR_WIDGET:
        return 6; // Calendar grid height
      case WIDGET_TYPES.TODO_WIDGET:
        return 6; // Todo list height
      case WIDGET_TYPES.KPI_WIDGET:
        return 5; // KPI metrics height
      case WIDGET_TYPES.TABLE_WIDGET:
        return 6; // Table with pagination height
      default:
        // For unknown widgets, use a reasonable default
        return Math.max(widget.h, 6); // At least 6 units high
    }
  };
  // Convert widgets to layouts for react-grid-layout
  const layouts = {
    lg: widgets.map(widget => ({
      i: widget.id,
      x: widget.x,
      y: widget.y,
      w: widget.w,
      h: calculateAutoHeight(widget),
      minW: widget.minW,
      minH: widget.minH,
      maxW: widget.maxW,
      maxH: widget.maxH
      // Remove static property - let React Grid Layout handle positioning
    })),
    md: widgets.map(widget => ({
      i: widget.id,
      x: widget.x,
      y: widget.y,
      w: Math.min(widget.w, 8),
      h: calculateAutoHeight(widget),
      minW: widget.minW,
      minH: widget.minH,
      maxW: widget.maxW,
      maxH: widget.maxH
    })),
    sm: widgets.map(widget => ({
      i: widget.id,
      x: 0,
      y: widget.y,
      w: 6,
      h: calculateAutoHeight(widget),
      minW: widget.minW,
      minH: widget.minH,
      maxW: widget.maxW,
      maxH: widget.maxH
    })),
    xs: widgets.map(widget => ({
      i: widget.id,
      x: 0,
      y: widget.y,
      w: 4,
      h: calculateAutoHeight(widget),
      minW: widget.minW,
      minH: widget.minH,
      maxW: widget.maxW,
      maxH: widget.maxH
    }))
  };

  const breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480 };
  const cols = { lg: 12, md: 8, sm: 6, xs: 4 };

  return (
    <div
      className={`${className}`}
      data-draggable={canDrag}
      data-resizable={canResize}
    >
      <ResponsiveGridLayout
        className="layout"
        layouts={layouts}
        breakpoints={breakpoints}
        cols={cols}
        rowHeight={60}
        isDraggable={canDrag}
        isResizable={canResize}
        onLayoutChange={canDrag ? onLayoutChange : undefined}
        margin={[16, 16]}
        containerPadding={[0, 0]}
        autoSize={true}
        useCSSTransforms={true}
        preventCollision={false}
        compactType={smartLayoutMode ? 'vertical' : null}
        verticalCompact={smartLayoutMode}
        dragHandleClassName="widget-drag-handle"
        cancel=".widget-header, .widget-no-drag"
      >
        {widgets.map(widget => (
          <div key={widget.id}>
            <WidgetCard
              widget={widget}
              autoHeight={autoHeightMode}
              mode={mode}
              displayMode={displayMode}
              {...(onRemoveWidget ? { onRemove: onRemoveWidget } : {})}
              {...(onWidgetTitleChange ? { onTitleChange: onWidgetTitleChange } : {})}
              {...(onWidgetPropsChange ? { onPropsChange: onWidgetPropsChange } : {})}
            >
              {/* Render specific widgets based on type */}
              {renderWidgetContent(widget, displayMode, onWidgetPropsChange)}
            </WidgetCard>
          </div>
        ))}
      </ResponsiveGridLayout>
    </div>
  );
};

export default DashboardCard;
export { WidgetCard };
