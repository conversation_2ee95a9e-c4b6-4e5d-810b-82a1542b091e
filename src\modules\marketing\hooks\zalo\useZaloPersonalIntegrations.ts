import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getZaloPersonalIntegrations,
  deleteZaloPersonalIntegrations,
  createZaloPersonalIntegration,
  updateZaloPersonalIntegration,
  generateZaloPersonalQRCode
} from '@/modules/marketing/api/zalo/zaloPersonalApi';
import { ZALO_PERSONAL_QUERY_KEYS } from '@/modules/marketing/constants/zaloPersonalQueryKeys';
import type {
  ZaloPersonalIntegrationQueryDto,
  CreateZaloPersonalIntegrationDto,
  UpdateZaloPersonalIntegrationDto,
  GenerateZaloPersonalQRCodeDto
} from '@/modules/marketing/types/zaloPersonal';

/**
 * Hook để lấy danh sách tích hợp Zalo Personal
 */
export const useZaloPersonalIntegrations = (params?: ZaloPersonalIntegrationQueryDto) => {
  return useQuery({
    queryKey: ZALO_PERSONAL_QUERY_KEYS.LIST(params || {}),
    queryFn: () => getZaloPersonalIntegrations(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy chi tiết tích hợp Zalo Personal
 */
export const useZaloPersonalIntegration = (id: string) => {
  return useQuery({
    queryKey: ZALO_PERSONAL_QUERY_KEYS.DETAIL(id),
    queryFn: () => getZaloPersonalIntegrations({ id }),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook để tạo tích hợp Zalo Personal mới
 */
export const useCreateZaloPersonalIntegration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateZaloPersonalIntegrationDto) => 
      createZaloPersonalIntegration(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để cập nhật tích hợp Zalo Personal
 */
export const useUpdateZaloPersonalIntegration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateZaloPersonalIntegrationDto }) =>
      updateZaloPersonalIntegration(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate queries liên quan
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_QUERY_KEYS.ALL,
      });
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_QUERY_KEYS.DETAIL(id),
      });
    },
  });
};

/**
 * Hook để xóa tích hợp Zalo Personal
 */
export const useDeleteZaloPersonalIntegrations = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => deleteZaloPersonalIntegrations(ids),
    onSuccess: () => {
      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để đăng nhập lại tích hợp Zalo Personal
 */
export const useReloginZaloPersonalIntegration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (_id: string) => {
      // TODO: Implement relogin API call
      return Promise.resolve({ success: true });
    },
    onSuccess: (_, id) => {
      // Invalidate queries liên quan
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_QUERY_KEYS.DETAIL(id),
      });
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để kiểm tra trạng thái đăng nhập
 */
export const useCheckZaloPersonalIntegrationStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (_id: string) => {
      // TODO: Implement check status API call
      return Promise.resolve({ isActive: true, status: 'connected' });
    },
    onSuccess: (_, id) => {
      // Invalidate queries liên quan
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_QUERY_KEYS.DETAIL(id),
      });
      queryClient.invalidateQueries({
        queryKey: ZALO_PERSONAL_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để tạo QR code Zalo Personal
 */
export const useGenerateZaloPersonalQRCode = () => {
  return useMutation({
    mutationFn: (data?: GenerateZaloPersonalQRCodeDto) => generateZaloPersonalQRCode(data),
  });
};
