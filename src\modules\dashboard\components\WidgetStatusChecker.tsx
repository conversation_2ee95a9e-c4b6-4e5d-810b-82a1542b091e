import React, { useEffect, useState } from 'react';
import { Typo<PERSON>, Card } from '@/shared/components/common';
import { WIDGET_TYPES, WIDGET_CATEGORIES } from '../constants/widget-types';
import { WIDGET_CONFIGS } from '../registry/widgetConfigs';

interface WidgetStatus {
  type: string;
  displayName: string;
  category: string;
  hasConfig: boolean;
  hasComponent: boolean;
  status: 'available' | 'missing';
}

const WidgetStatusChecker: React.FC = () => {
  const [widgetStatus, setWidgetStatus] = useState<{
    available: WidgetStatus[];
    missing: WidgetStatus[];
    total: number;
  }>({ available: [], missing: [], total: 0 });

  useEffect(() => {
    const checkWidgetStatus = () => {
      const allWidgetTypes = Object.values(WIDGET_TYPES);
      const statuses: WidgetStatus[] = [];

      // Component paths mapping
      const componentPaths: Record<string, boolean> = {
        'data-count': true,
        'data-storage': true,
        'business-overview': true,
        'marketing-overview': true,
        'campaign-performance': true,
        'agent-overview': true,
        'agent-performance': true,
        'affiliate-overview': true,
        'integration-overview': true,
        'chart': false,
        'metric': false,
        'table': false,
        'custom': false,
      };

      allWidgetTypes.forEach(type => {
        const config = WIDGET_CONFIGS.find(c => c.type === type);
        const hasComponent = componentPaths[type] || false;

        const widgetStatus: WidgetStatus = {
          type,
          displayName: getWidgetDisplayName(type),
          category: getCategoryForType(type),
          hasConfig: !!config,
          hasComponent,
          status: config && hasComponent ? 'available' : 'missing',
        };

        statuses.push(widgetStatus);
      });

      const available = statuses.filter(s => s.status === 'available');
      const missing = statuses.filter(s => s.status === 'missing');

      setWidgetStatus({
        available,
        missing,
        total: statuses.length,
      });
    };

    checkWidgetStatus();
  }, []);

  const getWidgetDisplayName = (type: string): string => {
    const displayNames: Record<string, string> = {
      'data-count': 'Tổng số lượng dữ liệu',
      'data-storage': 'Dung lượng dữ liệu',
      'chart': 'Biểu đồ',
      'metric': 'Chỉ số',
      'table': 'Bảng dữ liệu',
      'custom': 'Tùy chỉnh',
      'marketing-overview': 'Tổng quan Marketing',
      'campaign-performance': 'Hiệu suất chiến dịch',
      'agent-overview': 'Tổng quan AI Agents',
      'agent-performance': 'Hiệu suất Agents',
      'business-overview': 'Tổng quan kinh doanh',
      'affiliate-overview': 'Tổng quan Affiliate',
      'integration-overview': 'Tổng quan tích hợp',
    };

    return displayNames[type] || 'Không xác định';
  };

  const getCategoryForType = (type: string): string => {
    const categoryMap: Record<string, string> = {
      'data-count': WIDGET_CATEGORIES.DATA,
      'data-storage': WIDGET_CATEGORIES.DATA,
      'chart': WIDGET_CATEGORIES.VISUALIZATION,
      'metric': WIDGET_CATEGORIES.VISUALIZATION,
      'table': WIDGET_CATEGORIES.VISUALIZATION,
      'marketing-overview': WIDGET_CATEGORIES.MARKETING,
      'campaign-performance': WIDGET_CATEGORIES.MARKETING,
      'agent-overview': WIDGET_CATEGORIES.AI_AGENTS,
      'agent-performance': WIDGET_CATEGORIES.AI_AGENTS,
      'business-overview': WIDGET_CATEGORIES.BUSINESS,
      'customer-products': WIDGET_CATEGORIES.BUSINESS,
      'customer-list': WIDGET_CATEGORIES.BUSINESS,
      'bank-account-overview': WIDGET_CATEGORIES.BUSINESS,
      'affiliate-overview': WIDGET_CATEGORIES.AFFILIATE,
      'integration-overview': WIDGET_CATEGORIES.INTEGRATION,
    };

    return categoryMap[type] || WIDGET_CATEGORIES.DATA;
  };

  const groupByCategory = (widgets: WidgetStatus[]) => {
    const grouped: Record<string, WidgetStatus[]> = {};
    widgets.forEach(widget => {
      if (!grouped[widget.category]) {
        grouped[widget.category] = [];
      }
      grouped[widget.category].push(widget);
    });
    return grouped;
  };

  const availableByCategory = groupByCategory(widgetStatus.available);
  const missingByCategory = groupByCategory(widgetStatus.missing);

  return (
    <div className="p-6 space-y-6">
      <Typography variant="h2">Widget Status Report</Typography>
      
      {/* Summary */}
      <Card className="p-4">
        <Typography variant="h3" className="mb-4">Tổng quan</Typography>
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <Typography variant="h4" className="text-2xl font-bold">{widgetStatus.total}</Typography>
            <Typography variant="body2" className="text-muted-foreground">Tổng số widget</Typography>
          </div>
          <div className="text-center">
            <Typography variant="h4" className="text-2xl font-bold text-green-600">{widgetStatus.available.length}</Typography>
            <Typography variant="body2" className="text-muted-foreground">Đã có sẵn</Typography>
          </div>
          <div className="text-center">
            <Typography variant="h4" className="text-2xl font-bold text-red-600">{widgetStatus.missing.length}</Typography>
            <Typography variant="body2" className="text-muted-foreground">Chưa có</Typography>
          </div>
        </div>
      </Card>

      {/* Available Widgets */}
      <Card className="p-4">
        <Typography variant="h3" className="mb-4 text-green-600">✅ Widget đã có sẵn ({widgetStatus.available.length})</Typography>
        {Object.entries(availableByCategory).map(([category, widgets]) => (
          <div key={category} className="mb-4">
            <Typography variant="h4" className="mb-2 capitalize">{category}</Typography>
            <div className="grid grid-cols-2 gap-2">
              {widgets.map(widget => (
                <div key={widget.type} className="p-2 bg-green-50 rounded border border-green-200">
                  <Typography variant="body2" className="font-medium">{widget.displayName}</Typography>
                  <Typography variant="caption" className="text-muted-foreground">({widget.type})</Typography>
                </div>
              ))}
            </div>
          </div>
        ))}
      </Card>

      {/* Missing Widgets */}
      <Card className="p-4">
        <Typography variant="h3" className="mb-4 text-red-600">❌ Widget chưa có ({widgetStatus.missing.length})</Typography>
        {Object.entries(missingByCategory).map(([category, widgets]) => (
          <div key={category} className="mb-4">
            <Typography variant="h4" className="mb-2 capitalize">{category}</Typography>
            <div className="grid grid-cols-2 gap-2">
              {widgets.map(widget => (
                <div key={widget.type} className="p-2 bg-red-50 rounded border border-red-200">
                  <Typography variant="body2" className="font-medium">{widget.displayName}</Typography>
                  <Typography variant="caption" className="text-muted-foreground">({widget.type})</Typography>
                  <div className="mt-1">
                    {!widget.hasConfig && <span className="text-xs bg-red-100 px-1 rounded">No Config</span>}
                    {!widget.hasComponent && <span className="text-xs bg-red-100 px-1 rounded ml-1">No Component</span>}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </Card>
    </div>
  );
};

export default WidgetStatusChecker;
