# Date to BigInt Conversion for Business Report APIs

## Tổng quan

Để đảm bảo t<PERSON>h ch<PERSON>h xác và hiệu suất trong xử lý ngà<PERSON>án<PERSON>, tất cả các API business reports đã được cập nhật để convert date strings sang BigInt timestamps trước khi gửi xuống backend.

## Thay đổi

### Trước đây
```typescript
// Frontend gửi date string trực tiếp
const params = {
  type: 'ORDER',
  begin: '2025-08-21',  // String
  end: '2025-08-23'     // String
};

// API URL: /v1/user/business/reports/line-chart?type=ORDER&begin=2025-08-21&end=2025-08-23
```

### Bây giờ
```typescript
// Frontend vẫn sử dụng date string
const params = {
  type: 'ORDER',
  begin: '2025-08-21',  // String (frontend)
  end: '2025-08-23'     // String (frontend)
};

// API layer tự động convert sang BigInt
// API URL: /v1/user/business/reports/line-chart?type=ORDER&begin=1724198400000&end=1724371200000
```

## Cách hoạt động

### 1. Utility Functions

```typescript
// src/shared/utils/date-form-utils.ts

/**
 * Convert date string (YYYY-MM-DD) to BigInt timestamp
 */
export const dateStringToBigInt = (dateString: string): bigint | null => {
  if (!dateString) return null;
  try {
    const date = new Date(dateString + 'T00:00:00.000Z');
    const timestamp = date.getTime();
    return isNaN(timestamp) ? null : BigInt(timestamp);
  } catch {
    return null;
  }
};
```

### 2. API Layer Conversion

```typescript
// src/modules/business/api/report.api.ts

const convertDateParamsToTimestamps = <T extends Record<string, any>>(params: T): T => {
  if (!params) return params;
  
  const convertedParams = { ...params };
  const dateFields = ['begin', 'end', 'startDate', 'endDate'];
  
  dateFields.forEach(field => {
    if (convertedParams[field] && typeof convertedParams[field] === 'string') {
      const timestamp = dateStringToBigInt(convertedParams[field]);
      if (timestamp !== null) {
        convertedParams[field] = timestamp.toString();
      }
    }
  });
  
  return convertedParams;
};
```

### 3. Automatic Conversion

Tất cả API functions đã được cập nhật:

```typescript
export const getLineChart = async (params: LineChartQueryDto) => {
  // Tự động convert date strings sang BigInt timestamps
  const apiParams = convertToApiParams(params);
  return apiClient.get(`${BASE_URL}/line-chart`, { params: apiParams });
};
```

## APIs được cập nhật

- ✅ `getLineChart` - API line-chart chính
- ✅ `getReportOverview` - Tổng quan báo cáo
- ✅ `getSalesChart` - Biểu đồ doanh thu
- ✅ `getOrdersChart` - Biểu đồ đơn hàng
- ✅ `getCustomersChart` - Biểu đồ khách hàng
- ✅ `getProductsChart` - Biểu đồ sản phẩm
- ✅ `getTopSellingProducts` - Sản phẩm bán chạy
- ✅ `getPotentialCustomers` - Khách hàng tiềm năng

## Date Fields được convert

- `begin` / `end` (line-chart API)
- `startDate` / `endDate` (các API khác)

## Ví dụ

### Input (Frontend)
```typescript
const params = {
  type: BusinessReportTypeEnum.ORDER,
  begin: '2025-08-21',
  end: '2025-08-23'
};
```

### Output (API Request)
```
GET /v1/user/business/reports/line-chart?type=ORDER&begin=1724198400000&end=1724371200000
```

### Conversion Details
```
'2025-08-21' -> 1724198400000 (BigInt timestamp)
'2025-08-23' -> 1724371200000 (BigInt timestamp)
```

## Lợi ích

1. **Tính chính xác**: BigInt đảm bảo không mất độ chính xác với timestamps lớn
2. **Hiệu suất**: Backend xử lý timestamps nhanh hơn date strings
3. **Consistency**: Tất cả APIs sử dụng cùng format
4. **Backward Compatible**: Frontend code không cần thay đổi

## Testing

```bash
# Run tests
npm test src/modules/business/api/__tests__/date-conversion.test.ts
```

## Debug

Trong development mode, conversion sẽ được log:

```
🔄 Date conversion: begin "2025-08-21" -> 1724198400000
🔄 Date conversion: end "2025-08-23" -> 1724371200000
```

## Migration Notes

- ✅ Frontend code không cần thay đổi
- ✅ Widgets dashboard hoạt động bình thường
- ✅ DoubleDatePicker tương thích
- ✅ Tất cả existing APIs được cập nhật
- ⚠️ Backend cần hỗ trợ BigInt timestamps
