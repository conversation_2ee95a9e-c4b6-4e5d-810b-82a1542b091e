import React, { useState, forwardRef, useCallback } from 'react';
import { IconCard, Typography } from '@/shared/components/common';
import Calendar from '../DatePicker/Calendar';
import { addMonths, format } from 'date-fns';
import { vi } from 'date-fns/locale';
import { Z_INDEX } from '@/shared/constants/breakpoints';
import {
  useFloating,
  autoUpdate,
  offset,
  flip,
  shift,
  useClick,
  useDismiss,
  useRole,
  useInteractions,
  FloatingPortal,
  FloatingFocusManager,
} from '@floating-ui/react';
import './DoubleDatePicker.css';

export interface DoubleDatePickerProps {
  /**
   * Giá trị đã chọn [startDate, endDate]
   */
  value?: [Date | null, Date | null];

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (dates: [Date | null, Date | null]) => void;

  /**
   * Icon hiển thị để mở dropdown
   */
  triggerIcon?: React.ReactNode;

  /**
   * <PERSON><PERSON><PERSON> thước của icon trigger
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Disabled component
   */
  disabled?: boolean;

  /**
   * Custom className
   */
  className?: string;

  /**
   * Ẩn border xung quanh icon
   */
  noBorder?: boolean;

  /**
   * Placeholder text
   */
  placeholder?: string;

  /**
   * Label cho ngày bắt đầu
   */
  startLabel?: string;

  /**
   * Label cho ngày kết thúc
   */
  endLabel?: string;

  /**
   * Hiển thị validation error
   */
  showValidationError?: boolean;
}

/**
 * DoubleDatePicker component hiển thị 2 tháng calendar liền nhau khi bấm vào icon
 * Với validation đảm bảo ngày kết thúc >= ngày bắt đầu
 *
 * @example
 * ```tsx
 * import { DoubleDatePicker } from '@/shared/components/common';
 * import { useState } from 'react';
 *
 * const MyComponent = () => {
 *   const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
 *
 *   return (
 *     <DoubleDatePicker
 *       value={dateRange}
 *       onChange={setDateRange}
 *       startLabel="Từ ngày"
 *       endLabel="Đến ngày"
 *       showValidationError={true}
 *     />
 *   );
 * };
 * ```
 */
const DoubleDatePicker = forwardRef<HTMLDivElement, DoubleDatePickerProps>(
  (
    {
      value = [null, null],
      onChange,
      triggerIcon,
      size = 'md',
      disabled = false,
      className = '',
      startLabel = 'Từ ngày',
      endLabel = 'Đến ngày',
    },
    ref
  ) => {
    const [isOpen, setIsOpen] = useState(false);
    const [currentMonth, setCurrentMonth] = useState(new Date());
    const [validationError, setValidationError] = useState<string>('');
    const [tempRange, setTempRange] = useState<[Date | null, Date | null]>(value);

    const { refs, floatingStyles, context } = useFloating({
      open: isOpen,
      onOpenChange: setIsOpen,
      middleware: [
        offset(8),
        flip({
          fallbackAxisSideDirection: 'start',
        }),
        shift({ padding: 8 }),
      ],
      whileElementsMounted: autoUpdate,
      placement: 'bottom-start',
    });

    const click = useClick(context);
    const dismiss = useDismiss(context);
    const role = useRole(context);

    const { getReferenceProps, getFloatingProps } = useInteractions([click, dismiss, role]);

    // Separate handlers for left and right calendars
    const handleLeftDateSelect = useCallback(
      (selectedDate: Date) => {
        setValidationError('');
        // Left calendar chỉ quản lý startDate (tempRange[0])
        const newRange: [Date | null, Date | null] = [selectedDate, tempRange[1]];
        setTempRange(newRange);
        onChange?.(newRange);
      },
      [tempRange, onChange]
    );

    const handleRightDateSelect = useCallback(
      (selectedDate: Date) => {
        setValidationError('');
        // Right calendar chỉ quản lý endDate (tempRange[1])
        const newRange: [Date | null, Date | null] = [tempRange[0], selectedDate];
        setTempRange(newRange);
        onChange?.(newRange);

        // Đóng dropdown khi đã chọn cả start và end
        if (tempRange[0] && selectedDate) {
          setIsOpen(false);
        }
      },
      [tempRange, onChange]
    );

    // Sync tempRange with external value changes
    React.useEffect(() => {
      setTempRange(value);
    }, [value]);

    // Get next month for second calendar
    const nextMonth = addMonths(currentMonth, 1);

    // Icon size mapping
    const iconSizeMap = {
      sm: 'sm' as const,
      md: 'md' as const,
      lg: 'lg' as const,
    };

    return (
      <div ref={ref} className="relative">
        {/* Trigger IconCard */}
        <div ref={refs.setReference} {...getReferenceProps()}>
          {triggerIcon ? (
            <div className={className}>{triggerIcon}</div>
          ) : (
            <IconCard
              icon="calendar"
              size={iconSizeMap[size]}
              variant="ghost"
              disabled={disabled}
              className={className}
              title="Chọn khoảng thời gian"
            />
          )}
        </div>

        {/* Dropdown Content */}
        {isOpen && (
          <FloatingPortal>
            <FloatingFocusManager context={context} modal={false}>
              <div
                ref={refs.setFloating}
                style={{
                  ...floatingStyles,
                  zIndex: Z_INDEX.dropdown,
                }}
                className="datepicker-dropdown rounded-lg overflow-hidden"
                {...getFloatingProps()}
              >
                <div className="double-datepicker-container bg-card border-2 border-primary/20 shadow-xl">
                  {/* Date Range Labels */}
                  <div className="flex border-b border-border bg-gradient-to-r from-primary/5 to-secondary/5">
                    <div className="flex-1 p-4 text-center">
                      <Typography variant="body2" className="font-semibold text-primary mb-1">
                        {startLabel}
                      </Typography>
                      <Typography variant="caption" className="text-muted-foreground">
                        {format(currentMonth, 'MMMM yyyy', { locale: vi })}
                      </Typography>
                    </div>
                    <div className="w-px bg-gradient-to-b from-primary/30 to-secondary/30"></div>
                    <div className="flex-1 p-4 text-center">
                      <Typography variant="body2" className="font-semibold text-secondary mb-1">
                        {endLabel}
                      </Typography>
                      <Typography variant="caption" className="text-muted-foreground">
                        {format(nextMonth, 'MMMM yyyy', { locale: vi })}
                      </Typography>
                    </div>
                  </div>

                  <div className="flex">
                    {/* Left Calendar - Chỉ quản lý và hiển thị startDate */}
                    <div className="double-datepicker-left">
                      <Calendar
                        month={currentMonth}
                        onMonthChange={setCurrentMonth}
                        rangeMode={false}
                        selectedDate={tempRange[0]}
                        onSelectDate={handleLeftDateSelect}
                        showToday={true}
                        firstDayOfWeek={1}
                        // Không truyền startDate/endDate để tránh hiển thị range
                      />
                    </div>

                    {/* Divider */}
                    <div className="w-px bg-border"></div>

                    {/* Right Calendar - Chỉ quản lý và hiển thị endDate */}
                    <div className="double-datepicker-right">
                      <Calendar
                        month={nextMonth}
                        onMonthChange={date => setCurrentMonth(addMonths(date, -1))}
                        rangeMode={false}
                        selectedDate={tempRange[1]}
                        onSelectDate={handleRightDateSelect}
                        showToday={true}
                        firstDayOfWeek={1}
                        // Không truyền startDate/endDate để tránh hiển thị range
                      />
                    </div>
                  </div>

                  {/* Validation Error */}
                  {validationError && (
                    <div className="border-t border-destructive/20 p-3 bg-destructive/5">
                      <Typography variant="caption" className="text-destructive font-medium">
                        ⚠️ {validationError}
                      </Typography>
                    </div>
                  )}

                  {/* Selected Range Display */}
                  {(tempRange[0] || tempRange[1]) && (
                    <div className="border-t border-border p-4 bg-gradient-to-r from-success/5 to-info/5">
                      {tempRange[0] && tempRange[1] ? (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Typography variant="body2" className="font-semibold text-success">
                              ✓ Đã chọn khoảng thời gian:
                            </Typography>
                            <Typography variant="caption" className="text-muted-foreground bg-muted px-2 py-1 rounded">
                              {Math.ceil((tempRange[1].getTime() - tempRange[0].getTime()) / (1000 * 60 * 60 * 24)) + 1} ngày
                            </Typography>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                              <Typography variant="caption" className="text-primary font-medium">
                                {startLabel}:
                              </Typography>
                              <Typography variant="body2" className="font-mono bg-primary/10 px-2 py-1 rounded text-primary">
                                {format(tempRange[0], 'dd/MM/yyyy')}
                              </Typography>
                            </div>
                            <Typography variant="caption" className="text-muted-foreground">→</Typography>
                            <div className="flex items-center gap-2">
                              <Typography variant="caption" className="text-secondary font-medium">
                                {endLabel}:
                              </Typography>
                              <Typography variant="body2" className="font-mono bg-secondary/10 px-2 py-1 rounded text-secondary">
                                {format(tempRange[1], 'dd/MM/yyyy')}
                              </Typography>
                            </div>
                          </div>
                        </div>
                      ) : tempRange[0] ? (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <Typography variant="body2" className="font-semibold text-primary">
                              {startLabel}:
                            </Typography>
                            <Typography variant="body2" className="font-mono bg-primary/10 px-2 py-1 rounded text-primary">
                              {format(tempRange[0], 'dd/MM/yyyy')}
                            </Typography>
                          </div>
                          <Typography variant="caption" className="text-muted-foreground italic">
                            💡 Vui lòng chọn {endLabel.toLowerCase()}
                          </Typography>
                        </div>
                      ) : tempRange[1] ? (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <Typography variant="body2" className="font-semibold text-secondary">
                              {endLabel}:
                            </Typography>
                            <Typography variant="body2" className="font-mono bg-secondary/10 px-2 py-1 rounded text-secondary">
                              {format(tempRange[1], 'dd/MM/yyyy')}
                            </Typography>
                          </div>
                          <Typography variant="caption" className="text-muted-foreground italic">
                            💡 Vui lòng chọn {startLabel.toLowerCase()}
                          </Typography>
                        </div>
                      ) : null}
                    </div>
                  )}
                </div>
              </div>
            </FloatingFocusManager>
          </FloatingPortal>
        )}
      </div>
    );
  }
);

DoubleDatePicker.displayName = 'DoubleDatePicker';

export default DoubleDatePicker;
