/**
 * ChatSidebar Component
 * Component sidebar trái với tag selector, search box và contact list
 */

import React, { useState, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Icon, Input, Button } from '@/shared/components/common';
import ContactList from './ContactList';
import CollapsedContactList from './CollapsedContactList';
import { useZaloAccounts, useContacts, useContactSearch } from '../../../hooks/zalo/useZaloChat';
import type { ZaloAccount, Contact } from '../../../types/zalo-chat.types';

interface ChatSidebarProps {
  selectedAccount?: ZaloAccount;
  selectedContact?: Contact;
  onAccountSelect: (account: ZaloAccount) => void;
  onContactSelect: (contact: Contact) => void;
  isMobile?: boolean;
  onCollapsedChange?: (isCollapsed: boolean) => void;
  className?: string;
}

// Constants
const SIDEBAR_STORAGE_KEY = 'zalo_chat_sidebar_collapsed';

/**
 * Component dropdown chọn tài khoản <PERSON>alo
 */
const AccountSelector: React.FC<{
  accounts: ZaloAccount[];
  selectedAccount?: ZaloAccount;
  onSelect: (account: ZaloAccount) => void;
  isLoading: boolean;
}> = ({ accounts, selectedAccount, onSelect, isLoading }) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [isOpen, setIsOpen] = useState(false);

  const activeAccounts = accounts.filter(account => account.isActive);

  if (isLoading) {
    return (
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"></div>
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 border-b border-gray-200 dark:border-gray-700">
      <Typography variant="caption" className="text-gray-600 dark:text-gray-400 mb-2 block">
        {t('marketing:zalo.chat.selectAccount')}
      </Typography>
      
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
        >
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
              <Icon name="message-circle" size="sm" className="text-white" />
            </div>
            <div className="text-left">
              <Typography variant="body2" className="font-medium text-gray-900 dark:text-gray-100">
                {selectedAccount?.name || t('marketing:zalo.chat.chooseAccount')}
              </Typography>
              {selectedAccount && (
                <Typography variant="caption" className="text-gray-500">
                  {selectedAccount.type === 'oa' ? 'Official Account' : 'Personal Account'}
                </Typography>
              )}
            </div>
          </div>
          <Icon
            name={isOpen ? "chevron-up" : "chevron-down"}
            size="sm"
            className="text-gray-500"
          />
        </button>

        {/* Dropdown menu */}
        {isOpen && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
            {activeAccounts.length === 0 ? (
              <div className="p-4 text-center">
                <Typography variant="body2" className="text-gray-500">
                  {t('marketing:zalo.chat.noActiveAccounts')}
                </Typography>
              </div>
            ) : (
              activeAccounts.map((account) => (
                <button
                  key={account.id}
                  onClick={() => {
                    onSelect(account);
                    setIsOpen(false);
                  }}
                  className={`w-full flex items-center space-x-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                    selectedAccount?.id === account.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                  }`}
                >
                  <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                    <Icon name="message-circle" size="sm" className="text-white" />
                  </div>
                  <div className="flex-1 text-left">
                    <Typography variant="body2" className="font-medium text-gray-900 dark:text-gray-100">
                      {account.name}
                    </Typography>
                    <Typography variant="caption" className="text-gray-500">
                      {account.type === 'oa' ? 'Official Account' : 'Personal Account'}
                    </Typography>
                  </div>
                  {selectedAccount?.id === account.id && (
                    <Icon name="check" size="sm" className="text-blue-500" />
                  )}
                </button>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Component search box
 */
const SearchBox: React.FC<{
  value: string;
  onChange: (value: string) => void;
  onClear: () => void;
  isSearching: boolean;
}> = ({ value, onChange, onClear, isSearching }) => {
  const { t } = useTranslation(['marketing', 'common']);

  return (
    <div className="p-4 border-b border-gray-200 dark:border-gray-700">
      <div className="relative">
        <Input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={t('marketing:zalo.chat.searchContacts')}
          className="pl-10 pr-10"
        />
        
        {/* Search icon */}
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
          {isSearching ? (
            <div className="animate-spin">
              <Icon name="loader" size="sm" className="text-gray-400" />
            </div>
          ) : (
            <Icon name="search" size="sm" className="text-gray-400" />
          )}
        </div>

        {/* Clear button */}
        {value && (
          <button
            onClick={onClear}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <Icon name="x" size="sm" />
          </button>
        )}
      </div>
    </div>
  );
};

/**
 * Component ChatSidebar chính
 */
const ChatSidebar: React.FC<ChatSidebarProps> = ({
  selectedAccount,
  selectedContact,
  onAccountSelect,
  onContactSelect,
  isMobile = false,
  onCollapsedChange,
  className = '',
}) => {
  const { t } = useTranslation(['marketing', 'common']);

  // Collapsed state - chỉ áp dụng cho desktop
  const [isCollapsed, setIsCollapsed] = useState(() => {
    if (isMobile) return false;
    const saved = localStorage.getItem(SIDEBAR_STORAGE_KEY);
    return saved ? JSON.parse(saved) : false;
  });

  // Save collapsed state to localStorage and notify parent
  useEffect(() => {
    if (!isMobile) {
      localStorage.setItem(SIDEBAR_STORAGE_KEY, JSON.stringify(isCollapsed));
      onCollapsedChange?.(isCollapsed);
    }
  }, [isCollapsed, isMobile, onCollapsedChange]);

  // Hooks
  const { accounts, isLoading: isLoadingAccounts } = useZaloAccounts();
  const {
    searchQuery,
    setSearchQuery,
    searchResults,
    isSearching
  } = useContactSearch(selectedAccount?.id);

  const {
    contacts,
    isLoading: isLoadingContacts
  } = useContacts(
    selectedAccount ? { zaloAccountId: selectedAccount.id } : undefined
  );

  // Determine which contacts to show
  const displayContacts = useMemo(() => {
    if (searchQuery.trim()) {
      return searchResults;
    }
    return contacts;
  }, [searchQuery, searchResults, contacts]);

  // Handle clear search
  const handleClearSearch = () => {
    setSearchQuery('');
  };

  // Handle toggle collapsed
  const handleToggleCollapsed = () => {
    if (!isMobile) {
      setIsCollapsed(!isCollapsed);
    }
  };

  const shouldShowCollapsed = !isMobile && isCollapsed;

  return (
    <div className={`flex flex-col h-full ${shouldShowCollapsed ? 'bg-white dark:bg-gray-900' : 'bg-white dark:bg-gray-900/95 dark:backdrop-blur-sm'} border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ${className}`}>
      {/* Header với smooth transition */}
      <div className={`flex-shrink-0 p-4 ${shouldShowCollapsed ? ' border-b border-gray-200 ': ''}  dark:border-gray-700 relative`}>
        {/* Collapsed header */}
        <div className={`transition-all duration-300 ease-in-out ${
          shouldShowCollapsed
            ? 'opacity-100 scale-100 delay-150'
            : 'opacity-0 scale-95 absolute inset-0 pointer-events-none'
        }`}>
          <div className="flex justify-center overflow-hidden">
            <button
              onClick={handleToggleCollapsed}
              className={`
                p-2 rounded-lg transition-all duration-200 group
                ${
                  'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400'
                }
              `}
              title={t('marketing:zalo.chat.expandSidebar')}
              type="button"
            >
              <Icon
                name="chevron-right"
                size="sm"
                className="transition-transform duration-200 group-hover:scale-110"
              />
            </button>
          </div>
        </div>

        {/* Expanded header */}
        <div className={`transition-all duration-300 ease-in-out ${
          shouldShowCollapsed
            ? 'opacity-0 scale-95 absolute inset-0 pointer-events-none'
            : 'opacity-100 scale-100 delay-150'
        }`}>
          <div className="flex items-center justify-between overflow-hidden">
            <div className={`transition-all duration-200 ${
              shouldShowCollapsed ? 'opacity-0 -translate-x-4' : 'opacity-100 translate-x-0 delay-200'
            }`}>
              <Typography variant="h6" className="text-gray-900 dark:text-gray-100 whitespace-nowrap">
                {t('marketing:zalo.chat.title')}
              </Typography>
            </div>

            <div className={`flex items-center space-x-2 transition-all duration-200 ${
              shouldShowCollapsed ? 'opacity-0 translate-x-4' : 'opacity-100 translate-x-0 delay-200'
            }`}>
              {/* Toggle button - chỉ hiển thị trên desktop */}
              {!isMobile && (
                <button
                  onClick={handleToggleCollapsed}
                  className={`
                    p-2 rounded-lg transition-all duration-200 group
                    ${
                      'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400'
                    }
                  `}
                  title={t('marketing:zalo.chat.collapseSidebar')}
                  type="button"
                >
                  <Icon
                    name="chevron-left"
                    size="sm"
                    className="transition-transform duration-200 group-hover:scale-110"
                  />
                </button>
              )}

              {/* New chat button */}
              <Button
                variant="ghost"
                size="sm"
                className="p-2"
                title={t('marketing:zalo.chat.newChat')}
              >
                <Icon name="plus" size="sm" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Account selector - với smooth transition */}
      <div className={`flex-shrink-0 transition-all duration-250 ease-in-out ${
        shouldShowCollapsed
          ? 'opacity-0 -translate-y-2 h-0 overflow-hidden'
          : 'opacity-100 translate-y-0 h-auto delay-100'
      }`}>
        <AccountSelector
          accounts={accounts}
          selectedAccount={selectedAccount}
          onSelect={onAccountSelect}
          isLoading={isLoadingAccounts}
        />
      </div>

      {/* Search box - với smooth transition */}
      {selectedAccount && (
        <div className={`flex-shrink-0 transition-all duration-250 ease-in-out ${
          shouldShowCollapsed
            ? 'opacity-0 -translate-y-2 h-0 overflow-hidden'
            : 'opacity-100 translate-y-0 h-auto delay-150'
        }`}>
          <SearchBox
            value={searchQuery}
            onChange={setSearchQuery}
            onClear={handleClearSearch}
            isSearching={isSearching}
          />
        </div>
      )}

      {/* Contact list */}
      <div className="flex-1 overflow-hidden relative">
        {!selectedAccount ? (
          <div className={`flex flex-col items-center justify-center h-full px-4 transition-all duration-300 ease-in-out ${
            shouldShowCollapsed ? 'opacity-0' : 'opacity-100'
          }`}>
            {!shouldShowCollapsed && (
              <>
                <Icon name="message-circle" size="2xl" className="text-gray-300 dark:text-gray-600 mb-4" />
                <Typography variant="body2" className="text-gray-500 dark:text-gray-400 text-center">
                  {t('marketing:zalo.chat.selectAccountFirst')}
                </Typography>
              </>
            )}
          </div>
        ) : (
          <>
            {/* Collapsed Contact List */}
            <div className={`absolute inset-0 transition-all duration-250 ease-in-out ${
              shouldShowCollapsed
                ? 'opacity-100 scale-100 delay-150'
                : 'opacity-0 scale-95 pointer-events-none'
            }`}>
              <CollapsedContactList
                contacts={displayContacts}
                selectedContactId={selectedContact?.id}
                onContactSelect={onContactSelect}
                isLoading={isLoadingContacts}
                className="h-full"
              />
            </div>

            {/* Expanded Contact List */}
            <div className={`absolute inset-0 transition-all duration-250 ease-in-out ${
              shouldShowCollapsed
                ? 'opacity-0 scale-95 pointer-events-none'
                : 'opacity-100 scale-100 delay-150'
            }`}>
              <ContactList
                contacts={displayContacts}
                selectedContactId={selectedContact?.id}
                onContactSelect={onContactSelect}
                isLoading={isLoadingContacts}
                searchQuery={searchQuery.trim() || undefined}
                className="h-full"
              />
            </div>
          </>
        )}
      </div>

      {/* Footer stats - với smooth transition */}
      {selectedAccount && displayContacts.length > 0 && (
        <div className={`flex-shrink-0 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 transition-all duration-250 ease-in-out ${
          shouldShowCollapsed
            ? 'opacity-0 translate-y-2 h-0 overflow-hidden p-0'
            : 'opacity-100 translate-y-0 h-auto p-3 delay-200'
        }`}>
          <Typography variant="caption" className="text-gray-500 dark:text-gray-400 text-center block whitespace-nowrap">
            {searchQuery.trim()
              ? t('marketing:zalo.chat.searchResults', { count: displayContacts.length })
              : t('marketing:zalo.chat.totalContacts', { count: displayContacts.length })
            }
          </Typography>
        </div>
      )}
    </div>
  );
};

export default ChatSidebar;
