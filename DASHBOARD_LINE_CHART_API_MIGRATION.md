# Dashboard Line Chart API Migration

## Tóm tắt

Đã cập nhật trang `/dashboard` để sử dụng API chính `/v1/user/business/reports/line-chart` thay vì các API widget riêng lẻ cho các widget kinh doanh:

- ~~Biểu đồ doanh số~~ → **Biểu đồ doanh số (API chính)**
- ~~Biểu đồ đơn hàng~~ → **Biểu đồ đơn hàng (API chính)**  
- ~~Thống kê đơn hàng~~ → **Thống kê đơn hàng (API chính)**

## API Chính

### Endpoint
```
GET /v1/user/business/reports/line-chart
```

### Parameters
- `type` (required): BusinessReportTypeEnum
  - `ORDER` - <PERSON><PERSON> lượng đơn hàng
  - `CUSTOMER` - Khách hàng
  - `PRODUCT` - <PERSON><PERSON><PERSON> phẩm
  - `REVENUE` - <PERSON><PERSON><PERSON> thu
  - `NEW_CUSTOMERS` - <PERSON>h<PERSON><PERSON> hàng mới
  - `RETURNING_CUSTOMERS` - <PERSON>h<PERSON>ch hàng quay lại
  - `TOTAL_CUSTOMERS` - Tổng khách hàng
  - `AVERAGE_ORDER_VALUE` - Giá trị đơn hàng trung bình

- `begin` (optional): Ngày bắt đầu (YYYY-MM-DD)
  - Nếu không truyền, API tự động lấy từ dữ liệu đầu tiên của user

- `end` (optional): Ngày kết thúc (YYYY-MM-DD)  
  - Nếu không truyền, mặc định là thời gian hiện tại

### Response
```json
{
  "code": 0,
  "message": "Success",
  "data": {
    "data": {
      "2024-01-01": 10,
      "2024-01-02": 15,
      "2024-01-03": 8
    },
    "period": "day"
  }
}
```

API tự động tính toán period (hour/day/week/month/year) dựa trên khoảng thời gian begin-end.

## Các thay đổi đã thực hiện

### 1. Business Module

#### Types mới (`src/modules/business/types/report.types.ts`)
```typescript
export enum BusinessReportTypeEnum {
  ORDER = 'ORDER',
  CUSTOMER = 'CUSTOMER', 
  PRODUCT = 'PRODUCT',
  REVENUE = 'REVENUE',
  NEW_CUSTOMERS = 'NEW_CUSTOMERS',
  RETURNING_CUSTOMERS = 'RETURNING_CUSTOMERS',
  TOTAL_CUSTOMERS = 'TOTAL_CUSTOMERS',
  AVERAGE_ORDER_VALUE = 'AVERAGE_ORDER_VALUE'
}

export interface LineChartQueryDto {
  begin?: string;
  end?: string;
  type: BusinessReportTypeEnum;
}

export interface LineChartResponseDto {
  data: Record<string, number>;
  period: string;
}
```

#### API Layer (`src/modules/business/api/report.api.ts`)
```typescript
export const getLineChart = async (
  params: LineChartQueryDto
): Promise<ApiResponseDto<LineChartResponseDto>> => {
  return apiClient.get(`${BASE_URL}/line-chart`, { params });
};
```

#### Service Layer (`src/modules/business/services/report.service.ts`)
```typescript
export const getLineChartWithBusinessLogic = async (
  params: LineChartQueryDto
): Promise<ApiResponseDto<LineChartResponseDto>> => {
  // Validation logic
  return reportApi.getLineChart(params);
};
```

#### Hooks (`src/modules/business/hooks/useReportQuery.ts`)
```typescript
export const useLineChart = (params: LineChartQueryDto) => {
  return useQuery({
    queryKey: REPORT_QUERY_KEYS.lineChartWithParams(params),
    queryFn: () => ReportService.getLineChart(params),
    staleTime: 5 * 60 * 1000,
    select: (data) => data.result,
    enabled: !!params.type,
  });
};
```

### 2. Dashboard Module

#### Widget Types mới (`src/modules/dashboard/constants/widget-types.ts`)
```typescript
export const WIDGET_TYPES = {
  // ... existing types
  SALES_LINE_CHART: 'sales-line-chart',
  ORDERS_LINE_CHART: 'orders-line-chart', 
  ORDER_STATS: 'order-stats',
} as const;
```

#### Widgets mới
1. **SalesLineChartWidget** - Biểu đồ doanh số sử dụng `BusinessReportTypeEnum.REVENUE`
2. **OrdersLineChartWidget** - Biểu đồ đơn hàng sử dụng `BusinessReportTypeEnum.ORDER`
3. **OrderStatsWidget** - Thống kê đơn hàng kết hợp `ORDER` và `AVERAGE_ORDER_VALUE`

#### Widget Registry (`src/modules/dashboard/registry/widgetConfigs.ts`)
Đã thêm cấu hình cho 3 widgets mới với dependencies `['line-chart-api']`.

### 3. Menu và Navigation

#### Menu cập nhật (`src/modules/dashboard/constants/menu-data.ts`)
- Loại bỏ: `sales-chart`, `orders-chart`, `orders-stats`
- Thêm: `business-line-charts` → `/dashboard/business/line-charts`

#### Route mới (`src/modules/dashboard/routers/dashboardRoutes.tsx`)
```typescript
{
  path: '/dashboard/business/line-charts',
  element: (
    <MainLayout title="Business Line Charts">
      <Suspense fallback={<Loading />}>
        <BusinessLineChartPage />
      </Suspense>
    </MainLayout>
  )
}
```

#### Trang demo (`src/modules/dashboard/pages/business/BusinessLineChartPage.tsx`)
Trang demo hiển thị 3 widgets mới với thông tin API.

### 4. Translations

Đã cập nhật `vi.json`, `en.json`, `zh.json` với:
- `dashboard:menu.items.businessLineCharts`
- `dashboard:pages.businessLineChart.*`

## Lợi ích

1. **Tập trung hóa API**: Chỉ cần maintain 1 API thay vì nhiều API riêng lẻ
2. **Linh hoạt**: API tự động tính period dựa trên khoảng thời gian
3. **Hiệu suất**: Giảm số lượng API calls
4. **Dễ bảo trì**: Logic business tập trung ở một nơi
5. **Mở rộng**: Dễ dàng thêm loại dữ liệu mới qua enum

## Cách sử dụng

### Trong Dashboard
1. Truy cập `/dashboard/business/line-charts` để xem demo
2. Các widget sẽ tự động load dữ liệu từ API chính
3. Không cần truyền begin/end, API sẽ tự động lấy toàn bộ dữ liệu

### Trong Code
```typescript
// Lấy dữ liệu doanh thu
const { data: revenueData } = useLineChart({
  type: BusinessReportTypeEnum.REVENUE
});

// Lấy dữ liệu đơn hàng với khoảng thời gian
const { data: ordersData } = useLineChart({
  type: BusinessReportTypeEnum.ORDER,
  begin: '2024-01-01',
  end: '2024-12-31'
});
```

## Sửa lỗi TypeScript

### Lỗi đã sửa:
1. **DashboardWidgetProps không tồn tại**: Đã thêm interface `DashboardWidgetProps` vào `src/modules/dashboard/types/index.ts`
2. **Widget components sử dụng sai props**: Đã chuyển từ `DashboardWidgetProps` sang `BaseWidgetProps`
3. **Biến không sử dụng**: Đã loại bỏ `onTitleChange`, `formatNumber`, `formatCurrency` không sử dụng
4. **BusinessLineChartPage sử dụng sai props**: Đã sửa từ `widget` sang `widgets` cho `DashboardCard`

### Thay đổi cụ thể:

#### 1. Thêm DashboardWidgetProps interface:
```typescript
// src/modules/dashboard/types/index.ts
export interface DashboardWidgetProps extends BaseWidgetProps {
  widget: DashboardWidget;
  onTitleChange?: (widgetId: string, newTitle: string) => void;
  onRemove?: (widgetId: string) => void;
  mode?: 'view' | 'edit';
  displayMode?: 'normal' | 'minimal' | 'ultra-minimal';
}
```

#### 2. Sửa widget components:
```typescript
// Trước
const SalesLineChartWidget: React.FC<DashboardWidgetProps> = ({
  widget,
  onTitleChange
}) => {

// Sau
const SalesLineChartWidget: React.FC<BaseWidgetProps> = ({
  className,
  isLoading: externalLoading = false
}) => {
```

#### 3. Sửa BusinessLineChartPage:
```typescript
// Trước
<DashboardCard
  widget={widget}
  onRemove={() => {}}
  onTitleChange={() => {}}
  isDraggable={false}
  isResizable={false}
  mode="view"
/>

// Sau
<DashboardCard
  widgets={widgets}
  onRemoveWidget={() => {}}
  onWidgetTitleChange={() => {}}
  isDraggable={false}
  isResizable={false}
  mode="view"
/>
```

## Testing

Để test các widget mới:
1. Chạy ứng dụng: `npm run dev`
2. Truy cập `/dashboard/business/line-charts`
3. Kiểm tra các widget hiển thị đúng
4. Kiểm tra API calls trong Network tab
5. Verify không có lỗi TypeScript: `npm run type-check`

## Next Steps

1. Backend cần implement API `/v1/user/business/reports/line-chart`
2. Test với dữ liệu thực
3. Có thể loại bỏ các API widget cũ sau khi confirm hoạt động tốt
4. Cân nhắc migrate các widget khác sang pattern tương tự

## Status

✅ **HOÀN THÀNH** - Tất cả lỗi TypeScript đã được sửa
✅ **READY FOR TESTING** - Code đã sẵn sàng để test với backend API
