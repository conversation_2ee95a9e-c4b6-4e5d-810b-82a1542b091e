import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Calendar, RefreshCw, TrendingUp } from 'lucide-react';
import { format } from 'date-fns';
import { Card, Typography, Button, DoubleDatePicker } from '@/shared/components/common';
import { LineChart } from '@/shared/components/charts';
import { useLineChart } from '@/modules/business/hooks/useReportQuery';
import { BusinessReportTypeEnum } from '@/modules/business/types/report.types';
import { BaseWidgetProps } from '../../types';

/**
 * Widget biểu đồ doanh số sử dụng API line-chart chính
 */
const SalesLineChartWidget: React.FC<BaseWidgetProps> = ({
  className,
  isLoading: externalLoading = false
}) => {
  const { t } = useTranslation(['dashboard', 'business']);

  // State cho date range picker
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

  // Tạo query params từ date range
  const queryParams = useMemo(() => {
    const [startDate, endDate] = dateRange;
    return {
      type: BusinessReportTypeEnum.REVENUE,
      begin: startDate ? format(startDate, 'yyyy-MM-dd') : undefined,
      end: endDate ? format(endDate, 'yyyy-MM-dd') : undefined,
    };
  }, [dateRange]);

  // Lấy dữ liệu từ API line-chart chính
  const { data: salesData, isLoading: internalLoading, error, refetch } = useLineChart(queryParams);

  const isLoading = externalLoading || internalLoading;

  // Chuyển đổi dữ liệu từ API thành format cho LineChart component
  const chartData = useMemo(() => {
    if (!salesData?.data) return [];

    return Object.entries(salesData.data).map(([date, value]) => ({
      date,
      period: date, // Sử dụng date làm period để hiển thị trên trục X
      revenue: value,
    }));
  }, [salesData?.data]);

  // Cấu hình đường biểu đồ
  const lines = useMemo(() => [
    {
      dataKey: 'revenue',
      name: t('business:report.charts.labels.revenue', 'Doanh thu'),
      color: '#2563eb',
      strokeWidth: 2,
      showDot: true,
      dotSize: 4,
    }
  ], [t]);

  // Xử lý thay đổi date range
  const handleDateRangeChange = (dates: [Date | null, Date | null]) => {
    setDateRange(dates);
  };

  // Xử lý refresh
  const handleRefresh = () => {
    refetch();
  };

  if (error) {
    return (
      <Card className={`p-4 h-full ${className || ''}`}>
        <Typography variant="h3" className="mb-4">
          Biểu đồ doanh số
        </Typography>
        <div className="flex items-center justify-center h-64">
          <Typography variant="body2" className="text-red-500">
            {t('dashboard:widgets.error.loadFailed', 'Không thể tải dữ liệu')}
          </Typography>
        </div>
      </Card>
    );
  }

  return (
    <Card className={`p-4 h-full ${className || ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <TrendingUp className="w-5 h-5 text-primary" />
          <Typography variant="h3" className="font-semibold">
            {t('business:report.charts.sales.title', 'Biểu đồ doanh số')}
          </Typography>
        </div>

        {/* Controls */}
        <div className="flex items-center gap-3">
          {/* Date Range Picker */}
          <DoubleDatePicker
            value={dateRange}
            onChange={handleDateRangeChange}
            triggerIcon={<Calendar className="w-4 h-4" />}
            size="sm"
          />

          {/* Refresh Button */}
          <Button variant="ghost" size="sm" onClick={handleRefresh}>
            <RefreshCw className="w-4 h-4" />
          </Button>

          {/* Period Info */}
          {salesData?.period && (
            <Typography variant="caption" className="text-muted-foreground">
              {t('dashboard:widgets.period', 'Chu kỳ')}: {salesData.period}
            </Typography>
          )}
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <Typography variant="body2" className="text-muted-foreground">
            {t('dashboard:widgets.loading', 'Đang tải...')}
          </Typography>
        </div>
      ) : chartData.length === 0 ? (
        <div className="flex items-center justify-center h-64">
          <Typography variant="body2" className="text-muted-foreground">
            {t('dashboard:widgets.noData', 'Không có dữ liệu')}
          </Typography>
        </div>
      ) : (
        <div className="h-64">
          <LineChart
            data={chartData}
            xAxisKey="period"
            lines={lines}
            height={240}
            showGrid
            showTooltip
            showLegend={false}
          />
        </div>
      )}
    </Card>
  );
};

export default SalesLineChartWidget;
