import React, { useState, useCallback, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Input, Select } from '@/shared/components/common';
import { BaseWidgetProps } from '../../types';

interface CounterWidgetProps extends BaseWidgetProps {
  initialValue?: number;
  targetValue?: number;
  label?: string;
  prefix?: string;
  suffix?: string;
  editable?: boolean;
  animationDuration?: number; // in milliseconds
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'default' | 'primary' | 'success' | 'warning' | 'destructive';
  showProgress?: boolean;
}

/**
 * Widget đếm số với animation và customization
 */
const CounterWidget: React.FC<CounterWidgetProps> = ({
  className,
  initialValue = 0,
  targetValue = 100,
  label = 'Counter',
  prefix = '',
  suffix = '',
  editable = true,
  animationDuration = 2000,
  size = 'lg',
  color = 'primary',
  showProgress = false,
  onPropsChange,
  ...props
}) => {
  const { t } = useTranslation(['dashboard', 'common']);
  
  // Use values from props if available
  const currentValue = (props.currentValue as number) ?? initialValue;
  const currentTarget = (props.targetValue as number) ?? targetValue;
  const currentLabel = (props.label as string) || label;
  const currentPrefix = (props.prefix as string) || prefix;
  const currentSuffix = (props.suffix as string) || suffix;
  const currentSettings = {
    animationDuration: (props.animationDuration as number) ?? animationDuration,
    size: (props.size as typeof size) || size,
    color: (props.color as typeof color) || color,
    showProgress: (props.showProgress as boolean) ?? showProgress,
  };

  const [displayValue, setDisplayValue] = useState(currentValue);
  const [isEditing, setIsEditing] = useState(false);
  const [tempSettings, setTempSettings] = useState({
    currentValue,
    targetValue: currentTarget,
    label: currentLabel,
    prefix: currentPrefix,
    suffix: currentSuffix,
    ...currentSettings,
  });
  
  const animationRef = useRef<number>();
  const startTimeRef = useRef<number>();

  // Sync with props changes
  useEffect(() => {
    const newValue = (props.currentValue as number) ?? initialValue;
    if (newValue !== displayValue) {
      animateToValue(newValue);
    }
  }, [props.currentValue, initialValue]);

  const animateToValue = useCallback((targetVal: number) => {
    const startValue = displayValue;
    const startTime = Date.now();
    startTimeRef.current = startTime;

    const animate = () => {
      const now = Date.now();
      const elapsed = now - startTime;
      const progress = Math.min(elapsed / currentSettings.animationDuration, 1);
      
      // Easing function (ease-out)
      const easeOut = 1 - Math.pow(1 - progress, 3);
      const currentVal = startValue + (targetVal - startValue) * easeOut;
      
      setDisplayValue(Math.round(currentVal));
      
      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      }
    };

    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
    
    animate();
  }, [displayValue, currentSettings.animationDuration]);

  const handleEdit = useCallback(() => {
    setTempSettings({
      currentValue,
      targetValue: currentTarget,
      label: currentLabel,
      prefix: currentPrefix,
      suffix: currentSuffix,
      ...currentSettings,
    });
    setIsEditing(true);
  }, [currentValue, currentTarget, currentLabel, currentPrefix, currentSuffix, currentSettings]);

  const handleSave = useCallback(() => {
    setIsEditing(false);
    
    // Animate to new value
    animateToValue(tempSettings.currentValue);
    
    // Save to widget props
    if (onPropsChange) {
      onPropsChange({
        currentValue: tempSettings.currentValue,
        targetValue: tempSettings.targetValue,
        label: tempSettings.label,
        prefix: tempSettings.prefix,
        suffix: tempSettings.suffix,
        animationDuration: tempSettings.animationDuration,
        size: tempSettings.size,
        color: tempSettings.color,
        showProgress: tempSettings.showProgress,
      });
    }
  }, [tempSettings, onPropsChange, animateToValue]);

  const handleCancel = useCallback(() => {
    setTempSettings({
      currentValue,
      targetValue: currentTarget,
      label: currentLabel,
      prefix: currentPrefix,
      suffix: currentSuffix,
      ...currentSettings,
    });
    setIsEditing(false);
  }, [currentValue, currentTarget, currentLabel, currentPrefix, currentSuffix, currentSettings]);

  const handleIncrement = useCallback(() => {
    const newValue = currentValue + 1;
    animateToValue(newValue);
    if (onPropsChange) {
      onPropsChange({ currentValue: newValue });
    }
  }, [currentValue, animateToValue, onPropsChange]);

  const handleDecrement = useCallback(() => {
    const newValue = Math.max(0, currentValue - 1);
    animateToValue(newValue);
    if (onPropsChange) {
      onPropsChange({ currentValue: newValue });
    }
  }, [currentValue, animateToValue, onPropsChange]);

  // Cleanup animation on unmount
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  const sizeClasses = {
    sm: { number: 'text-2xl', label: 'text-sm' },
    md: { number: 'text-4xl', label: 'text-base' },
    lg: { number: 'text-6xl', label: 'text-lg' },
    xl: { number: 'text-8xl', label: 'text-xl' },
  };

  const colorClasses = {
    default: 'text-foreground',
    primary: 'text-primary',
    success: 'text-green-600',
    warning: 'text-yellow-600',
    destructive: 'text-red-600',
  };

  const sizeOptions = [
    { value: 'sm', label: 'Small' },
    { value: 'md', label: 'Medium' },
    { value: 'lg', label: 'Large' },
    { value: 'xl', label: 'Extra Large' },
  ];

  const colorOptions = [
    { value: 'default', label: 'Default' },
    { value: 'primary', label: 'Primary' },
    { value: 'success', label: 'Success' },
    { value: 'warning', label: 'Warning' },
    { value: 'destructive', label: 'Destructive' },
  ];

  const progressPercentage = currentTarget > 0 ? Math.min((displayValue / currentTarget) * 100, 100) : 0;

  if (isEditing) {
    return (
      <div className={`w-full h-full p-4 ${className || ''}`}>
        <div className="h-full flex flex-col">
          <div className="space-y-4 flex-1">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Typography variant="body2" className="mb-2">
                  {t('dashboard:widgets.counter.currentValue', 'Giá trị hiện tại')}
                </Typography>
                <Input
                  type="number"
                  value={tempSettings.currentValue}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, currentValue: parseInt(e.target.value) || 0 }))}
                  className="w-full"
                />
              </div>
              
              <div>
                <Typography variant="body2" className="mb-2">
                  {t('dashboard:widgets.counter.targetValue', 'Giá trị mục tiêu')}
                </Typography>
                <Input
                  type="number"
                  value={tempSettings.targetValue}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, targetValue: parseInt(e.target.value) || 0 }))}
                  className="w-full"
                />
              </div>
            </div>

            <div>
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.counter.label', 'Nhãn')}
              </Typography>
              <Input
                value={tempSettings.label}
                onChange={(e) => setTempSettings(prev => ({ ...prev, label: e.target.value }))}
                placeholder={t('dashboard:widgets.counter.labelPlaceholder', 'Nhập nhãn...')}
                className="w-full"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Typography variant="body2" className="mb-2">
                  {t('dashboard:widgets.counter.prefix', 'Tiền tố')}
                </Typography>
                <Input
                  value={tempSettings.prefix}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, prefix: e.target.value }))}
                  placeholder="$, #, ..."
                  className="w-full"
                />
              </div>
              
              <div>
                <Typography variant="body2" className="mb-2">
                  {t('dashboard:widgets.counter.suffix', 'Hậu tố')}
                </Typography>
                <Input
                  value={tempSettings.suffix}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, suffix: e.target.value }))}
                  placeholder="%, K, M, ..."
                  className="w-full"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Typography variant="body2" className="mb-2">
                  {t('dashboard:widgets.counter.size', 'Kích thước')}
                </Typography>
                <Select
                  value={tempSettings.size}
                  onChange={(value) => setTempSettings(prev => ({ ...prev, size: value as typeof size }))}
                  options={sizeOptions}
                  className="w-full"
                />
              </div>
              
              <div>
                <Typography variant="body2" className="mb-2">
                  {t('dashboard:widgets.counter.color', 'Màu sắc')}
                </Typography>
                <Select
                  value={tempSettings.color}
                  onChange={(value) => setTempSettings(prev => ({ ...prev, color: value as typeof color }))}
                  options={colorOptions}
                  className="w-full"
                />
              </div>
            </div>

            <div>
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.counter.animationDuration', 'Thời gian animation (ms)')}
              </Typography>
              <Input
                type="number"
                value={tempSettings.animationDuration}
                onChange={(e) => setTempSettings(prev => ({ ...prev, animationDuration: parseInt(e.target.value) || 1000 }))}
                min="100"
                max="10000"
                step="100"
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.showProgress}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, showProgress: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.counter.showProgress', 'Hiển thị thanh tiến độ')}
                </Typography>
              </label>
            </div>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              {t('common:cancel')}
            </Button>
            <Button variant="primary" size="sm" onClick={handleSave}>
              {t('common:save')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`w-full h-full p-4 ${className || ''} ${editable ? 'cursor-pointer hover:bg-muted/50 transition-colors group' : ''}`}
      onClick={editable ? handleEdit : undefined}
    >
      <div className="h-full flex flex-col items-center justify-center text-center">
        {/* Counter Value */}
        <div className={`font-bold ${sizeClasses[currentSettings.size].number} ${colorClasses[currentSettings.color]} mb-2`}>
          {currentPrefix}{displayValue.toLocaleString()}{currentSuffix}
        </div>
        
        {/* Label */}
        {currentLabel && (
          <Typography
            variant="body1"
            className={`${sizeClasses[currentSettings.size].label} text-muted-foreground mb-2`}
          >
            {currentLabel}
          </Typography>
        )}

        {/* Progress Bar */}
        {currentSettings.showProgress && currentTarget > 0 && (
          <div className="w-full max-w-xs">
            <div className="w-full bg-muted rounded-full h-2 mb-1">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
            <Typography variant="caption" className="text-muted-foreground">
              {Math.round(progressPercentage)}% of {currentTarget.toLocaleString()}
            </Typography>
          </div>
        )}

        {/* Quick Actions */}
        {editable && (
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
            <Button variant="ghost" size="sm" onClick={(e) => { e.stopPropagation(); handleDecrement(); }}>
              -
            </Button>
            <Button variant="ghost" size="sm" onClick={(e) => { e.stopPropagation(); handleEdit(); }}>
              {t('common:edit')}
            </Button>
            <Button variant="ghost" size="sm" onClick={(e) => { e.stopPropagation(); handleIncrement(); }}>
              +
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CounterWidget;
