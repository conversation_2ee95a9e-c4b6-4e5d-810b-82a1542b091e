import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Textarea, Input, Icon } from '@/shared/components/common';
import { BaseWidgetProps } from '../../types';

interface Quote {
  text: string;
  author: string;
}

interface QuoteWidgetProps extends BaseWidgetProps {
  initialQuote?: Quote;
  editable?: boolean;
  autoRotate?: boolean;
  rotateInterval?: number; // in minutes
  textAlign?: 'left' | 'center' | 'right';
}

/**
 * Widget hiển thị trích dẫn
 */
const QuoteWidget: React.FC<QuoteWidgetProps> = ({
  className,
  initialQuote = { text: 'Thành công không phải là chìa khóa của hạnh phúc. Hạnh phúc là chìa khóa của thành công.', author: '<PERSON>' },
  editable = true,
  autoRotate = false,
  rotateInterval = 30,
  textAlign = 'center',
}) => {
  const { t } = useTranslation(['dashboard', 'common']);
  const [currentQuote, setCurrentQuote] = useState<Quote>(initialQuote);
  const [isEditing, setIsEditing] = useState(false);
  const [tempQuote, setTempQuote] = useState<Quote>(currentQuote);
  const [quotes, setQuotes] = useState<Quote[]>([initialQuote]);
  const [currentIndex, setCurrentIndex] = useState(0);

  // Predefined quotes
  const defaultQuotes: Quote[] = [
    { text: 'Thành công không phải là chìa khóa của hạnh phúc. Hạnh phúc là chìa khóa của thành công.', author: 'Albert Schweitzer' },
    { text: 'Cách duy nhất để làm việc tuyệt vời là yêu thích công việc bạn đang làm.', author: 'Steve Jobs' },
    { text: 'Đổi mới phân biệt giữa người lãnh đạo và người theo sau.', author: 'Steve Jobs' },
    { text: 'Thất bại là thành công nếu chúng ta học hỏi từ nó.', author: 'Malcolm Forbes' },
    { text: 'Đừng sợ từ bỏ điều tốt để theo đuổi điều tuyệt vời.', author: 'John D. Rockefeller' },
    { text: 'Cơ hội không xảy ra. Bạn tạo ra chúng.', author: 'Chris Grosser' },
  ];

  // Auto rotate quotes
  useEffect(() => {
    if (!autoRotate || quotes.length <= 1) return;

    const timer = setInterval(() => {
      setCurrentIndex(prev => {
        const nextIndex = (prev + 1) % quotes.length;
        setCurrentQuote(quotes[nextIndex]);
        return nextIndex;
      });
    }, rotateInterval * 60 * 1000);

    return () => clearInterval(timer);
  }, [autoRotate, rotateInterval, quotes]);

  const handleEdit = useCallback(() => {
    setTempQuote(currentQuote);
    setIsEditing(true);
  }, [currentQuote]);

  const handleSave = useCallback(() => {
    setCurrentQuote(tempQuote);
    // Update quotes array if this is a new quote
    const existingIndex = quotes.findIndex(q => q.text === currentQuote.text && q.author === currentQuote.author);
    if (existingIndex >= 0) {
      const newQuotes = [...quotes];
      newQuotes[existingIndex] = tempQuote;
      setQuotes(newQuotes);
    } else {
      setQuotes(prev => [...prev, tempQuote]);
    }
    setIsEditing(false);
  }, [tempQuote, currentQuote, quotes]);

  const handleCancel = useCallback(() => {
    setTempQuote(currentQuote);
    setIsEditing(false);
  }, [currentQuote]);

  const handleRandomQuote = useCallback(() => {
    const randomQuote = defaultQuotes[Math.floor(Math.random() * defaultQuotes.length)];
    setCurrentQuote(randomQuote);
    if (!quotes.some(q => q.text === randomQuote.text && q.author === randomQuote.author)) {
      setQuotes(prev => [...prev, randomQuote]);
    }
  }, [quotes]);

  const textAlignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  if (isEditing) {
    return (
      <div className={`w-full h-full p-4 ${className || ''}`}>
        <div className="h-full flex flex-col">
          <div className="flex-1 space-y-4">
            <div>
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.quote.text', 'Nội dung trích dẫn')}
              </Typography>
              <Textarea
                value={tempQuote.text}
                onChange={(e) => setTempQuote(prev => ({ ...prev, text: e.target.value }))}
                placeholder={t('dashboard:widgets.quote.textPlaceholder', 'Nhập nội dung trích dẫn...')}
                className="resize-none"
                rows={4}
              />
            </div>

            <div>
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.quote.author', 'Tác giả')}
              </Typography>
              <Input
                value={tempQuote.author}
                onChange={(e) => setTempQuote(prev => ({ ...prev, author: e.target.value }))}
                placeholder={t('dashboard:widgets.quote.authorPlaceholder', 'Tên tác giả...')}
                className="w-full"
              />
            </div>

            <div className="pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRandomQuote}
                className="w-full"
              >
                <Icon name="shuffle" size="sm" className="mr-2" />
                {t('dashboard:widgets.quote.random', 'Trích dẫn ngẫu nhiên')}
              </Button>
            </div>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              {t('common:cancel')}
            </Button>
            <Button variant="primary" size="sm" onClick={handleSave}>
              {t('common:save')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`w-full h-full p-6 ${className || ''} ${editable ? 'cursor-pointer hover:bg-muted/50 transition-colors group' : ''}`}
      onClick={editable ? handleEdit : undefined}
    >
      <div className={`h-full flex flex-col justify-center ${textAlignClasses[textAlign]}`}>
        <div className="relative">
          <Icon 
            name="quote" 
            size="lg" 
            className="text-muted-foreground/30 absolute -top-2 -left-2" 
          />
          
          <Typography
            variant="body1"
            className="text-lg leading-relaxed mb-4 italic text-foreground relative z-10"
          >
            "{currentQuote.text}"
          </Typography>
          
          <Typography
            variant="body2"
            className="text-muted-foreground font-medium"
          >
            — {currentQuote.author}
          </Typography>

          {autoRotate && quotes.length > 1 && (
            <div className="absolute bottom-0 right-0">
              <Typography variant="caption" className="text-muted-foreground/50">
                {currentIndex + 1}/{quotes.length}
              </Typography>
            </div>
          )}
        </div>

        {editable && (
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button variant="ghost" size="sm" onClick={handleEdit}>
              {t('common:edit')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default QuoteWidget;
