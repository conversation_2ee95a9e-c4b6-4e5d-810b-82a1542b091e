/**
 * Widget type constants for dashboard components
 * Centralized widget type definitions for type safety and consistency
 */

export const WIDGET_TYPES = {
  DATA_COUNT: 'data-count',
  DATA_STORAGE: 'data-storage',
  // Marketing widgets
  MARKETING_OVERVIEW: 'marketing-overview',
  CAMPAIGN_PERFORMANCE: 'campaign-performance',
  // AI Agents widgets
  AGENT_OVERVIEW: 'agent-overview',
  AGENT_PERFORMANCE: 'agent-performance',
  // Business widgets
  BUSINESS_OVERVIEW: 'business-overview',
  CUSTOMER_PRODUCTS: 'customer-products',
  CUSTOMER_LIST: 'customer-list',
  BANK_ACCOUNT_OVERVIEW: 'bank-account-overview',
  CUSTOMERS_CHART: 'customers-chart',
  CUSTOM_FIELD_FORM: 'custom-field-form',
  // Business widgets sử dụng API line-chart chính
  SALES_LINE_CHART: 'sales-line-chart',
  ORDERS_LINE_CHART: 'orders-line-chart',
  ORDER_STATS: 'order-stats',
  // Business pie chart widgets
  PRODUCT_TYPE_PIE_CHART: 'product-type-pie-chart',
  // Universal widget thay thế cho các widget riêng lẻ
  UNIVERSAL_LINE_CHART: 'universal-line-chart',
  BUSINESS_MULTI_LINE_CHART: 'business-multi-line-chart',
  // Affiliate widgets
  AFFILIATE_OVERVIEW: 'affiliate-overview',
  // Integration widgets
  INTEGRATION_OVERVIEW: 'integration-overview',
  // Simple content widgets
  TEXT_WIDGET: 'text-widget',
  IMAGE_WIDGET: 'image-widget',
  VIDEO_WIDGET: 'video-widget',
  HTML_WIDGET: 'html-widget',
  IFRAME_WIDGET: 'iframe-widget',
  CLOCK_WIDGET: 'clock-widget',
  WEATHER_WIDGET: 'weather-widget',
  QUOTE_WIDGET: 'quote-widget',
  COUNTER_WIDGET: 'counter-widget',
  PROGRESS_WIDGET: 'progress-widget',
  CALENDAR_WIDGET: 'calendar-widget',
  TODO_WIDGET: 'todo-widget',
  KPI_WIDGET: 'kpi-widget',
  TABLE_WIDGET: 'table-widget',
} as const;

/**
 * Widget type union type derived from WIDGET_TYPES constants
 */
export type WidgetType = typeof WIDGET_TYPES[keyof typeof WIDGET_TYPES];

/**
 * Widget type validation helper
 */
export const isValidWidgetType = (type: string): type is WidgetType => {
  return Object.values(WIDGET_TYPES).includes(type as WidgetType);
};

/**
 * Get widget type display name
 */
export const getWidgetTypeDisplayName = (type: WidgetType): string => {
  const displayNames: Record<WidgetType, string> = {
    [WIDGET_TYPES.DATA_COUNT]: 'Tổng số lượng dữ liệu',
    [WIDGET_TYPES.DATA_STORAGE]: 'Dung lượng dữ liệu',
    [WIDGET_TYPES.MARKETING_OVERVIEW]: 'Tổng quan Marketing',
    [WIDGET_TYPES.CAMPAIGN_PERFORMANCE]: 'Hiệu suất chiến dịch',
    [WIDGET_TYPES.AGENT_OVERVIEW]: 'Tổng quan AI Agents',
    [WIDGET_TYPES.AGENT_PERFORMANCE]: 'Hiệu suất Agents',
    [WIDGET_TYPES.BUSINESS_OVERVIEW]: 'Tổng quan kinh doanh',
    [WIDGET_TYPES.CUSTOMER_PRODUCTS]: 'Danh sách sản phẩm',
    [WIDGET_TYPES.CUSTOMER_LIST]: 'Danh sách khách hàng',
    [WIDGET_TYPES.BANK_ACCOUNT_OVERVIEW]: 'Tổng quan tài khoản ngân hàng',
    [WIDGET_TYPES.CUSTOMERS_CHART]: 'Biểu đồ khách hàng',
    [WIDGET_TYPES.CUSTOM_FIELD_FORM]: 'Thêm trường tùy chỉnh',
    [WIDGET_TYPES.SALES_LINE_CHART]: 'Biểu đồ doanh số',
    [WIDGET_TYPES.ORDERS_LINE_CHART]: 'Biểu đồ đơn hàng',
    [WIDGET_TYPES.ORDER_STATS]: 'Thống kê đơn hàng',
    [WIDGET_TYPES.PRODUCT_TYPE_PIE_CHART]: 'Biểu đồ tròn loại sản phẩm',
    [WIDGET_TYPES.UNIVERSAL_LINE_CHART]: 'Biểu đồ kinh doanh tổng hợp',
    [WIDGET_TYPES.BUSINESS_MULTI_LINE_CHART]: 'Biểu đồ kinh doanh đa dữ liệu',
    [WIDGET_TYPES.AFFILIATE_OVERVIEW]: 'Tổng quan Affiliate',
    [WIDGET_TYPES.INTEGRATION_OVERVIEW]: 'Tổng quan tích hợp',
    [WIDGET_TYPES.TEXT_WIDGET]: 'Widget văn bản',
    [WIDGET_TYPES.IMAGE_WIDGET]: 'Widget hình ảnh',
    [WIDGET_TYPES.VIDEO_WIDGET]: 'Widget video',
    [WIDGET_TYPES.HTML_WIDGET]: 'Widget HTML',
    [WIDGET_TYPES.IFRAME_WIDGET]: 'Widget iframe',
    [WIDGET_TYPES.CLOCK_WIDGET]: 'Widget đồng hồ',
    [WIDGET_TYPES.WEATHER_WIDGET]: 'Widget thời tiết',
    [WIDGET_TYPES.QUOTE_WIDGET]: 'Widget trích dẫn',
    [WIDGET_TYPES.COUNTER_WIDGET]: 'Widget đếm số',
    [WIDGET_TYPES.PROGRESS_WIDGET]: 'Widget tiến độ',
    [WIDGET_TYPES.CALENDAR_WIDGET]: 'Widget lịch',
    [WIDGET_TYPES.TODO_WIDGET]: 'Widget todo list',
    [WIDGET_TYPES.KPI_WIDGET]: 'Widget KPI',
    [WIDGET_TYPES.TABLE_WIDGET]: 'Widget bảng dữ liệu',
  };

  return displayNames[type] || 'Không xác định';
};

/**
 * Widget type categories for grouping
 */
export const WIDGET_CATEGORIES = {
  DATA: 'data',
  VISUALIZATION: 'visualization',
  MARKETING: 'marketing',
  AI_AGENTS: 'ai-agents',
  BUSINESS: 'business',
  AFFILIATE: 'affiliate',
  INTEGRATION: 'integration',
  CONTENT: 'content',
} as const;

export type WidgetCategory = typeof WIDGET_CATEGORIES[keyof typeof WIDGET_CATEGORIES];

/**
 * Map widget types to categories
 */
export const WIDGET_TYPE_CATEGORIES: Record<WidgetType, WidgetCategory> = {
  [WIDGET_TYPES.DATA_COUNT]: WIDGET_CATEGORIES.DATA,
  [WIDGET_TYPES.DATA_STORAGE]: WIDGET_CATEGORIES.DATA,
  [WIDGET_TYPES.MARKETING_OVERVIEW]: WIDGET_CATEGORIES.MARKETING,
  [WIDGET_TYPES.CAMPAIGN_PERFORMANCE]: WIDGET_CATEGORIES.MARKETING,
  [WIDGET_TYPES.AGENT_OVERVIEW]: WIDGET_CATEGORIES.AI_AGENTS,
  [WIDGET_TYPES.AGENT_PERFORMANCE]: WIDGET_CATEGORIES.AI_AGENTS,
  [WIDGET_TYPES.BUSINESS_OVERVIEW]: WIDGET_CATEGORIES.BUSINESS,
  [WIDGET_TYPES.CUSTOMER_PRODUCTS]: WIDGET_CATEGORIES.BUSINESS,
  [WIDGET_TYPES.CUSTOMER_LIST]: WIDGET_CATEGORIES.BUSINESS,
  [WIDGET_TYPES.BANK_ACCOUNT_OVERVIEW]: WIDGET_CATEGORIES.BUSINESS,
  [WIDGET_TYPES.CUSTOMERS_CHART]: WIDGET_CATEGORIES.BUSINESS,
  [WIDGET_TYPES.CUSTOM_FIELD_FORM]: WIDGET_CATEGORIES.BUSINESS,
  [WIDGET_TYPES.SALES_LINE_CHART]: WIDGET_CATEGORIES.BUSINESS,
  [WIDGET_TYPES.ORDERS_LINE_CHART]: WIDGET_CATEGORIES.BUSINESS,
  [WIDGET_TYPES.ORDER_STATS]: WIDGET_CATEGORIES.BUSINESS,
  [WIDGET_TYPES.PRODUCT_TYPE_PIE_CHART]: WIDGET_CATEGORIES.BUSINESS,
  [WIDGET_TYPES.UNIVERSAL_LINE_CHART]: WIDGET_CATEGORIES.BUSINESS,
  [WIDGET_TYPES.BUSINESS_MULTI_LINE_CHART]: WIDGET_CATEGORIES.BUSINESS,
  [WIDGET_TYPES.AFFILIATE_OVERVIEW]: WIDGET_CATEGORIES.AFFILIATE,
  [WIDGET_TYPES.INTEGRATION_OVERVIEW]: WIDGET_CATEGORIES.INTEGRATION,
  [WIDGET_TYPES.TEXT_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.IMAGE_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.VIDEO_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.HTML_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.IFRAME_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.CLOCK_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.WEATHER_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.QUOTE_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.COUNTER_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.PROGRESS_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.CALENDAR_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.TODO_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.KPI_WIDGET]: WIDGET_CATEGORIES.BUSINESS,
  [WIDGET_TYPES.TABLE_WIDGET]: WIDGET_CATEGORIES.DATA,
};
