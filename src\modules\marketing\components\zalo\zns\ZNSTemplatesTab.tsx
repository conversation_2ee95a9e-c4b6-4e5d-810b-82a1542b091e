import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Card,
  Typography,
  Table,
  ActionMenu,
  Chip,
  Icon,
} from '@/shared/components/common';
import { ZNSAccordionProvider } from '@/modules/marketing/contexts/ZNSAccordionContext';
import { ZNSTemplateWizard } from '@/modules/marketing/components/zns';

import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/shared/api';

// Import types
interface TemplateFormData {
  template_name: string;
  tag: string;
  templateType: string;
  components: any[];
}
import type { ActionMenuItem } from '@/shared/components/common/ActionMenu/ActionMenu';

// Zalo API Template interface
interface ZaloTemplate {
  templateId: number;
  templateName: string;
  createdTime: number;
  status: 'PENDING_REVIEW' | 'ENABLE' | 'REJECT' | 'DISABLE';
  templateQuality: 'UNDEFINED' | 'HIGH' | 'MEDIUM' | 'LOW';
}

interface ZaloTemplatesResponse {
  items: ZaloTemplate[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

interface ZNSTemplatesTabProps {
  oaId: string;
}

// Use the Zalo API type
type ZNSTemplate = ZaloTemplate;

const ZNSTemplatesTab: React.FC<ZNSTemplatesTabProps> = ({ oaId }) => {
  // Use oaId for future API calls
  console.log('ZNS Templates for OA:', oaId);
  const { t } = useTranslation(['marketing']);
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Persist form data across steps
  const [formData, setFormData] = useState<TemplateFormData>({
    template_name: '',
    tag: '',
    templateType: '',
    components: []
  });

  // Fetch templates from Zalo API
  const { data: zaloTemplatesData, isLoading, error } = useQuery({
    queryKey: ['zalo-templates', oaId, currentPage, pageSize],
    queryFn: async () => {
      const response = await apiClient.get<ZaloTemplatesResponse>(
        `/marketing/zalo/zns/templates/zalo-api/${oaId}`,
        {
          params: {
            page: currentPage,
            limit: pageSize,
          }
        }
      );
      return response.result;
    },
    enabled: !!oaId,
  });

  // Get templates from Zalo API data
  const templates = zaloTemplatesData?.items || [];
  const totalItems = zaloTemplatesData?.meta?.totalItems || 0;

  // Filter options - Zalo API doesn't have templateType, so just show all
  const filterOptions = [
    { id: 'all', label: 'Tất cả', count: templates.length },
  ];

  // Filtered templates - For Zalo API, we just show all templates
  const filteredTemplates = useMemo(() => {
    return templates;
  }, [templates]);

  // Status badge variant

  // Status label
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'ENABLE': return t('marketing:zalo.zns.status.approved', 'Đã duyệt');
      case 'PENDING_REVIEW': return t('marketing:zalo.zns.status.pending', 'Chờ duyệt');
      case 'REJECT': return t('marketing:zalo.zns.status.rejected', 'Từ chối');
      case 'DISABLE': return t('marketing:zalo.zns.status.disabled', 'Vô hiệu hóa');
      default: return status;
    }
  };



  // Handle actions
  

  const handlePreview = (template: ZNSTemplate) => {
    console.log('Preview template:', template.templateId);
    // TODO: Implement preview functionality
  };

  const handleCreateTemplate = () => {
    setCurrentStep(1);
    setShowCreateForm(true);
  };

  const handleCloseCreateForm = () => {
    setShowCreateForm(false);
    setCurrentStep(1);
    // Reset form data
    setFormData({
      template_name: '',
      tag: '',
      templateType: '',
      components: []
    });
  };

  // Table columns for Zalo API
  const columns = [
    {
      key: 'templateId',
      title: 'Template ID',
      render: (_: any, record: ZNSTemplate) => (
        <Typography variant="body2" className="font-medium">
          {record.templateId}
        </Typography>
      ),
    },
    {
      key: 'templateName',
      title: 'Tên template',
      render: (_: any, record: ZNSTemplate) => (
        <Typography variant="body2" className="font-medium">
          {record.templateName}
        </Typography>
      ),
    },
    {
      key: 'status',
      title: 'Trạng thái',
      render: (_: any, record: ZNSTemplate) => (
        <Chip
          variant={record.status === 'ENABLE' ? 'success' as const :
                   record.status === 'PENDING_REVIEW' ? 'warning' as const :
                   record.status === 'REJECT' ? 'danger' as const : 'default' as const}
        >
          {getStatusLabel(record.status)}
        </Chip>
      ),
    },
    {
      key: 'templateQuality',
      title: 'Chất lượng',
      render: (_: any, record: ZNSTemplate) => (
        <Chip
          variant={record.templateQuality === 'HIGH' ? 'success' as const :
                   record.templateQuality === 'MEDIUM' ? 'warning' as const :
                   record.templateQuality === 'LOW' ? 'danger' as const : 'default' as const}
        >
          {record.templateQuality}
        </Chip>
      ),
    },
    {
      key: 'createdTime',
      title: 'Thời gian tạo',
      render: (_: any, record: ZNSTemplate) => (
        <Typography variant="caption" className="text-muted-foreground">
          {new Date(record.createdTime).toLocaleString('vi-VN')}
        </Typography>
      ),
    },
  
    {
      key: 'actions',
      title: t('marketing:common.actions'),
      width: '80px',
      render: (_: any, record: ZNSTemplate) => {
        const actionItems: ActionMenuItem[] = [
          {
            id: 'preview',
            label: t('marketing:zalo.zns.preview.title'),
            icon: 'eye',
            onClick: () => handlePreview(record),
          },

        ];

        return (
          <ActionMenu
            items={actionItems}
            menuTooltip={t('marketing:common.actions')}
            iconSize="sm"
            iconVariant="default"
            placement="bottom"
            menuWidth="200px"
            showAllInMenu={false}
            preferRight={true}
          />
        );
      },
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h3" className="font-semibold">
            {t('marketing:zalo.zns.title')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            {t('marketing:zalo.zns.description')}
          </Typography>
        </div>
        {!showCreateForm && (
          <Button onClick={handleCreateTemplate} className="flex items-center gap-2">
            <Icon name="plus" size="sm" />
            {t('marketing:zalo.zns.createTemplate')}
          </Button>
        )}
      </div>

      {/* Create Form or Filters */}
      {showCreateForm ? (
        <Card className="p-6">

          <ZNSAccordionProvider>
            <ZNSTemplateWizard
              currentStep={currentStep}
              onStepChange={setCurrentStep}
              onClose={handleCloseCreateForm}
              formData={formData}
              onFormDataChange={setFormData}
            />
          </ZNSAccordionProvider>
        </Card>
      ) : (
          <div className="space-y-4">
            {/* Search */}
          
            {/* Filter chips */}
            <div className="flex flex-wrap gap-2">
              {filterOptions.map((option) => (
                <Chip
                  key={option.id}
                  isSelected={selectedFilter === option.id}
                  onClick={() => setSelectedFilter(option.id)}
                  variant={selectedFilter === option.id ? 'primary' : 'secondary'}
                >
                  {`${option.label} (${option.count})`}
                </Chip>
              ))}
            </div>
          </div>
      )}

      {/* Templates Table - Only show when not creating */}
      {!showCreateForm && (
        <Card className="overflow-hidden">
          {error ? (
            <div className="p-8 text-center">
              <Typography variant="body1" className="text-red-500 mb-4">
                Có lỗi xảy ra khi tải dữ liệu template
              </Typography>
              <Button
                variant="outline"
                onClick={() => window.location.reload()}
              >
                Thử lại
              </Button>
            </div>
          ) : (
            <Table<ZNSTemplate>
              columns={columns}
              data={filteredTemplates}
              rowKey="templateId"
              loading={isLoading}
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                total: totalItems,
                onChange: (page, size) => {
                  setCurrentPage(page);
                  if (size !== pageSize) {
                    setPageSize(size);
                  }
                },
                showSizeChanger: true,
                pageSizeOptions: [10, 20, 50],
                showFirstLastButtons: true,
                showPageInfo: true,
              }}
            />
          )}
        </Card>
      )}
    </div>
  );
};

export default ZNSTemplatesTab;
