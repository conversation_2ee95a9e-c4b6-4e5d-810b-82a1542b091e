import { useState, useCallback, useEffect } from 'react';
import { DashboardTab, DashboardTabsState, DashboardWidget } from '../types';
import {
  useDashboardWithFallback,
  useUpdateDashboard,
  useMigrateDashboard,
  useSyncDashboard,
} from './useDashboardManagement';
import { DashboardManagementService } from '../services/dashboard-management.service';

const createDefaultTab = (): DashboardTab => ({
  id: `tab-${Date.now()}`,
  name: 'Trang chính',
  widgets: [],
  mode: 'edit',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
});

const createNewTab = (name?: string): DashboardTab => ({
  id: `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  name: name || `Trang ${Date.now().toString().slice(-4)}`,
  widgets: [],
  mode: 'edit',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
});

export const useDashboardTabs = () => {
  // State management
  const [tabsState, setTabsState] = useState<DashboardTabsState>(() => {
    // Default state - will be overridden by server data if available
    const defaultTab = createDefaultTab();
    return {
      currentTabId: defaultTab.id,
      tabs: [defaultTab],
    };
  });

  const [isInitialized, setIsInitialized] = useState(false);
  const [currentDashboardId, setCurrentDashboardId] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Backend integration hooks
  const { data: fallbackData, isLoading: isLoadingFallback } = useDashboardWithFallback({
    preferServer: true,
    fallbackToLocal: true,
  });

  const updateDashboardMutation = useUpdateDashboard();
  const migrateDashboardMutation = useMigrateDashboard();
  const syncDashboardMutation = useSyncDashboard();

  const currentTab = tabsState.tabs.find(tab => tab.id === tabsState.currentTabId);

  // Initialize dashboard data from server/localStorage
  useEffect(() => {
    if (!isLoadingFallback && fallbackData && !isInitialized) {
      if (fallbackData.data) {
        setTabsState(fallbackData.data);
        setCurrentDashboardId(fallbackData.serverData?.id || null);
      }
      setIsInitialized(true);
    }
  }, [fallbackData, isLoadingFallback, isInitialized]);

  // Auto-save functionality (disabled - only manual save)
  const scheduleAutoSave = useCallback(() => {
    // Auto-save disabled - only save to localStorage for backup
    if (hasUnsavedChanges) {
      DashboardManagementService.saveToLocalStorage(tabsState);
    }
  }, [hasUnsavedChanges, tabsState]);

  // Only save to localStorage when state changes (no server auto-save)
  useEffect(() => {
    if (isInitialized && hasUnsavedChanges) {
      scheduleAutoSave();
    }
  }, [tabsState, hasUnsavedChanges, isInitialized, scheduleAutoSave]);

  // Save to localStorage (backward compatibility)
  const saveToStorage = useCallback(() => {
    try {
      DashboardManagementService.saveToLocalStorage(tabsState);

      // If we have a dashboard ID, also sync to server
      if (currentDashboardId) {
        syncDashboardMutation.mutate({
          dashboardId: currentDashboardId,
          localData: tabsState,
          options: { saveToLocal: true, createBackup: true },
        });
      }
    } catch (error) {
      console.error('Failed to save dashboard tabs:', error);
    }
  }, [tabsState, currentDashboardId, syncDashboardMutation]);

  // Save to server
  const saveToServer = useCallback(
    async (name?: string) => {
      try {
        if (currentDashboardId) {
          // Update existing dashboard
          await updateDashboardMutation.mutateAsync({
            id: currentDashboardId,
            data: { tabsConfig: tabsState },
          });
        } else {
          // Create new dashboard (migrate from localStorage)
          const result = await migrateDashboardMutation.mutateAsync({
            localData: tabsState,
            name: name || 'My Dashboard',
          });
          setCurrentDashboardId(result.id);
        }
        setHasUnsavedChanges(false);
      } catch (error) {
        console.error('Failed to save dashboard to server:', error);
        throw error;
      }
    },
    [currentDashboardId, tabsState, updateDashboardMutation, migrateDashboardMutation]
  );

  // Mark as having unsaved changes
  const markAsChanged = useCallback(() => {
    setHasUnsavedChanges(true);
  }, []);

  // Switch to a tab
  const switchToTab = useCallback(
    (tabId: string) => {
      setTabsState(prev => ({
        ...prev,
        currentTabId: tabId,
      }));
      markAsChanged();
    },
    [markAsChanged]
  );

  // Create new tab
  const createTab = useCallback(
    (name?: string) => {
      const newTab = createNewTab(name);
      setTabsState(prev => ({
        currentTabId: newTab.id,
        tabs: [...prev.tabs, newTab],
      }));
      markAsChanged();
    },
    [markAsChanged]
  );

  // Rename tab
  const renameTab = useCallback(
    (tabId: string, newName: string) => {
      setTabsState(prev => ({
        ...prev,
        tabs: prev.tabs.map(tab =>
          tab.id === tabId ? { ...tab, name: newName, updatedAt: new Date().toISOString() } : tab
        ),
      }));
      markAsChanged();
    },
    [markAsChanged]
  );

  // Delete tab
  const deleteTab = useCallback(
    (tabId: string) => {
      setTabsState(prev => {
        const newTabs = prev.tabs.filter(tab => tab.id !== tabId);

        // Don't allow deleting the last tab
        if (newTabs.length === 0) {
          return prev;
        }

        // If deleting current tab, switch to first available tab
        const newCurrentTabId =
          prev.currentTabId === tabId ? newTabs[0]?.id || prev.currentTabId : prev.currentTabId;

        return {
          currentTabId: newCurrentTabId,
          tabs: newTabs,
        };
      });
      markAsChanged();
    },
    [markAsChanged]
  );

  // Reorder tabs
  const reorderTabs = useCallback(
    (fromIndex: number, toIndex: number) => {
      setTabsState(prev => {
        const newTabs = [...prev.tabs];

        // Validate indices
        if (
          fromIndex < 0 ||
          fromIndex >= newTabs.length ||
          toIndex < 0 ||
          toIndex >= newTabs.length
        ) {
          return prev; // Return unchanged state if indices are invalid
        }

        const [movedTab] = newTabs.splice(fromIndex, 1);

        // Check if movedTab exists (should always be true with valid indices, but for type safety)
        if (movedTab) {
          newTabs.splice(toIndex, 0, movedTab);
        }

        return {
          ...prev,
          tabs: newTabs,
        };
      });
      markAsChanged();
    },
    [markAsChanged]
  );

  // Change tab mode
  const changeTabMode = useCallback(
    (mode: 'view' | 'edit') => {
      setTabsState(prev => ({
        ...prev,
        tabs: prev.tabs.map(tab =>
          tab.id === prev.currentTabId ? { ...tab, mode, updatedAt: new Date().toISOString() } : tab
        ),
      }));
      markAsChanged();
    },
    [markAsChanged]
  );

  // Update current tab widgets
  const updateTabWidgets = useCallback(
    (widgets: DashboardWidget[]) => {
      setTabsState(prev => ({
        ...prev,
        tabs: prev.tabs.map(tab =>
          tab.id === prev.currentTabId
            ? { ...tab, widgets, updatedAt: new Date().toISOString() }
            : tab
        ),
      }));
      markAsChanged();
    },
    [markAsChanged]
  );

  // Add widget to current tab
  const addWidgetToCurrentTab = useCallback(
    (widget: DashboardWidget) => {
      setTabsState(prev => ({
        ...prev,
        tabs: prev.tabs.map(tab =>
          tab.id === prev.currentTabId
            ? {
                ...tab,
                widgets: [...tab.widgets, widget],
                updatedAt: new Date().toISOString(),
              }
            : tab
        ),
      }));
      markAsChanged();
    },
    [markAsChanged]
  );

  // Remove widget from current tab
  const removeWidgetFromCurrentTab = useCallback(
    (widgetId: string) => {
      setTabsState(prev => ({
        ...prev,
        tabs: prev.tabs.map(tab =>
          tab.id === prev.currentTabId
            ? {
                ...tab,
                widgets: tab.widgets.filter(w => w.id !== widgetId),
                updatedAt: new Date().toISOString(),
              }
            : tab
        ),
      }));
      markAsChanged();
    },
    [markAsChanged]
  );

  // Update widget title in current tab
  const updateWidgetTitleInCurrentTab = useCallback(
    (widgetId: string, newTitle: string) => {
      setTabsState(prev => ({
        ...prev,
        tabs: prev.tabs.map(tab =>
          tab.id === prev.currentTabId
            ? {
                ...tab,
                widgets: tab.widgets.map(w => (w.id === widgetId ? { ...w, title: newTitle } : w)),
                updatedAt: new Date().toISOString(),
              }
            : tab
        ),
      }));
      markAsChanged();
    },
    [markAsChanged]
  );

  // Update widget props in current tab
  const updateWidgetPropsInCurrentTab = useCallback(
    (widgetId: string, newProps: Record<string, unknown>) => {
      setTabsState(prev => ({
        ...prev,
        tabs: prev.tabs.map(tab =>
          tab.id === prev.currentTabId
            ? {
                ...tab,
                widgets: tab.widgets.map(w =>
                  w.id === widgetId ? { ...w, props: { ...w.props, ...newProps } } : w
                ),
                updatedAt: new Date().toISOString(),
              }
            : tab
        ),
      }));
      markAsChanged();
    },
    [markAsChanged]
  );

  return {
    // State
    tabsState,
    currentTab,
    isInitialized,
    currentDashboardId,
    hasUnsavedChanges,
    isLoading: isLoadingFallback,
    isSyncing: syncDashboardMutation.isPending || updateDashboardMutation.isPending,

    // Actions
    switchToTab,
    createTab,
    renameTab,
    deleteTab,
    reorderTabs,
    changeTabMode,
    updateTabWidgets,
    addWidgetToCurrentTab,
    removeWidgetFromCurrentTab,
    updateWidgetTitleInCurrentTab,
    updateWidgetPropsInCurrentTab,
    saveToStorage,
    saveToServer,
    markAsChanged,

    // Sync status
    syncStatus: {
      isLoading: isLoadingFallback,
      isSyncing: syncDashboardMutation.isPending || updateDashboardMutation.isPending,
      lastSyncAt: undefined, // TODO: implement last sync tracking
      hasUnsavedChanges,
      syncError: syncDashboardMutation.error || updateDashboardMutation.error,
    },
  };
};
