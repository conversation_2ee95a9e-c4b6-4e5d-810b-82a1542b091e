import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Calendar, RefreshCw, BarChart3 } from 'lucide-react';
import { format } from 'date-fns';
import { Card, Typography, Button, DoubleDatePicker } from '@/shared/components/common';
import { LineChart } from '@/shared/components/charts';
import { useLineChart } from '@/modules/business/hooks/useReportQuery';
import { BusinessReportTypeEnum } from '@/modules/business/types/report.types';
import { BaseWidgetProps } from '../../types';

/**
 * Widget thống kê đơn hàng với nhiều đường biểu đồ sử dụng API line-chart chính
 */
const OrderStatsWidget: React.FC<BaseWidgetProps> = ({
  className,
  isLoading: externalLoading = false
}) => {
  const { t } = useTranslation(['dashboard', 'business']);

  // State cho date range picker
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

  // Tạo query params từ date range
  const queryParams = useMemo(() => {
    const [startDate, endDate] = dateRange;
    return {
      begin: startDate ? format(startDate, 'yyyy-MM-dd') : undefined,
      end: endDate ? format(endDate, 'yyyy-MM-dd') : undefined,
    };
  }, [dateRange]);

  // Lấy dữ liệu đơn hàng tổng
  const { data: ordersData, isLoading: ordersLoading, refetch: refetchOrders } = useLineChart({
    type: BusinessReportTypeEnum.ORDER,
    ...queryParams,
  });

  // Lấy dữ liệu giá trị đơn hàng trung bình
  const { data: avgOrderData, isLoading: avgLoading, refetch: refetchAvgOrder } = useLineChart({
    type: BusinessReportTypeEnum.AVERAGE_ORDER_VALUE,
    ...queryParams,
  });

  const isLoading = externalLoading || ordersLoading || avgLoading;

  // Chuyển đổi và kết hợp dữ liệu từ các API
  const chartData = useMemo(() => {
    if (!ordersData?.data || !avgOrderData?.data) return [];

    // Lấy tất cả các ngày từ cả hai dataset
    const allDates = new Set([
      ...Object.keys(ordersData.data),
      ...Object.keys(avgOrderData.data)
    ]);

    return Array.from(allDates)
      .sort()
      .map(date => ({
        date,
        period: date,
        orders: ordersData.data[date] || 0,
        avgOrderValue: avgOrderData.data[date] || 0,
      }));
  }, [ordersData?.data, avgOrderData?.data]);

  // Cấu hình các đường biểu đồ
  const lines = useMemo(() => [
    {
      dataKey: 'orders',
      name: t('business:report.charts.labels.orders', 'Đơn hàng'),
      color: '#16a34a',
      strokeWidth: 2,
      showDot: true,
      dotSize: 4,
    },
    {
      dataKey: 'avgOrderValue',
      name: t('business:report.charts.labels.avgOrderValue', 'Giá trị TB'),
      color: '#dc2626',
      strokeWidth: 2,
      showDot: true,
      dotSize: 4,
    }
  ], [t]);

  // Xử lý thay đổi date range
  const handleDateRangeChange = (dates: [Date | null, Date | null]) => {
    setDateRange(dates);
  };

  // Xử lý refresh
  const handleRefresh = () => {
    refetchOrders();
    refetchAvgOrder();
  };

  if (!ordersData && !avgOrderData && !isLoading) {
    return (
      <Card className={`p-4 h-full ${className || ''}`}>
        <Typography variant="h3" className="mb-4">
          Thống kê đơn hàng
        </Typography>
        <div className="flex items-center justify-center h-64">
          <Typography variant="body2" className="text-red-500">
            {t('dashboard:widgets.error.loadFailed', 'Không thể tải dữ liệu')}
          </Typography>
        </div>
      </Card>
    );
  }

  return (
    <Card className={`p-4 h-full flex flex-col ${className || ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <BarChart3 className="w-5 h-5 text-primary" />
          <Typography variant="h3" className="font-semibold">
            {t('business:report.charts.orderStats.title', 'Thống kê đơn hàng')}
          </Typography>
        </div>

        {/* Controls */}
        <div className="flex items-center gap-3">
          {/* Date Range Picker */}
          <DoubleDatePicker
            value={dateRange}
            onChange={handleDateRangeChange}
            triggerIcon={<Calendar className="w-4 h-4" />}
            size="sm"
          />

          {/* Refresh Button */}
          <Button variant="ghost" size="sm" onClick={handleRefresh}>
            <RefreshCw className="w-4 h-4" />
          </Button>

          {/* Period Info */}
          {(ordersData?.period || avgOrderData?.period) && (
            <Typography variant="caption" className="text-muted-foreground">
              {t('dashboard:widgets.period', 'Chu kỳ')}: {ordersData?.period || avgOrderData?.period}
            </Typography>
          )}
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <Typography variant="body2" className="text-muted-foreground">
            {t('dashboard:widgets.loading', 'Đang tải...')}
          </Typography>
        </div>
      ) : chartData.length === 0 ? (
        <div className="flex items-center justify-center h-64">
          <Typography variant="body2" className="text-muted-foreground">
            {t('dashboard:widgets.noData', 'Không có dữ liệu')}
          </Typography>
        </div>
      ) : (
        <div className="flex-1 min-h-0" style={{ height: 'calc(100% - 80px)' }}>
          <LineChart
            data={chartData}
            xAxisKey="period"
            lines={lines}
            height={300}
            showGrid
            showTooltip
            showLegend
            legendPosition="top"
          />
        </div>
      )}
    </Card>
  );
};

export default OrderStatsWidget;
