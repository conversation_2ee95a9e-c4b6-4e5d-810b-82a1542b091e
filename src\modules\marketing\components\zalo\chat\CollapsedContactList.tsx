/**
 * CollapsedContactList Component
 * Component hiển thị danh sách contact ở chế độ thu nhỏ (chỉ avatar + badge)
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Image, Icon, Loading, Tooltip } from '@/shared/components/common';
import type { Contact } from '../../../types/zalo-chat.types';
import { format, isToday, isYesterday } from 'date-fns';
import { vi } from 'date-fns/locale';

interface CollapsedContactListProps {
  contacts: Contact[];
  selectedContactId?: string;
  onContactSelect: (contact: Contact) => void;
  isLoading?: boolean;
  className?: string;
}

interface CollapsedContactItemProps {
  contact: Contact;
  isSelected: boolean;
  onClick: (contact: Contact) => void;
}

/**
 * Component hiển thị một item contact ở chế độ collapsed
 */
const CollapsedContactItem: React.FC<CollapsedContactItemProps> = ({
  contact,
  isSelected,
  onClick,
}) => {
  const { t } = useTranslation(['marketing', 'common']);

  // Format thời gian tin nhắn cuối cho tooltip
  const formatLastMessageTime = (timestamp?: string): string => {
    if (!timestamp) return '';

    const messageDate = new Date(timestamp);

    if (isToday(messageDate)) {
      return format(messageDate, 'HH:mm');
    } else if (isYesterday(messageDate)) {
      return t('common:yesterday');
    } else {
      return format(messageDate, 'dd/MM', { locale: vi });
    }
  };

  // Truncate tin nhắn cuối cho tooltip
  const truncateMessage = (message?: string, maxLength: number = 100): string => {
    if (!message) return t('marketing:zalo.chat.noMessages');
    return message.length > maxLength ? `${message.substring(0, maxLength)}...` : message;
  };

  // Tooltip content
  const tooltipContent = (
    <div className="max-w-xs">
      <div className="font-medium text-white mb-1">{contact.name}</div>
      <div className="text-gray-300 text-sm">
        {truncateMessage(contact.lastMessage?.content)}
      </div>
      {contact.lastMessageTime && (
        <div className="text-gray-400 text-xs mt-1">
          {formatLastMessageTime(contact.lastMessageTime)}
        </div>
      )}
    </div>
  );

  return (
    <Tooltip content={tooltipContent} position="right" delay={500}>
      <div
        onClick={() => onClick(contact)}
        className={`
          relative flex items-center justify-center p-2 mb-2 cursor-pointer transition-all duration-200 rounded-lg group w-10 h-10
          hover:bg-gray-100 dark:hover:bg-gray-700 hover:bg-primary/10 dark:hover:bg-primary/20
          ${isSelected ? 'bg-blue-100 dark:bg-blue-900/30 ring-2 ring-blue-500' : ''}
        `}
      >
        {/* Avatar */}
        <div className="relative flex items-center justify-center">
          {contact.avatar ? (
            <Image
              src={contact.avatar}
              alt={contact.name}
              className="w-6 h-6 rounded-full object-cover"
            />
          ) : (
            <div className="w-6 h-6 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
              <Icon name="user" size="xs" className="text-gray-600 dark:text-gray-300" />
            </div>
          )}

          {/* Online status indicator */}
          {contact.isOnline && (
            <div className="absolute -bottom-0.5 -right-0.5 w-2 h-2 bg-green-500 border border-white dark:border-gray-800 rounded-full"></div>
          )}

          {/* Unread count badge */}
          {contact.unreadCount > 0 && (
            <div className="absolute -top-0.5 -right-0.5 min-w-[12px] h-[12px] bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center px-0.5">
              {contact.unreadCount > 9 ? '9+' : contact.unreadCount}
            </div>
          )}
        </div>
      </div>
    </Tooltip>
  );
};

/**
 * Component CollapsedContactList chính
 */
const CollapsedContactList: React.FC<CollapsedContactListProps> = ({
  contacts,
  selectedContactId,
  onContactSelect,
  isLoading,
  className = '',
}) => {

  if (isLoading) {
    return (
      <div className={`flex flex-col items-center justify-center h-full ${className}`}>
        <Loading className="w-8 h-8" />
      </div>
    );
  }

  if (contacts.length === 0) {
    return (
      <div className={`flex flex-col items-center justify-center h-full px-2 ${className}`}>
        <Icon name="message-circle" size="md" className="text-gray-300 dark:text-gray-600" />
      </div>
    );
  }

  return (
    <div className={`overflow-y-auto py-2 flex flex-col items-center ${className}`}>
      {contacts.map((contact) => (
        <CollapsedContactItem
          key={contact.id}
          contact={contact}
          isSelected={contact.id === selectedContactId}
          onClick={onContactSelect}
        />
      ))}
    </div>
  );
};

export default CollapsedContactList;
