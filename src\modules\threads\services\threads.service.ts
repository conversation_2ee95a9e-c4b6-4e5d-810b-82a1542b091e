/**
 * Threads Service - Business logic layer
 * Xử lý logic nghiệp vụ cho threads management
 */

import { requestDeduplicationService } from '@/shared/chat-panel/services/request-deduplication.service';
import { apiClient } from '@/shared/api';
import {
  CreateThreadRequest,
  CreateThreadResponse,
  GetThreadsQuery,
  ThreadData,
  ThreadsResponse
} from '@/shared/types';
import {
  createThread as createThread<PERSON>pi,
  getThreadDetail as getThreadDetail<PERSON>pi,
  getThreads as getThreadsApi,
  updateThread as updateThreadApi,
  UpdateThreadRequest
} from '../api';

/**
 * Service class cho Threads operations
 */
export class ThreadsService {
  /**
   * Tạo thread mới với validation
   * Auto-truncate title to 255 characters for sendMessage integration
   */
  static async createThread(title: string): Promise<CreateThreadResponse> {
    // Validate và truncate title
    const cleanTitle = title.trim().substring(0, 255);

    if (!cleanTitle) {
      throw new Error('Thread title cannot be empty');
    }

    const request: CreateThreadRequest = {
      title: cleanTitle
    };

    const response = await createThreadApi(request);

    return response.result;
  }
  /**
   * Lấy danh sách threads với default parameters
   * ✅ PHASE 2: Uses request deduplication to prevent duplicate API calls
   */
  static async getThreads(query?: GetThreadsQuery): Promise<ThreadsResponse> {
    const defaultQuery: GetThreadsQuery = {
      page: 1,
      limit: 20,
      sortBy: 'updatedAt',
      sortDirection: 'DESC',
      ...query
    };

    // ✅ Use request deduplication to prevent duplicate calls
    return requestDeduplicationService.executeRequest(
      'GET',
      '/v1/user/chat/threads',
      async () => {
        const response = await getThreadsApi(defaultQuery);
        return response.result;
      },
      defaultQuery // Include query params in cache key
    );
  }

  /**
   * Lấy chi tiết thread
   * ✅ PHASE 2: Uses request deduplication to prevent duplicate API calls
   */
  static async getThreadDetail(threadId: string): Promise<ThreadData> {
    // ✅ Use request deduplication to prevent duplicate calls
    return requestDeduplicationService.executeRequest(
      'GET',
      `/v1/user/chat/threads/${threadId}`,
      async () => {
        const response = await getThreadDetailApi(threadId);
        return response.result;
      },
      { threadId } // Include threadId in cache key
    );
  }



  /**
   * Cập nhật thread title
   */
  static async updateThread(threadId: string, title: string): Promise<ThreadData> {
    // Validate và truncate title
    const cleanTitle = title.trim().substring(0, 255);

    if (!cleanTitle) {
      throw new Error('Thread title cannot be empty');
    }

    const request: UpdateThreadRequest = {
      title: cleanTitle
    };

    console.log('[ThreadsService] Updating thread:', threadId, 'with title:', cleanTitle);

    const response = await updateThreadApi(threadId, request);

    console.log('[ThreadsService] Thread updated successfully:', threadId);

    return response.result;
  }

  /**
   * Xóa thread - simplified to use API directly
   * Xóa thread - sử dụng API với body chứa threadIds
   */
  static async deleteThread(threadId: string): Promise<void> {
    console.log('[ThreadsService] Deleting thread:', threadId);

    await apiClient.delete('/user/chat/threads', {
      data: { threadIds: [threadId] }
    });

    console.log('[ThreadsService] Thread deleted successfully:', threadId);
  }

  /**
   * Xóa nhiều threads - sử dụng cùng API với body chứa array threadIds
   */
  static async deleteThreads(threadIds: string[]): Promise<void> {
    console.log('[ThreadsService] Deleting threads:', threadIds);

    await apiClient.delete('/user/chat/threads', {
      data: { threadIds }
    });

    console.log('[ThreadsService] Threads deleted successfully:', threadIds);
  }

  /**
   * Tìm kiếm threads theo tên - giờ server sẽ handle search
   * @deprecated Sử dụng getThreads với search parameter thay thế
   */
  static async searchThreads(searchTerm: string, query?: GetThreadsQuery): Promise<ThreadsResponse> {
    const searchQuery: GetThreadsQuery = {
      ...query,
      search: searchTerm
    };

    return this.getThreads(searchQuery);
  }
}
