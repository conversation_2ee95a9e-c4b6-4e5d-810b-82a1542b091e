/**
 * Zalo Chat API
 * API calls cho module chat Zalo
 */

import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import type {
  ZaloAccount,
  Contact,
  Message,
  Conversation,
  ContactSearchParams,
  MessageSearchParams,
  SendMessageRequest,
  ContactDto,
  ZaloAccountDto,
  ChatSettings,
} from '../types/zalo-chat.types';
import { ZALO_CHAT_ENDPOINTS } from '../constants/zalo-chat.constants';

/**
 * Zalo Accounts API
 */
export const zaloAccountsApi = {
  /**
   * Lấy danh sách tài khoản Zalo
   */
  getAccounts: async (): Promise<ApiResponseDto<ZaloAccount[]>> => {
    return apiClient.get<ZaloAccount[]>(ZALO_CHAT_ENDPOINTS.ACCOUNTS);
  },

  /**
   * Lấy chi tiết tài khoản <PERSON>alo
   */
  getAccount: async (id: string): Promise<ApiResponseDto<ZaloAccount>> => {
    return apiClient.get<ZaloAccount>(`${ZALO_CHAT_ENDPOINTS.ACCOUNTS}/${id}`);
  },

  /**
   * Tạo tài khoản Zalo mới
   */
  createAccount: async (data: ZaloAccountDto): Promise<ApiResponseDto<ZaloAccount>> => {
    return apiClient.post<ZaloAccount>(ZALO_CHAT_ENDPOINTS.ACCOUNTS, data);
  },

  /**
   * Cập nhật tài khoản Zalo
   */
  updateAccount: async (id: string, data: Partial<ZaloAccountDto>): Promise<ApiResponseDto<ZaloAccount>> => {
    return apiClient.put<ZaloAccount>(`${ZALO_CHAT_ENDPOINTS.ACCOUNTS}/${id}`, data);
  },

  /**
   * Xóa tài khoản Zalo
   */
  deleteAccount: async (id: string): Promise<ApiResponseDto<void>> => {
    return apiClient.delete<void>(`${ZALO_CHAT_ENDPOINTS.ACCOUNTS}/${id}`);
  },

  /**
   * Kết nối tài khoản Zalo
   */
  connectAccount: async (id: string, accessToken: string): Promise<ApiResponseDto<ZaloAccount>> => {
    return apiClient.post<ZaloAccount>(`${ZALO_CHAT_ENDPOINTS.ACCOUNTS}/${id}/connect`, { accessToken });
  },

  /**
   * Ngắt kết nối tài khoản Zalo
   */
  disconnectAccount: async (id: string): Promise<ApiResponseDto<void>> => {
    return apiClient.post<void>(`${ZALO_CHAT_ENDPOINTS.ACCOUNTS}/${id}/disconnect`);
  },
};

/**
 * Contacts API
 */
export const contactsApi = {
  /**
   * Lấy danh sách liên hệ
   */
  getContacts: async (params?: ContactSearchParams): Promise<ApiResponseDto<PaginatedResult<Contact>>> => {
    return apiClient.get<PaginatedResult<Contact>>(ZALO_CHAT_ENDPOINTS.CONTACTS, { params });
  },

  /**
   * Lấy chi tiết liên hệ
   */
  getContact: async (id: string): Promise<ApiResponseDto<Contact>> => {
    return apiClient.get<Contact>(`${ZALO_CHAT_ENDPOINTS.CONTACTS}/${id}`);
  },

  /**
   * Tạo liên hệ mới
   */
  createContact: async (data: ContactDto): Promise<ApiResponseDto<Contact>> => {
    return apiClient.post<Contact>(ZALO_CHAT_ENDPOINTS.CONTACTS, data);
  },

  /**
   * Cập nhật liên hệ
   */
  updateContact: async (id: string, data: Partial<ContactDto>): Promise<ApiResponseDto<Contact>> => {
    return apiClient.put<Contact>(`${ZALO_CHAT_ENDPOINTS.CONTACTS}/${id}`, data);
  },

  /**
   * Xóa liên hệ
   */
  deleteContact: async (id: string): Promise<ApiResponseDto<void>> => {
    return apiClient.delete<void>(`${ZALO_CHAT_ENDPOINTS.CONTACTS}/${id}`);
  },

  /**
   * Tìm kiếm liên hệ
   */
  searchContacts: async (query: string, accountId?: string): Promise<ApiResponseDto<Contact[]>> => {
    return apiClient.get<Contact[]>(`${ZALO_CHAT_ENDPOINTS.CONTACTS}/search`, {
      params: { query, accountId },
    });
  },

  /**
   * Lấy liên hệ gần đây
   */
  getRecentContacts: async (accountId: string, limit: number = 10): Promise<ApiResponseDto<Contact[]>> => {
    return apiClient.get<Contact[]>(`${ZALO_CHAT_ENDPOINTS.CONTACTS}/recent`, {
      params: { accountId, limit },
    });
  },

  /**
   * Thêm tag cho liên hệ
   */
  addTag: async (contactId: string, tag: string): Promise<ApiResponseDto<Contact>> => {
    return apiClient.post<Contact>(`${ZALO_CHAT_ENDPOINTS.CONTACTS}/${contactId}/tags`, { tag });
  },

  /**
   * Xóa tag khỏi liên hệ
   */
  removeTag: async (contactId: string, tag: string): Promise<ApiResponseDto<Contact>> => {
    return apiClient.delete<Contact>(`${ZALO_CHAT_ENDPOINTS.CONTACTS}/${contactId}/tags/${tag}`);
  },
};

/**
 * Conversations API
 */
export const conversationsApi = {
  /**
   * Lấy danh sách cuộc trò chuyện
   */
  getConversations: async (accountId: string): Promise<ApiResponseDto<Conversation[]>> => {
    return apiClient.get<Conversation[]>(ZALO_CHAT_ENDPOINTS.CONVERSATIONS, {
      params: { accountId },
    });
  },

  /**
   * Lấy chi tiết cuộc trò chuyện
   */
  getConversation: async (id: string): Promise<ApiResponseDto<Conversation>> => {
    return apiClient.get<Conversation>(`${ZALO_CHAT_ENDPOINTS.CONVERSATIONS}/${id}`);
  },

  /**
   * Tạo cuộc trò chuyện mới
   */
  createConversation: async (contactId: string, accountId: string): Promise<ApiResponseDto<Conversation>> => {
    return apiClient.post<Conversation>(ZALO_CHAT_ENDPOINTS.CONVERSATIONS, { contactId, accountId });
  },

  /**
   * Lưu trữ cuộc trò chuyện
   */
  archiveConversation: async (id: string): Promise<ApiResponseDto<Conversation>> => {
    return apiClient.post<Conversation>(`${ZALO_CHAT_ENDPOINTS.CONVERSATIONS}/${id}/archive`);
  },

  /**
   * Bỏ lưu trữ cuộc trò chuyện
   */
  unarchiveConversation: async (id: string): Promise<ApiResponseDto<Conversation>> => {
    return apiClient.post<Conversation>(`${ZALO_CHAT_ENDPOINTS.CONVERSATIONS}/${id}/unarchive`);
  },

  /**
   * Ghim cuộc trò chuyện
   */
  pinConversation: async (id: string): Promise<ApiResponseDto<Conversation>> => {
    return apiClient.post<Conversation>(`${ZALO_CHAT_ENDPOINTS.CONVERSATIONS}/${id}/pin`);
  },

  /**
   * Bỏ ghim cuộc trò chuyện
   */
  unpinConversation: async (id: string): Promise<ApiResponseDto<Conversation>> => {
    return apiClient.post<Conversation>(`${ZALO_CHAT_ENDPOINTS.CONVERSATIONS}/${id}/unpin`);
  },

  /**
   * Lấy số tin nhắn chưa đọc
   */
  getUnreadCount: async (accountId: string): Promise<ApiResponseDto<{ count: number }>> => {
    return apiClient.get<{ count: number }>(`${ZALO_CHAT_ENDPOINTS.CONVERSATIONS}/unread-count`, {
      params: { accountId },
    });
  },
};

/**
 * Messages API
 */
export const messagesApi = {
  /**
   * Lấy danh sách tin nhắn
   */
  getMessages: async (params: MessageSearchParams): Promise<ApiResponseDto<PaginatedResult<Message>>> => {
    return apiClient.get<PaginatedResult<Message>>(ZALO_CHAT_ENDPOINTS.MESSAGES, { params });
  },

  /**
   * Lấy chi tiết tin nhắn
   */
  getMessage: async (id: string): Promise<ApiResponseDto<Message>> => {
    return apiClient.get<Message>(`${ZALO_CHAT_ENDPOINTS.MESSAGES}/${id}`);
  },

  /**
   * Gửi tin nhắn
   */
  sendMessage: async (data: SendMessageRequest): Promise<ApiResponseDto<Message>> => {
    const formData = new FormData();
    formData.append('conversationId', data.conversationId);
    formData.append('type', data.type);
    formData.append('content', data.content);

    if (data.replyTo) {
      formData.append('replyTo', data.replyTo);
    }

    if (data.attachments) {
      data.attachments.forEach((file, index) => {
        formData.append(`attachments[${index}]`, file);
      });
    }

    return apiClient.post<Message>(ZALO_CHAT_ENDPOINTS.SEND_MESSAGE, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * Đánh dấu tin nhắn đã đọc
   */
  markAsRead: async (conversationId: string, messageIds: string[]): Promise<ApiResponseDto<void>> => {
    return apiClient.post<void>(ZALO_CHAT_ENDPOINTS.MARK_AS_READ, {
      conversationId,
      messageIds,
    });
  },

  /**
   * Xóa tin nhắn
   */
  deleteMessage: async (id: string): Promise<ApiResponseDto<void>> => {
    return apiClient.delete<void>(`${ZALO_CHAT_ENDPOINTS.MESSAGES}/${id}`);
  },

  /**
   * Tìm kiếm tin nhắn
   */
  searchMessages: async (conversationId: string, query: string): Promise<ApiResponseDto<Message[]>> => {
    return apiClient.get<Message[]>(`${ZALO_CHAT_ENDPOINTS.MESSAGES}/search`, {
      params: { conversationId, query },
    });
  },
};

/**
 * File Upload API
 */
export const uploadApi = {
  /**
   * Upload file đính kèm
   */
  uploadAttachment: async (file: File, onProgress?: (progress: number) => void): Promise<ApiResponseDto<{ url: string; name: string; size: number }>> => {
    const formData = new FormData();
    formData.append('file', file);

    return apiClient.post<{ url: string; name: string; size: number }>(ZALO_CHAT_ENDPOINTS.UPLOAD_ATTACHMENT, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent: any) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  },
};

/**
 * Settings API
 */
export const settingsApi = {
  /**
   * Lấy cài đặt chat
   */
  getChatSettings: async (): Promise<ApiResponseDto<ChatSettings>> => {
    return apiClient.get<ChatSettings>('/zalo/settings/chat');
  },

  /**
   * Cập nhật cài đặt chat
   */
  updateChatSettings: async (settings: Partial<ChatSettings>): Promise<ApiResponseDto<ChatSettings>> => {
    return apiClient.put<ChatSettings>('/zalo/settings/chat', settings);
  },
};
