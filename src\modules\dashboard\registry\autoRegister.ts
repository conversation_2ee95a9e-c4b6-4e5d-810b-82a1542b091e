import { widgetRegistry } from './WidgetRegistry';
import { WIDGET_CONFIGS } from './widgetConfigs';
import { type RegisterWidgetOptions, type WidgetType } from '../types';

/**
 * Auto-registration system cho widgets
 * Tự động đăng ký tất cả widgets từ config
 */
export class AutoRegister {
  private static isRegistered = false;
  private static registrationPromise: Promise<void> | null = null;

  /**
   * Đăng ký tất cả widgets từ config
   */
  static async registerAllWidgets(options: RegisterWidgetOptions = {}): Promise<void> {
    // Tránh đăng ký nhiều lần
    if (this.isRegistered) {
      console.log('✅ Widgets already registered');
      return;
    }

    // Nếu đang trong quá trình đăng ký, chờ hoàn thành
    if (this.registrationPromise) {
      return this.registrationPromise;
    }

    this.registrationPromise = this.performRegistration(options);
    return this.registrationPromise;
  }

  /**
   * Th<PERSON>c hiện đăng ký widgets
   */
  private static async performRegistration(options: RegisterWidgetOptions): Promise<void> {
    try {
      console.log('🚀 Starting widget auto-registration...');
      
      const startTime = Date.now();
      let successCount = 0;
      let errorCount = 0;

      // Đăng ký từng widget
      for (const config of WIDGET_CONFIGS) {
        try {
          console.log(`🔄 Registering widget: ${config.type} (${config.title})`);
          widgetRegistry.register(config, {
            lazy: false, // Đã sử dụng React.lazy() trong widgetConfigs rồi
            errorBoundary: true,
            ...options,
          });
          console.log(`✅ Successfully registered: ${config.type}`);
          successCount++;
        } catch (error) {
          console.error(`❌ Failed to register widget "${config.type}":`, error);
          console.error(`❌ Config:`, config);
          errorCount++;
        }
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Log kết quả
      console.log(`✅ Widget registration completed in ${duration}ms`);
      console.log(`📊 Results: ${successCount} success, ${errorCount} errors`);
      
      if (errorCount > 0) {
        console.warn(`⚠️ ${errorCount} widgets failed to register`);
      }

      // Lấy thống kê registry
      const stats = widgetRegistry.getStats();
      console.log('📈 Registry stats:', stats);

      this.isRegistered = true;
    } catch (error) {
      console.error('💥 Widget auto-registration failed:', error);
      throw error;
    } finally {
      this.registrationPromise = null;
    }
  }

  /**
   * Đăng ký widgets theo category
   */
  static async registerByCategory(
    category: string, 
    options: RegisterWidgetOptions = {}
  ): Promise<void> {
    const categoryWidgets = WIDGET_CONFIGS.filter(config => config.category === category);
    
    if (categoryWidgets.length === 0) {
      console.warn(`No widgets found for category: ${category}`);
      return;
    }

    console.log(`🎯 Registering ${categoryWidgets.length} widgets for category: ${category}`);

    for (const config of categoryWidgets) {
      try {
        widgetRegistry.register(config, {
          lazy: false, // Đã sử dụng React.lazy() trong widgetConfigs rồi
          errorBoundary: true,
          ...options,
        });
      } catch (error) {
        console.error(`❌ Failed to register widget "${config.type}":`, error);
      }
    }
  }

  /**
   * Đăng ký widgets theo danh sách types
   */
  static async registerByTypes(
    types: string[], 
    options: RegisterWidgetOptions = {}
  ): Promise<void> {
    const selectedWidgets = WIDGET_CONFIGS.filter(config => types.includes(config.type));
    
    if (selectedWidgets.length === 0) {
      console.warn(`No widgets found for types: ${types.join(', ')}`);
      return;
    }

    console.log(`🎯 Registering ${selectedWidgets.length} selected widgets`);

    for (const config of selectedWidgets) {
      try {
        widgetRegistry.register(config, {
          lazy: false, // Đã sử dụng React.lazy() trong widgetConfigs rồi
          errorBoundary: true,
          ...options,
        });
      } catch (error) {
        console.error(`❌ Failed to register widget "${config.type}":`, error);
      }
    }
  }

  /**
   * Preload widgets theo priority
   */
  static async preloadPriorityWidgets(): Promise<void> {
    // Danh sách widgets ưu tiên (thường được sử dụng)
    const priorityTypes = [
      'business-overview',
      'data-count',
      'marketing-overview',
    ];

    const availableTypes = priorityTypes.filter(type =>
      widgetRegistry.hasWidget(type as WidgetType)
    );

    if (availableTypes.length > 0) {
      console.log(`⚡ Preloading ${availableTypes.length} priority widgets...`);
      await widgetRegistry.preloadWidgets(availableTypes as WidgetType[]);
      console.log('✅ Priority widgets preloaded');
    }
  }

  /**
   * Reset registration state (for testing)
   */
  static reset(): void {
    this.isRegistered = false;
    this.registrationPromise = null;
    widgetRegistry.clear();
  }

  /**
   * Check registration status
   */
  static getRegistrationStatus(): {
    isRegistered: boolean;
    isRegistering: boolean;
    stats: ReturnType<typeof widgetRegistry.getStats>;
  } {
    return {
      isRegistered: this.isRegistered,
      isRegistering: this.registrationPromise !== null,
      stats: widgetRegistry.getStats(),
    };
  }
}

/**
 * Initialize widgets - gọi khi app khởi động
 */
export const initializeWidgets = async (options: RegisterWidgetOptions = {}): Promise<void> => {
  try {
    // Auto-register tất cả widgets
    await AutoRegister.registerAllWidgets(options);
    
    // Preload priority widgets
    await AutoRegister.preloadPriorityWidgets();
    
    console.log('🎉 Widget system initialized successfully');
  } catch (error) {
    console.error('💥 Widget system initialization failed:', error);
    throw error;
  }
};

export default AutoRegister;
