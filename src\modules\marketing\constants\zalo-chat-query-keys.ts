/**
 * Zalo Chat Query Keys
 * Query keys cho TanStack Query trong module chat Zalo
 */

import type { ContactSearchParams, MessageSearchParams } from '../types/zalo-chat.types';

/**
 * Base query keys
 */
const ZALO_CHAT_BASE = ['zalo-chat'] as const;

/**
 * Zalo Chat Query Keys
 */
export const ZALO_CHAT_QUERY_KEYS = {
  /**
   * Base key cho tất cả queries
   */
  ALL: ZALO_CHAT_BASE,

  /**
   * Zalo accounts queries
   */
  ACCOUNTS: {
    ALL: [...ZALO_CHAT_BASE, 'accounts'] as const,
    LIST: () => [...ZALO_CHAT_QUERY_KEYS.ACCOUNTS.ALL, 'list'] as const,
    DETAIL: (id: string) => [...ZALO_CHAT_QUERY_KEYS.ACCOUNTS.ALL, 'detail', id] as const,
    ACTIVE: () => [...ZALO_CHAT_QUERY_KEYS.ACCOUNTS.ALL, 'active'] as const,
  },

  /**
   * Contacts queries
   */
  CONTACTS: {
    ALL: [...ZALO_CHAT_BASE, 'contacts'] as const,
    LIST: (params: ContactSearchParams = {}) => 
      [...ZALO_CHAT_QUERY_KEYS.CONTACTS.ALL, 'list', params] as const,
    DETAIL: (id: string) => 
      [...ZALO_CHAT_QUERY_KEYS.CONTACTS.ALL, 'detail', id] as const,
    BY_ACCOUNT: (accountId: string) => 
      [...ZALO_CHAT_QUERY_KEYS.CONTACTS.ALL, 'by-account', accountId] as const,
    SEARCH: (query: string, accountId?: string) => 
      [...ZALO_CHAT_QUERY_KEYS.CONTACTS.ALL, 'search', { query, accountId }] as const,
    RECENT: (accountId: string, limit: number = 10) => 
      [...ZALO_CHAT_QUERY_KEYS.CONTACTS.ALL, 'recent', accountId, limit] as const,
  },

  /**
   * Conversations queries
   */
  CONVERSATIONS: {
    ALL: [...ZALO_CHAT_BASE, 'conversations'] as const,
    LIST: (accountId: string) => 
      [...ZALO_CHAT_QUERY_KEYS.CONVERSATIONS.ALL, 'list', accountId] as const,
    DETAIL: (id: string) => 
      [...ZALO_CHAT_QUERY_KEYS.CONVERSATIONS.ALL, 'detail', id] as const,
    BY_CONTACT: (contactId: string) => 
      [...ZALO_CHAT_QUERY_KEYS.CONVERSATIONS.ALL, 'by-contact', contactId] as const,
    UNREAD_COUNT: (accountId: string) => 
      [...ZALO_CHAT_QUERY_KEYS.CONVERSATIONS.ALL, 'unread-count', accountId] as const,
  },

  /**
   * Messages queries
   */
  MESSAGES: {
    ALL: [...ZALO_CHAT_BASE, 'messages'] as const,
    LIST: (params: MessageSearchParams) => 
      [...ZALO_CHAT_QUERY_KEYS.MESSAGES.ALL, 'list', params] as const,
    BY_CONVERSATION: (conversationId: string, page: number = 1, limit: number = 50) => 
      [...ZALO_CHAT_QUERY_KEYS.MESSAGES.ALL, 'by-conversation', conversationId, page, limit] as const,
    DETAIL: (id: string) => 
      [...ZALO_CHAT_QUERY_KEYS.MESSAGES.ALL, 'detail', id] as const,
    SEARCH: (conversationId: string, query: string) => 
      [...ZALO_CHAT_QUERY_KEYS.MESSAGES.ALL, 'search', conversationId, query] as const,
  },

  /**
   * Chat state queries
   */
  CHAT_STATE: {
    ALL: [...ZALO_CHAT_BASE, 'chat-state'] as const,
    CURRENT: () => [...ZALO_CHAT_QUERY_KEYS.CHAT_STATE.ALL, 'current'] as const,
    TYPING: (conversationId: string) => 
      [...ZALO_CHAT_QUERY_KEYS.CHAT_STATE.ALL, 'typing', conversationId] as const,
    ONLINE_STATUS: (contactId: string) => 
      [...ZALO_CHAT_QUERY_KEYS.CHAT_STATE.ALL, 'online-status', contactId] as const,
  },

  /**
   * Settings queries
   */
  SETTINGS: {
    ALL: [...ZALO_CHAT_BASE, 'settings'] as const,
    CHAT: () => [...ZALO_CHAT_QUERY_KEYS.SETTINGS.ALL, 'chat'] as const,
    NOTIFICATIONS: () => [...ZALO_CHAT_QUERY_KEYS.SETTINGS.ALL, 'notifications'] as const,
  },

  /**
   * Statistics queries
   */
  STATISTICS: {
    ALL: [...ZALO_CHAT_BASE, 'statistics'] as const,
    OVERVIEW: (accountId: string, dateRange?: { from: string; to: string }) => 
      [...ZALO_CHAT_QUERY_KEYS.STATISTICS.ALL, 'overview', accountId, dateRange] as const,
    MESSAGE_COUNT: (accountId: string, period: 'day' | 'week' | 'month' = 'day') => 
      [...ZALO_CHAT_QUERY_KEYS.STATISTICS.ALL, 'message-count', accountId, period] as const,
    RESPONSE_TIME: (accountId: string, dateRange?: { from: string; to: string }) => 
      [...ZALO_CHAT_QUERY_KEYS.STATISTICS.ALL, 'response-time', accountId, dateRange] as const,
  },

  /**
   * File upload queries
   */
  UPLOADS: {
    ALL: [...ZALO_CHAT_BASE, 'uploads'] as const,
    PROGRESS: (uploadId: string) => 
      [...ZALO_CHAT_QUERY_KEYS.UPLOADS.ALL, 'progress', uploadId] as const,
    HISTORY: (conversationId: string, type?: string) => 
      [...ZALO_CHAT_QUERY_KEYS.UPLOADS.ALL, 'history', conversationId, type] as const,
  },

  /**
   * Tags queries
   */
  TAGS: {
    ALL: [...ZALO_CHAT_BASE, 'tags'] as const,
    LIST: () => [...ZALO_CHAT_QUERY_KEYS.TAGS.ALL, 'list'] as const,
    BY_CONTACT: (contactId: string) => 
      [...ZALO_CHAT_QUERY_KEYS.TAGS.ALL, 'by-contact', contactId] as const,
    POPULAR: (limit: number = 10) => 
      [...ZALO_CHAT_QUERY_KEYS.TAGS.ALL, 'popular', limit] as const,
  },
} as const;

/**
 * Mutation keys cho các thao tác thay đổi dữ liệu
 */
export const ZALO_CHAT_MUTATION_KEYS = {
  /**
   * Message mutations
   */
  SEND_MESSAGE: 'send-message',
  EDIT_MESSAGE: 'edit-message',
  DELETE_MESSAGE: 'delete-message',
  MARK_AS_READ: 'mark-as-read',

  /**
   * Contact mutations
   */
  CREATE_CONTACT: 'create-contact',
  UPDATE_CONTACT: 'update-contact',
  DELETE_CONTACT: 'delete-contact',
  ADD_TAG: 'add-tag',
  REMOVE_TAG: 'remove-tag',

  /**
   * Account mutations
   */
  CONNECT_ACCOUNT: 'connect-account',
  DISCONNECT_ACCOUNT: 'disconnect-account',
  UPDATE_ACCOUNT: 'update-account',

  /**
   * Conversation mutations
   */
  CREATE_CONVERSATION: 'create-conversation',
  ARCHIVE_CONVERSATION: 'archive-conversation',
  PIN_CONVERSATION: 'pin-conversation',

  /**
   * File upload mutations
   */
  UPLOAD_FILE: 'upload-file',
  DELETE_FILE: 'delete-file',

  /**
   * Settings mutations
   */
  UPDATE_SETTINGS: 'update-settings',
} as const;

/**
 * Helper functions để tạo query keys
 */
export const createZaloChatQueryKey = {
  /**
   * Tạo key cho danh sách contacts với filter
   */
  contactsList: (accountId?: string, filters?: Partial<ContactSearchParams>) => {
    const params: ContactSearchParams = { zaloAccountId: accountId, ...filters };
    return ZALO_CHAT_QUERY_KEYS.CONTACTS.LIST(params);
  },

  /**
   * Tạo key cho danh sách messages với pagination
   */
  messagesList: (conversationId: string, page?: number, limit?: number) => {
    const params: MessageSearchParams = { conversationId, page, limit };
    return ZALO_CHAT_QUERY_KEYS.MESSAGES.LIST(params);
  },

  /**
   * Tạo key cho statistics với date range
   */
  statisticsOverview: (accountId: string, from?: string, to?: string) => {
    const dateRange = from && to ? { from, to } : undefined;
    return ZALO_CHAT_QUERY_KEYS.STATISTICS.OVERVIEW(accountId, dateRange);
  },
} as const;

/**
 * Query key patterns cho invalidation
 */
export const ZALO_CHAT_INVALIDATION_PATTERNS = {
  /**
   * Invalidate tất cả queries liên quan đến account
   */
  ACCOUNT_RELATED: (accountId: string) => [
    ZALO_CHAT_QUERY_KEYS.CONTACTS.BY_ACCOUNT(accountId),
    ZALO_CHAT_QUERY_KEYS.CONVERSATIONS.LIST(accountId),
    ZALO_CHAT_QUERY_KEYS.STATISTICS.OVERVIEW(accountId),
  ],

  /**
   * Invalidate tất cả queries liên quan đến conversation
   */
  CONVERSATION_RELATED: (conversationId: string) => [
    ZALO_CHAT_QUERY_KEYS.MESSAGES.BY_CONVERSATION(conversationId),
    ZALO_CHAT_QUERY_KEYS.CONVERSATIONS.DETAIL(conversationId),
  ],

  /**
   * Invalidate tất cả queries liên quan đến contact
   */
  CONTACT_RELATED: (contactId: string) => [
    ZALO_CHAT_QUERY_KEYS.CONTACTS.DETAIL(contactId),
    ZALO_CHAT_QUERY_KEYS.CONVERSATIONS.BY_CONTACT(contactId),
    ZALO_CHAT_QUERY_KEYS.TAGS.BY_CONTACT(contactId),
  ],
} as const;
