/**
 * Chat Panel Hooks Types
 * Centralized type definitions for all chat panel hooks
 */

import type {
  StreamEvent,
  StreamEventType,
  MessageRequestDto,
  MessageResponseDto,
  QueryMessagesDto,
  ThreadMessageResponseDto,
  ToolCallInterrupt,
  Message,
  UnifiedMessage
} from '../types';
import type { ToolCallInterruptData } from '../../types/tool-call-interrupt.types';
import type { AuthType } from '@/shared/hooks/useAuthCommon';
import type { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import type {
  MarkdownStreamProps
} from '@/shared/components/common/MarkdownStream/types';

// ============================================================================
// SHARED TYPES
// ============================================================================

/**
 * Chat Configuration Interface
 */
export interface ChatConfig {
  agentId: string;
  authType: AuthType;
  getAuthToken: () => string | Promise<string>;
  alwaysApproveToolCall: boolean;
  apiBaseUrl: string;
  sseBaseUrl: string;
}

// ============================================================================
// CORE HOOK TYPES
// ============================================================================

/**
 * useChatSSE Hook Types
 */
export interface UseChatSSEConfig {
  threadId?: string;
  runId?: string;
  onDelta?: (delta: string, messageId: string) => void;
  onMarkdownReady?: (handler: (delta: string) => void) => void;
  callbacks?: any; // SSECallbacks from chat-sse.service.ts
  getAuthToken?: () => string | Promise<string>; // ✅ Thêm getAuthToken
}

export interface UseChatSSEReturn {
  // Connection Management
  connect: (threadId: string, runId: string) => Promise<void>;
  disconnect: () => void;
  isConnected: boolean;
  connectionStatus: {
    isConnected: boolean;
    threadId: string | null;
    runId: string | null;
    lastEventTime: number;
    timeSinceLastEvent: number;
    timeoutDuration: number;
  };
  
  // MarkdownStream Integration
  registerMarkdownHandler: (messageId: string, handler: (delta: string) => void) => void;
  unregisterMarkdownHandler: (messageId: string) => void;
  
  // Event Handling
  addEventListener: (eventType: StreamEventType, handler: (event: StreamEvent) => void) => void;
  removeEventListener: (eventType: StreamEventType) => void;
  
  // Error Handling
  error: string | null;
  clearError: () => void;
}

/**
 * useChatAPI Hook Types
 */
export interface UseChatAPIConfig {
  authType: AuthType;
  getAuthToken: () => string | Promise<string>;
  timeout?: number;
}

export interface UseChatAPIReturn {
  // Message Operations
  sendMessage: (threadId: string, request: MessageRequestDto) => Promise<MessageResponseDto>;
  getMessages: (threadId: string, query: QueryMessagesDto) => Promise<PaginatedResult<ThreadMessageResponseDto>>;

  // State
  isLoading: boolean;
  error: string | null;
  isOnline: boolean;
  isRetrying: boolean;
  retryAttempts: number;

  // Utils
  clearError: () => void;
  retryLastRequest: () => Promise<void>;
  retryFailedRequests: () => Promise<void>;
  resetRetries: () => void;
}

/**
 * useChatConfig Hook Types
 */
export interface UseChatConfigReturn {
  // Config State
  config: ChatConfig;
  
  // Config Operations
  updateConfig: (updates: Partial<ChatConfig>) => void;
  resetConfig: () => void;
  
  // Specific Setters
  setAgentId: (agentId: string) => void;
  setAuthType: (authType: AuthType) => void;
  
  // Utils
  getSerializableConfig: () => any;
  isConfigValid: () => boolean;
}

// ============================================================================
// FEATURE HOOK TYPES
// ============================================================================

/**
 * useChatMessages Hook Types
 */
export interface UseChatMessagesConfig {
  threadId?: string;
  pageSize?: number;
  autoLoad?: boolean;
  enableHistory?: boolean;
  timeout?: number;
  isNewChatMode?: boolean; // ✅ Flag to prevent auto-loading when in new chat mode
}

export interface UseChatMessagesReturn {
  // Messages State
  messages: Message[];
  historyMessages: Message[];
  isLoadingHistory: boolean;
  hasMoreHistory: boolean;
  totalHistoryItems: number;

  // Operations
  addMessage: (message: Message) => void;
  updateMessage: (messageId: string, updates: Partial<Message>) => void;
  removeMessage: (messageId: string) => void;
  removeMessagesAfter: (messageId: string) => void;
  loadMoreHistory: () => Promise<void>;

  // Utils
  getMessageById: (messageId: string) => Message | null;
  clearMessages: () => void;
  refreshHistory: () => Promise<void>;
  
  // State
  error: string | null;
  clearError: () => void;
}

/**
 * useChatThread Hook Types
 */
export interface UseChatThreadConfig {
  initialThreadId?: string;
  autoSwitch?: boolean;
}

export interface UseChatThreadReturn {
  // Thread State
  threadId: string | null;
  isThreadSwitching: boolean;
  
  // Thread Operations
  switchToThread: (threadId: string) => Promise<void>;
  getCurrentThreadId: () => string | null;
  createNewThread: () => Promise<string>;
  
  // Utils
  clearThread: () => void;
  
  // State
  error: string | null;
  clearError: () => void;
}

/**
 * useChatMarkdown Hook Types
 */
export interface UseChatMarkdownConfig {
  messageId: string;
  isStreaming?: boolean;
  theme?: string;
  enablePerformanceMonitoring?: boolean;
}

export interface UseChatMarkdownReturn {
  // MarkdownStream State
  content: string;
  isProcessing: boolean;
  connectionError: string | null;

  // Delta Processing
  processDelta: (delta: string) => void;
  clearElements: () => void;

  // SSE Integration (using any for flexibility with different SSE implementations)
  registerWithSSE: (sseService: any) => void;
  unregisterFromSSE: () => void;
  handleSSETextContent: (event: any) => void;

  // Component Props
  getMarkdownStreamProps: () => MarkdownStreamProps;

  // Performance
  metrics: {
    deltaProcessingTime: number;
    renderingTime: number;
    elementCount: number;
    totalDeltas: number;
  };
}

/**
 * useChatToolCalls Hook Types
 */
export interface UseChatToolCallsConfig {
  autoApprove?: boolean;
  autoDismissOnSuccess?: boolean;
}

export interface UseChatToolCallsReturn {
  // Tool Call State
  toolCallInterrupt: ToolCallInterrupt | null;
  isProcessingApproval: boolean;

  // Tool Call Operations
  approveToolCall: (decision: 'yes' | 'no') => Promise<void>; // ✅ Removed 'always' option as per requirement
  dismissToolCallInterrupt: () => void;

  // Settings
  alwaysApproveToolCall: boolean;
  setAlwaysApproveToolCall: (value: boolean) => void;

  // State
  error: string | null;
  clearError: () => void;
}

// ============================================================================
// INTEGRATION HOOK TYPES
// ============================================================================

/**
 * useChatPanelIntegration Hook Types
 */
export interface UseChatPanelIntegrationReturn {
  // Integration State
  isRegistered: boolean;
  
  // Integration Operations
  getChatStream: () => any | null;
  getFocusChatInput: () => (() => void) | null;
  registerChatPanel: (instance: { chatStream: any; focusChatInput: (() => void) | null }) => void;
  
  // Utils
  getIntegrationStatus: () => {
    hasChatStream: boolean;
    hasFocusFunction: boolean;
    isFullyIntegrated: boolean;
  };
}

/**
 * useChatContext Hook Types
 */
export interface UseChatContextReturn {
  // Context Info
  contextType: 'admin' | 'user' | 'unknown';
  isAdminContext: boolean;
  isUserContext: boolean;
  shouldShowChatPanel: boolean;

  // Chat Config
  chatConfig: {
    agentId: string;
    getAuthToken: () => Promise<string>;
  };
}

/**
 * useChatPanelState Hook Types
 */
export interface UseChatPanelStateReturn {
  // Panel State
  isChatPanelOpen: boolean;
  setIsChatPanelOpen: (isOpen: boolean) => void;
  toggleChatPanel: () => void;
  openChatPanel: () => void;
  closeChatPanel: () => void;
  resetToDefault: () => void;
  
  // Hydration
  isHydrated: boolean;
}

// ============================================================================
// COMPOSITE HOOK TYPES
// ============================================================================

/**
 * useChatPanel Hook Types - Main Composite Hook
 */
export interface UseChatPanelConfig {
  // Core Config
  agentId?: string;
  authType?: AuthType;
  getAuthToken?: () => string | Promise<string>;
  
  // Features
  enableMarkdownStreaming?: boolean;
  enableThreadIntegration?: boolean;
  enableToolCallApproval?: boolean;
  
  // Message History
  messageHistory?: {
    pageSize?: number;
    autoLoad?: boolean;
    timeout?: number;
  };
  
  // Callbacks
  onRPointUpdate?: (points: number) => void;
  onThreadCreated?: (threadId: string, threadName: string) => void;
  onThreadSwitched?: (fromThreadId: string, toThreadId: string) => void;
}

export interface UseChatPanelReturn {
  // Message Management
  messages: Message[];
  unifiedMessages: UnifiedMessage[]; // ✅ Add unified messages
  sendMessage: (
    content: string | MessageRequestDto,
    fileMetadata?: Array<{
      fileId: string;
      name: string;
      viewUrl?: string;
      source?: string;
    }>
  ) => Promise<void>;
  updateMessage?: (messageId: string, content: string | MessageRequestDto) => Promise<void>;
  stopStreaming: () => Promise<void>;
  clearMessages: () => void;
  isLoading: boolean;
  isStreaming: boolean;

  // ✅ FIXED: Message History
  historyMessages: Message[];
  isLoadingHistory: boolean;
  hasMoreHistory: boolean;
  totalHistoryItems: number;
  loadMoreHistory: () => Promise<void>;
  historyError: string | null;

  // Streaming State
  currentStreamingMessageId: string | null;

  // Thread Management
  threadId: string | null;
  switchToThread: (threadId: string) => Promise<void>;
  getCurrentThreadId: () => string | null;
  clearCurrentThread: () => Promise<void>;

  // Thread Loading States
  isLoadingFirstThread: boolean;
  isSwitchingThread: boolean;
  isClearingThread: boolean;

  // Streaming & Markdown
  registerMarkdownHandler: (messageId: string, handler: (delta: string) => void) => void;
  getMarkdownProps: (messageId: string) => MarkdownStreamProps;
  
  // Tool Calls
  toolCallInterrupt: ToolCallInterruptData | null;
  approveToolCall: (decision: 'yes' | 'no') => Promise<void>; // ✅ Removed 'always' option as per requirement
  dismissToolCallInterrupt: () => void;

  // ✅ PHASE 3: Simplified streaming - removed complex state management

  // State & Config
  error: string | null;
  config: ChatConfig;

  // Network State
  isOnline: boolean;
  isRetrying: boolean;
  retryAttempts: number;

  // Integration
  focusChatInput: () => void;

  // Utils
  clearError: () => void;
  retryLastMessage: () => Promise<void>;
  retryFailedRequests: () => Promise<void>;
  resetRetries: () => void;
}
