/**
 * Dashboard Management API
 * API layer cho dashboard CRUD operations
 */

import { apiClient } from '../../../shared/api/axios';
import {
  CreateDashboardPageDto,
  UpdateDashboardPageDto,
  QueryDashboardPageDto,
  DashboardPageResponseDto,
  PageResult,
} from '../types/dashboard-api.types';

// ==================== BASE API ENDPOINTS ====================

const API_BASE = '/user/dashboard';

// ==================== DASHBOARD PAGE APIs ====================

/**
 * Lấy danh sách dashboard pages của user
 */
export const getDashboardPages = async (
  params?: QueryDashboardPageDto
): Promise<PageResult<DashboardPageResponseDto>> => {
  const response = await apiClient.get<PageResult<DashboardPageResponseDto>>(`${API_BASE}/pages`, {
    params,
  });
  return response.result;
};

/**
 * Lấy chi tiết dashboard page theo ID
 */
export const getDashboardPage = async (
  id: string,
  includeWidgets: boolean = false
): Promise<DashboardPageResponseDto> => {
  const response = await apiClient.get<DashboardPageResponseDto>(`${API_BASE}/pages/${id}`, {
    params: { includeWidgets },
  });
  return response.result;
};

/**
 * Tạo dashboard page mới
 */
export const createDashboardPage = async (
  data: CreateDashboardPageDto
): Promise<DashboardPageResponseDto> => {
  const response = await apiClient.post<DashboardPageResponseDto>(`${API_BASE}/pages`, data);
  return response.result;
};

/**
 * Cập nhật dashboard page
 */
export const updateDashboardPage = async (
  id: string,
  data: UpdateDashboardPageDto
): Promise<DashboardPageResponseDto> => {
  const response = await apiClient.put<DashboardPageResponseDto>(`${API_BASE}/pages/${id}`, data);
  return response.result;
};

/**
 * Xóa dashboard page
 */
export const deleteDashboardPage = async (id: string): Promise<{ success: boolean }> => {
  const response = await apiClient.delete<{ success: boolean }>(`${API_BASE}/pages/${id}`);
  return response.result;
};

/**
 * Đặt dashboard page làm mặc định
 */
export const setDefaultDashboardPage = async (id: string): Promise<DashboardPageResponseDto> => {
  const response = await apiClient.put<DashboardPageResponseDto>(
    `${API_BASE}/pages/${id}/set-default`
  );
  return response.result;
};

/**
 * Lấy dashboard page mặc định của user
 */
export const getDefaultDashboardPage = async (): Promise<DashboardPageResponseDto | null> => {
  try {
    const response = await apiClient.get<DashboardPageResponseDto>(`${API_BASE}/pages/default`);
    return response.result;
  } catch (error: any) {
    // Nếu không có dashboard mặc định, trả về null
    if (error?.statusCode === 404) {
      return null;
    }
    throw error;
  }
};

/**
 * Sao chép dashboard page
 */
export const duplicateDashboardPage = async (
  id: string,
  newName?: string
): Promise<DashboardPageResponseDto> => {
  const response = await apiClient.post<DashboardPageResponseDto>(
    `${API_BASE}/pages/${id}/duplicate`,
    { name: newName }
  );
  return response.result;
};

/**
 * Chia sẻ dashboard page
 */
export const shareDashboardPage = async (
  id: string,
  shareData: {
    users?: number[];
    employees?: number[];
    roles?: string[];
    accessLevel?: 'SHARED' | 'PUBLIC';
  }
): Promise<DashboardPageResponseDto> => {
  const response = await apiClient.put<DashboardPageResponseDto>(
    `${API_BASE}/pages/${id}/share`,
    shareData
  );
  return response.result;
};

/**
 * Hủy chia sẻ dashboard page
 */
export const unshareDashboardPage = async (id: string): Promise<DashboardPageResponseDto> => {
  const response = await apiClient.put<DashboardPageResponseDto>(`${API_BASE}/pages/${id}/unshare`);
  return response.result;
};

// ==================== DASHBOARD TEMPLATE APIs ====================

/**
 * Lấy danh sách system templates
 */
export const getSystemTemplates = async (
  targetAudience?: 'USER' | 'EMPLOYEE'
): Promise<DashboardPageResponseDto[]> => {
  const response = await apiClient.get<DashboardPageResponseDto[]>(`${API_BASE}/templates/system`, {
    params: { targetAudience },
  });
  return response.result;
};

/**
 * Lấy danh sách user templates (public)
 */
export const getUserTemplates = async (): Promise<DashboardPageResponseDto[]> => {
  const response = await apiClient.get<DashboardPageResponseDto[]>(`${API_BASE}/templates/user`);
  return response.result;
};

/**
 * Tạo dashboard từ template
 */
export const createFromTemplate = async (
  templateId: string,
  name: string,
  description?: string
): Promise<DashboardPageResponseDto> => {
  const response = await apiClient.post<DashboardPageResponseDto>(
    `${API_BASE}/pages/from-template/${templateId}`,
    { name, description }
  );
  return response.result;
};

/**
 * Chuyển dashboard thành template
 */
export const convertToTemplate = async (
  id: string,
  templateData: {
    name: string;
    description?: string;
    category?: string;
    tags?: string[];
    isPublic?: boolean;
  }
): Promise<DashboardPageResponseDto> => {
  const response = await apiClient.post<DashboardPageResponseDto>(
    `${API_BASE}/pages/${id}/convert-to-template`,
    templateData
  );
  return response.result;
};

// ==================== DASHBOARD STATISTICS APIs ====================

/**
 * Lấy thống kê dashboard của user
 */
export const getDashboardStatistics = async (): Promise<{
  totalPages: number;
  totalWidgets: number;
  pagesByType: Record<string, number>;
  widgetsByType: Record<string, number>;
  lastAccessedAt?: Date;
}> => {
  const response = await apiClient.get<{
    totalPages: number;
    totalWidgets: number;
    pagesByType: Record<string, number>;
    widgetsByType: Record<string, number>;
    lastAccessedAt?: Date;
  }>(`${API_BASE}/statistics`);
  return response.result;
};

// ==================== ERROR HANDLING ====================

export class DashboardApiException extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public endpoint?: string,
    public details?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'DashboardApiException';
  }
}

/**
 * Wrapper function với error handling
 */
export const withDashboardErrorHandling = async <T>(
  apiCall: () => Promise<T>,
  endpoint: string
): Promise<T> => {
  try {
    return await apiCall();
  } catch (error: any) {
    console.error(`Dashboard API Error [${endpoint}]:`, error);

    throw new DashboardApiException(
      error?.message || 'Có lỗi xảy ra khi gọi API dashboard',
      error?.statusCode || 500,
      endpoint,
      error?.details
    );
  }
};
