import React, { Suspense } from 'react';
import { Skeleton, Card, Typography } from '@/shared/components/common';
import { widgetRegistry } from '../registry/WidgetRegistry';
import { type DashboardWidget, type WidgetFactoryOptions } from '../types';
import { type WidgetType } from '../constants';

/**
 * Props cho WidgetErrorBoundary
 */
interface WidgetErrorBoundaryProps {
  children: React.ReactNode;
  widgetType: WidgetType;
  onError?: (error: Error) => void;
}

/**
 * Error Boundary cho widgets
 */
class WidgetErrorBoundary extends React.Component<
  WidgetErrorBoundaryProps,
  { hasError: boolean; error?: Error }
> {
  constructor(props: WidgetErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  override componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error(`Widget "${this.props.widgetType}" error:`, error, errorInfo);
    this.props.onError?.(error);
  }

  override render() {
    if (this.state.hasError) {
      return (
        <Card className="p-4 border-destructive/20 bg-destructive/5">
          <div className="text-center">
            <Typography variant="body2" className="text-destructive mb-2">
              Widget Error
            </Typography>
            <Typography variant="caption" className="text-muted-foreground">
              {this.state.error?.message || 'Unknown error occurred'}
            </Typography>
          </div>
        </Card>
      );
    }

    return this.props.children;
  }
}

/**
 * Loading fallback cho Suspense
 */
const WidgetLoadingFallback: React.FC<{ widgetType: WidgetType }> = () => (
  <Card className="p-4">
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-4 w-4" />
      </div>
      <Skeleton className="h-8 w-16" />
      <Skeleton className="h-3 w-20" />
    </div>
  </Card>
);

/**
 * Widget Factory - Tạo và render widgets
 */
export class WidgetFactory {
  /**
   * Tạo widget component từ config
   */
  static createWidget(
    widget: DashboardWidget,
    options: WidgetFactoryOptions = {}
  ): React.ReactElement | null {
    const { errorBoundary = true, displayMode = 'normal', onPropsChange, widgetId } = options;

    // Lấy config từ registry
    const config = widgetRegistry.getConfig(widget.type);
    if (!config) {
      return this.createNotFoundWidget(widget.type);
    }

    // Lấy component
    const Component = widgetRegistry.getComponent(widget.type);
    if (!Component) {
      return this.createNotFoundWidget(widget.type);
    }

    // Tạo widget element
    const widgetElement = (
      <Suspense fallback={<WidgetLoadingFallback widgetType={widget.type} />}>
        <Component
          {...(widget.props || {})}
          className={widget.config?.id ? `widget-${widget.config.id}` : undefined}
          isLoading={widget.isEmpty}
          displayMode={displayMode}
          onPropsChange={onPropsChange && widgetId ? (newProps: Record<string, unknown>) => onPropsChange(widgetId, newProps) : undefined}
        />
      </Suspense>
    );

    // Wrap với Error Boundary nếu cần
    if (errorBoundary) {
      return (
        <WidgetErrorBoundary
          widgetType={widget.type}
        >
          {widgetElement}
        </WidgetErrorBoundary>
      );
    }

    return widgetElement;
  }

  /**
   * Tạo widget từ type và props
   */
  static createWidgetFromType(
    type: WidgetType,
    props: Record<string, unknown> = {},
    options: WidgetFactoryOptions = {}
  ): React.ReactElement | null {
    const config = widgetRegistry.getConfig(type);
    if (!config) {
      return this.createNotFoundWidget(type);
    }

    const widget: DashboardWidget = {
      id: `widget-${type}-${Date.now()}`,
      title: config.title,
      type,
      x: 0,
      y: 0,
      w: config.defaultSize.w,
      h: config.defaultSize.h,
      minW: config.defaultSize.minW,
      minH: config.defaultSize.minH,
      maxW: config.defaultSize.maxW,
      maxH: config.defaultSize.maxH,
      config,
      ...props,
    };

    return this.createWidget(widget, options);
  }

  /**
   * Tạo widget "Not Found"
   */
  private static createNotFoundWidget(type: WidgetType): React.ReactElement {
    return (
      <Card className="p-4 border-warning/20 bg-warning/5">
        <div className="text-center">
          <Typography variant="body2" className="text-warning mb-2">
            Widget Not Found
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            Widget type "{type}" is not registered
          </Typography>
        </div>
      </Card>
    );
  }

  /**
   * Batch create widgets
   */
  static createWidgets(
    widgets: DashboardWidget[],
    options: WidgetFactoryOptions = {}
  ): (React.ReactElement | null)[] {
    return widgets.map(widget => this.createWidget(widget, options));
  }

  /**
   * Preload widgets
   */
  static async preloadWidgets(types: WidgetType[]): Promise<void> {
    await widgetRegistry.preloadWidgets(types);
  }

  /**
   * Check if widget type is available
   */
  static isWidgetAvailable(type: WidgetType): boolean {
    return widgetRegistry.hasWidget(type);
  }

  /**
   * Get available widget types
   */
  static getAvailableWidgets(): string[] {
    return Object.keys(widgetRegistry.getAllWidgets());
  }
}

export default WidgetFactory;
