import React from 'react';
import { type WidgetType, type WidgetCategory } from '../constants';
import { type LucideIcon } from 'lucide-react';

// Re-export types from constants
export type { WidgetType, WidgetCategory };

export interface DashboardData {
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  conversionRate: number;
}

export interface DashboardCard {
  id: string;
  title: string;
  value: string | number;
  change: number;
  changeType: 'increase' | 'decrease';
  icon: string;
}

export interface DashboardChart {
  id: string;
  title: string;
  type: 'line' | 'bar' | 'pie' | 'area';
  data: unknown[];
}

// New types for the analytics dashboard
export interface MenuItem {
  id: string;
  title: string;
  icon: string;
  path: string;
  children?: MenuItem[];
}

export interface MenuSection {
  id: string;
  title: string;
  items: MenuItem[];
}

// Base widget interfaces
export interface BaseWidgetProps extends Record<string, unknown> {
  className?: string;
  isLoading?: boolean;
  onPropsChange?: (newProps: Record<string, unknown>) => void;
}

// Props cho dashboard widgets với widget data và callbacks
export interface DashboardWidgetProps extends BaseWidgetProps {
  widget: DashboardWidget;
  onTitleChange?: (widgetId: string, newTitle: string) => void;
  onRemove?: (widgetId: string) => void;
  mode?: 'view' | 'edit';
  displayMode?: 'normal' | 'minimal' | 'ultra-minimal';
}

export interface WidgetConfig {
  id: string;
  type: WidgetType;
  category: WidgetCategory;
  title: string;
  description?: string;
  icon?: LucideIcon;
  defaultSize: {
    w: number;
    h: number;
    minW?: number;
    minH?: number;
    maxW?: number;
    maxH?: number;
  };
  component: React.ComponentType<BaseWidgetProps>;
  dependencies?: string[]; // API dependencies
  permissions?: string[]; // Required permissions
}

export interface DashboardWidget {
  id: string;
  title: string;
  type: WidgetType;
  x: number;
  y: number;
  w: number;
  h: number;
  minW?: number | undefined;
  minH?: number | undefined;
  maxW?: number | undefined;
  maxH?: number | undefined;
  content?: React.ReactNode;
  isEmpty?: boolean;
  config?: WidgetConfig;
  props?: Record<string, unknown>; // Additional props for widget components
}

export interface DashboardLayout {
  id: string;
  name: string;
  widgets: DashboardWidget[];
  isDefault?: boolean;
}

export interface DashboardState {
  selectedMenuItem: string | null;
  sidebarCollapsed: boolean;
  currentLayout: DashboardLayout | null;
  searchQuery: string;
  selectedView: string;
}

// New tab system types
export interface DashboardTab {
  id: string;
  name: string;
  widgets: DashboardWidget[];
  mode: 'view' | 'edit';
  createdAt: string;
  updatedAt: string;
}

export interface DashboardTabsState {
  currentTabId: string;
  tabs: DashboardTab[];
}

// Widget Registry types
export interface WidgetRegistry {
  [key: string]: WidgetConfig;
}

export interface WidgetFactoryOptions {
  lazy?: boolean;
  preload?: boolean;
  errorBoundary?: boolean;
  displayMode?: 'normal' | 'minimal' | 'ultra-minimal';
  onPropsChange?: (widgetId: string, newProps: Record<string, unknown>) => void;
  widgetId?: string;
}

export interface RegisterWidgetOptions extends WidgetFactoryOptions {
  override?: boolean;
}

// Widget lifecycle hooks
export interface WidgetLifecycle {
  onMount?: () => void | Promise<void>;
  onUnmount?: () => void | Promise<void>;
  onRefresh?: () => void | Promise<void>;
  onError?: (error: Error) => void;
}

// Export business analytics types
export * from './business-analytics.types';
