/* DoubleDatePicker specific styles */
.double-datepicker-container {
  display: flex;
  flex-direction: column;
  position: relative;
  min-width: 600px;
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.95);
}

.double-datepicker-left {
  position: relative;
  transition: all 0.2s ease-in-out;
}

.double-datepicker-left:hover {
  transform: translateY(-1px);
}

.double-datepicker-left > div {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-right: none !important;
  background: linear-gradient(135deg, hsl(var(--primary) / 0.02), hsl(var(--primary) / 0.05));
}

.double-datepicker-right {
  position: relative;
  transition: all 0.2s ease-in-out;
}

.double-datepicker-right:hover {
  transform: translateY(-1px);
}

.double-datepicker-right > div {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-left: none !important;
  background: linear-gradient(135deg, hsl(var(--secondary) / 0.02), hsl(var(--secondary) / 0.05));
}

/* Enhanced divider between calendars */
.double-datepicker-left::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom,
    hsl(var(--primary) / 0.3),
    hsl(var(--secondary) / 0.3),
    hsl(var(--primary) / 0.3)
  );
  z-index: 1;
  border-radius: 1px;
}

/* Remove any shadow between calendars */
.double-datepicker-left > div,
.double-datepicker-right > div {
  box-shadow: none !important;
}

/* Enhanced shadow and styling for the outer container */
.double-datepicker-container {
  box-shadow:
    0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 10px 10px -5px rgb(0 0 0 / 0.04),
    0 0 0 1px hsl(var(--primary) / 0.1);
  border-radius: 0.75rem;
  overflow: hidden;
  border: 2px solid transparent;
  background-clip: padding-box;
}

.double-datepicker-container:hover {
  box-shadow:
    0 25px 50px -12px rgb(0 0 0 / 0.15),
    0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 0 0 1px hsl(var(--primary) / 0.2);
  transform: translateY(-2px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark mode adjustments */
.dark .double-datepicker-container {
  background: rgba(0, 0, 0, 0.95);
  box-shadow:
    0 20px 25px -5px rgb(0 0 0 / 0.4),
    0 10px 10px -5px rgb(0 0 0 / 0.2),
    0 0 0 1px hsl(var(--primary) / 0.2);
}

.dark .double-datepicker-container:hover {
  box-shadow:
    0 25px 50px -12px rgb(0 0 0 / 0.6),
    0 20px 25px -5px rgb(0 0 0 / 0.3),
    0 0 0 1px hsl(var(--primary) / 0.3);
}

.dark .double-datepicker-left > div {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.05), hsl(var(--primary) / 0.1));
}

.dark .double-datepicker-right > div {
  background: linear-gradient(135deg, hsl(var(--secondary) / 0.05), hsl(var(--secondary) / 0.1));
}

/* Animation for dropdown appearance */
.datepicker-dropdown {
  animation: slideInFromTop 0.2s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .double-datepicker-container {
    min-width: 320px;
    flex-direction: column;
  }

  .double-datepicker-left::after {
    display: none;
  }

  .double-datepicker-left > div,
  .double-datepicker-right > div {
    border-radius: 0 !important;
  }
}
