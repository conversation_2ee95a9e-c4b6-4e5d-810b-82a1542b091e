/**
 * Utility để kiểm tra trạng thái các widget
 * Kiểm tra widget nào đã implement và widget nào chưa có
 */

import { WIDGET_TYPES, WIDGET_CATEGORIES } from '../constants/widget-types';
import { WIDGET_CONFIGS } from '../registry/widgetConfigs';
import { widgetRegistry } from '../registry/WidgetRegistry';

export interface WidgetStatus {
  type: string;
  displayName: string;
  category: string;
  hasConfig: boolean;
  hasComponent: boolean;
  isRegistered: boolean;
  componentPath?: string;
  status: 'available' | 'missing' | 'error';
}

/**
 * Kiểm tra trạng thái tất cả widgets
 */
export const checkAllWidgetStatus = (): {
  available: WidgetStatus[];
  missing: WidgetStatus[];
  total: number;
  summary: Record<string, number>;
} => {
  const allWidgetTypes = Object.values(WIDGET_TYPES);
  const statuses: WidgetStatus[] = [];

  // Component paths mapping
  const componentPaths: Record<string, string> = {
    'data-count': 'src/modules/dashboard/components/data/DataCountWidget.tsx',
    'data-storage': 'src/modules/dashboard/components/data/DataStorageWidget.tsx',
    'business-overview': 'src/modules/dashboard/components/business/BusinessOverviewWidget.tsx',
    'customer-products': 'src/modules/dashboard/components/business/CustomerProductsWidget.tsx',
    'customer-list': 'src/modules/dashboard/components/business/CustomerListWidget.tsx',
    'bank-account-overview': 'src/modules/dashboard/components/business/BankAccountOverviewWidget.tsx',
    'marketing-overview': 'src/modules/dashboard/components/marketing/MarketingOverviewWidget.tsx',
    'campaign-performance': 'src/modules/dashboard/components/marketing/CampaignPerformanceWidget.tsx',
    'agent-overview': 'src/modules/dashboard/components/ai-agents/AgentOverviewWidget.tsx',
    'agent-performance': 'src/modules/dashboard/components/ai-agents/AgentPerformanceWidget.tsx',
    'affiliate-overview': 'src/modules/dashboard/components/affiliate/AffiliateOverviewWidget.tsx',
    'integration-overview': 'src/modules/dashboard/components/integration/IntegrationOverviewWidget.tsx',
  };

  allWidgetTypes.forEach(type => {
    const config = WIDGET_CONFIGS.find(c => c.type === type);
    const isRegistered = widgetRegistry.hasWidget(type);
    const hasComponent = !!componentPaths[type];

    let status: 'available' | 'missing' | 'error' = 'missing';
    if (config && hasComponent && isRegistered) {
      status = 'available';
    } else if (config && hasComponent) {
      status = 'error'; // Có config và component nhưng chưa register
    }

    const widgetStatus: WidgetStatus = {
      type,
      displayName: getWidgetDisplayName(type),
      category: getCategoryForType(type),
      hasConfig: !!config,
      hasComponent,
      isRegistered,
      componentPath: componentPaths[type],
      status,
    };

    statuses.push(widgetStatus);
  });

  const available = statuses.filter(s => s.status === 'available');
  const missing = statuses.filter(s => s.status === 'missing' || s.status === 'error');

  // Summary by category
  const summary: Record<string, number> = {};
  Object.values(WIDGET_CATEGORIES).forEach(category => {
    summary[category] = statuses.filter(s => s.category === category && s.status === 'available').length;
  });

  return {
    available,
    missing,
    total: statuses.length,
    summary,
  };
};

/**
 * Get display name for widget type
 */
const getWidgetDisplayName = (type: string): string => {
  const displayNames: Record<string, string> = {
    'data-count': 'Tổng số lượng dữ liệu',
    'data-storage': 'Dung lượng dữ liệu',
    'chart': 'Biểu đồ',
    'metric': 'Chỉ số',
    'table': 'Bảng dữ liệu',
    'custom': 'Tùy chỉnh',
    'marketing-overview': 'Tổng quan Marketing',
    'campaign-performance': 'Hiệu suất chiến dịch',
    'agent-overview': 'Tổng quan AI Agents',
    'agent-performance': 'Hiệu suất Agents',
    'business-overview': 'Tổng quan kinh doanh',
    'affiliate-overview': 'Tổng quan Affiliate',
    'integration-overview': 'Tổng quan tích hợp',
  };

  return displayNames[type] || 'Không xác định';
};

/**
 * Get category for widget type
 */
const getCategoryForType = (type: string): string => {
  const categoryMap: Record<string, string> = {
    'data-count': WIDGET_CATEGORIES.DATA,
    'data-storage': WIDGET_CATEGORIES.DATA,
    'chart': WIDGET_CATEGORIES.VISUALIZATION,
    'metric': WIDGET_CATEGORIES.VISUALIZATION,
    'table': WIDGET_CATEGORIES.VISUALIZATION,
    'marketing-overview': WIDGET_CATEGORIES.MARKETING,
    'campaign-performance': WIDGET_CATEGORIES.MARKETING,
    'agent-overview': WIDGET_CATEGORIES.AI_AGENTS,
    'agent-performance': WIDGET_CATEGORIES.AI_AGENTS,
    'business-overview': WIDGET_CATEGORIES.BUSINESS,
    'affiliate-overview': WIDGET_CATEGORIES.AFFILIATE,
    'integration-overview': WIDGET_CATEGORIES.INTEGRATION,
  };

  return categoryMap[type] || WIDGET_CATEGORIES.DATA;
};

/**
 * Print widget status to console
 */
export const printWidgetStatus = (): void => {
  const status = checkAllWidgetStatus();
  
  console.log('\n🔍 WIDGET STATUS REPORT');
  console.log('========================');
  console.log(`📊 Total widgets: ${status.total}`);
  console.log(`✅ Available: ${status.available.length}`);
  console.log(`❌ Missing: ${status.missing.length}`);
  
  console.log('\n📈 BY CATEGORY:');
  Object.entries(status.summary).forEach(([category, count]) => {
    const total = Object.values(WIDGET_TYPES).filter(type => getCategoryForType(type) === category).length;
    console.log(`  ${category}: ${count}/${total}`);
  });

  console.log('\n✅ AVAILABLE WIDGETS:');
  status.available.forEach(widget => {
    console.log(`  ✓ ${widget.displayName} (${widget.type})`);
  });

  console.log('\n❌ MISSING WIDGETS:');
  status.missing.forEach(widget => {
    const reason = !widget.hasComponent ? 'No component' : !widget.hasConfig ? 'No config' : 'Not registered';
    console.log(`  ✗ ${widget.displayName} (${widget.type}) - ${reason}`);
  });
};

export default checkAllWidgetStatus;
