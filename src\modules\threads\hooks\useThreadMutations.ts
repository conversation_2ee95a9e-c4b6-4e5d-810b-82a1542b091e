/**
 * React Query mutation hooks cho Threads operations
 */

import { useMutation, useQueryClient, UseMutationOptions } from '@tanstack/react-query';
import { THREADS_QUERY_KEYS } from '../constants';
import { ThreadsService } from '../services';
import { ThreadData } from '@/shared/types';

/**
 * Hook để update thread với optimistic updates
 * Tạm thời mock implementation vì chưa có API updateThread
 */
export const useOptimisticUpdateThread = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async ({ threadId, newTitle }: { threadId: string; newTitle: string }) => {
      // Use actual API call
      return await ThreadsService.updateThread(threadId, newTitle);
    },
    onMutate: async ({ threadId, newTitle }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: THREADS_QUERY_KEYS.DETAIL(threadId) });
      await queryClient.cancelQueries({ queryKey: THREADS_QUERY_KEYS.ALL });

      // Snapshot the previous values
      const previousThread = queryClient.getQueryData<ThreadData>(THREADS_QUERY_KEYS.DETAIL(threadId));
      const previousThreadsList = queryClient.getQueryData(THREADS_QUERY_KEYS.ALL);

      // Optimistically update thread detail
      if (previousThread) {
        queryClient.setQueryData<ThreadData>(THREADS_QUERY_KEYS.DETAIL(threadId), {
          ...previousThread,
          title: newTitle,
        });
      }

      // Optimistically update threads list
      queryClient.setQueriesData({ queryKey: THREADS_QUERY_KEYS.ALL }, (old: any) => {
        if (!old?.items) return old;

        return {
          ...old,
          items: old.items.map((thread: ThreadData) =>
            thread.id === threadId ? { ...thread, title: newTitle } : thread
          ),
        };
      });

      // Return context for rollback
      return { previousThread, previousThreadsList, threadId };
    },
    onError: (_, __, context) => {
      // Rollback optimistic updates on error
      if (context?.previousThread) {
        queryClient.setQueryData(THREADS_QUERY_KEYS.DETAIL(context.threadId), context.previousThread);
      }
      if (context?.previousThreadsList) {
        queryClient.setQueryData(THREADS_QUERY_KEYS.ALL, context.previousThreadsList);
      }
    },
    onSettled: (_, __, { threadId }) => {
      // Refetch to ensure we have the latest data
      queryClient.invalidateQueries({ queryKey: THREADS_QUERY_KEYS.DETAIL(threadId) });
      queryClient.invalidateQueries({ queryKey: THREADS_QUERY_KEYS.ALL });
    },
  });

  return {
    optimisticUpdate: (threadId: string, newTitle: string) => mutation.mutate({ threadId, newTitle }),
    optimisticUpdateAsync: (threadId: string, newTitle: string) => mutation.mutateAsync({ threadId, newTitle }),
    isLoading: mutation.isPending,
    error: mutation.error,
    reset: mutation.reset,
  };
};

/**
 * Hook để xóa thread
 */
export const useDeleteThread = (
  options?: UseMutationOptions<void, Error, string>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (threadId: string) => ThreadsService.deleteThread(threadId),
    onSuccess: (_, threadId) => {
      // Invalidate và refetch threads list
      queryClient.invalidateQueries({ queryKey: THREADS_QUERY_KEYS.ALL });

      // Remove thread detail từ cache
      queryClient.removeQueries({ queryKey: THREADS_QUERY_KEYS.DETAIL(threadId) });
    },
    ...options,
  });
};


