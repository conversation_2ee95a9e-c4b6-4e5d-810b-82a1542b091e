// Core types and constants
export * from './types';
export * from './types/dashboard-api.types';
export * from './constants';
export * from './constants/query-keys';

// API and Services
export { DashboardManagementService } from './services/dashboard-management.service';

// Hooks and services
export * from './hooks';
export {
  useDashboardPages,
  useDashboardPage,
  useDefaultDashboardPage,
  useDashboardWithFallback,
  useCreateDashboard,
  useUpdateDashboard,
  useDeleteDashboard,
  useSetDefaultDashboard,
  useSyncDashboard,
  useMigrateDashboard,
  useRefreshDashboardManagement,
  useClearDashboardCache,
  useDashboardBackups,
} from './hooks/useDashboardManagement';

// Components (excluding conflicting exports)
export { default as DashboardCard, WidgetCard } from './components/DashboardCard';
export { renderWidgetContent } from './components/DashboardCard.utils';
export { default as DashboardSidebar } from './components/DashboardSidebar';
export { default as EmptyDashboard } from './components/EmptyDashboard';
export { default as DashboardTabs } from './components/DashboardTabs';
export * from './components/data';

// Pages
export * from './pages';

// Routes
export { default as dashboardRoutes } from './routers/dashboardRoutes';

// Widget system - New pattern
export * from './registry';
export { WidgetFactory } from './factory/WidgetFactory';
// Export utils with explicit naming to avoid conflicts
export { WidgetUtils, widgetUtils } from './utils';
export { debugWidgets } from './utils/debugWidgets';
export {
  cleanupDashboardTabs,
  debugDashboardWidgets,
  resetDashboard,
  getInvalidWidgetTypes,
  filterValidWidgets,
  isValidWidgetType as isValidWidgetTypeUtil, // Rename to avoid conflict
} from './utils/cleanupWidgets';

// Initialization helpers
export {
  initDashboardWidgets,
  getWidgetSystemStatus,
  resetWidgetSystem,
  preloadPriorityWidgets,
} from './init';

// Base components for widget development
export * from './components/base';

// Testing utilities
export * from './test-integration';
