import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typo<PERSON>, Button } from '@/shared/components/common';
import { DashboardCard } from '../../components';
import { DashboardWidget } from '../../types';
import { widgetRegistry } from '../../registry';
import { WIDGET_TYPES } from '../../constants';
import { WIDGET_CONFIGS } from '../../registry/widgetConfigs';
import { AutoRegister } from '../../registry';

/**
 * Trang demo cho các widget business sử dụng API line-chart chính
 */
const BusinessLineChartPage: React.FC = () => {
  const { t } = useTranslation(['dashboard', 'business']);

  // Debug widget registry
  useEffect(() => {
    console.log('🔍 Debug Widget Registry:');
    console.log('📊 Available widgets:', Object.keys(widgetRegistry.getAllWidgets()));
    console.log('🎯 Sales Line Chart registered:', widgetRegistry.hasWidget(WIDGET_TYPES.SALES_LINE_CHART));
    console.log('🎯 Orders Line Chart registered:', widgetRegistry.hasWidget(WIDGET_TYPES.ORDERS_LINE_CHART));
    console.log('🎯 Order Stats registered:', widgetRegistry.hasWidget(WIDGET_TYPES.ORDER_STATS));

    // Test getting components
    try {
      const salesComponent = widgetRegistry.getComponent(WIDGET_TYPES.SALES_LINE_CHART);
      console.log('🧩 Sales component:', salesComponent);
    } catch (error) {
      console.error('❌ Error getting sales component:', error);
    }
  }, []);

  const debugRegistry = () => {
    console.log('🔍 DETAILED WIDGET REGISTRY DEBUG:');
    console.log('📊 Total configs:', WIDGET_CONFIGS.length);
    console.log('📋 All widget configs:', WIDGET_CONFIGS.map(c => ({ type: c.type, title: c.title })));

    const allWidgets = widgetRegistry.getAllWidgets();
    console.log('✅ Registered widgets:', Object.keys(allWidgets));

    // Check specific widgets
    const salesConfig = WIDGET_CONFIGS.find(c => c.type === WIDGET_TYPES.SALES_LINE_CHART);
    console.log('🎯 Sales config found:', salesConfig);

    const ordersConfig = WIDGET_CONFIGS.find(c => c.type === WIDGET_TYPES.ORDERS_LINE_CHART);
    console.log('🎯 Orders config found:', ordersConfig);

    const statsConfig = WIDGET_CONFIGS.find(c => c.type === WIDGET_TYPES.ORDER_STATS);
    console.log('🎯 Stats config found:', statsConfig);
  };

  const forceReRegister = async () => {
    console.log('🔄 Force re-registering business widgets...');
    try {
      // Clear registry first
      widgetRegistry.clear();

      // Re-register all widgets with override
      await AutoRegister.registerAllWidgets({ override: true });

      console.log('✅ Force re-registration completed');

      // Debug again
      debugRegistry();
    } catch (error) {
      console.error('❌ Force re-registration failed:', error);
    }
  };

  const widgets: DashboardWidget[] = [
    {
      id: 'sales-line-chart-demo',
      title: 'Biểu đồ doanh số (API chính)',
      type: WIDGET_TYPES.SALES_LINE_CHART,
      x: 0,
      y: 0,
      w: 8,
      h: 6,
      minW: 6,
      minH: 5,
      isEmpty: false
    },
    {
      id: 'orders-line-chart-demo',
      title: 'Biểu đồ đơn hàng (API chính)',
      type: WIDGET_TYPES.ORDERS_LINE_CHART,
      x: 8,
      y: 0,
      w: 4,
      h: 6,
      minW: 4,
      minH: 5,
      isEmpty: false
    },
    {
      id: 'order-stats-demo',
      title: 'Thống kê đơn hàng (API chính)',
      type: WIDGET_TYPES.ORDER_STATS,
      x: 0,
      y: 6,
      w: 8,
      h: 6,
      minW: 6,
      minH: 5,
      isEmpty: false
    },
    {
      id: 'product-type-pie-chart-demo',
      title: 'Biểu đồ tròn loại sản phẩm',
      type: WIDGET_TYPES.PRODUCT_TYPE_PIE_CHART,
      x: 8,
      y: 6,
      w: 4,
      h: 6,
      minW: 4,
      minH: 5,
      isEmpty: false
    }
  ];

  return (
    <div className="w-full bg-background text-foreground p-6">
      <div className="mb-6">
        <Typography variant="h1" className="mb-2">
          {t('dashboard:pages.businessLineChart.title', 'Business Line Chart Widgets')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          {t('dashboard:pages.businessLineChart.description', 'Demo các widget business sử dụng API line-chart chính thay vì các API widget riêng lẻ')}
        </Typography>

        <div className="mt-4 space-x-2">
          <Button onClick={debugRegistry} variant="outline" size="sm">
            Debug Widget Registry
          </Button>
          <Button onClick={forceReRegister} variant="outline" size="sm">
            Force Re-Register
          </Button>
          <Button
            onClick={() => window.open('/dashboard/test/line-chart-widgets', '_blank')}
            variant="outline"
            size="sm"
          >
            Test Direct Widgets
          </Button>
        </div>
      </div>

      <div className="min-h-[600px]">
        <DashboardCard
          widgets={widgets}
          onRemoveWidget={() => {}}
          onWidgetTitleChange={() => {}}
          isDraggable={false}
          isResizable={false}
          mode="view"
        />
      </div>

      <div className="mt-8 p-4 bg-muted rounded-lg">
        <Typography variant="h3" className="mb-2">
          {t('dashboard:pages.businessLineChart.apiInfo.title', 'Thông tin API')}
        </Typography>
        <Typography variant="body2" className="mb-2">
          <strong>API Endpoint:</strong> GET /v1/user/business/reports/line-chart
        </Typography>
        <Typography variant="body2" className="mb-2">
          <strong>Parameters:</strong>
        </Typography>
        <ul className="list-disc list-inside ml-4 space-y-1">
          <li>
            <Typography variant="body2">
              <code>type</code>: BusinessReportTypeEnum (ORDER, REVENUE, AVERAGE_ORDER_VALUE, etc.)
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              <code>begin</code>: Ngày bắt đầu (optional, tự động lấy từ dữ liệu đầu tiên nếu không có)
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              <code>end</code>: Ngày kết thúc (optional, mặc định là thời gian hiện tại)
            </Typography>
          </li>
        </ul>
        <Typography variant="body2" className="mt-2">
          <strong>Response:</strong> {`{ data: { "2024-01-01": 10, "2024-01-02": 15 }, period: "day" }`}
        </Typography>
      </div>
    </div>
  );
};

export default BusinessLineChartPage;
