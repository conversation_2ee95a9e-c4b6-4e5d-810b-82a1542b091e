import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Input, Icon } from '@/shared/components/common';
import { BaseWidgetProps } from '../../types';

interface TableColumn {
  id: string;
  label: string;
  key: string;
  sortable?: boolean;
  width?: number;
  align?: 'left' | 'center' | 'right';
  format?: 'text' | 'number' | 'currency' | 'date' | 'boolean';
}

interface TableRow {
  id: string;
  [key: string]: any;
}

interface TableWidgetProps extends BaseWidgetProps {
  initialColumns?: TableColumn[];
  initialData?: TableRow[];
  editable?: boolean;
  showSearch?: boolean;
  showPagination?: boolean;
  pageSize?: number;
  sortable?: boolean;
  striped?: boolean;
}

/**
 * Widget table với sort/filter/pagination
 */
const TableWidget: React.FC<TableWidgetProps> = ({
  className,
  initialColumns = [
    { id: '1', label: 'Name', key: 'name', sortable: true, align: 'left' },
    { id: '2', label: 'Email', key: 'email', sortable: true, align: 'left' },
    { id: '3', label: 'Status', key: 'status', sortable: true, align: 'center' },
    { id: '4', label: 'Amount', key: 'amount', sortable: true, align: 'right', format: 'currency' },
  ],
  initialData = [
    { id: '1', name: 'John Doe', email: '<EMAIL>', status: 'Active', amount: 1250000 },
    { id: '2', name: 'Jane Smith', email: '<EMAIL>', status: 'Inactive', amount: 850000 },
    { id: '3', name: 'Bob Johnson', email: '<EMAIL>', status: 'Active', amount: 2100000 },
    { id: '4', name: 'Alice Brown', email: '<EMAIL>', status: 'Pending', amount: 750000 },
    { id: '5', name: 'Charlie Wilson', email: '<EMAIL>', status: 'Active', amount: 1800000 },
  ],
  editable = true,
  showSearch = true,
  showPagination = true,
  pageSize = 5,
  sortable = true,
  striped = true,
  onPropsChange,
  ...props
}) => {
  const { t } = useTranslation(['dashboard', 'common']);
  
  // Use values from props if available
  const currentColumns = (props.columns as TableColumn[]) || initialColumns;
  const currentData = (props.data as TableRow[]) || initialData;
  const currentSettings = {
    showSearch: (props.showSearch as boolean) ?? showSearch,
    showPagination: (props.showPagination as boolean) ?? showPagination,
    pageSize: (props.pageSize as number) ?? pageSize,
    sortable: (props.sortable as boolean) ?? sortable,
    striped: (props.striped as boolean) ?? striped,
  };

  const [columns, setColumns] = useState<TableColumn[]>(currentColumns);
  const [data, setData] = useState<TableRow[]>(currentData);
  const [isEditing, setIsEditing] = useState(false);
  const [tempColumns, setTempColumns] = useState<TableColumn[]>(currentColumns);
  const [tempData, setTempData] = useState<TableRow[]>(currentData);
  const [tempSettings, setTempSettings] = useState(currentSettings);
  
  // Table state
  const [searchTerm, setSearchTerm] = useState('');
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [currentPage, setCurrentPage] = useState(1);

  // Sync with props changes
  useEffect(() => {
    const newColumns = (props.columns as TableColumn[]) || initialColumns;
    const newData = (props.data as TableRow[]) || initialData;
    
    if (JSON.stringify(newColumns) !== JSON.stringify(columns)) {
      setColumns(newColumns);
      setTempColumns(newColumns);
    }
    
    if (JSON.stringify(newData) !== JSON.stringify(data)) {
      setData(newData);
      setTempData(newData);
    }
  }, [props.columns, props.data, initialColumns, initialData, columns, data]);

  const handleEdit = useCallback(() => {
    setTempColumns([...columns]);
    setTempData([...data]);
    setTempSettings(currentSettings);
    setIsEditing(true);
  }, [columns, data, currentSettings]);

  const handleSave = useCallback(() => {
    setColumns(tempColumns);
    setData(tempData);
    setIsEditing(false);
    
    // Save to widget props
    if (onPropsChange) {
      onPropsChange({
        columns: tempColumns,
        data: tempData,
        ...tempSettings,
      });
    }
  }, [tempColumns, tempData, tempSettings, onPropsChange]);

  const handleCancel = useCallback(() => {
    setTempColumns([...columns]);
    setTempData([...data]);
    setTempSettings(currentSettings);
    setIsEditing(false);
  }, [columns, data, currentSettings]);

  const handleSort = useCallback((columnKey: string) => {
    if (!currentSettings.sortable) return;
    
    if (sortColumn === columnKey) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(columnKey);
      setSortDirection('asc');
    }
    setCurrentPage(1);
  }, [sortColumn, currentSettings.sortable]);

  const formatCellValue = useCallback((value: any, format?: TableColumn['format']) => {
    if (value === null || value === undefined) return '-';
    
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(value);
      case 'number':
        return typeof value === 'number' ? value.toLocaleString() : value;
      case 'date':
        return new Date(value).toLocaleDateString('vi-VN');
      case 'boolean':
        return value ? 'Yes' : 'No';
      default:
        return String(value);
    }
  }, []);

  const getStatusColor = useCallback((status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'inactive': return 'text-red-600 bg-red-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-muted-foreground bg-muted';
    }
  }, []);

  // Filter and sort data
  const processedData = useMemo(() => {
    let filtered = data;
    
    // Search filter
    if (searchTerm && currentSettings.showSearch) {
      filtered = data.filter(row =>
        columns.some(col =>
          String(row[col.key] || '').toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }
    
    // Sort
    if (sortColumn && currentSettings.sortable) {
      filtered = [...filtered].sort((a, b) => {
        const aVal = a[sortColumn];
        const bVal = b[sortColumn];
        
        if (aVal === bVal) return 0;
        
        let comparison = 0;
        if (typeof aVal === 'number' && typeof bVal === 'number') {
          comparison = aVal - bVal;
        } else {
          comparison = String(aVal).localeCompare(String(bVal));
        }
        
        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }
    
    return filtered;
  }, [data, searchTerm, sortColumn, sortDirection, columns, currentSettings.showSearch, currentSettings.sortable]);

  // Pagination
  const totalPages = Math.ceil(processedData.length / currentSettings.pageSize);
  const paginatedData = currentSettings.showPagination
    ? processedData.slice((currentPage - 1) * currentSettings.pageSize, currentPage * currentSettings.pageSize)
    : processedData;

  const alignmentClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  if (isEditing) {
    return (
      <div className={`w-full h-full p-4 ${className || ''}`}>
        <div className="h-full flex flex-col">
          <div className="space-y-4 flex-1 overflow-y-auto">
            {/* Settings */}
            <div className="space-y-2">
              <Typography variant="body2">
                {t('dashboard:widgets.table.settings', 'Cài đặt')}
              </Typography>
              
              <div className="grid grid-cols-2 gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={tempSettings.showSearch}
                    onChange={(e) => setTempSettings(prev => ({ ...prev, showSearch: e.target.checked }))}
                    className="rounded"
                  />
                  <Typography variant="body2">
                    {t('dashboard:widgets.table.showSearch', 'Hiển thị tìm kiếm')}
                  </Typography>
                </label>

                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={tempSettings.showPagination}
                    onChange={(e) => setTempSettings(prev => ({ ...prev, showPagination: e.target.checked }))}
                    className="rounded"
                  />
                  <Typography variant="body2">
                    {t('dashboard:widgets.table.showPagination', 'Hiển thị phân trang')}
                  </Typography>
                </label>

                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={tempSettings.sortable}
                    onChange={(e) => setTempSettings(prev => ({ ...prev, sortable: e.target.checked }))}
                    className="rounded"
                  />
                  <Typography variant="body2">
                    {t('dashboard:widgets.table.sortable', 'Cho phép sắp xếp')}
                  </Typography>
                </label>

                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={tempSettings.striped}
                    onChange={(e) => setTempSettings(prev => ({ ...prev, striped: e.target.checked }))}
                    className="rounded"
                  />
                  <Typography variant="body2">
                    {t('dashboard:widgets.table.striped', 'Dòng kẻ sọc')}
                  </Typography>
                </label>
              </div>

              <div>
                <Typography variant="body2" className="mb-1">
                  {t('dashboard:widgets.table.pageSize', 'Số dòng mỗi trang')}
                </Typography>
                <Input
                  type="number"
                  value={tempSettings.pageSize}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, pageSize: parseInt(e.target.value) || 5 }))}
                  min="1"
                  max="50"
                  className="w-full"
                />
              </div>
            </div>

            {/* Preview */}
            <div className="border border-border rounded-lg p-3">
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.table.preview', 'Xem trước')}
              </Typography>
              
              <div className="text-center py-8 text-muted-foreground">
                <Icon name="table" size="lg" className="mb-2 mx-auto" />
                <Typography variant="body2">
                  {t('dashboard:widgets.table.previewNote', 'Bảng sẽ hiển thị với dữ liệu mẫu')}
                </Typography>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              {t('common:cancel')}
            </Button>
            <Button variant="primary" size="sm" onClick={handleSave}>
              {t('common:save')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (columns.length === 0 || data.length === 0) {
    return (
      <div 
        className={`w-full h-full p-4 ${className || ''} ${editable ? 'cursor-pointer hover:bg-muted/50 transition-colors' : ''}`}
        onClick={editable ? handleEdit : undefined}
      >
        <div className="h-full flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/30 rounded-lg">
          <Icon name="table" size="lg" className="text-muted-foreground mb-2" />
          <Typography variant="body2" className="text-muted-foreground text-center">
            {editable 
              ? t('dashboard:widgets.table.empty', 'Click để cấu hình bảng')
              : t('dashboard:widgets.table.noData', 'Không có dữ liệu')
            }
          </Typography>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`w-full h-full p-4 relative group ${className || ''}`}
    >
      <div className="h-full flex flex-col">
        {/* Header with Search */}
        {currentSettings.showSearch && (
          <div className="flex items-center justify-between mb-4">
            <Typography variant="h3" className="font-semibold">
              {t('dashboard:widgets.table.title', 'Data Table')}
            </Typography>
            
            <div className="flex items-center gap-2">
              <Input
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={t('dashboard:widgets.table.searchPlaceholder', 'Tìm kiếm...')}
                className="w-48"
              />
              <Icon name="search" size="sm" className="text-muted-foreground" />
            </div>
          </div>
        )}

        {/* Table */}
        <div className="flex-1 overflow-auto border border-border rounded-lg">
          <table className="w-full">
            <thead className="bg-muted/50">
              <tr>
                {columns.map((column) => (
                  <th
                    key={column.id}
                    className={`
                      px-4 py-3 font-medium text-sm
                      ${alignmentClasses[column.align || 'left']}
                      ${currentSettings.sortable && column.sortable ? 'cursor-pointer hover:bg-muted' : ''}
                    `}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-2">
                      <span>{column.label}</span>
                      {currentSettings.sortable && column.sortable && sortColumn === column.key && (
                        <Icon 
                          name={sortDirection === 'asc' ? 'chevron-up' : 'chevron-down'} 
                          size="xs" 
                        />
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            
            <tbody>
              {paginatedData.map((row, index) => (
                <tr
                  key={row.id}
                  className={`
                    border-t border-border hover:bg-muted/30
                    ${currentSettings.striped && index % 2 === 1 ? 'bg-muted/20' : ''}
                  `}
                >
                  {columns.map((column) => (
                    <td
                      key={column.id}
                      className={`px-4 py-3 text-sm ${alignmentClasses[column.align || 'left']}`}
                    >
                      {column.key === 'status' ? (
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(row[column.key])}`}>
                          {row[column.key]}
                        </span>
                      ) : (
                        formatCellValue(row[column.key], column.format)
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {currentSettings.showPagination && totalPages > 1 && (
          <div className="flex items-center justify-between mt-4">
            <Typography variant="caption" className="text-muted-foreground">
              {t('dashboard:widgets.table.showing', 'Hiển thị')} {(currentPage - 1) * currentSettings.pageSize + 1}-{Math.min(currentPage * currentSettings.pageSize, processedData.length)} {t('dashboard:widgets.table.of', 'của')} {processedData.length}
            </Typography>
            
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                <Icon name="chevron-left" size="sm" />
              </Button>
              
              <span className="text-sm">
                {currentPage} / {totalPages}
              </span>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                <Icon name="chevron-right" size="sm" />
              </Button>
            </div>
          </div>
        )}

        {/* Edit Button */}
        {editable && (
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button variant="secondary" size="sm" onClick={handleEdit}>
              {t('common:edit')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default TableWidget;
