/**
 * MessageList Component
 * Component hiển thị danh sách tin nhắn với scroll và auto-scroll to bottom
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Icon, Loading } from '@/shared/components/common';
import MessageBubble from './MessageBubble';
import type { Message, Contact } from '../../../types/zalo-chat.types';
import { UI_CONSTANTS } from '../../../constants/zalo-chat.constants';
import { format, isSameDay } from 'date-fns';
import { vi } from 'date-fns/locale';

interface MessageListProps {
  messages: Message[];
  contact?: Contact;
  isLoading?: boolean;
  isLoadingMore?: boolean;
  hasMore?: boolean;
  onLoadMore?: () => void;
  onMessageRead?: (messageIds: string[]) => void;
  className?: string;
}

/**
 * Component hiển thị ngày phân cách
 */
const DateSeparator: React.FC<{ date: Date }> = ({ date }) => {
  const { t } = useTranslation(['marketing', 'common']);
  
  const formatDate = (date: Date): string => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (isSameDay(date, today)) {
      return t('common:today');
    } else if (isSameDay(date, yesterday)) {
      return t('common:yesterday');
    } else {
      return format(date, 'EEEE, dd/MM/yyyy', { locale: vi });
    }
  };

  return (
    <div className="flex items-center justify-center my-4">
      <div className="bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full">
        <Typography variant="caption" className="text-gray-600 dark:text-gray-400">
          {formatDate(date)}
        </Typography>
      </div>
    </div>
  );
};

/**
 * Component hiển thị typing indicator
 */
const TypingIndicator: React.FC<{ contactName?: string }> = () => {

  return (
    <div className="flex items-end space-x-2 mb-4">
      <div className="w-8 h-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
        <Icon name="user" size="sm" className="text-gray-600 dark:text-gray-300" />
      </div>
      <div className="bg-gray-100 dark:bg-gray-700 px-4 py-2 rounded-2xl rounded-bl-md">
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
      </div>
    </div>
  );
};

/**
 * Component MessageList chính
 */
const MessageList: React.FC<MessageListProps> = ({
  messages,
  contact,
  isLoading = false,
  isLoadingMore = false,
  hasMore = false,
  onLoadMore,
  onMessageRead,
  className = '',
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const [, setUnreadMessageIds] = useState<string[]>([]);

  // Auto scroll to bottom khi có tin nhắn mới
  const scrollToBottom = useCallback((smooth: boolean = true) => {
    messagesEndRef.current?.scrollIntoView({ 
      behavior: smooth ? 'smooth' : 'auto',
      block: 'end'
    });
  }, []);

  // Check if user is near bottom of scroll
  const checkScrollPosition = useCallback(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
    setShouldAutoScroll(distanceFromBottom < UI_CONSTANTS.AUTO_SCROLL_THRESHOLD);
  }, []);

  // Handle scroll event
  const handleScroll = useCallback(() => {
    checkScrollPosition();
    
    // Load more messages when scrolled to top
    const container = messagesContainerRef.current;
    if (container && container.scrollTop === 0 && hasMore && onLoadMore && !isLoadingMore) {
      onLoadMore();
    }
  }, [checkScrollPosition, hasMore, onLoadMore, isLoadingMore]);

  // Auto scroll when new messages arrive
  useEffect(() => {
    if (shouldAutoScroll && messages.length > 0) {
      scrollToBottom();
    }
  }, [messages, shouldAutoScroll, scrollToBottom]);

  // Mark messages as read when they come into view
  useEffect(() => {
    if (!onMessageRead || messages.length === 0) return;

    const unreadMessages = messages.filter(
      msg => msg.senderType === 'contact' && msg.status !== 'read'
    );

    if (unreadMessages.length > 0) {
      const messageIds = unreadMessages.map(msg => msg.id);
      setUnreadMessageIds(messageIds);

      // Delay marking as read to simulate reading time
      const timer = setTimeout(() => {
        onMessageRead(messageIds);
        setUnreadMessageIds([]);
      }, 1000);

      return () => clearTimeout(timer);
    }

    return undefined;
  }, [messages, onMessageRead]);

  // Group messages by date and consecutive sender
  const groupedMessages = React.useMemo(() => {
    const groups: Array<{
      date: Date;
      messages: Array<{
        message: Message;
        isConsecutive: boolean;
      }>;
    }> = [];

    let currentDate: Date | null = null;
    let currentGroup: typeof groups[0] | null = null;
    let lastSenderId: string | null = null;
    let lastSenderType: string | null = null;

    messages.forEach((message) => {
      const messageDate = new Date(message.timestamp);
      const messageDateOnly = new Date(messageDate.getFullYear(), messageDate.getMonth(), messageDate.getDate());

      // Check if we need a new date group
      if (!currentDate || !isSameDay(currentDate, messageDateOnly)) {
        currentDate = messageDateOnly;
        currentGroup = {
          date: currentDate,
          messages: [],
        };
        groups.push(currentGroup);
        lastSenderId = null;
        lastSenderType = null;
      }

      // Check if message is consecutive (same sender within 5 minutes)
      const isConsecutive = 
        lastSenderId === message.senderId && 
        lastSenderType === message.senderType &&
        currentGroup!.messages.length > 0;

      currentGroup!.messages.push({
        message,
        isConsecutive,
      });

      lastSenderId = message.senderId;
      lastSenderType = message.senderType;
    });

    return groups;
  }, [messages]);

  if (isLoading && messages.length === 0) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <Loading />
      </div>
    );
  }

  if (messages.length === 0) {
    return (
      <div className={`flex flex-col items-center justify-center h-full px-4 ${className}`}>
        <Icon name="message-circle" size="2xl" className="text-gray-300 dark:text-gray-600 mb-4" />
        <Typography variant="h6" className="text-gray-500 dark:text-gray-400 text-center mb-2">
          {t('marketing:zalo.chat.noMessages')}
        </Typography>
        <Typography variant="body2" className="text-gray-400 dark:text-gray-500 text-center">
          {contact 
            ? t('marketing:zalo.chat.startConversation', { name: contact.name })
            : t('marketing:zalo.chat.selectContact')
          }
        </Typography>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Messages container */}
      <div
        ref={messagesContainerRef}
        onScroll={handleScroll}
        className="flex-1 overflow-y-auto px-4 py-4 space-y-1"
        style={{ scrollBehavior: 'smooth' }}
      >
        {/* Load more indicator */}
        {isLoadingMore && (
          <div className="flex justify-center py-4">
            <Loading size="sm" />
          </div>
        )}

        {/* Messages grouped by date */}
        {groupedMessages.map((group, groupIndex) => (
          <div key={groupIndex}>
            {/* Date separator */}
            <DateSeparator date={group.date} />
            
            {/* Messages for this date */}
            {group.messages.map(({ message, isConsecutive }) => (
              <MessageBubble
                key={message.id}
                message={message}
                senderName={message.senderType === 'contact' ? contact?.name : undefined}
                senderAvatar={message.senderType === 'contact' ? contact?.avatar : undefined}
                showAvatar={!isConsecutive}
                isConsecutive={isConsecutive}
                className="mb-2"
              />
            ))}
          </div>
        ))}

        {/* Typing indicator */}
        {/* TODO: Implement real-time typing indicator */}
        {false && <TypingIndicator contactName={contact?.name} />}

        {/* Scroll anchor */}
        <div ref={messagesEndRef} />
      </div>

      {/* Scroll to bottom button */}
      {!shouldAutoScroll && (
        <div className="absolute bottom-20 right-4">
          <button
            onClick={() => scrollToBottom()}
            className="bg-blue-500 hover:bg-blue-600 text-white rounded-full p-2 shadow-lg transition-colors"
            title={t('marketing:zalo.chat.scrollToBottom')}
          >
            <Icon name="chevron-down" size="lg" />
          </button>
        </div>
      )}
    </div>
  );
};

export default MessageList;
