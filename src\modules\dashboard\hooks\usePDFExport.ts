import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { DashboardPDFExporter, type ExportPDFOptions } from '../utils/exportPDF';

/**
 * Hook để xử lý export PDF dashboard
 */
export const usePDFExport = () => {
  const { t } = useTranslation(['dashboard', 'common']);
  const { success, error: showError, info } = useSmartNotification();
  const [isExporting, setIsExporting] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  /**
   * Export dashboard thành PDF
   */
  const exportToPDF = useCallback(async (options?: ExportPDFOptions) => {
    if (isExporting) {
      return;
    }

    setIsExporting(true);

    try {
      // Hiển thị notification bắt đầu export
      info({
        title: t('dashboard:export.pdf.starting', 'Đang xuất PDF'),
        message: t('dashboard:export.pdf.startingMessage', 'Đang chuẩn bị xuất dashboard thành PDF...'),
        duration: 2000,
      });

      // Tìm dashboard container
      const dashboardElement = document.querySelector('[data-dashboard-container]') as HTMLElement;
      
      if (!dashboardElement) {
        throw new Error('Dashboard container not found');
      }

      // Export PDF
      await DashboardPDFExporter.exportToPDF(dashboardElement, {
        filename: 'redai-bao-cao',
        quality: 0.95,
        format: 'a4',
        orientation: 'landscape',
        margin: 10,
        scale: 2,
        includeBackground: true,
        ...options,
      });

      // Hiển thị notification thành công
      success({
        title: t('dashboard:export.pdf.success', 'Xuất PDF thành công'),
        message: t('dashboard:export.pdf.successMessage', 'Dashboard đã được xuất thành PDF và tải xuống.'),
        duration: 4000,
      });

    } catch (error) {
      console.error('Failed to export PDF:', error);

      // Hiển thị notification lỗi
      showError({
        title: t('dashboard:export.pdf.error', 'Lỗi xuất PDF'),
        message: t('dashboard:export.pdf.errorMessage', 'Có lỗi xảy ra khi xuất PDF. Vui lòng thử lại.'),
        duration: 5000,
      });
    } finally {
      setIsExporting(false);
    }
  }, [isExporting, t, success, showError, info]);

  /**
   * Export với options cụ thể cho landscape
   */
  const exportLandscape = useCallback(() => {
    return exportToPDF({
      orientation: 'landscape',
      format: 'a4',
    });
  }, [exportToPDF]);

  /**
   * Export với options cụ thể cho portrait
   */
  const exportPortrait = useCallback(() => {
    return exportToPDF({
      orientation: 'portrait',
      format: 'a4',
    });
  }, [exportToPDF]);

  /**
   * Export với chất lượng cao
   */
  const exportHighQuality = useCallback(() => {
    return exportToPDF({
      quality: 1.0,
      scale: 3,
      format: 'a3',
      orientation: 'landscape',
    });
  }, [exportToPDF]);

  /**
   * Mở modal cấu hình export
   */
  const openExportModal = useCallback(() => {
    setIsModalOpen(true);
  }, []);

  /**
   * Đóng modal cấu hình export
   */
  const closeExportModal = useCallback(() => {
    setIsModalOpen(false);
  }, []);

  return {
    exportToPDF,
    exportLandscape,
    exportPortrait,
    exportHighQuality,
    openExportModal,
    closeExportModal,
    isExporting,
    isModalOpen,
  };
};

export default usePDFExport;
