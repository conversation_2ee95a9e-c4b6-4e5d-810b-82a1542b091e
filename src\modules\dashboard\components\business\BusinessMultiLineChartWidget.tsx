import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { TrendingUp, ShoppingCart, BarChart3, Users } from 'lucide-react';
import { MultiLineChartWidget, ChartTypeOption } from '@/shared/components/charts';
import { useLineChart } from '@/modules/business/hooks/useReportQuery';
import { BusinessReportTypeEnum } from '@/modules/business/types/report.types';
import { BaseWidgetProps } from '../../types';



/**
 * Chart type options cho Business Multi Line Chart Widget
 */
const getChartTypeOptions = (t: any): ChartTypeOption[] => [
  {
    value: BusinessReportTypeEnum.REVENUE,
    label: t('business:report.types.revenue', 'Doanh thu'),
    icon: TrendingUp,
    color: '#2563eb',
    dataKey: 'revenue',
  },
  {
    value: BusinessReportTypeEnum.ORDER,
    label: t('business:report.types.order', 'Đơn hàng'),
    icon: ShoppingCart,
    color: '#16a34a',
    dataKey: 'orders',
  },
  {
    value: BusinessReportTypeEnum.AVERAGE_ORDER_VALUE,
    label: t('business:report.types.averageOrderValue', 'Giá trị TB đơn hàng'),
    icon: BarChart3,
    color: '#dc2626',
    dataKey: 'avgOrderValue',
  },
  {
    value: BusinessReportTypeEnum.CUSTOMER,
    label: t('business:report.types.customer', 'Khách hàng'),
    icon: Users,
    color: '#7c3aed',
    dataKey: 'customers',
  },
  {
    value: BusinessReportTypeEnum.NEW_CUSTOMERS,
    label: t('business:report.types.newCustomers', 'Khách hàng mới'),
    icon: Users,
    color: '#059669',
    dataKey: 'newCustomers',
  },
];

/**
 * Business Multi Line Chart Widget - Hiển thị dữ liệu kinh doanh
 */
const BusinessMultiLineChartWidget: React.FC<BaseWidgetProps> = (props) => {
  const { t } = useTranslation(['dashboard', 'business']);

  // Chart type options với đa ngôn ngữ
  const chartTypeOptions = useMemo(() => getChartTypeOptions(t), [t]);

  return (
    <MultiLineChartWidget
      {...props}
      title={t('business:report.charts.multiLine.title', 'Biểu đồ kinh doanh')}
      chartTypeOptions={chartTypeOptions}
      useDataHook={useLineChart}
      translationNamespace="business"
      defaultChartType={BusinessReportTypeEnum.REVENUE}
      icon={TrendingUp}
    />
  );
};

export default BusinessMultiLineChartWidget;
