{"dashboard": {"title": "Dashboard", "search": {"placeholder": "Search reports", "noResults": "No results found"}, "actions": {"save": "Save", "cancel": "Cancel", "help": "Help", "collapse": "Collapse", "expand": "Expand"}, "drag_to_move": "Drag to move widget", "remove_widget": "Remove widget", "sidebar": {"collapseSidebar": "Collapse sidebar", "expandSidebar": "Expand sidebar"}, "views": {"default": "Default View", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "quarterly": "Quarterly", "yearly": "Yearly"}, "empty": {"title": "Net Revenue - from Sales Channels", "description": "Select an item from the left menu to view detailed reports", "noData": "No data available", "selectMenu": "Select an item from the left menu to view reports"}, "menu": {"business": "Business", "integration": "Integration", "marketing": "Marketing", "marketplace": "Marketplace", "data": "Data Management", "aiAgents": "AI Agents", "affiliate": "Affiliate", "finance": "Finance", "accounts": "Accounts", "website": "Website", "items": {"businessOverview": "Business Overview", "customerProducts": "Customer Products", "customerList": "Customer List", "bankAccountOverview": "Bank Account Overview", "businessLineCharts": "Business Charts (Main API)", "integrationOverview": "Integration Overview", "integrationStats": "Integration Statistics", "marketingOverview": "Marketing Overview", "campaignPerformance": "Campaign Performance", "dataCount": "Data Statistics", "dataStorage": "Data Storage", "agentOverview": "AI Agents Overview", "agentPerformance": "Agent Performance", "affiliateOverview": "Affiliate Overview"}}, "business": {"salesOverview": "Sales Overview", "revenue": "Revenue", "revenueAnalysis": "Revenue Analysis", "salesPerformance": "Sales Performance", "revenueByChannel": "Revenue by Channel", "revenueTrends": "Revenue Trends", "orders": "Orders", "orderStatus": "Order Status", "orderAnalytics": "Order Analytics"}, "integration": {"apiUsage": "API Usage", "webhooks": "Webhooks", "thirdParty": "Third Party Integration"}, "marketing": {"campaigns": "Campaigns", "emailCampaigns": "Email Campaigns", "smsCampaigns": "SMS Campaigns", "customerAnalytics": "Customer Analytics", "conversionFunnel": "Conversion Funnel"}, "marketplace": {"productPerformance": "Product Performance", "competitorAnalysis": "Competitor Analysis", "marketTrends": "Market Trends"}, "finance": {"overview": "Financial Overview", "profitLoss": "Profit & Loss", "cashFlow": "Cash Flow"}, "accounts": {"userAnalytics": "User Analytics", "accessLogs": "Access Logs"}, "website": {"traffic": "Traffic Analytics", "pagePerformance": "Page Performance"}, "data": {"dataCount": "Total Data Count", "dataStorage": "Data Storage"}, "aiAgents": {"agentOverview": "AI Agents Overview", "agentPerformance": "Agent Performance", "agentIntegrations": "Agent Integrations"}, "affiliate": {"affiliateOverview": "Affiliate Overview", "affiliateCommissions": "Commissions", "affiliatePartners": "Partners"}, "widgets": {"noData": "No data available", "dataWillShow": "Data will be displayed when available", "widgetContent": "Widget content", "dataCount": {"title": "Total Data Count", "users": "Users", "usersDescription": "Total users in the system", "knowledgeFiles": "Knowledge Files", "knowledgeFilesDescription": "Knowledge files for AI and vector store", "mediaFiles": "Media Files", "mediaFilesDescription": "Images, videos, audio and documents", "urls": "URLs", "urlsDescription": "Links and web resources", "vectorStores": "Vector Stores", "vectorStoresDescription": "Vector storage repositories"}, "businessOverview": {"title": "Business Overview", "totalRevenue": "Total Revenue", "totalRevenueDescription": "Total revenue for the period", "orders": "Orders", "ordersDescription": "Total number of orders", "customers": "Customers", "customersDescription": "Total number of customers", "conversionRate": "Conversion Rate", "conversionRateDescription": "Customer conversion rate"}, "marketingOverview": {"title": "Marketing Overview", "totalContacts": "Total Contacts", "totalContactsDescription": "Total contacts in the system", "segments": "Segments", "segmentsDescription": "Number of segments created", "activeCampaigns": "Active Campaigns", "activeCampaignsDescription": "Number of active campaigns", "emailTemplates": "Email Templates", "emailTemplatesDescription": "Total email templates", "smsTemplates": "SMS Templates", "smsTemplatesDescription": "Total SMS templates", "tags": "Tags", "tagsDescription": "Total tags created", "customFields": "Custom Fields", "customFieldsDescription": "Number of custom fields created"}, "customFieldForm": {"title": "Add Custom Field", "submitSuccess": "Custom field created successfully!"}}, "addWidget": {"title": "Add Widget", "button": "Add Widget", "searchPlaceholder": "Search widgets...", "noResults": "No widgets found", "allWidgetsAdded": "All widgets have been added", "selectedCount": "Selected {{count}} widgets", "addSelected": "Add Selected"}, "categories": {"title": "Categories", "all": "All", "data": "Data", "visualization": "Visualization", "marketing": "Marketing", "ai-agents": "AI Agents", "business": "Business", "affiliate": "Affiliate", "integration": "Integration", "content": "Content", "custom": "Custom"}, "no_data": "No data available", "data_will_display": "Data will be displayed when available", "widget_content": "Widget content", "displayMode": {"normal": "Normal mode", "minimal": "Minimal mode", "ultraMinimal": "Ultra-minimal mode"}, "showCardTitles": "Show widget titles", "hideCardTitles": "Hide widget titles", "ultraMinimalMode": "Ultra-minimal mode - Pure content only", "smartLayout": {"enable": "Enable smart layout", "disable": "Disable smart layout"}, "export": {"pdf": {"button": "Export Report", "starting": "Exporting Report", "startingMessage": "Preparing to export dashboard report...", "success": "Report exported successfully", "successMessage": "Dashboard report has been exported and downloaded.", "error": "Report export error", "errorMessage": "An error occurred while exporting report. Please try again.", "configTitle": "Report Export Configuration", "configDescription": "Customize report export options according to your needs", "preview": {"title": "Preview Dashboard", "generate": "Generate preview", "generating": "Generating...", "update": "Update preview", "configChanged": "Settings changed", "clickToZoom": "Click to zoom", "description": "Click \"Generate preview\" to see how dashboard will be exported", "filterNote": "This preview reflects the selected content filtering settings"}}}, "pages": {"businessLineChart": {"title": "Business Line Chart Widgets", "description": "Demo business widgets using main line-chart API instead of individual widget APIs", "apiInfo": {"title": "API Information"}}}}}