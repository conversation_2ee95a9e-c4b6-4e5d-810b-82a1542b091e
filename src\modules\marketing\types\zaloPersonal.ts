import type { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import type { PaginationMeta } from '@/shared/dto/response/api-response.dto';

/**
 * Zalo Personal Integration DTO
 */
export interface ZaloPersonalIntegrationDto {
  id: string;
  integrationName: string;
  createdAt: number;
  metadata: {
    profile?: {
      id?: string;
      name?: string;
      avatar?: string;
    };
    status?: string;
    lastLogin?: number;
    [key: string]: any;
  };
  isActive: boolean;
}

/**
 * Query parameters cho danh sách tích hợp Zalo Personal
 */
export interface ZaloPersonalIntegrationQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  id?: string;
}

/**
 * Response DTO cho danh sách tích hợp Zalo Personal
 */
export interface ZaloPersonalIntegrationResponseDto extends ApiResponseDto {
  result: {
    items: ZaloPersonalIntegrationDto[];
    meta: PaginationMeta;
  };
}

/**
 * DTO để tạo tích hợp <PERSON>alo <PERSON> mới
 */
export interface CreateZaloPersonalIntegrationDto {
  integrationName: string;
  authCode?: string;
  redirectUri?: string;
  metadata?: Record<string, any>;
}

/**
 * DTO để cập nhật tích hợp Zalo Personal
 */
export interface UpdateZaloPersonalIntegrationDto {
  integrationName?: string;
  metadata?: Record<string, any>;
  isActive?: boolean;
}

/**
 * Response DTO cho QR code generation
 */
export interface ZaloPersonalQRCodeResponseDto extends ApiResponseDto {
  result: {
    sessionId: string;
    qrCodeBase64: string;
    expiresAt: number;
    message: string;
  };
}

/**
 * DTO để tạo QR code Zalo Personal
 */
export interface GenerateZaloPersonalQRCodeDto {
  // Có thể thêm các tham số khác nếu cần
}

/**
 * Response DTO cho chi tiết tích hợp Zalo Personal
 */
export interface ZaloPersonalIntegrationDetailResponseDto extends ApiResponseDto {
  result: ZaloPersonalIntegrationDto;
}

/**
 * DTO cho trạng thái đăng nhập
 */
export interface ZaloPersonalIntegrationStatusDto {
  isActive: boolean;
  status: 'connected' | 'disconnected' | 'expired' | 'error';
  lastChecked: number;
  profile?: {
    id: string;
    name: string;
    avatar?: string;
  };
}

/**
 * Response DTO cho trạng thái đăng nhập
 */
export interface ZaloPersonalIntegrationStatusResponseDto extends ApiResponseDto {
  result: ZaloPersonalIntegrationStatusDto;
}

/**
 * DTO cho đăng nhập lại
 */
export interface ZaloPersonalReloginResponseDto extends ApiResponseDto {
  result: {
    success: boolean;
    redirectUrl?: string;
    authUrl?: string;
  };
}
