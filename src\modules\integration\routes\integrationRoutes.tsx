import { lazy, Suspense } from 'react';
import { RouteObject } from 'react-router-dom';
import MainLayout from '@/shared/layouts/MainLayout';
import { Loading } from '@/shared/components/common';
import i18n from '@/lib/i18n';
import UserIntegrationManagementPage from '../pages/UserIntegrationManagementPage';
import { ZaloOfficialAccountConnectPage } from '@/modules/marketing';
import { CallbackPage } from '../callback';

const MyIntegrationsPage = lazy(() => import('../pages/MyIntegrationsPage'));
// Import các trang integration riêng biệt
const GHTKIntegrationPage = lazy(() => import('../pages/GHTKIntegrationPage'));
const GHNIntegrationPage = lazy(() => import('../pages/GHNIntegrationPage'));
const AhamoveIntegrationPage = lazy(() => import('../pages/AhamoveIntegrationPage'));

const FacebookIntegrationPage = lazy(() => import('../pages/FacebookIntegrationPage'));
const FacebookAdsIntegrationPage = lazy(
  () => import('../facebook-ads/pages/FacebookAdsIntegrationPage')
);

const WebsiteIntegrationPage = lazy(() => import('../pages/WebsiteIntegrationPage'));
const EmailServerManagementPage = lazy(() => import('../pages/EmailServerManagementPage'));
const SMTPIntegrationPage = lazy(() => import('../pages/SMTPIntegrationPage'));
const ProviderModelManagementPage = lazy(() => import('../pages/ProviderModelManagementPage'));
const DatabaseIntegrationPage = lazy(() => import('../pages/DatabaseIntegrationPage'));
const ZaloOAuthCallbackPage = lazy(() => import('../pages/ZaloOAuthCallbackPage'));
const ZaloOAManagementPage = lazy(() => import('../zalo/pages/ZaloOAManagementPage'));
const FptSmsBrandnameManagementPage = lazy(() => import('../pages/FptSmsBrandnameManagementPage'));

const ShippingIntegrationPage = lazy(() => import('../pages/ShippingIntegrationPage'));
const CloudStorageIntegrationPage = lazy(() => import('../pages/CloudStorageIntegrationPage'));
const EnterpriseStorageIntegrationPage = lazy(
  () => import('../pages/EnterpriseStorageIntegrationPage')
);
const ExternalAgentIntegrationPage = lazy(() => import('../pages/ExternalAgentIntegrationPage'));

// Email and SMS Provider Pages
const EmailProviderPage = lazy(() => import('../pages/EmailProviderPage'));
const GmailIntegrationPage = lazy(() => import('../gmail/pages/GmailIntegrationPage'));
const GmailManagementPage = lazy(() => import('../gmail/pages/GmailManagementPage'));

// Calendar Integration Pages
const GoogleCalendarIntegrationPage = lazy(
  () => import('../calendar/pages/GoogleCalendarIntegrationPage')
);
const GoogleCalendarManagementPage = lazy(
  () => import('../calendar/pages/GoogleCalendarManagementPage')
);

// Google Ads Integration Pages
const GoogleAdsIntegrationPage = lazy(() => import('../google-ads/pages/GoogleAdsIntegrationPage'));

const OpenAIManagePage = lazy(() => import('../pages/OpenAIManagePage'));
const AnthropicManagePage = lazy(() => import('../pages/AnthropicManagePage'));
const GeminiManagePage = lazy(() => import('../pages/GeminiManagePage'));
const DeepSeekManagePage = lazy(() => import('../pages/DeepSeekManagePage'));

const XAIGrokManagePage = lazy(() => import('../pages/XAIGrokManagePage'));
const FptSmsIntegrationPage = lazy(() => import('../sms/pages/FptSmsIntegrationPage'));
const SMSProviderPage = lazy(() => import('../pages/TwilioSMSProviderPage'));
const ZaloPersonalIntegrationPage = lazy(() => import('../pages/ZaloPersonalIntegrationPage'));

// Banking Integration Pages
const ACBBankingPage = lazy(() => import('../../integrations/pages/ACBBankingPage'));
const MBBankingPage = lazy(() => import('../../integrations/pages/MBBankingPage'));
const OCBBankingPage = lazy(() => import('../../integrations/pages/OCBBankingPage'));
const KienLongBankingPage = lazy(() => import('../../integrations/pages/KienLongBankingPage'));

// Bank Account List Pages
const BankAccountListPage = lazy(() => import('../pages/BankAccountListPage'));
const MBBankAccountListPage = lazy(() => import('../pages/MBBankAccountListPage'));
const ACBBankAccountListPage = lazy(() => import('../pages/ACBBankAccountListPage'));
const OCBBankAccountListPage = lazy(() => import('../pages/OCBBankAccountListPage'));
// const KienLongBankAccountListPage = lazy(() => import('../pages/KienLongBankAccountListPage'));
const KLBankAccountListPage = lazy(() => import('../pages/KLBBankAccountListPage'));

// Provider Model Integration Pages
const OpenAIIntegrationPage = lazy(() => import('../provider-model/pages/OpenAIIntegrationPage'));
const AnthropicIntegrationPage = lazy(
  () => import('../provider-model/pages/AnthropicIntegrationPage')
);
const GoogleIntegrationPage = lazy(() => import('../provider-model/pages/GoogleIntegrationPage'));
const DeepSeekIntegrationPage = lazy(
  () => import('../provider-model/pages/DeepSeekIntegrationPage')
);
const XaiIntegrationPage = lazy(() => import('../provider-model/pages/XaiIntegrationPage'));

const integrationRoutes: RouteObject[] = [
  // Trang chính - Hiển thị chỉ integrations của user (swap với my-integrations)
  {
    path: '/integrations/my-integrations',
    element: (
      <MainLayout title={i18n.t('integration:myIntegrations.title', 'Tích hợp của tôi')}>
        <Suspense fallback={<Loading />}>
          <MyIntegrationsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang tất cả integrations - hiển thị tất cả integrations có sẵn (swap với trang chính)
  {
    path: '/integrations',
    element: (
      <MainLayout title={i18n.t('integration:title', 'Tích hợp')}>
        <Suspense fallback={<Loading />}>
          <UserIntegrationManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang tạo cấu hình SMTP
  {
    path: '/integrations/smtp',
    element: (
      <MainLayout title={i18n.t('integration:smtp.title', 'Cấu hình SMTP')}>
        <Suspense fallback={<Loading />}>
          <SMTPIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  // Trang quản lý danh sách email servers
  {
    path: '/integrations/email-servers',
    element: (
      <MainLayout title={i18n.t('integration:emailSMTP.title', 'Quản lý Email Servers')}>
        <Suspense fallback={<Loading />}>
          <EmailServerManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/sms/fpt/manage',
    element: (
      <MainLayout title={i18n.t('integration:sms.fpt.title', 'Quản lý FPT SMS Brandname')}>
        <Suspense fallback={<Loading />}>
          <FptSmsBrandnameManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang quản lý tài khoản đã liên kết
  {
    path: '/integrations/facebook',
    element: (
      <MainLayout title={i18n.t('integration:facebook.title', 'Facebook')}>
        <Suspense fallback={<Loading />}>
          <FacebookIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/facebook-ads',
    element: (
      <MainLayout title="Facebook Ads">
        <Suspense fallback={<Loading />}>
          <FacebookAdsIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/facebook-ads/manage',
    element: (
      <MainLayout title={i18n.t('integration:facebookAds.management.title', 'Quản lý Facebook Ads')}>
        <Suspense fallback={<Loading />}>
          <FacebookAdsIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  {
    path: '/integrations/website',
    element: (
      <MainLayout title={i18n.t('integration:website.title', 'Tích hợp Website')}>
        <Suspense fallback={<Loading />}>
          <WebsiteIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/zalo/oa',
    element: (
      <MainLayout title={i18n.t('integration:zalo.title', 'Zalo')}>
        <Suspense fallback={<Loading />}>
          <ZaloOAManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/zalo-personal',
    element: (
      <MainLayout title={i18n.t('integration:zalo.personal.title', 'Tích hợp Zalo cá nhân')}>
        <Suspense fallback={<Loading />}>
          <ZaloPersonalIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang quản lý tích hợp Database
  {
    path: '/integrations/database',
    element: (
      <MainLayout title={i18n.t('integration:database.title', 'Tích hợp Database')}>
        <Suspense fallback={<Loading />}>
          <DatabaseIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/provider-model',
    element: (
      <MainLayout title={i18n.t('integration:providerModel.title', 'Quản lý API keys Model')}>
        <Suspense fallback={<Loading />}>
          <ProviderModelManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Provider Model Integration Pages
  {
    path: '/integrations/ai/openai',
    element: (
      <MainLayout title={i18n.t('integration:ai.providers.openai.name', 'OpenAI')}>
        <Suspense fallback={<Loading />}>
          <OpenAIIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/ai/openai/manage',
    element: (
      <MainLayout title={i18n.t('integration:openai.title', 'Quản lý OpenAI Keys')}>
        <Suspense fallback={<Loading />}>
          <OpenAIManagePage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/ai/anthropic/manage',
    element: (
      <MainLayout title={i18n.t('integration:anthropic.title', 'Quản lý Anthropic Claude Keys')}>
        <Suspense fallback={<Loading />}>
          <AnthropicManagePage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/ai/gemini/manage',
    element: (
      <MainLayout title={i18n.t('integration:gemini.title', 'Quản lý Google Gemini Keys')}>
        <Suspense fallback={<Loading />}>
          <GeminiManagePage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/ai/deepseek/manage',
    element: (
      <MainLayout title={i18n.t('integration:deepseek.title', 'Quản lý DeepSeek Keys')}>
        <Suspense fallback={<Loading />}>
          <DeepSeekManagePage />
        </Suspense>
      </MainLayout>
    ),
  },

  {
    path: '/integrations/ai/xai-grok/manage',
    element: (
      <MainLayout title={i18n.t('integration:xaiGrok.title', 'Quản lý XAI Grok Keys')}>
        <Suspense fallback={<Loading />}>
          <XAIGrokManagePage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/ai/anthropic',
    element: (
      <MainLayout title={i18n.t('integration:ai.providers.anthropic.name', 'Anthropic')}>
        <Suspense fallback={<Loading />}>
          <AnthropicIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/ai/google',
    element: (
      <MainLayout title={i18n.t('integration:ai.providers.google.name', 'Google AI')}>
        <Suspense fallback={<Loading />}>
          <GoogleIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/ai/deepseek',
    element: (
      <MainLayout title={i18n.t('integration:ai.providers.deepseek.name', 'DeepSeek')}>
        <Suspense fallback={<Loading />}>
          <DeepSeekIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  {
    path: '/integrations/ai/xai',
    element: (
      <MainLayout title={i18n.t('integration:ai.providers.xai.name', 'XAI')}>
        <Suspense fallback={<Loading />}>
          <XaiIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // ACB Banking Integration
  {
    path: '/integrations/banking/acb',
    element: (
      <MainLayout title={i18n.t('integration:banking.acb.title', 'Liên kết tài khoản ACB')}>
        <Suspense fallback={<Loading />}>
          <ACBBankingPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // MB Banking Integration
  {
    path: '/integrations/banking/mb',
    element: (
      <MainLayout title={i18n.t('integration:banking.mb.title', 'Liên kết tài khoản MB Bank')}>
        <Suspense fallback={<Loading />}>
          <MBBankingPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // OCB Banking Integration
  {
    path: '/integrations/banking/ocb',
    element: (
      <MainLayout title={i18n.t('integration:banking.ocb.title', 'Liên kết tài khoản OCB')}>
        <Suspense fallback={<Loading />}>
          <OCBBankingPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Kiên Long Banking Integration
  {
    path: '/integrations/banking/kienlong',
    element: (
      <MainLayout
        title={i18n.t('integration.banking:kienlong.title', 'Liên kết tài khoản Kiên Long Bank')}
      >
        <Suspense fallback={<Loading />}>
          <KienLongBankingPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Bank Account List Page
  {
    path: '/integrations/bank-accounts',
    element: (
      <MainLayout title={i18n.t('integration:bankAccount.title', 'Tài khoản ngân hàng')}>
        <Suspense fallback={<Loading />}>
          <BankAccountListPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Individual Bank Account List Pages
  {
    path: '/integrations/bank-accounts/mb',
    element: (
      <MainLayout title={i18n.t('integration:bankAccount.mb.title', 'Tài khoản MB Bank')}>
        <Suspense fallback={<Loading />}>
          <MBBankAccountListPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/bank-accounts/acb',
    element: (
      <MainLayout title={i18n.t('integration:bankAccount.acb.title', 'Tài khoản ACB Bank')}>
        <Suspense fallback={<Loading />}>
          <ACBBankAccountListPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/bank-accounts/ocb',
    element: (
      <MainLayout title={i18n.t('integration:bankAccount.ocb.title', 'Tài khoản OCB Bank')}>
        <Suspense fallback={<Loading />}>
          <OCBBankAccountListPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/bank-accounts/kienlong',
    element: (
      <MainLayout
        title={i18n.t('integration:bankAccount.kienlong.title', 'Tài khoản Kiên Long Bank')}
      >
        <Suspense fallback={<Loading />}>
          <KLBankAccountListPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Shipping Integration
  {
    path: '/integrations/shipping',
    element: (
      <MainLayout title={i18n.t('integration:shipping.title', 'Quản lý Vận chuyển')}>
        <Suspense fallback={<Loading />}>
          <ShippingIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/ghtk',
    element: (
      <MainLayout title={i18n.t('integration:shipping.ghtk.title', 'Tích hợp GHTK')}>
        <Suspense fallback={<Loading />}>
          <GHTKIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/ghn',
    element: (
      <MainLayout title={i18n.t('integration:shipping.ghn.title', 'Tích hợp GHN')}>
        <Suspense fallback={<Loading />}>
          <GHNIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/ahamove',
    element: (
      <MainLayout title={i18n.t('integration:shipping.ahamove.title', 'Tích hợp Ahamove')}>
        <Suspense fallback={<Loading />}>
          <AhamoveIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Cloud Storage Integration
  {
    path: '/integrations/cloud-storage',
    element: (
      <MainLayout title={i18n.t('integration:cloudStorage.title', 'Tích hợp Cloud Storage')}>
        <Suspense fallback={<Loading />}>
          <CloudStorageIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Enterprise Storage Integration
  {
    path: '/integrations/enterprise-storage',
    element: (
      <MainLayout
        title={i18n.t('integration:enterpriseStorage.title', 'Tích hợp Enterprise Storage')}
      >
        <Suspense fallback={<Loading />}>
          <EnterpriseStorageIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // External Agent Integration
  {
    path: '/integrations/external-agents',
    element: (
      <MainLayout title={i18n.t('integration:externalAgents.title', 'Quản lý External Agents')}>
        <Suspense fallback={<Loading />}>
          <ExternalAgentIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  {
    path: '/integrations/email/outlook',
    element: (
      <MainLayout title={i18n.t('integration:email.providers.outlook', 'Microsoft Outlook')}>
        <Suspense fallback={<Loading />}>
          <EmailProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/email/yahoo',
    element: (
      <MainLayout title={i18n.t('integration:email.providers.yahoo', 'Yahoo Mail')}>
        <Suspense fallback={<Loading />}>
          <EmailProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/email/sendgrid',
    element: (
      <MainLayout title={i18n.t('integration:email.providers.sendgrid', 'SendGrid')}>
        <Suspense fallback={<Loading />}>
          <EmailProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/email/mailchimp',
    element: (
      <MainLayout
        title={i18n.t('integration:email.providers.mailchimp', 'Mailchimp Transactional')}
      >
        <Suspense fallback={<Loading />}>
          <EmailProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/email/amazon-ses',
    element: (
      <MainLayout title={i18n.t('integration:email.providers.amazonSes', 'Amazon SES')}>
        <Suspense fallback={<Loading />}>
          <EmailProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/email/mailgun',
    element: (
      <MainLayout title={i18n.t('integration:email.providers.mailgun', 'Mailgun')}>
        <Suspense fallback={<Loading />}>
          <EmailProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/email/gmail',
    element: (
      <MainLayout title={i18n.t('integration:email.providers.gmail', 'Gmail')}>
        <Suspense fallback={<Loading />}>
          <EmailProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/gmail',
    element: (
      <MainLayout title={i18n.t('integration:gmail.title', 'Gmail Integration')}>
        <Suspense fallback={<Loading />}>
          <GmailIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/gmail/manage',
    element: (
      <MainLayout
        title={i18n.t('integration:gmail.management.title', 'Quản lý Gmail Integrations')}
      >
        <Suspense fallback={<Loading />}>
          <GmailManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // SMS Provider Routes
  {
    path: '/integrations/sms/twilio',
    element: (
      <MainLayout title={i18n.t('integration:sms.twilio.title', 'Tích hợp Twilio SMS')}>
        <Suspense fallback={<Loading />}>
          <SMSProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/sms/fpt',
    element: (
      <MainLayout title={i18n.t('integration:sms.fpt.title', 'FPT SMS Brandname')}>
        <Suspense fallback={<Loading />}>
          <FptSmsIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/sms/vnpt',
    element: (
      <MainLayout title={i18n.t('integration:sms.providers.vnpt', 'VNPT SMS')}>
        <Suspense fallback={<Loading />}>
          <SMSProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Zalo Official Account Connect
  {
    path: '/integrations/social/zalo-oa',
    element: (
      <MainLayout
        title={i18n.t(
          'marketing:zalo.accounts.connect.page.title',
          'Kết nối Zalo Official Account'
        )}
      >
        <Suspense fallback={<Loading />}>
          <ZaloOfficialAccountConnectPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Zalo Official Account Management
  {
    path: '/integrations/social/zalo-oa/management',
    element: (
      <MainLayout title="Quản lý Zalo Official Account">
        <Suspense fallback={<Loading />}>
          <ZaloOAManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Zalo OAuth Callback - nhận parameters từ callback
  {
    path: '/integration/zalo/oa/callback',
    element: (
      <Suspense fallback={<Loading />}>
        <ZaloOAuthCallbackPage />
      </Suspense>
    ),
  },
  {
    path: '/integration/callback',
    element: (
      <MainLayout title="Callback">
        <Suspense fallback={<Loading />}>
          <CallbackPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Google Calendar Integration
  {
    path: '/integrations/calendar/google',
    element: (
      <MainLayout title={i18n.t('integration:calendar.title', 'Google Calendar Integration')}>
        <Suspense fallback={<Loading />}>
          <GoogleCalendarIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/calendar/google/manage',
    element: (
      <MainLayout
        title={i18n.t('integration:googleCalendar.management.title', 'Quản lý Google Calendar')}
      >
        <Suspense fallback={<Loading />}>
          <GoogleCalendarManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Google Ads Integration
  {
    path: '/integrations/google-ads',
    element: (
      <MainLayout title={i18n.t('integration:googleAds.title', 'Google Ads Integration')}>
        <Suspense fallback={<Loading />}>
          <GoogleAdsIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  {
    path: '/integrations/gmail/manage',
    element: (
      <MainLayout
        title={i18n.t('integration:gmail.management.title', 'Quản lý Gmail Integrations')}
      >
        <Suspense fallback={<Loading />}>
          <GmailManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default integrationRoutes;
