/**
 * Export tất cả types của module marketing
 */

// Export PagingResponseDto từ một file duy nhất để tránh trùng lặp
export type { PagingResponseDto } from './google-ads.types';

// Google Ads types
export * from './google-ads.types';

// Facebook Ads types
export type {
  FacebookAdsAccountDto,
  FacebookAdsCampaignDto,
  FacebookAdsAdSetDto,
  FacebookAdsAdDto,
  FacebookAdsInsightDto,
  FacebookAdsAccountQueryDto,
  FacebookAdsCampaignQueryDto,
  FacebookAdsInsightQueryDto,
  CreateFacebookAdsAccountDto,
  UpdateFacebookAdsAccountDto,
  CreateFacebookAdsCampaignDto,
  UpdateFacebookAdsCampaignDto,
} from './facebook-ads.types';
export {
  FacebookAdsAccountStatus,
  FacebookAdsCampaignObjective,
  FacebookAdsCampaignStatus,
  FacebookAdsFormat,
} from './facebook-ads.types';

// SMS Marketing types
export type {
  SmsBrandnameDto,
  SmsTemplateDto,
  SmsMessageDto,
  SmsCampaignDto,
  SmsBrandnameQueryDto,
  SmsTemplateQueryDto,
  SmsMessageQueryDto,
  SmsCampaignQueryDto,
  CreateSmsBrandnameDto,
  UpdateSmsBrandnameDto,
  CreateSmsTemplateDto,
  UpdateSmsTemplateDto,
  SendSmsMessageDto,
  SendBulkSmsMessageDto,
  CreateSmsCampaignDto,
  UpdateSmsCampaignDto,
} from './sms.types';
export {
  SmsBrandnameStatus,
  SmsBrandnameType,
  SmsTemplateStatus,
  SmsMessageStatus,
  SmsCampaignStatus,
} from './sms.types';

// Zalo OA/ZNS types
export type {
  ZaloOAAccountDto,
  ZaloFollowerDto,
  ZaloGroupDto,
  ZaloMessageDto,
  ZaloAttachmentDto,
  ZNSTemplateDto,
  ZNSMessageDto,
  ZNSCampaignDto,
  ZaloOAAccountQueryDto,
  ZaloFollowerQueryDto,
  ZaloGroupQueryDto,
  ZaloMessageQueryDto,
  ZNSTemplateQueryDto,
  CreateZaloOAAccountDto,
  UpdateZaloOAAccountDto,
  CreateZNSTemplateDto,
  SendZNSMessageDto,
  CreateZNSCampaignDto,
  ConnectOfficialAccountDto,
  OfficialAccountResponseDto,
} from './zalo.types';
export {
  ZaloOAStatus,
  ZaloGroupStatus,
  ZNSTemplateStatus,
  ZNSMessageStatus,
  ZaloMessageType,
} from './zalo.types';

// Zalo Personal Campaign types
export type {
  ZaloPersonalCampaignDto,
  ZaloPersonalCampaignQueryDto,
  ZaloPersonalCampaignResponseDto,
  CreateZaloPersonalCampaignDto,
  UpdateZaloPersonalCampaignDto,
} from './zaloPersonalCampaign';
export {
  ZaloPersonalCampaignType,
  ZaloPersonalCampaignStatus,
  ZALO_PERSONAL_CAMPAIGN_TYPE_LABELS,
  ZALO_PERSONAL_CAMPAIGN_STATUS_LABELS,
  ZALO_PERSONAL_CAMPAIGN_STATUS_COLORS,
} from './zaloPersonalCampaign';

// TikTok Ads types
export type {
  TikTokAdsAccountDto,
  TikTokAdsCampaignDto,
  TikTokAdsAdGroupDto,
  TikTokAdsCreativeDto,
  TikTokAdsAudienceDto,
  TikTokAdsTargeting,
  TikTokAdsAccountQueryDto,
  TikTokAdsCampaignQueryDto,
  TikTokAdsAdGroupQueryDto,
  TikTokAdsCreativeQueryDto,
  TikTokAdsAudienceQueryDto,
  CreateTikTokAdsAccountDto,
  UpdateTikTokAdsAccountDto,
  CreateTikTokAdsCampaignDto,
  UpdateTikTokAdsCampaignDto,
  CreateTikTokAdsCreativeDto,
  UpdateTikTokAdsCreativeDto,
  CreateTikTokAdsAudienceDto,
  UpdateTikTokAdsAudienceDto,
  TikTokAdsAccountResponse,
  TikTokAdsCampaignResponse,
  TikTokAdsCreativeResponse,
  TikTokAdsAudienceResponse,
} from './tiktok-ads.types';
export {
  TikTokAdsAccountStatus,
  TikTokAdsCampaignStatus,
  TikTokAdsCampaignObjective,
  TikTokAdsAdGroupStatus,
  TikTokAdsCreativeStatus,
  TikTokAdsCreativeType,
  TikTokAdsAudienceStatus,
} from './tiktok-ads.types';

// Zalo Groups types
export * from './zalo-groups.types';

// Zalo OA Campaigns types
export * from './zalo-oa-campaigns.types';

// Zalo Articles types
export * from './zalo-articles.types';

// Marketing Audiences types
export * from './marketing-audiences.types';

// Các types khác sẽ được thêm vào sau
export * from './segment.types';
export * from './campaign.types';
export * from './tag.types';
export * from './custom-field.types';
export * from './zalo-articles.types';
export type {
  GetZaloArticlesDto as GetZaloArticlesDtoNew,
  ZaloArticleStatus as ZaloArticleStatusNew,
  ZaloArticleType as ZaloArticleTypeNew
} from './zalo-article.types';
export * from './email.types';
export * from './statistics.types';
export * from './template-email.types';
export * from './system-template-email.types';
export * from './gmail.types';

// Zalo Chat types
export * from './zalo-chat.types';
