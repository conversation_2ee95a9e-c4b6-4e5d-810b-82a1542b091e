/**
 * C<PERSON><PERSON> hình cho một phần trong biểu đồ tròn
 */
export interface PieSliceConfig {
  /**
   * Khóa dữ liệu cho tên
   */
  nameKey: string;

  /**
   * Khóa dữ liệu cho giá trị
   */
  valueKey: string;

  /**
   * <PERSON><PERSON><PERSON> sắc của phần (nếu không cung cấp, sẽ sử dụng màu từ colorScheme)
   */
  color?: string;

  /**
   * Có hiển thị nhãn không
   * @default false
   */
  showLabel?: boolean;

  /**
   * Vị trí của nhãn
   * @default 'outside'
   */
  labelPosition?: 'inside' | 'outside';

  /**
   * Định dạng nhãn
   */
  labelFormatter?: (value: number, name: string, entry: Record<string, unknown>) => string;
}

/**
 * Props cho PieChart component
 */
export interface PieChartProps {
  /**
   * Dữ liệu cho biểu đồ
   */
  data: Record<string, unknown>[];

  /**
   * C<PERSON><PERSON> hình cho các phần
   */
  slices: PieSliceConfig[];

  /**
   * Chiều cao của biểu đồ
   * @default 300
   */
  height?: number;

  /**
   * Chiều rộng của biểu đồ (mặc định là 100%)
   * @default '100%'
   */
  width?: number | string;

  /**
   * Có hiển thị tooltip không
   * @default true
   */
  showTooltip?: boolean;

  /**
   * Có hiển thị legend không
   * @default true
   */
  showLegend?: boolean;

  /**
   * Vị trí của legend
   * @default 'bottom'
   */
  legendPosition?: 'top' | 'right' | 'bottom' | 'left';

  /**
   * Margin của biểu đồ
   */
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };

  /**
   * Bán kính trong (cho doughnut chart)
   * @default 0
   */
  innerRadius?: number | string;

  /**
   * Bán kính ngoài
   * @default '80%'
   */
  outerRadius?: number | string;

  /**
   * Góc bắt đầu (độ)
   * @default 0
   */
  startAngle?: number;

  /**
   * Góc kết thúc (độ)
   * @default 360
   */
  endAngle?: number;

  /**
   * Góc xoay (độ)
   * @default 0
   */
  paddingAngle?: number;

  /**
   * Có hiển thị animation không
   * @default true
   */
  animated?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Màu sắc cho các phần (nếu không cung cấp màu cụ thể cho từng phần)
   */
  colorScheme?: string[];

  /**
   * Nội dung hiển thị ở giữa (cho doughnut chart)
   */
  centerContent?: React.ReactNode;

  /**
   * Custom tooltip formatter
   */
  tooltipFormatter?: (value: any, name: string, props: any) => [string, string];

  /**
   * Custom tooltip content component
   */
  tooltipContent?: React.ComponentType<any>;
}
