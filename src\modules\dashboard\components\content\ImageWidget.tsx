import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Input, Icon } from '@/shared/components/common';
import { BaseWidgetProps } from '../../types';

interface ImageWidgetProps extends BaseWidgetProps {
  initialImageUrl?: string;
  editable?: boolean;
  alt?: string;
  objectFit?: 'cover' | 'contain' | 'fill' | 'scale-down';
  borderRadius?: 'none' | 'sm' | 'md' | 'lg' | 'full';
}

/**
 * Widget hiển thị hình ảnh
 */
const ImageWidget: React.FC<ImageWidgetProps> = ({
  className,
  initialImageUrl = '',
  editable = true,
  alt = 'Widget image',
  objectFit = 'cover',
  borderRadius = 'md',
  onPropsChange,
  ...props
}) => {
  const { t } = useTranslation(['dashboard', 'common']);

  // Use imageUrl from props if available, otherwise use initialImageUrl
  const currentImageUrl = (props.imageUrl as string) || initialImageUrl;
  const [imageUrl, setImageUrl] = useState(currentImageUrl);
  const [isEditing, setIsEditing] = useState(false);
  const [tempUrl, setTempUrl] = useState(imageUrl);
  const [imageError, setImageError] = useState(false);

  // Sync with props changes
  useEffect(() => {
    const newImageUrl = (props.imageUrl as string) || initialImageUrl;
    if (newImageUrl !== imageUrl) {
      setImageUrl(newImageUrl);
      setTempUrl(newImageUrl);
    }
  }, [props.imageUrl, initialImageUrl, imageUrl]);

  const handleEdit = useCallback(() => {
    setTempUrl(imageUrl);
    setIsEditing(true);
  }, [imageUrl]);

  const handleSave = useCallback(() => {
    setImageUrl(tempUrl);
    setImageError(false);
    setIsEditing(false);

    // Save to widget props
    if (onPropsChange) {
      onPropsChange({
        imageUrl: tempUrl,
        alt,
        objectFit,
        borderRadius,
      });
    }
  }, [tempUrl, onPropsChange, alt, objectFit, borderRadius]);

  const handleCancel = useCallback(() => {
    setTempUrl(imageUrl);
    setIsEditing(false);
  }, [imageUrl]);

  const handleImageError = useCallback(() => {
    setImageError(true);
  }, []);

  const handleImageLoad = useCallback(() => {
    setImageError(false);
  }, []);

  const borderRadiusClasses = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    full: 'rounded-full',
  };

  const objectFitClasses = {
    cover: 'object-cover',
    contain: 'object-contain',
    fill: 'object-fill',
    'scale-down': 'object-scale-down',
  };

  if (isEditing) {
    return (
      <div className={`w-full h-full p-4 ${className || ''}`}>
        <div className="h-full flex flex-col">
          <div className="mb-3">
            <Typography variant="body2" className="mb-2">
              {t('dashboard:widgets.image.urlLabel', 'URL hình ảnh')}
            </Typography>
            <Input
              value={tempUrl}
              onChange={(e) => setTempUrl(e.target.value)}
              placeholder={t('dashboard:widgets.image.urlPlaceholder', 'https://example.com/image.jpg')}
              className="w-full"
            />
          </div>
          
          {tempUrl && (
            <div className="flex-1 mb-3 min-h-0">
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.image.preview', 'Xem trước')}
              </Typography>
              <div className="w-full h-full border border-border rounded-md overflow-hidden">
                <img
                  src={tempUrl}
                  alt="Preview"
                  className={`w-full h-full ${objectFitClasses[objectFit]}`}
                  onError={() => {}}
                />
              </div>
            </div>
          )}

          <div className="flex justify-end gap-2">
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              {t('common:cancel')}
            </Button>
            <Button variant="primary" size="sm" onClick={handleSave}>
              {t('common:save')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!imageUrl) {
    return (
      <div 
        className={`w-full h-full p-4 ${className || ''} ${editable ? 'cursor-pointer hover:bg-muted/50 transition-colors' : ''}`}
        onClick={editable ? handleEdit : undefined}
      >
        <div className="h-full flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/30 rounded-lg">
          <Icon name="image" size="lg" className="text-muted-foreground mb-2" />
          <Typography variant="body2" className="text-muted-foreground text-center">
            {editable 
              ? t('dashboard:widgets.image.empty', 'Click để thêm hình ảnh')
              : t('dashboard:widgets.image.noImage', 'Không có hình ảnh')
            }
          </Typography>
        </div>
      </div>
    );
  }

  if (imageError) {
    return (
      <div 
        className={`w-full h-full p-4 ${className || ''} ${editable ? 'cursor-pointer hover:bg-muted/50 transition-colors' : ''}`}
        onClick={editable ? handleEdit : undefined}
      >
        <div className="h-full flex flex-col items-center justify-center border border-destructive/30 rounded-lg bg-destructive/5">
          <Icon name="alert-circle" size="lg" className="text-destructive mb-2" />
          <Typography variant="body2" className="text-destructive text-center">
            {t('dashboard:widgets.image.error', 'Không thể tải hình ảnh')}
          </Typography>
          {editable && (
            <Button variant="ghost" size="sm" className="mt-2" onClick={handleEdit}>
              {t('common:edit')}
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`w-full h-full relative group ${className || ''}`}
    >
      <img
        src={imageUrl}
        alt={alt}
        className={`w-full h-full ${objectFitClasses[objectFit]} ${borderRadiusClasses[borderRadius]}`}
        onError={handleImageError}
        onLoad={handleImageLoad}
      />
      {editable && (
        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
          <Button variant="secondary" size="sm" onClick={handleEdit}>
            {t('common:edit')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default ImageWidget;
