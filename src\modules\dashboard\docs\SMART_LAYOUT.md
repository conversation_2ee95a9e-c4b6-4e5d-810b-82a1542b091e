# Smart Layout Mode - Dashboard

## Tổng quan

Smart Layout Mode là tính năng mới cho phép các widget trong dashboard tự động sắp xếp và đẩy lên khi có widget bên trên bị x<PERSON>, gi<PERSON><PERSON> tối ưu hóa không gian hiển thị.

## Vấn đề được giải quyết

**Vấn đề trước đây:**
- Khi xóa widget ở phía trên, các widget bên dưới không tự động đẩy lên
- Tạo ra khoảng trống không cần thiết trong dashboard
- Layout không được tối ưu hóa

**Giải pháp Smart Layout:**
- Tự động compact các widget theo chiều dọc
- Widget bên dưới sẽ tự động đẩy lên khi có khoảng trống
- Layout luôn đư<PERSON><PERSON> tối ưu hóa

## Cách sử dụng

### 1. Trong Dashboard Page (User)

Smart Layout toggle được đặt ở đầu sidebar, bên trá<PERSON> các IconCard khác:

```tsx
// IconCard đầu tiên trong sidebar
<IconCard
  icon={smartLayoutMode ? "grid" : "layout"}
  variant={smartLayoutMode ? "primary" : "ghost"}
  onClick={onToggleSmartLayout}
  title={smartLayoutMode ? t('dashboard:smartLayout.disable') : t('dashboard:smartLayout.enable')}
/>
```

**Icons:**
- `layout`: Khi Smart Layout tắt (layout tự do)
- `grid`: Khi Smart Layout bật (layout compact)

### 2. Trong Admin Dashboard Page

Smart Layout toggle được đặt ở góc phải trên, trước khu vực dashboard grid:

```tsx
<div className="flex justify-end p-2 border-b">
  <IconCard
    icon={smartLayoutMode ? "grid" : "layout"}
    variant={smartLayoutMode ? "primary" : "ghost"}
    onClick={handleToggleSmartLayout}
    title={smartLayoutMode ? t('dashboard:smartLayout.disable') : t('dashboard:smartLayout.enable')}
  />
</div>
```

## Cách hoạt động

### React Grid Layout Configuration

Smart Layout mode thay đổi các thuộc tính của React Grid Layout:

```tsx
// Khi smartLayoutMode = false (mặc định)
compactType={null}
verticalCompact={false}

// Khi smartLayoutMode = true
compactType={'vertical'}
verticalCompact={true}
```

### Behavior

1. **Smart Layout OFF:**
   - Widget giữ nguyên vị trí khi widget khác bị xóa
   - Không có auto-compacting
   - Layout tự do, người dùng kiểm soát hoàn toàn

2. **Smart Layout ON (mặc định):**
   - Widget tự động đẩy lên khi có khoảng trống
   - Vertical compacting được bật
   - Layout luôn được tối ưu hóa

## Translation Keys

### Vietnamese (vi.json)
```json
{
  "dashboard": {
    "smartLayout": {
      "enable": "Bật sắp xếp thông minh",
      "disable": "Tắt sắp xếp thông minh"
    }
  }
}
```

### English (en.json)
```json
{
  "dashboard": {
    "smartLayout": {
      "enable": "Enable smart layout",
      "disable": "Disable smart layout"
    }
  }
}
```

### Chinese (zh.json)
```json
{
  "dashboard": {
    "smartLayout": {
      "enable": "启用智能布局",
      "disable": "禁用智能布局"
    }
  }
}
```

## Component Props Flow

```
DashboardPage
├── smartLayoutMode: boolean (state)
├── handleToggleSmartLayout: () => void
└── DashboardCollapsibleSidebar
    ├── smartLayoutMode: boolean
    ├── onToggleSmartLayout: () => void
    └── DashboardWorkspace
        ├── smartLayoutMode: boolean
        └── DashboardCard
            └── smartLayoutMode: boolean (controls React Grid Layout)
```

## Technical Implementation

### 1. State Management
```tsx
const [smartLayoutMode, setSmartLayoutMode] = useState(true);

const handleToggleSmartLayout = useCallback(() => {
  setSmartLayoutMode(prev => !prev);
}, []);
```

### 2. Props Interface Updates
```tsx
interface DashboardCardProps {
  // ... existing props
  smartLayoutMode?: boolean;
}

interface DashboardWorkspaceProps {
  // ... existing props
  smartLayoutMode?: boolean;
}

interface DashboardCollapsibleSidebarProps {
  // ... existing props
  smartLayoutMode?: boolean;
  onToggleSmartLayout?: () => void;
}
```

### 3. React Grid Layout Integration
```tsx
<ResponsiveGridLayout
  // ... other props
  compactType={smartLayoutMode ? 'vertical' : null}
  verticalCompact={smartLayoutMode}
  // ... other props
>
```

## Best Practices

1. **Default State**: Smart Layout mặc định là ON để tối ưu hóa trải nghiệm người dùng
2. **User Control**: Người dùng có thể toggle bất cứ lúc nào
3. **Visual Feedback**: Icon và variant thay đổi rõ ràng khi toggle
4. **Tooltip**: Hiển thị trạng thái hiện tại và hành động sẽ thực hiện

## Testing

### Manual Testing Steps

1. **Tạo dashboard với nhiều widget:**
   - Thêm 4-5 widget vào dashboard
   - Sắp xếp chúng ở các vị trí khác nhau

2. **Test Smart Layout OFF:**
   - Xóa widget ở giữa
   - Verify: Widget bên dưới không di chuyển

3. **Test Smart Layout ON:**
   - Bật Smart Layout mode
   - Xóa widget ở giữa
   - Verify: Widget bên dưới tự động đẩy lên

4. **Test Toggle:**
   - Toggle giữa ON/OFF nhiều lần
   - Verify: Layout thay đổi tương ứng
   - Verify: Icon và tooltip cập nhật đúng

### Edge Cases

1. **Widget ở bottom**: Không ảnh hưởng khi xóa
2. **Single widget**: Không có behavior khác biệt
3. **Multiple deletions**: Tất cả widget đều compact đúng cách

## Future Enhancements

1. **Persistence**: Lưu trạng thái Smart Layout vào localStorage
2. **Animation**: Thêm smooth transition khi compact
3. **Horizontal Compact**: Hỗ trợ compact theo chiều ngang
4. **Auto-detect**: Tự động bật Smart Layout khi có nhiều khoảng trống
