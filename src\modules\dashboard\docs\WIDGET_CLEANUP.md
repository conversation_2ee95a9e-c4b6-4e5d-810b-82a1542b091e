# Widget Cleanup System

Hệ thống cleanup tự động để xử lý các widget không hợp lệ trong dashboard.

## Vấn đề

<PERSON>hi widget types bị thay đổi hoặc xóa khỏi registry, các widget cũ vẫn có thể tồn tại trong localStorage, gây ra lỗi "Widget Not Found" hoặc "Widget type 'xxx' is not registered".

## Gi<PERSON>i pháp

### 1. Automatic Cleanup

Dashboard sẽ tự động clean up các widget không hợp lệ khi khởi tạo:

```typescript
// Trong DashboardPage.tsx và AdminDashboardPage.tsx
useEffect(() => {
  const hasCleanedUp = cleanupDashboardTabs();
  if (hasCleanedUp) {
    console.log('🧹 Dashboard cleanup completed - page will reload');
    window.location.reload();
  }
}, []);
```

### 2. Manual Cleanup

Có thể chạy cleanup thủ công từ browser console:

```javascript
// Debug dashboard state
window.dashboardScripts.debug();

// Clean up invalid widgets
window.dashboardScripts.cleanup();

// Reset dashboard hoàn toàn
window.dashboardScripts.reset();
```

### 3. Programmatic Cleanup

```typescript
import { cleanupDashboardTabs, debugDashboardWidgets } from '@/modules/dashboard/utils/cleanupWidgets';

// Kiểm tra và clean up
const hasChanges = cleanupDashboardTabs();

// Debug thông tin
debugDashboardWidgets();
```

## API Reference

### `cleanupDashboardTabs()`

Xóa các widget không hợp lệ khỏi localStorage.

**Returns:** `boolean` - `true` nếu có thay đổi, `false` nếu không

### `debugDashboardWidgets()`

Hiển thị thông tin debug về dashboard state.

### `getInvalidWidgetTypes()`

Lấy danh sách các widget types không hợp lệ.

**Returns:** `string[]` - Mảng các widget types không hợp lệ

### `isValidWidgetType(type: string)`

Kiểm tra xem widget type có hợp lệ không.

**Parameters:**
- `type: string` - Widget type cần kiểm tra

**Returns:** `boolean` - `true` nếu hợp lệ

### `resetDashboard()`

Reset dashboard về trạng thái mặc định (xóa tất cả data trong localStorage).

## Workflow

1. **Khởi tạo Dashboard:**
   - Tự động chạy `cleanupDashboardTabs()`
   - Nếu có widget không hợp lệ → cleanup và reload page

2. **Development:**
   - Sử dụng `window.dashboardScripts.debug()` để kiểm tra
   - Sử dụng `window.dashboardScripts.cleanup()` để clean up

3. **Production:**
   - Cleanup tự động chạy mỗi khi load dashboard
   - Không cần can thiệp thủ công

## Logging

Hệ thống sẽ log các thông tin sau:

```
🗑️ Removing invalid widget: chart (Thống kê đơn hàng)
🧹 Cleaned tab "Kinh doanh": 5 → 4 widgets
✅ Dashboard cleanup completed
```

## Best Practices

1. **Khi xóa widget type:**
   - Xóa khỏi `WIDGET_TYPES`
   - Xóa khỏi `widgetConfigs.ts`
   - Cleanup sẽ tự động xử lý data cũ

2. **Khi rename widget type:**
   - Tạo migration script nếu cần
   - Hoặc để cleanup tự động xóa widget cũ

3. **Testing:**
   - Test với data cũ trong localStorage
   - Verify cleanup hoạt động đúng

## Troubleshooting

### Widget vẫn hiển thị lỗi sau cleanup

1. Kiểm tra browser console có log cleanup không
2. Chạy `window.dashboardScripts.debug()` để xem state
3. Thử `window.dashboardScripts.reset()` để reset hoàn toàn

### Cleanup không hoạt động

1. Kiểm tra widget type có trong registry không
2. Verify localStorage có data không
3. Check console có error không

### Performance

Cleanup chỉ chạy:
- Khi khởi tạo dashboard (1 lần)
- Khi có widget không hợp lệ
- Không ảnh hưởng performance runtime
