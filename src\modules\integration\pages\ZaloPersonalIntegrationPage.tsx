import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Container,
  Card,
  Icon,
  ResponsiveGrid,
} from '@/shared/components/common';
import QRCodeDisplay from '../components/QRCodeDisplay';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { useGenerateZaloPersonalQRCode } from '@/modules/marketing/hooks/zalo/useZaloPersonalIntegrations';
import { useZaloQRCodeSSE } from '@/modules/marketing/hooks/zalo/useZaloQRCodeSSE';

/**
 * Trang tích hợp Zalo cá nhân
 */
const ZaloPersonalIntegrationPage: React.FC = () => {
  const { t } = useTranslation(['integration', 'common']);
  const { showNotification } = useSmartNotification();

  // State cho QR code
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [sessionId, setSessionId] = useState<string>('');
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const [hasInitialized, setHasInitialized] = useState<boolean>(false);

  // API hooks
  const generateQRCodeMutation = useGenerateZaloPersonalQRCode();

  // SSE hook để theo dõi trạng thái QR code
  const qrCodeSSE = useZaloQRCodeSSE({
    sessionId,
    autoConnect: true, // Tự động connect khi có sessionId
    onQRScanned: (data) => {
      console.log('QR Code được quét:', data);
      showNotification('info', t('integration:zalo.personal.qrScanned', 'QR Code đã được quét, đang chờ xác nhận...'));
    },
    onLoginSuccess: (data) => {
      console.log('Đăng nhập thành công:', data);
      setConnectionStatus('connected');
      showNotification('success', t('integration:zalo.personal.loginSuccess', 'Đăng nhập Zalo thành công!'));
      qrCodeSSE.disconnect(); // Ngắt kết nối SSE sau khi thành công
    },
    onLoginFailed: (data) => {
      console.log('Đăng nhập thất bại:', data);
      setConnectionStatus('disconnected');
      setQrCodeUrl('');
      showNotification('error', data.message || t('integration:zalo.personal.loginFailed', 'Đăng nhập Zalo thất bại'));
    },
    onExpired: (data) => {
      console.log('QR Code hết hạn:', data);
      setConnectionStatus('disconnected');
      setQrCodeUrl('');
      showNotification('warning', t('integration:zalo.personal.qrExpired', 'QR Code đã hết hạn'));
    },
    onError: (data) => {
      console.log('Lỗi SSE:', data);
      // Không hiển thị notification cho lỗi SSE để tránh spam
      // showNotification('error', data.message || t('integration:zalo.personal.sseError', 'Có lỗi xảy ra khi theo dõi trạng thái'));
    },
  });

  // Tạo QR code thông qua API
  const generateQRCode = useCallback(async () => {
    try {
      console.log('Generating QR code...');
      const response = await generateQRCodeMutation.mutateAsync({});
      console.log('QR code response:', response);

      if (response.code === 200 && response.result) {
        console.log('Setting QR code URL:', response.result.qrCodeBase64);
        setQrCodeUrl(response.result.qrCodeBase64);
        setSessionId(response.result.sessionId);
        setConnectionStatus('connecting');

        showNotification('success', response.result.message || t('integration:zalo.personal.qrGenerated', 'QR Code đã được tạo'));

        // SSE sẽ tự động connect khi có sessionId mới
      } else {
        console.error('Invalid response:', response);
        throw new Error(response.message || 'Failed to generate QR code');
      }
    } catch (error) {
      console.error('Error generating QR code:', error);
      showNotification('error', t('integration:zalo.personal.qrError', 'Không thể tạo QR Code'));
    }
  }, [generateQRCodeMutation, showNotification, t]);

  // Làm mới QR code
  const handleRefreshQR = useCallback(() => {
    generateQRCode();
  }, [generateQRCode]);

  // Thông tin hiển thị cho QR code
  const getQRCodeInfo = () => {
    return ''; // Bỏ text thông tin
  };



  // Auto-generate QR code khi component mount (chỉ một lần)
  useEffect(() => {
    if (!hasInitialized) {
      console.log('Initializing QR code generation...');
      setHasInitialized(true);
      generateQRCode();
    }
  }, [hasInitialized, generateQRCode]);



  // Cleanup SSE khi component unmount
  useEffect(() => {
    return () => {
      console.log('Cleaning up SSE connection...');
      qrCodeSSE.disconnect();
    };
  }, []); // Chỉ cleanup khi unmount, không phụ thuộc vào sessionId

  // Debug log for component re-renders
  useEffect(() => {
    console.log('ZaloPersonalIntegrationPage rendered:', {
      qrCodeUrl: !!qrCodeUrl,
      sessionId,
      connectionStatus,
      timestamp: new Date().toISOString()
    });
  });

  // Debug log for qrCodeUrl changes
  useEffect(() => {
    console.log('QR Code URL changed:', qrCodeUrl);
  }, [qrCodeUrl]);

  const renderConnectionStatus = () => {
    switch (connectionStatus) {
      case 'disconnected':
        return (
          <Card variant="bordered" className="p-4">
            <div className="flex items-center gap-3">
              <Icon name="circle" size="sm" className="text-gray-400" />
              <Typography variant="body2">
                {t('integration:zalo.personal.status.disconnected', 'Chưa kết nối')}
              </Typography>
            </div>
          </Card>
        );
      case 'connecting':
        return (
          <Card variant="bordered" className="p-4">
            <div className="flex items-center gap-3">
              <div className="animate-pulse">
                <Icon name="circle" size="sm" className="text-yellow-500" />
              </div>
              <Typography variant="body2">
                {t('integration:zalo.personal.status.connecting', 'Đang chờ kết nối...')}
              </Typography>
            </div>
          </Card>
        );
      case 'connected':
        return (
          <Card variant="bordered" className="p-4">
            <div className="flex items-center gap-3">
              <Icon name="check-circle" size="sm" className="text-green-500" />
              <Typography variant="body2" className="text-green-600">
                {t('integration:zalo.personal.status.connected', 'Đã kết nối thành công')}
              </Typography>
            </div>
          </Card>
        );
      default:
        return null;
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      <Container className="max-w-6xl mx-auto">
        <div className="flex justify-center">
          <ResponsiveGrid
            maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2 }}
            gap={6}
            className="w-full max-w-4xl"
          >
          {/* QR Code Section */}
          <div className="space-y-4">
            <Typography variant="h6" className="font-medium">
              {t('integration:zalo.personal.qrTitle', 'Quét mã QR để kết nối')}
            </Typography>

            <QRCodeDisplay
              qrCodeUrl={qrCodeUrl}
              additionalInfo={getQRCodeInfo()}
              onRefresh={handleRefreshQR}
              isLoading={generateQRCodeMutation.isPending}
            />
          </div>

          {/* Status and Instructions */}
          <div className="space-y-4">
            <Typography variant="h6" className="font-medium">
              {t('integration:zalo.personal.statusTitle', 'Trạng thái kết nối')}
            </Typography>
            
            {renderConnectionStatus()}

            {/* Instructions */}
            <Card variant="bordered" className="p-4">
              <Typography variant="subtitle2" className="mb-3 font-medium">
                {t('integration:zalo.personal.instructionsTitle', 'Hướng dẫn kết nối')}
              </Typography>
              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <span className="text-primary font-medium">1.</span>
                  <Typography variant="body2">
                    {t('integration:zalo.personal.step1', 'Mở ứng dụng Zalo trên điện thoại')}
                  </Typography>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-primary font-medium">2.</span>
                  <Typography variant="body2">
                    {t('integration:zalo.personal.step2', 'Chọn biểu tượng quét mã QR')}
                  </Typography>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-primary font-medium">3.</span>
                  <Typography variant="body2">
                    {t('integration:zalo.personal.step3', 'Quét mã QR hiển thị bên trái')}
                  </Typography>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-primary font-medium">4.</span>
                  <Typography variant="body2">
                    {t('integration:zalo.personal.step4', 'Xác nhận kết nối trên ứng dụng Zalo')}
                  </Typography>
                </div>
              </div>
            </Card>

            {/* Features */}
            <Card variant="bordered" className="p-4">
              <Typography variant="subtitle2" className="mb-3 font-medium">
                {t('integration:zalo.personal.featuresTitle', 'Tính năng sau khi kết nối')}
              </Typography>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Icon name="check" size="sm" className="text-green-500" />
                  <Typography variant="body2">
                    {t('integration:zalo.personal.feature1', 'Gửi tin nhắn tự động')}
                  </Typography>
                </div>
                <div className="flex items-center gap-2">
                  <Icon name="check" size="sm" className="text-green-500" />
                  <Typography variant="body2">
                    {t('integration:zalo.personal.feature2', 'Quản lý danh sách bạn bè')}
                  </Typography>
                </div>
                <div className="flex items-center gap-2">
                  <Icon name="check" size="sm" className="text-green-500" />
                  <Typography variant="body2">
                    {t('integration:zalo.personal.feature3', 'Tạo chiến dịch marketing')}
                  </Typography>
                </div>
                <div className="flex items-center gap-2">
                  <Icon name="check" size="sm" className="text-green-500" />
                  <Typography variant="body2">
                    {t('integration:zalo.personal.feature4', 'Thống kê hiệu quả')}
                  </Typography>
                </div>
              </div>
            </Card>
          </div>
        </ResponsiveGrid>
        </div>
      </Container>
    </div>
  );
};

export default ZaloPersonalIntegrationPage;
