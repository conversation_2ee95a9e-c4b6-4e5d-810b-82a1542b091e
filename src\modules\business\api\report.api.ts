import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { dateStringToBigInt } from '@/shared/utils/date-form-utils';
import {
  LineChartQueryDto,
  LineChartApiQueryDto,
  LineChartResponseDto,
  ReportOverviewQueryDto,
  ReportOverviewResponseDto,
  SalesChartQueryDto,
  SalesChartResponseDto,
  OrdersChartQueryDto,
  OrdersChartResponseDto,
  CustomersChartQueryDto,
  CustomersChartResponseDto,
  ProductsChartQueryDto,
  ProductsChartResponseDto,
  TopSellingProductsQueryDto,
  TopSellingProductsResponseDto,
  PotentialCustomersQueryDto,
  PotentialCustomersResponseDto,
  ProductTypePieChartQueryDto,
  ProductTypePieChartResponseDto,
} from '../types/report.types';

/**
 * Business Report API Layer
 * Raw API calls without business logic
 */

const BASE_URL = '/user/business/reports';

/**
 * Convert date parameters to bigint timestamps for API calls
 */
const convertDateParamsToTimestamps = <T extends Record<string, any>>(params: T): T => {
  if (!params) return params;

  const convertedParams = { ...params };

  // Convert common date field names
  const dateFields = ['begin', 'end', 'startDate', 'endDate'];

  dateFields.forEach(field => {
    if (
      (convertedParams as Record<string, any>)[field] &&
      typeof (convertedParams as Record<string, any>)[field] === 'string'
    ) {
      const originalValue = (convertedParams as Record<string, any>)[field];
      const timestamp = dateStringToBigInt(originalValue);
      if (timestamp !== null) {
        (convertedParams as Record<string, any>)[field] = timestamp.toString();
      }
    }
  });

  return convertedParams;
};

/**
 * Convert LineChartQueryDto to LineChartApiQueryDto with bigint timestamps
 */
const convertToApiParams = (params: LineChartQueryDto): LineChartApiQueryDto => {
  return convertDateParamsToTimestamps(params) as LineChartApiQueryDto;
};

/**
 * Lấy dữ liệu biểu đồ line-chart chính (API chính cho tất cả biểu đồ business)
 */
export const getLineChart = async (
  params: LineChartQueryDto
): Promise<ApiResponseDto<LineChartResponseDto>> => {
  // Convert date strings to bigint timestamps
  const apiParams = convertToApiParams(params);

  return apiClient.get(`${BASE_URL}/line-chart`, { params: apiParams });
};

/**
 * Lấy dữ liệu tổng quan báo cáo
 */
export const getReportOverview = async (
  params?: ReportOverviewQueryDto
): Promise<ApiResponseDto<ReportOverviewResponseDto>> => {
  // Convert date strings to bigint timestamps
  const apiParams = params ? convertDateParamsToTimestamps(params) : undefined;
  return apiClient.get(`${BASE_URL}/overview`, { params: apiParams });
};

/**
 * Lấy dữ liệu biểu đồ doanh thu
 */
export const getSalesChart = async (
  params?: SalesChartQueryDto
): Promise<ApiResponseDto<SalesChartResponseDto>> => {
  // Convert date strings to bigint timestamps
  const apiParams = params ? convertDateParamsToTimestamps(params) : undefined;
  return apiClient.get(`${BASE_URL}/sales-chart`, { params: apiParams });
};

/**
 * Lấy dữ liệu biểu đồ đơn hàng
 */
export const getOrdersChart = async (
  params?: OrdersChartQueryDto
): Promise<ApiResponseDto<OrdersChartResponseDto>> => {
  // Convert date strings to bigint timestamps
  const apiParams = params ? convertDateParamsToTimestamps(params) : undefined;
  return apiClient.get(`${BASE_URL}/orders-chart`, { params: apiParams });
};

/**
 * Lấy dữ liệu biểu đồ khách hàng
 */
export const getCustomersChart = async (
  params?: CustomersChartQueryDto
): Promise<ApiResponseDto<CustomersChartResponseDto>> => {
  // Convert date strings to bigint timestamps
  const apiParams = params ? convertDateParamsToTimestamps(params) : undefined;
  return apiClient.get(`${BASE_URL}/customers-chart`, { params: apiParams });
};

/**
 * Lấy dữ liệu biểu đồ sản phẩm
 */
export const getProductsChart = async (
  params?: ProductsChartQueryDto
): Promise<ApiResponseDto<ProductsChartResponseDto>> => {
  // Convert date strings to bigint timestamps
  const apiParams = params ? convertDateParamsToTimestamps(params) : undefined;
  return apiClient.get(`${BASE_URL}/products-chart`, { params: apiParams });
};

/**
 * Lấy danh sách sản phẩm bán chạy
 */
export const getTopSellingProducts = async (
  params?: TopSellingProductsQueryDto
): Promise<ApiResponseDto<TopSellingProductsResponseDto>> => {
  // Convert date strings to bigint timestamps
  const apiParams = params ? convertDateParamsToTimestamps(params) : undefined;
  return apiClient.get(`${BASE_URL}/top-selling-products`, { params: apiParams });
};

/**
 * Lấy danh sách khách hàng tiềm năng
 */
export const getPotentialCustomers = async (
  params?: PotentialCustomersQueryDto
): Promise<ApiResponseDto<PotentialCustomersResponseDto>> => {
  // Convert date strings to bigint timestamps
  const apiParams = params ? convertDateParamsToTimestamps(params) : undefined;
  return apiClient.get(`${BASE_URL}/potential-customers`, { params: apiParams });
};

/**
 * Lấy dữ liệu biểu đồ tròn loại sản phẩm
 */
export const getProductTypePieChart = async (
  params?: ProductTypePieChartQueryDto
): Promise<ApiResponseDto<ProductTypePieChartResponseDto>> => {
  return apiClient.get(`${BASE_URL}/product-type-pie-chart`, { params });
};
