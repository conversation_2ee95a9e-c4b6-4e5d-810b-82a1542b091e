/**
 * useChatMessages Hook
 * Quản lý messages và message history
 */

import { useState, useRef, useCallback, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useChatAPI } from '../core/useChatAPI';
import { AuthType } from '@/shared/hooks/useAuthCommon';
// ThreadsService removed - no longer auto-loading first thread here
import type {
  UseChatMessagesConfig,
  UseChatMessagesReturn
} from '../types';
import {
  type Message,
  type QueryMessagesDto,
  type ThreadMessageResponseDto,
  MessageRole,
  AttachmentType
} from '../../types';


/**
 * useChatMessages Hook
 * Quản lý messages state và history loading
 */
export const useChatMessages = (config: UseChatMessagesConfig = {}): UseChatMessagesReturn => {
  const {
    threadId,
    pageSize = 20,
    autoLoad = true,
    enableHistory = true,
    timeout = 5000,
    isNewChatMode = false // ✅ New flag to prevent auto-loading
  } = config;

  // State
  const [messages, setMessages] = useState<Message[]>([]);
  const [historyMessages, setHistoryMessages] = useState<Message[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [hasMoreHistory, setHasMoreHistory] = useState(true);
  const [totalHistoryItems, setTotalHistoryItems] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Refs
  const currentPageRef = useRef(1);
  const loadingRef = useRef(false);

  // Get API service from context (will be provided by parent)
  const chatAPI = useChatAPI({
    authType: AuthType.USER,
    getAuthToken: () => '',
    timeout,
  });

  // Convert ThreadMessageResponseDto to Message
  const convertToMessage = useCallback((dto: ThreadMessageResponseDto): Message => {
    // ✅ Convert role - handle both string and enum cases
    let role: MessageRole;
    switch (dto.role) {
      case 'user':
      case 'USER':
        role = MessageRole.USER;
        break;
      case 'assistant':
      case 'ASSISTANT':
        role = MessageRole.ASSISTANT;
        break;
      case 'thinking':
      case 'THINKING':
        role = MessageRole.THINKING;
        break;
      case 'tool_call_interrup':
      case 'TOOL_CALL_INTERRUP':
        role = MessageRole.TOOL_CALL_INTERRUP;
        break;
      default:
        // ✅ Handle case where API returns MessageRole enum directly
        role = dto.role as MessageRole;
    }

    // Convert attachments
    const attachments = (dto.attachments || []).map(att => ({
      attachmentId: att.attachmentId,
      attachmentType: att.attachmentType as AttachmentType,
      name: att.name,
      viewUrl: att.viewUrl
    }));

    return {
      messageId: dto.messageId,
      messageText: dto.messageText,
      messageCreatedAt: dto.messageCreatedAt,
      role,
      hasAttachments: dto.hasAttachments,
      isToolCallConfirm: dto.isToolCallConfirm || false,
      avatar: dto.avatar,
      attachments,
      replyMessageId: dto.replyMessageId // ✅ Map reply message ID from API response
    };
  }, []);

  // Load message history
  const loadMoreHistory = useCallback(async (): Promise<void> => {
    if (!threadId || !enableHistory || loadingRef.current || !hasMoreHistory) {
      return;
    }

    loadingRef.current = true;
    setIsLoadingHistory(true);
    setError(null);

    try {
      const query: QueryMessagesDto = {
        page: currentPageRef.current,
        limit: pageSize
      };

      const response = await chatAPI.getMessages(threadId, query);

      // ✅ Validate response structure with fallback
      if (!response) {
        throw new Error('No response from getMessages API');
      }

      // ✅ Handle case where response.items is undefined
      const items = response.items || [];
      const meta = response.meta || { totalItems: 0, currentPage: 1, itemsPerPage: pageSize, totalPages: 0, itemCount: 0 };

      if (items.length > 0) {
        const newMessages = items.map(convertToMessage);

        setHistoryMessages(prev => [...prev, ...newMessages]);
        setTotalHistoryItems(meta.totalItems);
        setHasMoreHistory(items.length === pageSize);

        currentPageRef.current += 1;
      } else {
        setHasMoreHistory(false);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load message history';
      setError(errorMessage);
      setHasMoreHistory(false); // ✅ Stop trying to load more on error
    } finally {
      setIsLoadingHistory(false);
      loadingRef.current = false;
    }
  }, [threadId, enableHistory, hasMoreHistory, pageSize, convertToMessage]); // ❌ Remove chatAPI dependency to prevent infinite loop

  // Refresh history (reload from beginning) - break circular dependency
  const refreshHistory = useCallback(async (): Promise<void> => {
    if (!threadId || !enableHistory) return;

    currentPageRef.current = 1;
    setHistoryMessages([]);
    setHasMoreHistory(true);
    setTotalHistoryItems(0);

    // Call loadMoreHistory directly without dependency
    if (!loadingRef.current) {
      loadingRef.current = true;
      setIsLoadingHistory(true);

      try {
        const response = await chatAPI.getMessages(threadId, {
          page: currentPageRef.current,
          limit: pageSize,
          sortDirection: 'DESC'
        });

        if (response?.items) {
          const convertedMessages = response.items.map(convertToMessage);
          setHistoryMessages(convertedMessages);
          setTotalHistoryItems(response.meta?.totalItems || 0);
          setHasMoreHistory(convertedMessages.length === pageSize);
          currentPageRef.current = 2; // Next page
        }
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to load history');
      } finally {
        setIsLoadingHistory(false);
        loadingRef.current = false;
      }
    }
  }, [threadId, enableHistory, pageSize, convertToMessage, chatAPI]); // ✅ Remove timeout and loadMoreHistory dependency

  // Add message
  const addMessage = useCallback((message: Message) => {
    setMessages(prev => [...prev, message]);
  }, []);

  // Update message
  const updateMessage = useCallback((messageId: string, updates: Partial<Message>) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.messageId === messageId ? { ...msg, ...updates } : msg
      )
    );

    // Also update in history if exists
    setHistoryMessages(prev =>
      prev.map(msg =>
        msg.messageId === messageId ? { ...msg, ...updates } : msg
      )
    );
  }, []);

  // Remove message
  const removeMessage = useCallback((messageId: string) => {
    setMessages(prev => prev.filter(msg => msg.messageId !== messageId));
    setHistoryMessages(prev => prev.filter(msg => msg.messageId !== messageId));
  }, []);

  // Remove all messages after a specific message (for edit functionality)
  const removeMessagesAfter = useCallback((messageId: string) => {
    setMessages(prev => {
      const messageIndex = prev.findIndex(msg => msg.messageId === messageId);
      if (messageIndex === -1) return prev;
      // Keep messages up to and including the target message
      return prev.slice(0, messageIndex + 1);
    });
    setHistoryMessages(prev => {
      const messageIndex = prev.findIndex(msg => msg.messageId === messageId);
      if (messageIndex === -1) return prev;
      // Keep messages up to and including the target message
      return prev.slice(0, messageIndex + 1);
    });
  }, []);

  // Get message by ID
  const getMessageById = useCallback((messageId: string): Message | null => {
    // Search in current messages first
    const currentMessage = messages.find(msg => msg.messageId === messageId);
    if (currentMessage) return currentMessage;

    // Search in history messages
    const historyMessage = historyMessages.find(msg => msg.messageId === messageId);
    return historyMessage || null;
  }, [messages, historyMessages]);

  // Clear messages
  const clearMessages = useCallback(() => {
    setMessages([]);
    setHistoryMessages([]);
    setError(null);
    currentPageRef.current = 1;
    setHasMoreHistory(true);
    setTotalHistoryItems(0);
    lastLoadedThreadIdRef.current = null;
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Auto-load history when threadId changes - ONLY load messages if threadId exists
  // Use ref to track last loaded threadId to prevent duplicate calls
  const lastLoadedThreadIdRef = useRef<string | null>(null);

  useEffect(() => {
    const shouldAutoLoad = autoLoad && enableHistory && threadId && !isNewChatMode;

    // ✅ Don't auto-load messages when in new chat mode
    if (shouldAutoLoad) {
      // Prevent loading same thread multiple times
      if (threadId === lastLoadedThreadIdRef.current || loadingRef.current) {
        return;
      }

      // Only load messages if we have a specific threadId
      loadingRef.current = true;
      setIsLoadingHistory(true);
      currentPageRef.current = 1;
      setHistoryMessages([]);
      setHasMoreHistory(true);
      setTotalHistoryItems(0);
      lastLoadedThreadIdRef.current = threadId; // Mark as loading

      const loadMessagesForThread = async (targetThreadId: string, maxRetries: number = 3) => {

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
          try {
            const response = await chatAPI.getMessages(targetThreadId, {
              page: 1,
              limit: pageSize,
              sortDirection: 'DESC'
            });

            if (response?.items) {
              const convertedMessages = response.items.map(convertToMessage);
              setHistoryMessages(convertedMessages);
              setTotalHistoryItems(response.meta?.totalItems || 0);
              setHasMoreHistory(convertedMessages.length === pageSize);
              currentPageRef.current = 2; // Next page
            }
            return; // Success, exit retry loop

          } catch (error) {
            console.warn(`Failed to load messages (attempt ${attempt}/${maxRetries}):`, error);

            if (attempt === maxRetries) {
              // All retries failed
              setError(error instanceof Error ? error.message : 'Failed to load history');
              lastLoadedThreadIdRef.current = null; // Reset on error to allow retry
            } else {
              // Wait before retry with exponential backoff
              const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }
        }
      };

      // Load messages for the provided threadId
      loadMessagesForThread(threadId).finally(() => {
        setIsLoadingHistory(false);
        loadingRef.current = false;
      });
    } else if (!threadId) {
      // Clear messages when no threadId (don't auto-load first thread)
      setHistoryMessages([]);
      setHasMoreHistory(false);
      setTotalHistoryItems(0);
      setIsLoadingHistory(false);
      loadingRef.current = false;
      lastLoadedThreadIdRef.current = null;
    }
  }, [threadId, autoLoad, enableHistory, pageSize, isNewChatMode]); // ✅ Add isNewChatMode dependency

  // ✅ Cleanup on unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      // Clear any pending API calls or timeouts
      loadingRef.current = false;
      lastLoadedThreadIdRef.current = null;
    };
  }, []);

  // ✅ Fix: Clear all messages when switching to different thread
  const previousThreadIdRef = useRef<string | null>(null);

  useEffect(() => {
    // Clear messages when switching to a different thread (including null)
    if (previousThreadIdRef.current !== null && previousThreadIdRef.current !== threadId) {
      setMessages([]);
      setHistoryMessages([]);
      setError(null);
      currentPageRef.current = 1;
      setHasMoreHistory(true);
      setTotalHistoryItems(0);
      lastLoadedThreadIdRef.current = null;
    }
    previousThreadIdRef.current = threadId || null;
  }, [threadId]);

  return {
    // Messages State
    messages,
    historyMessages,
    isLoadingHistory,
    hasMoreHistory,
    totalHistoryItems,
    
    // Operations
    addMessage,
    updateMessage,
    removeMessage,
    removeMessagesAfter,
    loadMoreHistory,
    
    // Utils
    getMessageById,
    clearMessages,
    refreshHistory,
    
    // State
    error,
    clearError
  };
};

/**
 * ✅ NEW: React Query-based hook for chat messages
 * Replaces manual useEffect API calls with React Query for better caching and deduplication
 */
export const useChatMessagesQuery = (config: UseChatMessagesConfig = {}): UseChatMessagesReturn => {
  const {
    threadId,
    pageSize = 20,
    autoLoad = true,
    enableHistory = true,
    timeout = 5000
  } = config;

  // Get API service
  const chatAPI = useChatAPI({
    authType: AuthType.USER,
    getAuthToken: () => '',
    timeout,
  });

  // Convert ThreadMessageResponseDto to Message (reuse existing function)
  const convertToMessage = useCallback((dto: ThreadMessageResponseDto): Message => {
    // ✅ Convert role - handle both string and enum cases
    let role: MessageRole;
    switch (dto.role) {
      case 'user':
      case 'USER':
        role = MessageRole.USER;
        break;
      case 'assistant':
      case 'ASSISTANT':
        role = MessageRole.ASSISTANT;
        break;
      default:
        role = MessageRole.USER; // fallback
    }

    // ✅ Convert attachments with proper type mapping
    const attachments = dto.attachments?.map(att => ({
      attachmentId: att.attachmentId,
      attachmentType: att.attachmentType,
      name: att.name,
      viewUrl: att.viewUrl
    })) || [];

    return {
      messageId: dto.messageId,
      messageText: dto.messageText || '',
      role,
      messageCreatedAt: dto.messageCreatedAt,
      hasAttachments: dto.hasAttachments,
      attachments,
      isToolCallConfirm: dto.isToolCallConfirm || false,
      avatar: dto.avatar,
      replyMessageId: dto.replyMessageId // ✅ Map reply message ID from streaming response
    };
  }, []);

  // Local state for streaming messages (not from history)
  const [messages, setMessages] = useState<Message[]>([]);
  const [error, setError] = useState<string | null>(null);

  // ✅ Use React Query for history messages with caching and deduplication
  const historyQuery = useQuery({
    queryKey: ['chat-messages', threadId, { page: 1, limit: pageSize }],
    queryFn: async () => {
      if (!threadId) throw new Error('No thread ID provided');

      const response = await chatAPI.getMessages(threadId, {
        page: 1,
        limit: pageSize,
        sortDirection: 'DESC'
      });

      return response;
    },
    enabled: !!threadId && enableHistory && autoLoad,
    // ✅ OPTIMIZATION: Caching settings to prevent duplicate API calls
    staleTime: 2 * 60 * 1000, // 2 minutes - messages stay fresh for 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes - cache persists for 5 minutes
    refetchOnWindowFocus: false, // Don't refetch when window gains focus
    refetchOnMount: false, // Don't refetch on component mount if data exists
    refetchOnReconnect: true, // Refetch when network reconnects
    retry: 1, // Only retry once to avoid multiple calls
    retryDelay: 1000, // 1 second delay between retries
  });

  // Computed values from React Query
  const historyMessages = historyQuery.data?.items?.map(convertToMessage) || [];
  const isLoadingHistory = historyQuery.isLoading;
  const totalHistoryItems = historyQuery.data?.meta?.totalItems || 0;
  const hasMoreHistory = false; // Simplified for now - can add infinite query later

  // Load more history function (simplified for now)
  const loadMoreHistory = useCallback(async () => {
    // TODO: Implement infinite loading later
    console.log('Load more history not implemented yet');
  }, []);

  // Other functions (keeping the same interface)
  const addMessage = useCallback((message: Message) => {
    setMessages(prev => [...prev, message]);
  }, []);

  const updateMessage = useCallback((messageId: string, updates: Partial<Message>) => {
    setMessages(prev => prev.map(msg =>
      msg.messageId === messageId ? { ...msg, ...updates } : msg
    ));
  }, []);

  const removeMessage = useCallback((messageId: string) => {
    setMessages(prev => prev.filter(msg => msg.messageId !== messageId));
  }, []);

  // Remove all messages after a specific message (for edit functionality)
  const removeMessagesAfter = useCallback((messageId: string) => {
    setMessages(prev => {
      const messageIndex = prev.findIndex(msg => msg.messageId === messageId);
      if (messageIndex === -1) return prev;
      // Keep messages up to and including the target message
      return prev.slice(0, messageIndex + 1);
    });
  }, []);

  const getMessageById = useCallback((messageId: string): Message | undefined => {
    return messages.find(msg => msg.messageId === messageId) ||
           historyMessages.find(msg => msg.messageId === messageId);
  }, [messages, historyMessages]);

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  const refreshHistory = useCallback(async () => {
    await historyQuery.refetch();
  }, [historyQuery]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // Messages
    messages,
    historyMessages,

    // History state
    isLoadingHistory,
    hasMoreHistory,
    totalHistoryItems,

    // History operations
    loadMoreHistory,

    // Message operations
    addMessage,
    updateMessage,
    removeMessage,
    removeMessagesAfter,
    getMessageById: getMessageById as (messageId: string) => Message | null,
    clearMessages,
    refreshHistory,

    // State
    error: error || (historyQuery.error as Error)?.message || null,
    clearError
  };
};
