/**
 * Zalo Chat Constants
 * Các constants cho module chat Zalo
 */

/**
 * API endpoints
 */
export const ZALO_CHAT_ENDPOINTS = {
  ACCOUNTS: '/zalo/accounts',
  CONTACTS: '/zalo/contacts',
  CONVERSATIONS: '/zalo/conversations',
  MESSAGES: '/zalo/messages',
  SEND_MESSAGE: '/zalo/messages/send',
  UPLOAD_ATTACHMENT: '/zalo/attachments/upload',
  MARK_AS_READ: '/zalo/messages/mark-read',
} as const;

/**
 * Message types
 */
export const MESSAGE_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  FILE: 'file',
  STICKER: 'sticker',
  LOCATION: 'location',
} as const;

/**
 * Message status
 */
export const MESSAGE_STATUS = {
  SENDING: 'sending',
  SENT: 'sent',
  DELIVERED: 'delivered',
  READ: 'read',
  FAILED: 'failed',
} as const;

/**
 * Sender types
 */
export const SENDER_TYPES = {
  USER: 'user',
  CONTACT: 'contact',
} as const;

/**
 * Account types
 */
export const ACCOUNT_TYPES = {
  OA: 'oa',
  PERSONAL: 'personal',
} as const;

/**
 * Chat event types
 */
export const CHAT_EVENT_TYPES = {
  MESSAGE_RECEIVED: 'message_received',
  MESSAGE_SENT: 'message_sent',
  MESSAGE_READ: 'message_read',
  CONTACT_ONLINE: 'contact_online',
  CONTACT_OFFLINE: 'contact_offline',
  TYPING_START: 'typing_start',
  TYPING_STOP: 'typing_stop',
} as const;

/**
 * Pagination defaults
 */
export const PAGINATION_DEFAULTS = {
  PAGE: 1,
  LIMIT: 20,
  MESSAGES_LIMIT: 50,
  CONTACTS_LIMIT: 20,
} as const;

/**
 * File upload limits
 */
export const FILE_UPLOAD_LIMITS = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_FILES: 5,
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_FILE_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
  ],
} as const;

/**
 * UI constants
 */
export const UI_CONSTANTS = {
  SIDEBAR_WIDTH: '30%',
  CHAT_AREA_WIDTH: '70%',
  MESSAGE_BUBBLE_MAX_WIDTH: '70%',
  AVATAR_SIZE: 40,
  CONTACT_AVATAR_SIZE: 48,
  TYPING_TIMEOUT: 3000, // 3 seconds
  AUTO_SCROLL_THRESHOLD: 100, // pixels from bottom
} as const;

/**
 * Local storage keys
 */
export const STORAGE_KEYS = {
  SELECTED_ZALO_ACCOUNT: 'zalo_chat_selected_account',
  SELECTED_CONTACT: 'zalo_chat_selected_contact',
  CHAT_SETTINGS: 'zalo_chat_settings',
  DRAFT_MESSAGES: 'zalo_chat_draft_messages',
} as const;

/**
 * Default chat settings
 */
export const DEFAULT_CHAT_SETTINGS = {
  autoReply: false,
  autoReplyMessage: 'Xin chào! Cảm ơn bạn đã liên hệ. Chúng tôi sẽ phản hồi sớm nhất có thể.',
  notificationEnabled: true,
  soundEnabled: true,
  showOnlineStatus: true,
} as const;

/**
 * Error messages
 */
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Lỗi kết nối mạng',
  UNAUTHORIZED: 'Không có quyền truy cập',
  ACCOUNT_NOT_FOUND: 'Không tìm thấy tài khoản Zalo',
  CONTACT_NOT_FOUND: 'Không tìm thấy liên hệ',
  MESSAGE_SEND_FAILED: 'Gửi tin nhắn thất bại',
  FILE_TOO_LARGE: 'File quá lớn',
  FILE_TYPE_NOT_SUPPORTED: 'Loại file không được hỗ trợ',
  CONVERSATION_NOT_FOUND: 'Không tìm thấy cuộc trò chuyện',
} as const;

/**
 * Success messages
 */
export const SUCCESS_MESSAGES = {
  MESSAGE_SENT: 'Tin nhắn đã được gửi',
  FILE_UPLOADED: 'File đã được tải lên',
  CONTACT_UPDATED: 'Liên hệ đã được cập nhật',
  SETTINGS_SAVED: 'Cài đặt đã được lưu',
} as const;

/**
 * Regex patterns
 */
export const REGEX_PATTERNS = {
  PHONE: /^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  URL: /^https?:\/\/.+/,
} as const;

/**
 * Time formats
 */
export const TIME_FORMATS = {
  MESSAGE_TIME: 'HH:mm',
  MESSAGE_DATE: 'dd/MM/yyyy',
  FULL_DATETIME: 'HH:mm dd/MM/yyyy',
  ISO_DATE: 'yyyy-MM-dd',
} as const;

/**
 * Animation durations (in milliseconds)
 */
export const ANIMATION_DURATIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  MESSAGE_APPEAR: 200,
  TYPING_INDICATOR: 1000,
} as const;

/**
 * Breakpoints for responsive design
 */
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
} as const;

/**
 * Z-index values
 */
export const Z_INDEX = {
  DROPDOWN: 1000,
  MODAL: 1050,
  TOOLTIP: 1100,
  NOTIFICATION: 1200,
} as const;
