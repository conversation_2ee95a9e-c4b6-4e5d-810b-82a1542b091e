/**
 * T<PERSON>h ngày đầu tuần (thứ 2) từ năm và số tuần ISO
 */
const getWeekStartDate = (year: number, week: number): Date => {
  // Ngày 4 tháng 1 luôn thuộc tuần đầu tiên của năm theo ISO
  const jan4 = new Date(year, 0, 4);

  // Tìm thứ 2 của tuần đầu tiên
  const firstMonday = new Date(jan4);
  firstMonday.setDate(jan4.getDate() - ((jan4.getDay() + 6) % 7));

  // Tính ngày đầu tuần cần tìm
  const targetWeekStart = new Date(firstMonday);
  targetWeekStart.setDate(firstMonday.getDate() + (week - 1) * 7);

  return targetWeekStart;
};

/**
 * Format period label từ API với đa ngôn ngữ
 * - 2025-W30 -> 21/07 - 27/07/2025 (thứ 2 đến chủ nhật)
 * - 2025-03 -> Tháng 3, 2025
 * - 2025-03-15 -> 15/03/2025
 */
export const formatPeriodLabel = (label: string, t: any): string => {
  if (!label) return '';

  // Xử lý format tuần: 2025-W30
  if (label.includes('-W')) {
    const [year, weekPart] = label.split('-W');
    const weekNumber = parseInt(weekPart);
    const yearNum = parseInt(year);

    // Tính ngày đầu tuần (thứ 2) và cuối tuần (chủ nhật)
    const weekStart = getWeekStartDate(yearNum, weekNumber);
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);

    // Format: 21/07 - 27/07/2025
    const startStr = weekStart.toLocaleDateString('vi-VN', { day: '2-digit', month: '2-digit' });
    const endStr = weekEnd.toLocaleDateString('vi-VN', { day: '2-digit', month: '2-digit' });

    return `${startStr} - ${endStr}/${yearNum}`;
  }

  // Xử lý format tháng: 2025-03
  if (/^\d{4}-\d{2}$/.test(label)) {
    const [year, month] = label.split('-');
    const monthNames = [
      t('dashboard:periods.month1', 'Tháng 1'), t('dashboard:periods.month2', 'Tháng 2'), 
      t('dashboard:periods.month3', 'Tháng 3'), t('dashboard:periods.month4', 'Tháng 4'), 
      t('dashboard:periods.month5', 'Tháng 5'), t('dashboard:periods.month6', 'Tháng 6'),
      t('dashboard:periods.month7', 'Tháng 7'), t('dashboard:periods.month8', 'Tháng 8'), 
      t('dashboard:periods.month9', 'Tháng 9'), t('dashboard:periods.month10', 'Tháng 10'), 
      t('dashboard:periods.month11', 'Tháng 11'), t('dashboard:periods.month12', 'Tháng 12')
    ];
    return `${monthNames[parseInt(month) - 1]}, ${year}`;
  }

  // Xử lý format ngày: 2025-03-15
  if (/^\d{4}-\d{2}-\d{2}$/.test(label)) {
    const date = new Date(label);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  // Trả về label gốc nếu không match format nào
  return label;
};

/**
 * Default period formatter function
 */
export const defaultPeriodFormatter = formatPeriodLabel;
