import React, { useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON> as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Toolt<PERSON>,
  Legend,
  ResponsiveContainer,
  Label,
} from 'recharts';
import { PieChartProps } from './PieChart.types';
import { useTheme } from '@/shared/contexts/theme';

/**
 * PieChart component hiển thị dữ liệu dạng tròn
 *
 * @example
 * ```tsx
 * <PieChart
 *   data={[
 *     { name: 'Group A', value: 400 },
 *     { name: 'Group B', value: 300 },
 *     { name: 'Group C', value: 300 },
 *     { name: 'Group D', value: 200 },
 *   ]}
 *   slices={[
 *     { nameKey: 'name', valueKey: 'value' }
 *   ]}
 *   height={300}
 *   showTooltip
 *   showLegend
 * />
 * ```
 */
const PieChart: React.FC<PieChartProps> = ({
  data,
  slices,
  height = 300,
  width = '100%',
  showTooltip = true,
  showLegend = true,
  legendPosition = 'bottom',
  margin = { top: 20, right: 30, left: 20, bottom: 20 },
  innerRadius = 0,
  outerRadius = '80%',
  startAngle = 0,
  endAngle = 360,
  paddingAngle = 0,
  animated = true,
  className = '',
  colorScheme,
  centerContent,
  tooltipFormatter,
  tooltipContent,
}) => {
  const { currentTheme } = useTheme();

  // Lấy màu từ theme
  const textColor = useMemo(
    () => currentTheme?.semanticColors?.foreground || '#111827',
    [currentTheme]
  );

  // Tạo màu mặc định nếu không có colorScheme
  const defaultColors = useMemo(() => {
    if (colorScheme) return colorScheme;

    return [
      currentTheme?.semanticColors?.primary || '#3B82F6',
      currentTheme?.semanticColors?.secondary || '#10B981',
      currentTheme?.semanticColors?.accent || '#F59E0B',
      currentTheme?.semanticColors?.info || '#3B82F6',
      currentTheme?.semanticColors?.success || '#10B981',
      currentTheme?.semanticColors?.warning || '#F59E0B',
      currentTheme?.semanticColors?.destructive || '#EF4444',
      '#8B5CF6', // Purple
      '#EC4899', // Pink
      '#6366F1', // Indigo
    ];
  }, [colorScheme, currentTheme]);

  // Tạo các phần tròn
  const renderPies = useMemo(() => {
    return slices.map((slice, sliceIndex) => {
      const { nameKey, valueKey, showLabel, labelPosition } = slice;

      return (
        <Pie
          key={`pie-${sliceIndex}`}
          data={data}
          nameKey={nameKey}
          dataKey={valueKey}
          cx="50%"
          cy="50%"
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          startAngle={startAngle}
          endAngle={endAngle}
          paddingAngle={paddingAngle}
          isAnimationActive={animated}
          animationBegin={0}
          animationDuration={800}
          animationEasing="ease-out"
          label={showLabel ? true : false}
          labelLine={showLabel && labelPosition === 'outside'}
        >
          {data.map((_, index) => (
            <Cell
              key={`cell-${index}`}
              fill={slice.color || defaultColors[index % defaultColors.length]}
              stroke="rgba(255, 255, 255, 0.8)"
              strokeWidth={1}
              style={{
                filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1))',
                cursor: 'pointer',
                transition: 'all 0.2s ease-in-out'
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLElement;
                if (target && target.style) {
                  target.style.filter = 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2)) brightness(1.1)';
                  target.style.transform = 'scale(1.02)';
                }
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLElement;
                if (target && target.style) {
                  target.style.filter = 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1))';
                  target.style.transform = 'scale(1)';
                }
              }}
            />
          ))}
          {centerContent && (
            <Label
              content={() => (
                <foreignObject x="0" y="0" width="100%" height="100%">
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '100%',
                      height: '100%',
                    }}
                  >
                    {centerContent}
                  </div>
                </foreignObject>
              )}
              position="center"
            />
          )}
        </Pie>
      );
    });
  }, [
    slices,
    data,
    innerRadius,
    outerRadius,
    startAngle,
    endAngle,
    paddingAngle,
    animated,
    defaultColors,
    centerContent,
  ]);

  // Custom tooltip formatter
  const defaultTooltipFormatter = (value: any, name: string, props: any) => {
    const { payload } = props;
    const formattedValue = typeof value === 'number' ? value.toLocaleString('vi-VN') : value;

    // Hiển thị cả giá trị và phần trăm nếu có
    if (payload?.percentage !== undefined) {
      return [
        `${formattedValue} (${payload.percentage.toFixed(1)}%)`,
        name
      ];
    }

    return [formattedValue, name];
  };

  // Custom tooltip content
  const DefaultTooltipContent = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3 min-w-[120px]">
          <p className="text-sm font-medium text-foreground mb-1">
            {data.name || label}
          </p>
          <p className="text-sm text-muted-foreground">
            Giá trị: <span className="font-medium text-foreground">{data.value?.toLocaleString('vi-VN')}</span>
          </p>
          {data.payload && typeof data.payload === 'object' && 'percentage' in data.payload && (
            <p className="text-sm text-muted-foreground">
              Tỷ lệ: <span className="font-medium text-foreground">{data.payload.percentage.toFixed(1)}%</span>
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  // Sử dụng custom tooltip content hoặc default
  const TooltipContent = tooltipContent || DefaultTooltipContent;

  // Tạo component chính
  const chart = (
    <RechartsPieChart margin={margin} className={className}>
      {showTooltip && (
        <Tooltip
          content={<TooltipContent />}
          formatter={tooltipFormatter || defaultTooltipFormatter}
          wrapperStyle={{
            zIndex: 9999,
            outline: 'none',
            border: 'none'
          }}
        />
      )}

      {showLegend && (
        <Legend
          verticalAlign={
            legendPosition === 'top' || legendPosition === 'bottom' ? legendPosition : 'middle'
          }
          align={
            legendPosition === 'left' || legendPosition === 'right' ? legendPosition : 'center'
          }
          wrapperStyle={{
            color: textColor,
            fontSize: '12px',
            fontWeight: '500'
          }}
          formatter={(value, entry: any) => {
            const payload = entry?.payload;
            if (payload && typeof payload === 'object' && 'percentage' in payload) {
              return `${value} (${payload.percentage.toFixed(1)}%)`;
            }
            return value;
          }}
        />
      )}

      {renderPies}
    </RechartsPieChart>
  );

  // Sử dụng div với kích thước cố định và ResponsiveContainer
  return (
    <div style={{ width, height, position: 'relative', minHeight: '300px' }}>
      {/* Sử dụng aspect ratio để đảm bảo biểu đồ luôn hiển thị */}
      <ResponsiveContainer width="100%" height="100%" aspect={1}>
        {chart}
      </ResponsiveContainer>
    </div>
  );
};

export default PieChart;
