import React from 'react';
import {
  HardDrive,
  Hash,
  Users,
  DollarSign,
  BarChart3,
  Activity,
  Target,
  Zap,
  Globe,
  Package,
  Building2,
  PieChart,
  Settings,
  Type,
  ImageIcon,
  Clock,
  Quote,
  Code,
  ExternalLink,
  Play,
  Cloud,
  Calendar,
  CheckSquare,
  TrendingUp,
  Table,
} from 'lucide-react';
import { type WidgetConfig } from '../types';
import { WIDGET_TYPES, WIDGET_CATEGORIES } from '../constants';

// Sử dụng dynamic imports với proper default export handling
const DataCountWidget = React.lazy(() =>
  import('../components/data/DataCountWidget').then(module => ({ default: module.default }))
);
const DataStorageWidget = React.lazy(() =>
  import('../components/data/DataStorageWidget').then(module => ({ default: module.default }))
);
const BusinessOverviewWidget = React.lazy(() =>
  import('../components/business/BusinessOverviewWidget').then(module => ({
    default: module.default,
  }))
);
const CustomerProductsWidget = React.lazy(() =>
  import('../components/business/CustomerProductsWidget').then(module => ({
    default: module.default,
  }))
);
const CustomerListWidget = React.lazy(() =>
  import('../components/business/CustomerListWidget').then(module => ({
    default: module.default,
  }))
);
const BankAccountOverviewWidget = React.lazy(() =>
  import('../components/business/BankAccountOverviewWidget').then(module => ({
    default: module.default,
  }))
);
const CustomFieldFormWidget = React.lazy(() =>
  import('../components/business/CustomFieldFormWidget').then(module => ({
    default: module.default,
  }))
);
// Widgets sử dụng API line-chart chính - Đã được thay thế bằng BusinessMultiLineChartWidget
const BusinessMultiLineChartWidget = React.lazy(() =>
  import('../components/business/BusinessMultiLineChartWidget').then(module => {
    console.log('✅ BusinessMultiLineChartWidget loaded:', module);
    return {
      default: module.default,
    };
  }).catch(error => {
    console.error('❌ Error loading BusinessMultiLineChartWidget:', error);
    throw error;
  })
);

const ProductTypePieChartWidget = React.lazy(() =>
  import('../components/business/ProductTypePieChartWidget').then(module => {
    console.log('✅ ProductTypePieChartWidget loaded:', module);
    return {
      default: module.default,
    };
  }).catch(error => {
    console.error('❌ Error loading ProductTypePieChartWidget:', error);
    throw error;
  })
);

const MarketingOverviewWidget = React.lazy(() =>
  import('../components/marketing/MarketingOverviewWidget').then(module => ({
    default: module.default,
  }))
);
const CampaignPerformanceWidget = React.lazy(() =>
  import('../components/marketing/CampaignPerformanceWidget').then(module => ({
    default: module.default,
  }))
);
const AgentOverviewWidget = React.lazy(() =>
  import('../components/ai-agents/AgentOverviewWidget').then(module => ({
    default: module.default,
  }))
);
const AgentPerformanceWidget = React.lazy(() =>
  import('../components/ai-agents/AgentPerformanceWidget').then(module => ({
    default: module.default,
  }))
);
const AffiliateOverviewWidget = React.lazy(() =>
  import('../components/affiliate/AffiliateOverviewWidget').then(module => ({
    default: module.default,
  }))
);
const IntegrationOverviewWidget = React.lazy(() =>
  import('../components/integration/IntegrationOverviewWidget').then(module => ({
    default: module.default,
  }))
);

// Content widgets
const TextWidget = React.lazy(() =>
  import('../components/content/TextWidget').then(module => ({
    default: module.default,
  }))
);
const ImageWidget = React.lazy(() =>
  import('../components/content/ImageWidget').then(module => ({
    default: module.default,
  }))
);
const ClockWidget = React.lazy(() =>
  import('../components/content/ClockWidget').then(module => ({
    default: module.default,
  }))
);
const QuoteWidget = React.lazy(() =>
  import('../components/content/QuoteWidget').then(module => ({
    default: module.default,
  }))
);
const HTMLWidget = React.lazy(() =>
  import('../components/content/HTMLWidget').then(module => ({
    default: module.default,
  }))
);
const IframeWidget = React.lazy(() =>
  import('../components/content/IframeWidget').then(module => ({
    default: module.default,
  }))
);
const VideoWidget = React.lazy(() =>
  import('../components/content/VideoWidget').then(module => ({
    default: module.default,
  }))
);
const CounterWidget = React.lazy(() =>
  import('../components/content/CounterWidget').then(module => ({
    default: module.default,
  }))
);
const WeatherWidget = React.lazy(() =>
  import('../components/content/WeatherWidget').then(module => ({
    default: module.default,
  }))
);
const ProgressWidget = React.lazy(() =>
  import('../components/content/ProgressWidget').then(module => ({
    default: module.default,
  }))
);
const CalendarWidget = React.lazy(() =>
  import('../components/content/CalendarWidget').then(module => ({
    default: module.default,
  }))
);
const TodoWidget = React.lazy(() =>
  import('../components/content/TodoWidget').then(module => ({
    default: module.default,
  }))
);
const KPIWidget = React.lazy(() =>
  import('../components/content/KPIWidget').then(module => ({
    default: module.default,
  }))
);
const TableWidget = React.lazy(() =>
  import('../components/content/TableWidget').then(module => ({
    default: module.default,
  }))
);

/**
 * Widget configurations cho auto-registration
 * Mỗi widget được định nghĩa với đầy đủ metadata và dependencies
 */
export const WIDGET_CONFIGS: WidgetConfig[] = [
  // Data Widgets
  {
    id: 'data-count-widget',
    type: WIDGET_TYPES.DATA_COUNT,
    category: WIDGET_CATEGORIES.DATA,
    title: 'Tổng số lượng dữ liệu',
    description: 'Hiển thị tổng số lượng các loại dữ liệu trong hệ thống',
    icon: Hash,
    defaultSize: {
      w: 12,
      h: 4,
      minW: 4,
      minH: 3,
      maxW: 12,
      maxH: 6,
    },
    component: DataCountWidget,
    dependencies: ['data-overview-api'],
    permissions: ['data:read'],
  },
  {
    id: 'data-storage-widget',
    type: WIDGET_TYPES.DATA_STORAGE,
    category: WIDGET_CATEGORIES.DATA,
    title: 'Dung lượng dữ liệu',
    description: 'Hiển thị thông tin sử dụng dung lượng lưu trữ',
    icon: HardDrive,
    defaultSize: {
      w: 6,
      h: 4,
      minW: 4,
      minH: 3,
      maxW: 12,
      maxH: 6,
    },
    component: DataStorageWidget,
    dependencies: ['storage-api'],
    permissions: ['data:read'],
  },

  // Business Widgets
  {
    id: 'business-overview-widget',
    type: WIDGET_TYPES.BUSINESS_OVERVIEW,
    category: WIDGET_CATEGORIES.BUSINESS,
    title: 'Tổng quan kinh doanh',
    description: 'Hiển thị các chỉ số kinh doanh quan trọng',
    icon: DollarSign,
    defaultSize: {
      w: 12,
      h: 4,
      minW: 8,
      minH: 3,
      maxW: 12,
      maxH: 6,
    },
    component: BusinessOverviewWidget,
    dependencies: ['orders-api', 'products-api', 'customers-api'],
    permissions: ['business:read'],
  },
  {
    id: 'customer-products-widget',
    type: WIDGET_TYPES.CUSTOMER_PRODUCTS,
    category: WIDGET_CATEGORIES.BUSINESS,
    title: 'Danh sách sản phẩm',
    description: 'Hiển thị danh sách sản phẩm khách hàng',
    icon: Package,
    defaultSize: {
      w: 12,
      h: 6,
      minW: 8,
      minH: 4,
      maxW: 12,
      maxH: 8,
    },
    component: CustomerProductsWidget,
    dependencies: ['customer-products-api'],
    permissions: ['business:read'],
  },
  {
    id: 'customer-list-widget',
    type: WIDGET_TYPES.CUSTOMER_LIST,
    category: WIDGET_CATEGORIES.BUSINESS,
    title: 'Danh sách khách hàng',
    description: 'Hiển thị danh sách khách hàng chuyển đổi',
    icon: Users,
    defaultSize: {
      w: 12,
      h: 6,
      minW: 8,
      minH: 4,
      maxW: 12,
      maxH: 8,
    },
    component: CustomerListWidget,
    dependencies: ['convert-customers-api'],
    permissions: ['business:read'],
  },
  {
    id: 'bank-account-overview-widget',
    type: WIDGET_TYPES.BANK_ACCOUNT_OVERVIEW,
    category: WIDGET_CATEGORIES.BUSINESS,
    title: 'Tổng quan tài khoản ngân hàng',
    description: 'Hiển thị thống kê tài khoản ngân hàng',
    icon: Building2,
    defaultSize: {
      w: 8,
      h: 5,
      minW: 6,
      minH: 4,
      maxW: 12,
      maxH: 6,
    },
    component: BankAccountOverviewWidget,
    dependencies: ['bank-account-stats-api'],
    permissions: ['business:read'],
  },
  {
    id: 'custom-field-form-widget',
    type: WIDGET_TYPES.CUSTOM_FIELD_FORM,
    category: WIDGET_CATEGORIES.BUSINESS,
    title: 'Thêm trường tùy chỉnh',
    description: 'Form tạo trường tùy chỉnh mới cho hệ thống',
    icon: Settings,
    defaultSize: {
      w: 12,
      h: 8,
      minW: 8,
      minH: 6,
      maxW: 12,
      maxH: 10,
    },
    component: CustomFieldFormWidget,
    dependencies: ['custom-field-api'],
    permissions: ['business:write'],
  },
  // Business widget tổng hợp duy nhất - Thay thế tất cả các widget biểu đồ kinh doanh
  {
    id: 'business-multi-line-chart-widget',
    type: WIDGET_TYPES.BUSINESS_MULTI_LINE_CHART,
    category: WIDGET_CATEGORIES.BUSINESS,
    title: 'Biểu đồ kinh doanh',
    description: 'Hiển thị dữ liệu kinh doanh với khả năng chọn loại (doanh thu, đơn hàng, khách hàng)',
    icon: BarChart3,
    defaultSize: {
      w: 12,
      h: 7,
      minW: 6,
      minH: 5,
      maxW: 12,
      maxH: 10,
    },
    component: BusinessMultiLineChartWidget,
    dependencies: ['line-chart-api'],
    permissions: ['business:read'],
  },
  {
    id: 'product-type-pie-chart-widget',
    type: WIDGET_TYPES.PRODUCT_TYPE_PIE_CHART,
    category: WIDGET_CATEGORIES.BUSINESS,
    title: 'Biểu đồ tròn loại sản phẩm',
    description: 'Hiển thị phân bố loại sản phẩm dưới dạng biểu đồ tròn',
    icon: PieChart,
    defaultSize: {
      w: 6,
      h: 6,
      minW: 4,
      minH: 5,
      maxW: 8,
      maxH: 8,
    },
    component: ProductTypePieChartWidget,
    dependencies: ['product-type-pie-chart-api'],
    permissions: ['business:read'],
  },

  // Marketing Widgets
  {
    id: 'marketing-overview-widget',
    type: WIDGET_TYPES.MARKETING_OVERVIEW,
    category: WIDGET_CATEGORIES.MARKETING,
    title: 'Tổng quan Marketing',
    description: 'Hiển thị tổng quan về các hoạt động marketing',
    icon: Target,
    defaultSize: {
      w: 12,
      h: 5,
      minW: 8,
      minH: 4,
      maxW: 12,
      maxH: 8,
    },
    component: MarketingOverviewWidget,
    dependencies: ['marketing-api'],
    permissions: ['marketing:read'],
  },
  {
    id: 'campaign-performance-widget',
    type: WIDGET_TYPES.CAMPAIGN_PERFORMANCE,
    category: WIDGET_CATEGORIES.MARKETING,
    title: 'Hiệu suất chiến dịch',
    description: 'Hiển thị hiệu suất của các chiến dịch marketing',
    icon: BarChart3,
    defaultSize: {
      w: 8,
      h: 6,
      minW: 6,
      minH: 4,
      maxW: 12,
      maxH: 8,
    },
    component: CampaignPerformanceWidget,
    dependencies: ['campaigns-api'],
    permissions: ['marketing:read'],
  },

  // AI Agents Widgets
  {
    id: 'agent-overview-widget',
    type: WIDGET_TYPES.AGENT_OVERVIEW,
    category: WIDGET_CATEGORIES.AI_AGENTS,
    title: 'Tổng quan AI Agents',
    description: 'Hiển thị tổng quan về các AI agents',
    icon: Zap,
    defaultSize: {
      w: 8,
      h: 5,
      minW: 6,
      minH: 4,
      maxW: 12,
      maxH: 8,
    },
    component: AgentOverviewWidget,
    dependencies: ['agents-api'],
    permissions: ['ai-agents:read'],
  },
  {
    id: 'agent-performance-widget',
    type: WIDGET_TYPES.AGENT_PERFORMANCE,
    category: WIDGET_CATEGORIES.AI_AGENTS,
    title: 'Hiệu suất Agents',
    description: 'Hiển thị hiệu suất hoạt động của AI agents',
    icon: Activity,
    defaultSize: {
      w: 8,
      h: 6,
      minW: 6,
      minH: 4,
      maxW: 12,
      maxH: 8,
    },
    component: AgentPerformanceWidget,
    dependencies: ['agents-performance-api'],
    permissions: ['ai-agents:read'],
  },

  // Affiliate Widgets
  {
    id: 'affiliate-overview-widget',
    type: WIDGET_TYPES.AFFILIATE_OVERVIEW,
    category: WIDGET_CATEGORIES.AFFILIATE,
    title: 'Tổng quan Affiliate',
    description: 'Hiển thị tổng quan về hệ thống affiliate',
    icon: Users,
    defaultSize: {
      w: 8,
      h: 5,
      minW: 6,
      minH: 4,
      maxW: 12,
      maxH: 8,
    },
    component: AffiliateOverviewWidget,
    dependencies: ['affiliate-api'],
    permissions: ['affiliate:read'],
  },

  // Integration Widgets
  {
    id: 'integration-overview-widget',
    type: WIDGET_TYPES.INTEGRATION_OVERVIEW,
    category: WIDGET_CATEGORIES.INTEGRATION,
    title: 'Tổng quan tích hợp',
    description: 'Hiển thị trạng thái các tích hợp hệ thống',
    icon: Globe,
    defaultSize: {
      w: 8,
      h: 5,
      minW: 6,
      minH: 4,
      maxW: 12,
      maxH: 8,
    },
    component: IntegrationOverviewWidget,
    dependencies: ['integrations-api'],
    permissions: ['integrations:read'],
  },

  // Content Widgets
  {
    id: 'text-widget',
    type: WIDGET_TYPES.TEXT_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget văn bản',
    description: 'Hiển thị văn bản tùy chỉnh có thể chỉnh sửa',
    icon: Type,
    defaultSize: {
      w: 6,
      h: 4,
      minW: 3,
      minH: 2,
      maxW: 12,
      maxH: 8,
    },
    component: TextWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'image-widget',
    type: WIDGET_TYPES.IMAGE_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget hình ảnh',
    description: 'Hiển thị hình ảnh từ URL với các tùy chọn hiển thị',
    icon: ImageIcon,
    defaultSize: {
      w: 6,
      h: 6,
      minW: 3,
      minH: 3,
      maxW: 12,
      maxH: 12,
    },
    component: ImageWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'clock-widget',
    type: WIDGET_TYPES.CLOCK_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget đồng hồ',
    description: 'Hiển thị đồng hồ thời gian thực với múi giờ tùy chỉnh',
    icon: Clock,
    defaultSize: {
      w: 4,
      h: 3,
      minW: 3,
      minH: 2,
      maxW: 8,
      maxH: 6,
    },
    component: ClockWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'quote-widget',
    type: WIDGET_TYPES.QUOTE_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget trích dẫn',
    description: 'Hiển thị trích dẫn truyền cảm hứng với tác giả',
    icon: Quote,
    defaultSize: {
      w: 8,
      h: 4,
      minW: 4,
      minH: 3,
      maxW: 12,
      maxH: 8,
    },
    component: QuoteWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'html-widget',
    type: WIDGET_TYPES.HTML_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget HTML',
    description: 'Hiển thị HTML tùy chỉnh với sandbox bảo mật',
    icon: Code,
    defaultSize: {
      w: 8,
      h: 6,
      minW: 4,
      minH: 4,
      maxW: 12,
      maxH: 12,
    },
    component: HTMLWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'iframe-widget',
    type: WIDGET_TYPES.IFRAME_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget iframe',
    description: 'Nhúng trang web hoặc ứng dụng bên ngoài',
    icon: ExternalLink,
    defaultSize: {
      w: 8,
      h: 8,
      minW: 4,
      minH: 4,
      maxW: 12,
      maxH: 12,
    },
    component: IframeWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'video-widget',
    type: WIDGET_TYPES.VIDEO_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget video',
    description: 'Nhúng video từ YouTube, Vimeo hoặc URL trực tiếp',
    icon: Play,
    defaultSize: {
      w: 8,
      h: 6,
      minW: 4,
      minH: 3,
      maxW: 12,
      maxH: 10,
    },
    component: VideoWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'counter-widget',
    type: WIDGET_TYPES.COUNTER_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget đếm số',
    description: 'Hiển thị số đếm với animation và customization',
    icon: Hash,
    defaultSize: {
      w: 4,
      h: 3,
      minW: 3,
      minH: 2,
      maxW: 8,
      maxH: 6,
    },
    component: CounterWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'weather-widget',
    type: WIDGET_TYPES.WEATHER_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget thời tiết',
    description: 'Hiển thị thông tin thời tiết và dự báo',
    icon: Cloud,
    defaultSize: {
      w: 6,
      h: 5,
      minW: 4,
      minH: 4,
      maxW: 10,
      maxH: 8,
    },
    component: WeatherWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'progress-widget',
    type: WIDGET_TYPES.PROGRESS_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget tiến độ',
    description: 'Hiển thị progress bars với animation',
    icon: BarChart3,
    defaultSize: {
      w: 6,
      h: 4,
      minW: 4,
      minH: 3,
      maxW: 12,
      maxH: 8,
    },
    component: ProgressWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'calendar-widget',
    type: WIDGET_TYPES.CALENDAR_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget lịch',
    description: 'Lịch với event highlighting và date picker',
    icon: Calendar,
    defaultSize: {
      w: 8,
      h: 6,
      minW: 6,
      minH: 5,
      maxW: 12,
      maxH: 10,
    },
    component: CalendarWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'todo-widget',
    type: WIDGET_TYPES.TODO_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget todo list',
    description: 'Quản lý task với add/remove/check functionality',
    icon: CheckSquare,
    defaultSize: {
      w: 6,
      h: 6,
      minW: 4,
      minH: 4,
      maxW: 10,
      maxH: 12,
    },
    component: TodoWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'kpi-widget',
    type: WIDGET_TYPES.KPI_WIDGET,
    category: WIDGET_CATEGORIES.BUSINESS,
    title: 'Widget KPI',
    description: 'Hiển thị KPI metrics với trend indicators',
    icon: TrendingUp,
    defaultSize: {
      w: 8,
      h: 5,
      minW: 6,
      minH: 4,
      maxW: 12,
      maxH: 8,
    },
    component: KPIWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'table-widget',
    type: WIDGET_TYPES.TABLE_WIDGET,
    category: WIDGET_CATEGORIES.DATA,
    title: 'Widget bảng dữ liệu',
    description: 'Bảng dữ liệu với sort/filter/pagination',
    icon: Table,
    defaultSize: {
      w: 10,
      h: 6,
      minW: 8,
      minH: 5,
      maxW: 12,
      maxH: 10,
    },
    component: TableWidget,
    dependencies: [],
    permissions: [],
  },
];

/**
 * Get widget config by type
 */
export const getWidgetConfig = (type: string): WidgetConfig | undefined => {
  return WIDGET_CONFIGS.find(config => config.type === type);
};

/**
 * Get widgets by category
 */
export const getWidgetsByCategory = (category: string): WidgetConfig[] => {
  return WIDGET_CONFIGS.filter(config => config.category === category);
};

/**
 * Get all available widget types
 */
export const getAvailableWidgetTypes = (): string[] => {
  return WIDGET_CONFIGS.map(config => config.type);
};

export default WIDGET_CONFIGS;
