/**
 * Thread Loading Indicator
 * Shows loading states for thread operations (switching, clearing, creating)
 */

import React from 'react';

interface ThreadLoadingIndicatorProps {
  /**
   * Type of loading operation
   */
  type: 'switching' | 'clearing' | 'creating' | 'loading';
  
  /**
   * Optional message to display
   */
  message?: string;
  
  /**
   * Size of the indicator
   */
  size?: 'sm' | 'md' | 'lg';
  
  /**
   * Whether to show as overlay
   */
  overlay?: boolean;
}

const ThreadLoadingIndicator: React.FC<ThreadLoadingIndicatorProps> = ({
  type,
  message,
  size = 'md',
  overlay = false
}) => {
  const getDefaultMessage = () => {
    switch (type) {
      case 'switching':
        return 'Đang chuyển thread...';
      case 'clearing':
        return 'Đang xóa thread...';
      case 'creating':
        return 'Đang tạo thread mới...';
      case 'loading':
        return 'Đang tải...';
      default:
        return 'Đang xử lý...';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'lg':
        return 'w-8 h-8';
      default:
        return 'w-6 h-6';
    }
  };

  const getTextSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'text-sm';
      case 'lg':
        return 'text-lg';
      default:
        return 'text-base';
    }
  };

  const displayMessage = message || getDefaultMessage();

  const content = (
    <div className="flex items-center justify-center space-x-3">
      {/* Spinning loader */}
      <div className={`${getSizeClasses()} animate-spin`}>
        <svg
          className="w-full h-full text-blue-500"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      </div>
      
      {/* Message */}
      <span className={`${getTextSizeClasses()} text-gray-600 dark:text-gray-300 font-medium`}>
        {displayMessage}
      </span>
    </div>
  );

  if (overlay) {
    return (
      <div className="absolute inset-0 bg-white/80 dark:bg-dark/80 backdrop-blur-sm z-50 flex items-center justify-center">
        {content}
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center py-4">
      {content}
    </div>
  );
};

export default ThreadLoadingIndicator;
