import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Select } from '@/shared/components/common';
import { BaseWidgetProps } from '../../types';

interface ClockWidgetProps extends BaseWidgetProps {
  timezone?: string;
  format24Hour?: boolean;
  showDate?: boolean;
  showSeconds?: boolean;
  editable?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Widget hiển thị đồng hồ
 */
const ClockWidget: React.FC<ClockWidgetProps> = ({
  className,
  timezone = 'Asia/Ho_Chi_Minh',
  format24Hour = true,
  showDate = true,
  showSeconds = true,
  editable = true,
  size = 'md',
}) => {
  const { t } = useTranslation(['dashboard', 'common']);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isEditing, setIsEditing] = useState(false);
  const [tempSettings, setTempSettings] = useState({
    timezone,
    format24Hour,
    showDate,
    showSeconds,
  });
  const [settings, setSettings] = useState({
    timezone,
    format24Hour,
    showDate,
    showSeconds,
  });

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleEdit = useCallback(() => {
    setTempSettings(settings);
    setIsEditing(true);
  }, [settings]);

  const handleSave = useCallback(() => {
    setSettings(tempSettings);
    setIsEditing(false);
  }, [tempSettings]);

  const handleCancel = useCallback(() => {
    setTempSettings(settings);
    setIsEditing(false);
  }, [settings]);

  const formatTime = useCallback((date: Date) => {
    try {
      const options: Intl.DateTimeFormatOptions = {
        timeZone: settings.timezone,
        hour: '2-digit',
        minute: '2-digit',
        ...(settings.showSeconds && { second: '2-digit' }),
        hour12: !settings.format24Hour,
      };

      return new Intl.DateTimeFormat('vi-VN', options).format(date);
    } catch (error) {
      // Fallback if timezone is invalid
      const options: Intl.DateTimeFormatOptions = {
        hour: '2-digit',
        minute: '2-digit',
        ...(settings.showSeconds && { second: '2-digit' }),
        hour12: !settings.format24Hour,
      };
      return new Intl.DateTimeFormat('vi-VN', options).format(date);
    }
  }, [settings]);

  const formatDate = useCallback((date: Date) => {
    try {
      const options: Intl.DateTimeFormatOptions = {
        timeZone: settings.timezone,
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      };

      return new Intl.DateTimeFormat('vi-VN', options).format(date);
    } catch (error) {
      // Fallback if timezone is invalid
      const options: Intl.DateTimeFormatOptions = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      };
      return new Intl.DateTimeFormat('vi-VN', options).format(date);
    }
  }, [settings]);

  const sizeClasses = {
    sm: { time: 'text-2xl', date: 'text-sm' },
    md: { time: 'text-4xl', date: 'text-base' },
    lg: { time: 'text-6xl', date: 'text-lg' },
  };

  const timezoneOptions = [
    { value: 'Asia/Ho_Chi_Minh', label: 'Việt Nam (GMT+7)' },
    { value: 'Asia/Tokyo', label: 'Tokyo (GMT+9)' },
    { value: 'Asia/Shanghai', label: 'Shanghai (GMT+8)' },
    { value: 'Europe/London', label: 'London (GMT+0)' },
    { value: 'Europe/Paris', label: 'Paris (GMT+1)' },
    { value: 'America/New_York', label: 'New York (GMT-5)' },
    { value: 'America/Los_Angeles', label: 'Los Angeles (GMT-8)' },
    { value: 'UTC', label: 'UTC (GMT+0)' },
  ];

  if (isEditing) {
    return (
      <div className={`w-full h-full p-4 ${className || ''}`}>
        <div className="h-full flex flex-col">
          <div className="space-y-4 flex-1">
            <div>
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.clock.timezone', 'Múi giờ')}
              </Typography>
              <Select
                value={tempSettings.timezone}
                onChange={(value) => setTempSettings(prev => ({ ...prev, timezone: value as string }))}
                options={timezoneOptions}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.format24Hour}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, format24Hour: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.clock.format24h', 'Định dạng 24 giờ')}
                </Typography>
              </label>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.showDate}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, showDate: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.clock.showDate', 'Hiển thị ngày')}
                </Typography>
              </label>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.showSeconds}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, showSeconds: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.clock.showSeconds', 'Hiển thị giây')}
                </Typography>
              </label>
            </div>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              {t('common:cancel')}
            </Button>
            <Button variant="primary" size="sm" onClick={handleSave}>
              {t('common:save')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`w-full h-full p-4 ${className || ''} ${editable ? 'cursor-pointer hover:bg-muted/50 transition-colors group' : ''}`}
      onClick={editable ? handleEdit : undefined}
    >
      <div className="h-full flex flex-col items-center justify-center text-center">
        <Typography
          variant="h1"
          className={`font-mono font-bold ${sizeClasses[size].time} text-foreground mb-2`}
        >
          {formatTime(currentTime)}
        </Typography>
        
        {settings.showDate && (
          <Typography
            variant="body1"
            className={`${sizeClasses[size].date} text-muted-foreground`}
          >
            {formatDate(currentTime)}
          </Typography>
        )}

        {editable && (
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button variant="ghost" size="sm" onClick={handleEdit}>
              {t('common:edit')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ClockWidget;
