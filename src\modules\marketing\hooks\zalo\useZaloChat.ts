/**
 * Zalo Chat Hooks
 * Custom hooks cho module chat Zalo
 */

import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import {
  zaloAccountsService,
  contactsService,
  messagesService,
  uploadService,
  settingsService,
} from '../../services/zalo-chat.service';
import { ZALO_CHAT_QUERY_KEYS, ZALO_CHAT_MUTATION_KEYS } from '../../constants/zalo-chat-query-keys';
import type {
  ZaloAccount,
  Contact,
  Conversation,
  ContactSearchParams,
} from '../../types/zalo-chat.types';
import { STORAGE_KEYS, PAGINATION_DEFAULTS } from '../../constants/zalo-chat.constants';

/**
 * Hook quản lý tài khoản Zalo
 */
export const useZaloAccounts = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const { error: showError } = useSmartNotification();

  const query = useQuery({
    queryKey: ZALO_CHAT_QUERY_KEYS.ACCOUNTS.LIST(),
    queryFn: zaloAccountsService.getAccountsWithBusinessLogic,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });

  // Handle error separately
  if (query.error) {
    showError({
      message: t('marketing:zalo.chat.errors.loadAccountsFailed'),
    });
  }

  return {
    accounts: query.data || [],
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch,
  };
};

/**
 * Hook quản lý liên hệ
 */
export const useContacts = (params?: ContactSearchParams) => {
  const { t } = useTranslation(['marketing', 'common']);
  const { error: showError, success } = useSmartNotification();
  const queryClient = useQueryClient();

  const query = useQuery({
    queryKey: ZALO_CHAT_QUERY_KEYS.CONTACTS.LIST(params || {}),
    queryFn: () => contactsService.getContactsWithBusinessLogic(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    enabled: !!params?.zaloAccountId,
  });

  // Handle error separately
  if (query.error) {
    showError({
      message: t('marketing:zalo.chat.errors.loadContactsFailed'),
    });
  }

  const createContactMutation = useMutation({
    mutationKey: [ZALO_CHAT_MUTATION_KEYS.CREATE_CONTACT],
    mutationFn: contactsService.createContactWithValidation,
    onSuccess: (_newContact) => {
      // Invalidate contacts list
      queryClient.invalidateQueries({ queryKey: ZALO_CHAT_QUERY_KEYS.CONTACTS.ALL });
      success({
        message: t('marketing:zalo.chat.success.contactCreated'),
      });
    },
    onError: (_error: Error) => {
      showError({
        message: t('marketing:zalo.chat.errors.createContactFailed'),
      });
    },
  });

  return {
    contacts: query.data?.items || [],
    total: query.data?.meta?.totalItems || 0,
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch,
    createContact: createContactMutation.mutate,
    isCreating: createContactMutation.isPending,
  };
};

/**
 * Hook tìm kiếm liên hệ
 */
export const useContactSearch = (accountId?: string) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const query = useQuery({
    queryKey: ZALO_CHAT_QUERY_KEYS.CONTACTS.SEARCH(debouncedQuery, accountId),
    queryFn: () => contactsService.searchContactsWithDebounce(debouncedQuery, accountId),
    enabled: debouncedQuery.length >= 2 && !!accountId,
    staleTime: 30 * 1000, // 30 seconds
  });

  return {
    searchQuery,
    setSearchQuery,
    searchResults: query.data || [],
    isSearching: query.isLoading,
    searchError: query.error,
  };
};

/**
 * Hook quản lý tin nhắn với infinite scroll
 */
export const useMessages = (conversationId: string) => {
  const { t } = useTranslation(['marketing', 'common']);
  const { error: showError } = useSmartNotification();

  const query = useInfiniteQuery({
    queryKey: ZALO_CHAT_QUERY_KEYS.MESSAGES.BY_CONVERSATION(conversationId),
    queryFn: ({ pageParam = 1 }) =>
      messagesService.getMessagesWithBusinessLogic({
        conversationId,
        page: pageParam as number,
        limit: PAGINATION_DEFAULTS.MESSAGES_LIMIT,
      }),
    enabled: !!conversationId,
    getNextPageParam: (lastPage: any) => {
      if (lastPage.page < lastPage.totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
    staleTime: 30 * 1000, // 30 seconds
  });

  // Handle error separately
  if (query.error) {
    showError({
      message: t('marketing:zalo.chat.errors.loadMessagesFailed'),
    });
  }

  // Flatten all messages from all pages
  const messages = query.data?.pages.flatMap((page: any) => page.items) || [];

  return {
    messages,
    isLoading: query.isLoading,
    isLoadingMore: query.isFetchingNextPage,
    hasMore: query.hasNextPage,
    loadMore: query.fetchNextPage,
    error: query.error,
    refetch: query.refetch,
  };
};

/**
 * Hook gửi tin nhắn
 */
export const useSendMessage = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const { success, error: showError } = useSmartNotification();
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationKey: [ZALO_CHAT_MUTATION_KEYS.SEND_MESSAGE],
    mutationFn: messagesService.sendMessageWithValidation,
    onSuccess: (newMessage) => {
      // Invalidate messages for the conversation
      queryClient.invalidateQueries({
        queryKey: ZALO_CHAT_QUERY_KEYS.MESSAGES.BY_CONVERSATION(newMessage.conversationId),
      });

      // Invalidate contacts list to update last message
      queryClient.invalidateQueries({
        queryKey: ZALO_CHAT_QUERY_KEYS.CONTACTS.ALL,
      });

      success({
        message: t('marketing:zalo.chat.success.messageSent'),
      });
    },
    onError: (_error: Error) => {
      showError({
        message: t('marketing:zalo.chat.errors.sendMessageFailed'),
      });
    },
  });

  return {
    sendMessage: mutation.mutate,
    isSending: mutation.isPending,
    error: mutation.error,
  };
};

/**
 * Hook upload file
 */
export const useFileUpload = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const { success, error: showError } = useSmartNotification();
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});

  const uploadFile = useCallback(async (file: File): Promise<{ url: string; name: string; size: number }> => {
    const fileId = `${file.name}-${Date.now()}`;

    try {
      setUploadProgress(prev => ({ ...prev, [fileId]: 0 }));

      const result = await uploadService.uploadFileWithValidation(file, (progress) => {
        setUploadProgress(prev => ({ ...prev, [fileId]: progress }));
      });

      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[fileId];
        return newProgress;
      });

      success({
        message: t('marketing:zalo.chat.success.fileUploaded'),
      });

      return result;
    } catch (error) {
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[fileId];
        return newProgress;
      });

      showError({
        message: t('marketing:zalo.chat.errors.uploadFailed'),
      });

      throw error;
    }
  }, [t, success, showError]);

  return {
    uploadFile,
    uploadProgress,
  };
};

/**
 * Hook quản lý trạng thái chat chính
 */
export const useZaloChat = () => {
  const [selectedAccount, setSelectedAccount] = useState<ZaloAccount | undefined>();
  const [selectedContact, setSelectedContact] = useState<Contact | undefined>();
  const [selectedConversation, setSelectedConversation] = useState<Conversation | undefined>();

  // Load từ localStorage khi component mount
  useEffect(() => {
    const savedAccountId = localStorage.getItem(STORAGE_KEYS.SELECTED_ZALO_ACCOUNT);
    const savedContactId = localStorage.getItem(STORAGE_KEYS.SELECTED_CONTACT);

    if (savedAccountId) {
      // TODO: Load account by ID
    }

    if (savedContactId) {
      // TODO: Load contact by ID
    }
  }, []);

  // Save to localStorage khi state thay đổi
  useEffect(() => {
    if (selectedAccount) {
      localStorage.setItem(STORAGE_KEYS.SELECTED_ZALO_ACCOUNT, selectedAccount.id);
    }
  }, [selectedAccount]);

  useEffect(() => {
    if (selectedContact) {
      localStorage.setItem(STORAGE_KEYS.SELECTED_CONTACT, selectedContact.id);
    }
  }, [selectedContact]);

  const selectAccount = useCallback((account: ZaloAccount) => {
    setSelectedAccount(account);
    setSelectedContact(undefined);
    setSelectedConversation(undefined);
  }, []);

  const selectContact = useCallback((contact: Contact) => {
    setSelectedContact(contact);
    // TODO: Load or create conversation for this contact
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedContact(undefined);
    setSelectedConversation(undefined);
    localStorage.removeItem(STORAGE_KEYS.SELECTED_CONTACT);
  }, []);

  return {
    selectedAccount,
    selectedContact,
    selectedConversation,
    selectAccount,
    selectContact,
    clearSelection,
  };
};

/**
 * Hook quản lý cài đặt chat
 */
export const useChatSettings = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const { success, error: showError } = useSmartNotification();
  const queryClient = useQueryClient();

  const query = useQuery({
    queryKey: ZALO_CHAT_QUERY_KEYS.SETTINGS.CHAT(),
    queryFn: settingsService.getChatSettingsWithDefaults,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const updateMutation = useMutation({
    mutationKey: [ZALO_CHAT_MUTATION_KEYS.UPDATE_SETTINGS],
    mutationFn: settingsService.updateChatSettingsWithValidation,
    onSuccess: (updatedSettings) => {
      queryClient.setQueryData(ZALO_CHAT_QUERY_KEYS.SETTINGS.CHAT(), updatedSettings);
      success({
        message: t('marketing:zalo.chat.success.settingsUpdated'),
      });
    },
    onError: (_error: Error) => {
      showError({
        message: t('marketing:zalo.chat.errors.updateSettingsFailed'),
      });
    },
  });

  return {
    settings: query.data,
    isLoading: query.isLoading,
    updateSettings: updateMutation.mutate,
    isUpdating: updateMutation.isPending,
    error: query.error,
  };
};
