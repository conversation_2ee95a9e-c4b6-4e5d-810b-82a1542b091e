{"integration": {"title": "集成", "breadcrumb": {"home": "首页", "integrations": "集成", "googleAds": "Google Ads", "facebookAds": "Facebook Ads", "gmail": "Gmail", "calendar": "Google Calendar"}, "myIntegrations": {"title": "我的集成"}, "myIntegrationsDescription": "管理您的个人集成", "types": {"bank": "银行账户", "llm": "LLM", "sms": "短信", "email": "电子邮件", "database": "数据库", "social": "社交媒体", "shipping": "物流", "calendar": "日历", "ads": "广告", "other": "其他"}, "bankAccounts": {"title": "银行账户管理"}, "googleAds": {"title": "谷歌广告集成", "description": "与谷歌广告集成以管理广告活动", "connectTitle": "连接到谷歌广告", "connectDescription": "连接到谷歌广告以管理广告活动", "connectButton": "连接到谷歌广告"}, "facebookAds": {"title": "Facebook广告集成", "description": "与Facebook广告集成以管理广告活动", "connectTitle": "连接到Facebook广告", "connectDescription": "连接到Facebook广告以管理广告活动", "connectButton": "连接到Facebook广告", "management": {"title": "Facebook广告管理", "description": "管理您的Facebook广告集成"}}, "allIntegrations": "所有集成", "cards": {"banking": {"mb": {"description": "与MB银行集成"}, "acb": {"description": "与ACB银行集成"}, "ocb": {"description": "与OCB银行集成"}, "kienlong": {"description": "与建隆银行集成"}}, "llm": {"openai": {"description": "与OpenAI GPT模型集成"}, "anthropic": {"description": "与Anthropic Claude AI集成"}, "gemini": {"description": "与Google Gemini Pro集成"}, "deepseek": {"description": "与DeepSeek AI模型集成"}, "meta": {"description": "与Meta Llama模型集成"}, "xai": {"description": "与XAI Grok模型集成"}}, "sms": {"twilio": {"description": "与Twilio集成发送短信", "breadcrumb": "<PERSON><PERSON><PERSON>短信", "title": "<PERSON><PERSON><PERSON>短信集成", "form": {"title": "创建Twilio短信集成", "description": "填写信息以创建新的Twilio短信集成", "fields": {"integrationName": "集成名称", "integrationNameHelp": "用于识别此集成的名称", "integrationNamePlaceholder": "例如：<PERSON><PERSON><PERSON>生产短信", "authToken": "Twilio 身份验证令牌", "authTokenHelp": "从Twilio控制台获取的Auth Token", "authTokenPlaceholder": "输入<PERSON><PERSON><PERSON>", "baseDomain": "<PERSON><PERSON><PERSON> 基础域", "baseDomainHelp": "Twilio API的基域（例如：api.twilio.com）", "baseDomainPlaceholder": "输入Twilio Base Domain"}, "placeholders": {"integrationName": "输入集成名称", "authToken": "输入<PERSON><PERSON><PERSON>", "baseDomain": "输入Twilio Base Domain"}, "create": "创建集成", "cancel": "取消", "saving": "保存中...", "required": "必填"}, "validation": {"integrationName": {"required": "集成名称是必填项", "maxLength": "集成名称不能超过100个字符"}, "authToken": {"required": "<PERSON><PERSON><PERSON>是必填项", "minLength": "<PERSON><PERSON><PERSON>必须至少10个字符"}, "baseDomain": {"required": "Twilio Base Domain是必填项", "minLength": "Twilio Base Domain必须至少3个字符"}}}, "fpt": {"title": "FPT短信品牌名", "description": "与FPT电信短信品牌名服务集成", "descriptionManagement": "管理FPT短信品牌名配置", "breadcrumb": "FPT短信品牌名", "validation": {"integrationName": {"required": "集成名称是必填项", "maxLength": "集成名称不能超过100个字符"}, "clientId": {"required": "客户ID是必填项", "maxLength": "客户ID不能超过255个字符"}, "clientSecret": {"required": "客户密钥是必填项", "maxLength": "客户密钥不能超过255个字符"}, "brandName": {"required": "品牌名称是必填项", "maxLength": "品牌名称不能超过255个字符"}}, "actions": {"edit": "编辑", "delete": "删除", "testConnection": "测试连接", "moreActions": "更多操作"}, "empty": {"title": "暂无FPT短信品牌名配置", "description": "点击下方按钮创建第一个配置"}, "list": {"title": "FPT短信品牌名管理", "description": "管理FPT短信品牌名配置", "columns": {"integrationName": "集成名称", "brandName": "品牌名称", "clientId": "客户ID", "endpoint": "端点", "status": "状态", "createdAt": "创建时间", "actions": "操作"}, "empty": "暂无FPT短信品牌名配置", "emptyDescription": "点击下方按钮创建第一个配置"}, "confirmations": {"deleteTitle": "确认删除", "delete": "您确定要删除此配置吗？"}, "form": {"fields": {"integrationName": "集成名称", "integrationNameHelp": "用于识别此集成的名称", "integrationNamePlaceholder": "例如：FPT短信品牌名 - 公司ABC", "clientId": "客户ID", "clientIdHelp": "用于身份验证的FPT短信客户ID", "clientIdPlaceholder": "输入FPT短信的客户ID", "clientSecret": "客户密钥", "clientSecretHelp": "用于身份验证的FPT短信客户密钥", "clientSecretPlaceholder": "输入FPT短信的客户密钥", "brandName": "品牌名称", "brandNameHelp": "发送短信时显示的品牌名称", "brandNamePlaceholder": "输入品牌名称"}, "placeholders": {"integrationName": "输入集成名称", "clientId": "输入FPT短信的客户ID", "clientSecret": "输入FPT短信的客户密钥", "brandName": "输入品牌名称"}, "validation": {"integrationName": {"required": "集成名称是必填项", "maxLength": "集成名称不能超过100个字符"}, "clientId": {"required": "客户ID是必填项", "maxLength": "客户ID不能超过255个字符"}, "clientSecret": {"required": "客户密钥是必填项", "maxLength": "客户密钥不能超过255个字符"}, "brandName": {"maxLength": "品牌名称不能超过255个字符"}}, "createTitle": "创建FPT短信品牌名配置", "editTitle": "编辑FPT短信品牌名配置", "createDescription": "填写信息以创建新的FPT短信品牌名集成", "editDescription": "更新FPT短信品牌名配置信息", "integrationName": "集成名称", "integrationNameHelp": "用于识别此集成的名称", "integrationNamePlaceholder": "例如：FPT短信品牌名 - 公司ABC", "clientId": "客户ID", "clientIdHelp": "用于身份验证的FPT短信客户ID", "clientIdPlaceholder": "输入FPT短信的客户ID", "clientSecret": "客户密钥", "clientSecretHelp": "用于身份验证的FPT短信客户密钥", "clientSecretPlaceholder": "输入FPT短信的客户密钥", "brandName": "品牌名称", "brandNameHelp": "发送短信时显示的品牌名称", "brandNamePlaceholder": "输入品牌名称", "testConnection": "测试连接", "testConnectionDescription": "测试与FPT短信API的连接", "testNow": "立即测试", "testing": "测试中...", "create": "创建配置", "cancel": "取消", "saving": "保存中...", "required": "必填"}, "test": {"saveFirst": "请先保存配置再测试连接", "failed": "连接测试失败"}, "success": {"created": "FPT短信配置成功！", "createdDescription": "集成\"{{name}}\"已成功创建。", "updated": "配置更新成功！", "deleted": "配置删除成功！"}, "error": {"createFailed": "FPT短信配置失败", "createFailedDescription": "配置过程中发生错误。请重试。", "updateFailed": "配置更新失败", "deleteFailed": "配置删除失败", "invalidConfig": "配置信息无效"}}}, "social": {"zalo": {"description": "与Zalo官方账户集成", "breadcrumb": "Zalo OA", "title": "Zalo官方账户", "list": {"title": "Zalo官方账户管理", "description": "管理Zalo官方账户集成", "columns": {"name": "名称", "description": "描述", "status": "状态", "createdAt": "创建时间", "actions": "操作"}}, "status": {"active": "活动", "inactive": "非活动", "suspended": "暂停", "error": "错误"}, "actions": {"disconnect": "断开连接", "delete": "删除", "moreActions": "更多操作"}}}, "shipping": {"title": "物流集成", "description": "与物流服务商集成", "breadcrumb": "物流集成", "addProvider": "添加物流提供商", "editProvider": "编辑物流提供商", "viewProvider": "查看物流提供商", "addFirstProvider": "添加第一个物流提供商", "selectProviderType": "选择物流提供商", "testConnection": "测试连接", "test": "测试", "runTest": "运行测试", "rateCalculator": "费率计算器", "create": "创建", "viettelPost": {"description": "与Viettel Post集成", "breadcrumb": "越南电信邮政"}, "vnpost": {"description": "与VNPost集成", "breadcrumb": "越南邮政"}, "list": {"title": "物流集成管理", "description": "管理物流集成配置", "columns": {"integrationName": "集成名称", "providerName": "提供商名称", "providerType": "提供商类型", "shopId": "店铺ID", "default": "默认", "status": "状态", "actions": "操作"}}, "ghtk": {"description": "与Giao Hang Tiet Kiem (GHTK)集成", "breadcrumb": "经济型物流"}, "ghn": {"description": "与<PERSON><PERSON><PERSON> (GHN)集成", "breadcrumb": "快速物流"}, "ahamove": {"description": "与Ahamove集成 - 快速送货", "breadcrumb": "快速送货"}}}, "googleCalendar": {"title": "Google日历集成", "description": "与Google日历进行集成", "addCalendar": "添加Google日历", "editCalendar": "编辑Google日历", "viewCalendar": "查看Google日历", "addFirstCalendar": "添加第一个Google日历", "management": {"title": "Google日历管理", "description": "管理Google日历配置"}, "table": {"columns": {"name": "名称", "user": "用户", "status": "状态", "calendars": "日历", "lastSync": "上次同步"}}, "card": {"connected": "已连接", "connectTitle": "连接Google日历", "connectDescription": "连接Google日历以同步日历和事件，更高效地管理时间", "disconnect": "断开连接", "connect": "连接Google日历", "connecting": "连接中...", "securityNote": "安全可靠的连接", "calendarsCount": "个日历", "features": {"syncEvents": "同步事件", "manageSchedule": "管理日程", "autoReminder": "自动提醒", "shareCalendar": "共享日历"}}, "form": {"title": "Google日历配置", "displayName": "显示名称", "displayNameHelp": "此Google日历配置的显示名称", "displayNamePlaceholder": "例如：主要Google日历配置", "clientId": "客户端ID", "clientIdHelp": "从Google Cloud Console获取的客户端ID", "clientIdPlaceholder": "输入客户端ID", "clientSecret": "客户端密钥", "clientSecretHelp": "从Google Cloud Console获取的客户端密钥", "clientSecretPlaceholder": "输入客户端密钥", "refreshToken": "刷新令牌", "refreshTokenHelp": "用于保持连接的刷新令牌", "refreshTokenPlaceholder": "输入刷新令牌", "calendarId": "日历ID", "calendarIdHelp": "要同步的特定日历ID（可选）", "calendarIdPlaceholder": "输入日历ID（可选）", "isActive": "激活", "isActiveHelp": "启用/禁用此集成"}}, "gmail": {"title": "Gmail集成", "description": "与Gmail集成以发送和接收电子邮件", "addGmail": "添加Gmail", "editGmail": "编辑Gmail", "viewGmail": "查看Gmail", "addFirstGmail": "添加第一个Gmail", "management": {"title": "Gmail管理", "description": "管理Gmail配置"}, "table": {"columns": {"name": "名称", "email": "电子邮件", "status": "状态", "createdAt": "创建时间"}}, "form": {"title": "Gmail配置", "displayName": "显示名称", "displayNameHelp": "此Gmail配置的显示名称", "displayNamePlaceholder": "例如：主要Gmail配置", "isActive": "激活", "isActiveHelp": "启用/禁用此集成"}, "card": {"description": "连接Gmail以自动发送和接收电子邮件", "connectedAndActive": "已连接并活跃", "disconnect": "断开连接", "connect": "连接Gmail", "connecting": "连接中..."}}, "calendar": {"title": "Google日历集成", "description": "与Google Calendar进行集成", "addCalendar": "添加Google日历", "editCalendar": "编辑Google日历", "viewCalendar": "查看Google日历", "addFirstCalendar": "添加第一个Google日历", "form": {"title": "Google日历配置", "displayName": "显示名称", "displayNameHelp": "此Google日历配置的显示名称", "displayNamePlaceholder": "例如：主要Google日历配置", "clientId": "客户端ID", "clientIdHelp": "从Google Cloud Console获取的客户端ID", "clientIdPlaceholder": "输入客户端ID", "clientSecret": "客户端密钥", "clientSecretHelp": "从Google Cloud Console获取的客户端密钥", "clientSecretPlaceholder": "输入客户端密钥", "refreshToken": "刷新令牌", "refreshTokenHelp": "用于保持连接的刷新令牌", "refreshTokenPlaceholder": "输入刷新令牌", "calendarId": "日历ID", "calendarIdHelp": "要同步的特定日历ID（可选）", "calendarIdPlaceholder": "输入日历ID（可选）", "isActive": "激活", "isActiveHelp": "激活/停用此集成", "syncEnabled": "启用同步", "syncEnabledHelp": "自动同步事件", "testConnection": "测试连接", "connectWithOAuth": "使用OAuth连接", "authSuccess": "认证成功", "authFailed": "认证失败"}}, "table": {"integrationName": "集成名称", "type": "类型", "status": "状态", "actions": "操作"}, "dashboard": {"totalIntegrations": "总集成数", "totalIntegrationsDesc": "已配置的集成总数", "activeIntegrations": "活跃集成", "activeIntegrationsDesc": "正常工作的集成数量", "websites": "网站", "websitesDesc": "已集成的网站数量", "apiCallsToday": "今日API调用", "apiCallsTodayDesc": "今日API调用次数", "webhooks": "Webhooks", "webhooksDesc": "活跃的webhook数量", "integrationErrors": "集成错误", "integrationErrorsDesc": "需要处理的错误集成数量"}, "externalAgents": {"title": "外部代理管理", "description": "通过MCP、REST API、WebSocket与外部代理集成", "overview": "概览", "protocolDistribution": "协议分布", "quickActions": "快速操作", "recentActivity": "最近活动", "noRecentActivity": "暂无最近活动", "activityWillAppear": "外部代理活动将在此处显示", "gettingStarted": "开始使用", "gettingStartedDescription": "尚未配置任何外部代理。通过创建您的第一个代理开始使用。", "step1": "选择集成协议（MCP、Google Agent、REST API等）", "step2": "配置端点和身份验证信息", "step3": "测试连接并开始使用", "createDescription": "创建具有自定义协议配置的新外部代理", "manageDescription": "管理所有现有外部代理及其设置", "protocolsDescription": "查看和配置支持的协议", "analyticsDescription": "查看性能分析和使用统计"}, "boxChat": {"configTitle": "聊天框配置", "welcomeText": "欢迎消息", "welcomeTextPlaceholder": "输入欢迎消息", "avatar": "头像", "avatarError": "头像错误", "avatarErrorDesc": "只接受图片文件", "avatarSizeError": "文件大小不得超过2MB", "avatarUpdateSuccess": "头像更新成功！", "avatarUpdateSuccessDesc": "聊天框头像已更新。", "avatarUpdateError": "头像更新失败！", "avatarUpdateErrorDesc": "请稍后重试。", "clickToChangeAvatar": "点击图片更换头像", "clickToUploadAvatar": "点击框架上传头像", "avatarDescription": "接受图片文件（JPEG、PNG、WebP），最大2MB", "placeholderMessage": "占位符消息", "placeholderMessagePlaceholder": "输入您的消息...", "displayMode": "显示模式", "sideMode": "侧边模式", "colorPrimary": "主色调", "icon": "图标", "iconError": "图标错误", "iconErrorDesc": "只接受图片文件", "iconSizeError": "文件大小不得超过2MB", "clickToChangeIcon": "点击图片更换图标", "clickToUploadIcon": "点击框架上传图标", "iconDescription": "接受图片文件（JPEG、PNG、WebP），最大2MB", "selectedFormat": "已选格式", "bannerMediaIds": "横幅媒体", "addBannerMedia": "添加横幅媒体", "banners": "横幅图片", "bannerError": "横幅错误", "bannerErrorDesc": "只接受图片文件", "bannerSizeError": "文件大小不得超过10MB", "editBanner": "编辑横幅", "bannerSlot": "横幅 {{index}}", "addBanner": "添加横幅", "quickMessages": "快速消息", "quickMessagePlaceholder": "快速消息", "addQuickMessage": "添加快速消息", "components": "组件", "componentPlaceholder": "组件名称", "addComponent": "添加组件", "iframeWidth": "宽度（像素）", "iframeHeight": "高度（像素）", "textColor": "文字颜色", "backgroundColor": "背景颜色", "updateSuccess": "更新成功！", "updateSuccessDesc": "聊天框配置已更新。", "updateError": "更新失败！", "updateErrorDesc": "请稍后重试。", "updating": "更新中...", "update": "更新", "corner": "角落", "center": "中心", "selectDisplayMode": "选择显示模式", "floating": "浮动", "fixed": "固定", "selectSideMode": "选择侧边模式", "selectMedia": "选择媒体"}, "providerModel": {"title": "提供商模型管理", "create": "创建提供商模型", "form": {"view": "查看提供商模型", "edit": "编辑提供商模型", "create": "创建提供商模型", "viewDescription": "查看提供商模型的详细信息", "editDescription": "编辑提供商模型信息。只能更改名称和API密钥。", "createDescription": "创建新的提供商模型以与AI提供商集成", "cannotChange": "无法更改", "fields": {"type": "提供商类型", "name": "提供商模型名称", "namePlaceholder": "输入提供商模型名称", "apiKey": "API密钥", "apiKeyPlaceholder": "输入API密钥", "apiKeyPlaceholderEdit": "如果不想更改API密钥请留空..."}}, "actions": {"cancel": "取消", "save": "保存", "create": "创建提供商模型", "edit": "编辑", "delete": "删除", "retry": "重试"}, "empty": {"title": "暂无提供商模型", "description": "添加提供商模型以开始使用。"}, "error": {"title": "数据加载错误", "description": "加载提供商模型列表时发生错误。请重试。"}, "confirmations": {"deleteTitle": "确认删除", "delete": "您确定要删除此提供商模型吗？", "bulkDelete": "您确定要删除{{count}}个选中的提供商模型吗？", "noItemsSelected": "未选择任何项目"}, "validation": {"name": {"required": "名称不能为空", "maxLength": "名称不能超过255个字符"}, "type": {"invalid": "无效的提供商类型"}, "apiKey": {"required": "API密钥不能为空", "minLength": "API密钥至少需要10个字符", "format": "API密钥只能包含字母、数字、连字符、下划线和点号"}, "apiSecret": {"required": "API密钥不能为空", "minLength": "API密钥至少需要6个字符", "maxLength": "API密钥不能超过255个字符"}, "shopId": {"required": "Shop ID不能为空", "maxLength": "Shop ID不能超过100个字符"}, "clientId": {"required": "Client ID不能为空", "maxLength": "Client ID不能超过255个字符"}, "settings": {"invalidJson": "设置必须是有效的JSON格式"}, "phone": {"required": "电话号码不能为空", "maxLength": "电话号码不能超过20个字符"}, "address": {"required": "地址不能为空"}, "weight": {"min": "最小重量为0.1kg"}, "serviceType": {"required": "服务类型不能为空"}, "timeout": {"min": "最小超时为1000ms", "max": "最大超时为300000ms"}}, "provider": "提供商", "name": "显示名称", "apiKey": "API密钥", "nameRequired": "名称是必需的", "apiKeyRequired": "API密钥是必需的", "createSuccess": "创建成功", "createSuccessMessage": "提供商模型已成功创建", "createError": "创建提供商模型错误", "createErrorMessage": "创建提供商模型时发生错误", "metaNamePlaceholder": "例如：我的Meta密钥", "metaApiKeyPlaceholder": "meta-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "metaApiKeyFormat": "Meta API密钥必须以\"meta-\"开头", "createMetaButton": "创建Meta提供商模型", "xaiNamePlaceholder": "例如：我的XAI密钥", "xaiApiKeyPlaceholder": "xai-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "xaiApiKeyFormat": "XAI API密钥必须以\"xai-\"开头", "createXaiButton": "创建XAI提供商模型"}, "ai": {"openai": {"title": "集成OpenAI"}, "anthropic": {"title": "集成Anthropic"}, "google": {"title": "集成Google Gemini"}, "meta": {"title": "集成Meta <PERSON>"}, "deepseek": {"title": "集成DeepSeek"}, "xai": {"title": "集成XAI"}}, "openai": {"title": "OpenAI密钥列表", "provider": "提供商", "description": "管理已集成的OpenAI API密钥", "addKey": "添加OpenAI密钥", "systemModelsTitle": "系统模型", "userModelsTitle": "用户模型", "editKey": "编辑OpenAI密钥", "deleteKey": "删除OpenAI密钥", "keyName": "显示名称", "keyNamePlaceholder": "例如：我的OpenAI密钥", "keyNameHelp": "用于区分不同API密钥的名称", "apiKey": "API密钥", "apiKeyPlaceholder": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "apiKeyHelp": "从https://platform.openai.com/api-keys获取API密钥", "status": "状态", "viewUserModels": "查看用户模型", "viewSystemModels": "查看系统模型", "createdAt": "创建日期", "actions": "操作", "confirmDelete": "您确定要删除此API密钥吗？", "deleteSuccess": "OpenAI密钥删除成功", "deleteError": "删除OpenAI密钥时发生错误", "createSuccess": "OpenAI密钥创建成功", "createError": "创建OpenAI密钥时发生错误", "loadError": "无法加载OpenAI密钥列表", "noKeys": "暂无OpenAI密钥", "noKeysDescription": "添加您的第一个API密钥以开始使用OpenAI"}, "anthropic": {"title": "Anthropic Claude密钥列表", "description": "管理已集成的Anthropic Claude API密钥", "provider": "提供商", "addKey": "添加Anthropic密钥", "editKey": "编辑Anthropic密钥", "deleteKey": "删除Anthropic密钥", "keyName": "显示名称", "keyNamePlaceholder": "例如：我的Anthropic密钥", "keyNameHelp": "用于区分不同API密钥的名称", "apiKey": "API密钥", "apiKeyPlaceholder": "sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "apiKeyHelp": "从https://console.anthropic.com/获取API密钥", "status": "状态", "viewUserModels": "查看用户模型", "viewSystemModels": "查看系统模型", "createdAt": "创建日期", "actions": "操作", "confirmDelete": "您确定要删除此API密钥吗？", "deleteSuccess": "Anthropic密钥删除成功", "deleteError": "删除Anthropic密钥时发生错误", "createSuccess": "Anthropic密钥创建成功", "createError": "创建Anthropic密钥时发生错误", "loadError": "无法加载Anthropic密钥列表", "noKeys": "暂无Anthropic密钥", "noKeysDescription": "添加您的第一个API密钥以开始使用Anthropic Claude"}, "gemini": {"title": "Google Gemini密钥列表", "description": "管理已集成的Google Gemini API密钥", "addKey": "添加Gemini密钥", "provider": "提供商", "editKey": "编辑Gemini密钥", "deleteKey": "删除Gemini密钥", "keyName": "显示名称", "keyNamePlaceholder": "例如：我的Gemini密钥", "keyNameHelp": "用于区分不同API密钥的名称", "apiKey": "API密钥", "apiKeyPlaceholder": "AIzaxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "apiKeyHelp": "从Google AI Studio获取API密钥", "status": "状态", "viewUserModels": "查看用户模型", "viewSystemModels": "查看系统模型", "createdAt": "创建日期", "actions": "操作", "confirmDelete": "您确定要删除此API密钥吗？", "deleteSuccess": "Gemini密钥删除成功", "deleteError": "删除Gemini密钥时发生错误", "createSuccess": "Gemini密钥创建成功", "createError": "创建Gemini密钥时发生错误", "loadError": "无法加载Gemini密钥列表", "noKeys": "暂无Gemini密钥", "noKeysDescription": "添加您的第一个API密钥以开始使用Google Gemini"}, "deepseek": {"title": "DeepSeek密钥列表", "description": "管理已集成的DeepSeek API密钥", "provider": "提供商", "addKey": "添加DeepSeek密钥", "editKey": "编辑DeepSeek密钥", "deleteKey": "删除DeepSeek密钥", "keyName": "显示名称", "keyNamePlaceholder": "例如：我的DeepSeek密钥", "keyNameHelp": "用于区分不同API密钥的名称", "apiKey": "API密钥", "apiKeyPlaceholder": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "apiKeyHelp": "从https://platform.deepseek.com/获取API密钥", "status": "状态", "viewUserModels": "查看用户模型", "viewSystemModels": "查看系统模型", "createdAt": "创建日期", "actions": "操作", "confirmDelete": "您确定要删除此API密钥吗？", "deleteSuccess": "DeepSeek密钥删除成功", "deleteError": "删除DeepSeek密钥时发生错误", "createSuccess": "DeepSeek密钥创建成功", "createError": "创建DeepSeek密钥时发生错误", "loadError": "无法加载DeepSeek密钥列表", "noKeys": "暂无DeepSeek密钥", "noKeysDescription": "添加您的第一个API密钥以开始使用DeepSeek"}, "xaiGrok": {"title": "XAI Grok密钥列表", "description": "管理已集成的XAI Grok API密钥", "provider": "提供商", "addKey": "添加XAI Grok密钥", "editKey": "编辑XAI Grok密钥", "deleteKey": "删除XAI Grok密钥", "keyName": "显示名称", "keyNamePlaceholder": "例如：我的XAI Grok密钥", "keyNameHelp": "用于区分不同API密钥的名称", "apiKey": "API密钥", "apiKeyPlaceholder": "xai-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "apiKeyHelp": "从xAI平台获取API密钥", "status": "状态", "viewUserModels": "查看用户模型", "viewSystemModels": "查看系统模型", "createdAt": "创建日期", "actions": "操作", "confirmDelete": "您确定要删除此API密钥吗？", "deleteSuccess": "XAI Grok密钥删除成功", "deleteError": "删除XAI Grok密钥时发生错误", "createSuccess": "XAI Grok密钥创建成功", "createError": "创建XAI Grok密钥时发生错误", "loadError": "无法加载XAI Grok密钥列表", "noKeys": "暂无XAI Grok密钥", "noKeysDescription": "添加您的第一个API密钥以开始使用XAI Grok"}, "emailSMTP": {"title": "电子邮件服务器配置", "titleManagement": "电子邮件SMTP管理", "description": "配置用于发送电子邮件的SMTP服务器", "descriptionManagement": "管理SMTP配置", "configName": "配置名称", "configNameHelp": "例如：公司电子邮件，个人Gmail", "configNamePlaceholder": "输入配置名称", "senderEmail": "发件人电子邮件", "senderName": "发件人名称", "senderNameHelp": "收件人查看电子邮件时显示的名称", "senderNamePlaceholder": "输入发件人名称", "sendTest": "发送测试"}, "shipping": {"title": "物流集成", "description": "与GHN、GHTK、Ahamove 等物流提供商进行集成", "addProvider": "添加物流提供商", "editProvider": "编辑物流提供商", "viewProvider": "查看物流提供商", "addFirstProvider": "添加第一个物流提供商", "selectProviderType": "选择物流提供商", "testConnection": "测试连接", "ghtk": {"title": "GHTK物流提供商配置"}, "ghn": {"title": "GHN物流提供商配置"}, "providers": {"ghtk": {"form": {"title": "GHTK物流提供商配置", "displayName": "显示名称", "username": "用户名", "password": "密码", "usernameHelp": "来自GHTK的用户名用于身份验证", "usernamePlaceholder": "输入来自GHTK的用户名", "usernamePlaceholderEdit": "输入新的用户名（如无需更改请留空）", "usernamePlaceholderReadonly": "数据已加密", "passwordHelp": "来自GHTK的密码用于身份验证", "passwordPlaceholder": "输入来自GHTK的密码", "passwordPlaceholderEdit": "输入新的密码（如无需更改请留空）", "passwordPlaceholderReadonly": "数据已加密", "displayNameHelp": "此GHTK配置的显示名称", "displayNamePlaceholder": "例如：主要GHTK配置", "token": "令牌", "tokenHelp": "用于身份验证的GHTK API令牌", "tokenPlaceholder": "输入GHTK令牌", "tokenPlaceholderEdit": "输入新的令牌（如无需更改请留空）", "tokenPlaceholderReadonly": "数据已加密", "timeout": "超时（毫秒）", "timeoutHelp": "每个请求的最大等待时间（默认：30000毫秒）", "timeoutPlaceholder": "30000", "testConnection": "测试连接", "testConnectionResult": "GHTK连接测试结果", "testConnectionSuccess": "✅连接成功", "testConnectionFailed": "❌连接失败", "testConnectionError": "❌连接错误", "testConnectionSuccessDetails": "• API端点可访问\n• 令牌验证有效\n• 准备就绪", "testConnectionErrorDetails": "• 请检查令牌是否正确\n• 确保网络连接稳定\n• 如问题持续，请联系GHTK", "testConnectionErrorDefault": "无法连接到GHTK", "testConnectionRequired": "令牌是测试连接所必需的", "testMode": "测试模式", "createButton": "创建GHTK配置"}}, "ghn": {"form": {"title": "GHN物流提供商配置", "displayName": "显示名称", "displayNameHelp": "此GHN配置的显示名称", "displayNamePlaceholder": "例如：主要GHN配置", "token": "令牌", "tokenHelp": "用于身份验证的GHN API令牌", "tokenPlaceholder": "输入GHN令牌", "tokenPlaceholderEdit": "输入新的令牌（如无需更改请留空）", "tokenPlaceholderReadonly": "数据已加密", "shopId": "商店ID", "shopIdHelp": "用于标识商店的GHN商店ID", "shopIdPlaceholder": "输入GHN商店ID", "shopIdPlaceholderEdit": "输入新的商店ID（如无需更改请留空）", "shopIdPlaceholderReadonly": "数据已加密", "timeout": "超时（毫秒）", "timeoutHelp": "每个请求的最大等待时间（默认：30000毫秒）", "timeoutPlaceholder": "30000", "testConnection": "测试连接", "testConnectionResult": "GHN连接测试结果", "testConnectionSuccess": "✅连接成功", "testConnectionFailed": "❌连接失败", "testConnectionError": "❌连接错误", "testConnectionSuccessDetails": "• API端点可访问\n• 令牌和商店ID验证有效\n• 准备就绪", "testConnectionErrorDetails": "• 请检查令牌和商店ID是否正确\n• 确保网络连接稳定\n• 如问题持续，请联系GHN", "testConnectionErrorDefault": "无法连接到GHN", "testConnectionRequired": "令牌和商店ID是测试连接所必需的", "testMode": "测试模式", "createButton": "创建GHN配置"}}, "ahamove": {"form": {"title": "Ahamove物流提供商配置", "displayName": "显示名称", "displayNameHelp": "此Ahamove配置的显示名称", "displayNamePlaceholder": "例如：主要Ahamove配置", "mobile": "手机号码", "mobileHelp": "用于身份验证的Ahamove手机号码", "mobilePlaceholder": "输入Ahamove手机号码", "mobilePlaceholderEdit": "输入新的手机号码（如无需更改请留空）", "mobilePlaceholderReadonly": "数据已加密", "testMode": "测试模式", "createButton": "创建Ahamove配置"}}}, "validation": {"providerType": {"invalid": "无效的物流提供商类型"}, "providerName": {"required": "提供商名称是必需的", "maxLength": "提供商名称不能超过100个字符"}, "name": {"required": "显示名称是必需的", "maxLength": "显示名称不能超过100个字符"}, "token": {"required": "令牌是必需的", "maxLength": "令牌不能超过255个字符"}, "username": {"required": "用户名是必需的", "maxLength": "用户名不能超过255个字符"}, "password": {"required": "密码是必需的", "minLength": "密码至少需要6个字符", "maxLength": "密码不能超过255个字符"}, "mobile": {"required": "手机号码是必需的", "minLength": "手机号码至少需要10个字符", "maxLength": "手机号码不能超过15个字符", "format": "手机号码格式无效"}, "timeout": {"min": "超时时间至少需要1000毫秒", "max": "超时时间不能超过300000毫秒"}, "shopId": {"required": "商店ID是必需的", "maxLength": "商店ID不能超过999999999"}}}, "cloudStorage": {"title": "云存储集成", "description": "与Google Drive、OneDrive、Dropbox等云存储服务进行集成", "addStorage": "添加云存储", "editStorage": "编辑云存储", "viewStorage": "查看云存储", "addFirstStorage": "添加第一个云存储", "form": {"title": "云存储配置", "displayName": "显示名称", "displayNameHelp": "此云存储配置的显示名称", "displayNamePlaceholder": "例如：主要云存储配置", "providerType": "提供商", "providerTypeHelp": "选择云存储提供商", "providerTypePlaceholder": "选择提供商", "clientId": "客户端ID", "clientIdHelp": "从提供商控制台获取的客户端ID", "clientIdPlaceholder": "输入客户端ID", "clientSecret": "客户端密钥", "clientSecretHelp": "从提供商控制台获取的客户端密钥", "clientSecretPlaceholder": "输入客户端密钥", "refreshToken": "刷新令牌", "refreshTokenHelp": "用于保持连接的刷新令牌", "refreshTokenPlaceholder": "输入刷新令牌", "rootFolderId": "根文件夹ID"}}, "common": {"refresh": "刷新"}, "sms": {"title": "短信集成", "description": "配置与短信提供商的集成", "configName": "配置名称", "configNameHelp": "例如：营销短信，通知短信", "configNamePlaceholder": "输入配置名称", "isActive": "激活", "provider": "短信提供商", "selectProvider": "选择提供商", "otherProvider": "其他", "fromPhone": "发送电话号码", "fromPhoneHelp": "发送短信时显示的电话号码", "apiKey": "API密钥", "apiSecret": "API密钥", "testConnection": "测试连接", "testResult": "连接测试结果", "testSuccess": "成功连接到短信提供商", "testError": "无法连接到短信提供商。请检查您的配置。", "saveSuccess": "短信配置保存成功", "saveError": "保存短信配置时出错"}, "email": {"providers": {"outlook": "Microsoft Outlook", "yahoo": "Yahoo Mail", "sendgrid": "SendGrid", "mailchimp": "Mailchimp Transactional", "amazonSes": "Amazon SES", "mailgun": "Mailgun", "gmail": "Gmail"}}, "social": {"title": "社交媒体集成", "description": "管理与社交媒体平台的集成", "facebook": "Facebook", "instagram": "Instagram", "twitter": "Twitter", "youtube": "YouTube", "tiktok": "TikTok", "connect": "连接", "disconnect": "断开连接", "connected": "已连接", "notConnected": "未连接", "connectSuccess": "成功连接到{{platform}}", "connectError": "连接到{{platform}}时出错", "disconnectSuccess": "成功从{{platform}}断开连接", "disconnectError": "从{{platform}}断开连接时出错", "networkAriaLabel": "{{name}}社交网络"}, "zalo": {"personal": {"title": "Zalo个人集成", "description": "连接您的个人Zalo账户以使用营销功能", "qrTitle": "扫描二维码连接", "qrInstruction": "使用Zalo应用扫描二维码", "qrDescription": "打开Zalo应用 > 扫描二维码 > 扫描下方二维码", "qrNote": "二维码将在5分钟后过期", "qrGenerated": "二维码已生成", "qrError": "无法生成二维码", "connected": "成功连接到Zalo", "statusTitle": "连接状态", "status": {"disconnected": "未连接", "connecting": "等待连接...", "connected": "连接成功"}, "instructionsTitle": "连接说明", "step1": "在手机上打开Zalo应用", "step2": "选择二维码扫描图标", "step3": "扫描左侧显示的二维码", "step4": "在Zalo应用中确认连接", "featuresTitle": "连接后的功能", "feature1": "发送自动消息", "feature2": "管理好友列表", "feature3": "创建营销活动", "feature4": "性能分析"}}, "facebook": {"title": "Facebook", "description": "管理已链接的Facebook账户", "addPage": "添加Facebook页面", "connecting": "连接中...", "processing": "正在处理Facebook连接...", "search": "搜索Facebook页面...", "loadError": "无法加载Facebook页面列表", "noPages": "暂无Facebook页面", "noPagesDescription": "您还没有链接任何Facebook页面。添加Facebook页面以开始使用。", "confirmDelete": "您确定要删除此Facebook页面吗？", "connectAgent": "连接代理", "disconnectAgent": "断开代理连接", "status": {"label": "状态", "active": "活跃", "inactive": "非活跃", "error": "错误"}, "agent": {"label": "代理"}, "pageName": "页面名称", "personalName": "个人姓名", "pageId": "页面ID", "isActive": "是否活跃", "hasError": "是否有错误"}, "accounts": {"title": "关联账户", "description": "管理与系统关联的账户", "addAccount": "添加账户", "removeAccount": "删除账户", "accountName": "账户名称", "accountType": "账户类型", "linkedDate": "关联日期", "noAccounts": "尚未关联任何账户", "confirmRemove": "您确定要删除此账户吗？", "removeSuccess": "账户删除成功", "removeError": "删除账户时出错", "defaultAccount": "默认账户", "failedToLoad": "无法加载账户"}, "website": {"title": "网站集成", "description": "管理与网站的集成", "domain": "域名", "apiKey": "API密钥", "secretKey": "密钥", "webhookUrl": "Webhook网址", "generateKey": "生成新密钥", "copyKey": "复制", "keyCopied": "已复制到剪贴板", "saveSuccess": "网站配置保存成功", "saveError": "保存网站配置时出错", "confirmActivate": "您确定要激活此网站吗？", "confirmDeactivate": "您确定要停用此网站吗？", "host": "主机", "status": "状态", "verified": "已验证", "notVerified": "未验证", "connected": "已连接", "notConnected": "未连接", "agent": "代理", "noAgent": "未连接代理", "createdAt": "创建时间", "actions": "操作", "widgetScript": "小部件脚本", "widgetScriptDesc": "将此脚本复制并粘贴到您的网站中以集成聊天小部件。", "createTitle": "添加新网站", "createSuccess": "网站创建成功！", "createSuccessDesc": "网站已添加到列表中。", "createError": "创建网站失败！", "createErrorDesc": "请稍后重试。", "creating": "创建中...", "create": "创建网站", "deleteSuccess": "网站删除成功！", "deleteSuccessDesc": "网站已从列表中移除。", "deleteError": "删除网站失败！", "deleteErrorDesc": "请稍后重试。", "copySuccess": "已复制！", "copySuccessDesc": "脚本已复制到剪贴板。", "copyScript": "复制脚本", "noWebsites": "暂无网站", "noWebsitesDescription": "您还没有添加任何网站。添加网站以开始使用。", "addWebsite": "添加网站", "noSearchResults": "未找到结果", "noSearchResultsDescription": "没有网站符合您的搜索条件。", "clearFilters": "清除筛选", "confirmDelete": "确认删除", "confirmDeleteDesc": "您确定要删除此网站吗？此操作无法撤销。", "deleting": "删除中...", "form": {"websiteName": "网站名称", "websiteNamePlaceholder": "输入网站名称", "host": "主机/域名", "hostPlaceholder": "redai.vn 或 https://www.redai.vn", "hostDescription": "输入域名或完整URL。系统将自动规范化。"}, "filter": {"websiteName": "网站名称", "host": "主机", "createdAt": "创建时间", "verify": "验证状态", "asc": "升序", "desc": "降序", "all": "全部", "verified": "已验证", "unverified": "未验证"}, "logoError": "Logo错误", "logoErrorDesc": "只接受图片文件", "logoSizeError": "文件大小不得超过5MB", "logoUpdateSuccess": "Logo更新成功！", "logoUpdateSuccessDesc": "网站Logo已更新。", "logoUpdateError": "Logo更新失败！", "logoUpdateErrorDesc": "请稍后重试。", "editBoxChatConfig": "配置聊天框", "changeLogo": "更换Logo"}, "bankAccount": {"mb": {"title": "MB银行账户列表", "description": "输入MB银行账户信息以与系统关联"}, "acb": {"title": "ACB银行账户列表", "description": "输入ACB银行账户信息以与系统关联"}, "ocb": {"title": "OCB银行账户列表", "description": "输入OCB银行账户信息以与系统关联"}, "kienlong": {"title": "<PERSON><PERSON><PERSON>银行账户列表", "description": "输入<PERSON>ên Long银行账户信息以与系统关联"}, "createVirtualAccount": "创建虚拟账户", "addAccount": "添加账户", "disableVA": "禁用虚拟", "reconnectAccount": "重新连接账户", "reconnectOtpSentMessage": "验证码已发送到手机号码", "enterOtpToReconnect": "输入验证码以重新连接账户", "otpSentMessage": "验证码已发送到手机号码", "enableVA": "启用虚拟", "realAccount": "真实账户", "accountType": "账户类型", "enterOtpToDelete": "输入验证码以删除账户", "forceDeleteWarning": "您确定要删除此银行账户吗？此账户未连接到银行API，将立即删除。", "confirmDelete": "确认强制删除", "balance": "余额", "confirmReconnect": "确认重新连接", "title": "银行账户集成", "description": "管理与系统集成的银行账户", "createTitle": "添加银行账户", "createDescription": "输入银行账户信息以与系统集成", "bankName": "银行", "createVirtualAccountFromReal": "从真实账户创建虚拟账户", "reconnect": "重新连接", "selectBank": "选择银行", "label": "标签", "connectionStatus": "连接状态", "connected": "已连接", "notConnected": "未连接", "activated": "已激活", "notActivated": "未激活", "accountNumber": "账号", "accountNumberPlaceholder": "输入账号", "accountNumberHelp": "输入银行账号（6-20位数字）", "accountName": "账户名称", "bankApiConnected": "银行API已连接", "accountHolderName": "账户持有人姓名", "idNumber": "身份证号", "idNumberPlaceholder": "输入身份证号", "idNumberHelp": "输入在银行注册的身份证号", "phoneNumber": "手机号码", "phoneNumberPlaceholder": "输入手机号码", "phoneNumberHelp": "在银行注册的手机号码，用于接收验证码", "storeName": "店铺名称", "storeNamePlaceholder": "输入店铺名称", "storeNameHelp": "您的店铺/商户名称", "storeAddress": "店铺地址", "storeAddressPlaceholder": "输入店铺地址", "storeAddressHelp": "您的店铺/商户详细地址", "create": "创建账户", "bankInfo": "银行信息", "status": "状态", "statusActive": "活跃", "statusPending": "待验证", "statusInactive": "不活跃", "statusSuspended": "已暂停", "statusExpired": "已过期", "virtualAccount": "虚拟账户", "createVA": "创建虚拟账户", "createSuccess": "账户创建成功", "createSuccessDescription": "银行账户已成功创建", "createError": "创建账户失败", "createErrorDescription": "无法创建银行账户。请检查信息。", "createCompleteSuccess": "账户创建完成", "createCompleteSuccessDescription": "银行账户已成功创建并激活", "deleteSuccess": "删除成功", "deleteSuccessDescription": "银行账户已成功删除", "deleteError": "删除失败", "deleteErrorDescription": "无法删除银行账户。请重试。", "deleteConfirmTitle": "确认删除账户", "deleteConfirmMessage": "您确定要删除此银行账户吗？此操作无法撤销。", "bulkDeleteSuccess": "删除成功", "bulkDeleteSuccessDescription": "已删除{{count}}个银行账户", "bulkDeleteError": "删除失败", "bulkDeleteErrorDescription": "无法删除银行账户。请重试。", "bulkDeleteConfirmTitle": "确认批量删除账户", "bulkDeleteConfirmMessage": "您确定要删除{{count}}个选中的银行账户吗？此操作无法撤销。", "selectAccountsToDelete": "请至少选择一个账户进行删除", "createVASuccess": "虚拟账户创建成功", "createVASuccessDescription": "虚拟账户已成功创建", "createVAError": "创建虚拟账户失败", "createVAErrorDescription": "无法创建虚拟账户。请重试。", "activateSuccess": "激活成功", "activateSuccessDescription": "银行账户已成功激活", "otpVerification": "短信验证", "otpVerificationDescription": "输入发送到{{bankName}}注册手机号的验证码", "enterOtp": "输入验证码", "otpSent": "验证码已发送", "otpSentDescription": "请检查您手机上的短信", "otpSendError": "发送验证码失败", "otpSendErrorDescription": "无法发送验证码。请稍后重试。", "resendOtp": "重新发送验证码", "resendOtpCountdown": "{{countdown}}秒后重新发送", "verify": "验证", "otpVerifySuccess": "验证成功", "otpVerifySuccessDescription": "银行账户已成功激活", "otpVerifyError": "验证失败", "otpVerifyErrorDescription": "验证码错误或已过期。请重试。", "invalidOtpLength": "验证码长度错误", "invalidOtpLengthDescription": "验证码必须为{{length}}位", "acbOtpDescription": "输入发送到ACB注册电话号码的OTP代码以完成连接", "ocbOtpVerification": "OCB OTP验证", "ocbOtpDescription": "输入发送到OCB注册电话号码的OTP代码以完成连接", "ocbOtpLengthError": "OTP代码必须为6个字符", "klbOtpVerification": "建隆银行OTP验证", "mbOtpVerification": "MB银行OTP验证", "mbOtpDescription": "输入发送到MB银行注册电话号码的OTP代码以完成连接", "klbOtpDescription": "输入发送到建隆银行注册电话号码的OTP代码以完成连接", "klbOtpLengthError": "OTP代码必须为6个字符", "resendOtpIn": "{{seconds}} 秒后重新发送代码", "otpExpired": "OTP代码已过期", "resendOtpSuccess": "OTP重新发送成功", "resendOtpSuccessDescription": "新的OTP代码已发送到注册的电话号码", "resendOtpError": "重新发送OTP失败", "activatingConnection": "正在激活与银行的连接...", "connectionCompleted": "连接已成功建立！", "pleaseWait": "请稍候...", "setupComplete": "设置完成！", "otpProcessError": "验证过程中发生错误", "connectionSuccess": "连接成功", "mbConnectionSuccessDescription": "MB银行账户已成功链接并激活", "acbConnectionSuccessDescription": "ACB账户已成功链接并激活", "ocbConnectionSuccessDescription": "OCB账户已成功链接并激活", "klbConnectionSuccessDescription": "建隆银行账户已成功链接并激活", "otpError": "OTP错误", "otpLengthError": "OTP代码必须为8个字符"}, "banking": {"bankAccount": "银行账户", "bank": "银行", "connectionMethod": "连接方式", "estimatedBalance": "预估余额", "accountName": "账户名称", "accountNumber": "账号", "connectApi": "连接银行API", "acb": {"title": "关联ACB账户", "description": "输入ACB银行账户信息以与系统关联", "accountHolderName": "账户持有人姓名", "accountHolderNamePlaceholder": "输入ACB账户持有人姓名", "accountNumber": "账号", "accountNumberPlaceholder": "输入ACB账号", "phoneNumber": "电话号码", "phoneNumberPlaceholder": "输入在ACB注册的电话号码", "label": "标签", "labelPlaceholder": "输入标签（可选）", "saveSuccess": "ACB账户信息保存成功", "saveError": "保存ACB账户信息时发生错误", "submitError": "保存账户信息时发生错误", "otpVerificationTitle": "ACB OTP验证"}, "mb": {"title": "关联MB Bank账户", "description": "输入MB Bank银行账户信息以与系统关联", "accountHolderName": "账户持有人姓名", "accountHolderNamePlaceholder": "姓名将从API自动获取", "accountNumber": "账号", "accountNumberPlaceholder": "输入MB Bank账号", "identificationNumber": "身份证号", "identificationNumberPlaceholder": "输入在MB Bank注册的身份证号", "phoneNumber": "电话号码", "phoneNumberPlaceholder": "输入在MB Bank注册的电话号码", "label": "标签", "labelPlaceholder": "输入标签（可选）", "fetchNameSuccess": "账户持有人姓名获取成功", "fetchNameError": "无法获取账户持有人姓名", "saveSuccess": "MB Bank账户信息保存成功", "saveError": "保存MB Bank账户信息时发生错误", "submitError": "保存账户信息时发生错误"}, "ocb": {"title": "关联OCB账户", "description": "输入OCB银行账户信息以与系统关联", "accountHolderName": "账户持有人姓名", "accountHolderNamePlaceholder": "姓名将从API自动获取", "accountNumber": "账号", "accountNumberPlaceholder": "输入OCB账号", "identificationNumber": "身份证号", "identificationNumberPlaceholder": "输入在OCB注册的身份证号", "phoneNumber": "电话号码", "phoneNumberPlaceholder": "输入在OCB注册的电话号码", "label": "标签", "labelPlaceholder": "输入标签（可选）", "fetchNameSuccess": "账户持有人姓名获取成功", "fetchNameError": "无法获取账户持有人姓名", "saveSuccess": "OCB账户信息保存成功", "saveError": "保存OCB账户信息时发生错误", "submitError": "保存账户信息时发生错误"}, "kienlong": {"title": "关联建隆银行账户", "description": "输入建隆银行账户信息以与系统关联", "accountHolderName": "账户持有人姓名", "accountHolderNamePlaceholder": "输入建隆银行账户持有人姓名", "accountNumber": "账号", "accountNumberPlaceholder": "输入建隆银行账号", "identificationNumber": "身份证号", "identificationNumberPlaceholder": "输入注册账户的身份证号", "phoneNumber": "电话号码", "phoneNumberPlaceholder": "输入在建隆银行注册的电话号码", "label": "标签", "labelPlaceholder": "输入标签（可选）", "saveSuccess": "建隆银行账户信息保存成功", "saveError": "保存建隆银行账户信息时发生错误", "submitError": "保存账户信息时发生错误"}, "validation": {"bankId": {"required": "银行ID是必填项"}, "accountNumber": {"required": "账号是必填项", "minLength": "账号必须至少包含6个字符", "maxLength": "账号不能超过20个字符", "numbersOnly": "账号只能包含数字"}, "accountHolderName": {"required": "账户持有人姓名是必填项", "minLength": "账户持有人姓名必须至少包含2个字符", "maxLength": "账户持有人姓名不能超过100个字符", "lettersOnly": "账户持有人姓名只能包含字母和空格", "invalidChars": "账户持有人姓名包含无效字符"}, "phoneNumber": {"required": "电话号码是必填项", "minLength": "电话号码必须至少包含10个字符", "maxLength": "电话号码不能超过20个字符", "numbersOnly": "电话号码只能包含数字"}, "identificationNumber": {"required": "身份证号是必填项", "minLength": "身份证号必须至少包含9个字符", "maxLength": "身份证号不能超过100个字符", "numbersOnly": "身份证号只能包含数字"}, "label": {"lengthRange": "标签必须在2-100个字符之间", "required": "标签是必填项"}, "baseDomain": {"required": "Base Domain是必填项", "maxLength": "Base Domain不能超过255个字符"}}}, "apiKeys": {"title": "API密钥管理", "description": "描述", "id": "ID", "apiKey": "API密钥", "scope": "访问范围", "environment": "环境", "expiredAt": "过期日期", "status": "状态", "actions": "操作", "active": "活跃", "inactive": "不活跃", "addNew": "创建新API密钥", "createNew": "创建新API密钥", "list": "API密钥列表", "descriptionPlaceholder": "输入API密钥的描述", "selectDate": "选择过期日期", "noData": "没有可用数据", "searchPlaceholder": "按描述搜索...", "filterByStatus": "按状态筛选", "allStatuses": "所有状态", "enable": "启用", "disable": "禁用", "confirmDeleteMessage": "您确定要删除此API密钥吗？", "createSuccess": "已创建描述为{{description}}的新API密钥", "deleteSuccess": "已删除API密钥：{{apiKey}}", "toggleSuccess": "已{{action}}API密钥：{{apiKey}}"}, "sepayHub": {"title": "Sepay-Hub组织配置", "description": "管理Sepay-Hub银行账户和交易", "overview": {"totalBankAccounts": "银行账户总数", "totalVAAccounts": "VA账户总数", "totalTransactions": "交易总数", "availableTransactions": "可用交易数"}, "table": {"id": "ID", "companyId": "公司ID", "bankId": "银行ID", "accountHolderName": "账户持有人姓名", "accountNumber": "账号", "accumulated": "余额", "label": "标签", "bankApiConnected": "API状态", "lastTransaction": "最后交易", "createdAt": "创建时间", "updatedAt": "更新时间", "connected": "已连接", "notConnected": "未连接"}, "actions": {"buyMoreTransactions": "购买更多交易"}, "companyConfig": {"title": "公司配置", "companyInfo": "公司信息", "paymentConfig": "支付配置", "fullName": "全名", "fullNamePlaceholder": "输入全名", "status": "状态", "organizationName": "组织名称", "organizationNamePlaceholder": "输入组织名称", "shortName": "简称", "shortNamePlaceholder": "输入简称", "paymentCode": {"label": "支付代码识别配置", "on": "开启", "off": "关闭"}, "paymentCodePrefix": "支付代码前缀配置", "paymentCodePrefixPlaceholder": "输入支付代码前缀", "paymentCodeSuffixFrom": "支付代码后缀最小长度配置", "paymentCodeSuffixTo": "支付代码后缀最大长度配置", "paymentCodeSuffixCharacterType": "支付代码后缀字符类型配置", "characterType": {"numberAndLetter": "允许字母和数字", "numberOnly": "仅允许数字"}, "transactionAmount": "交易数量配置", "transactionAmountPlaceholder": "输入交易数量或'Unlimited'", "availableTransactions": "可用交易数量", "validation": {"organizationNameRequired": "组织名称是必需的", "shortNameRequired": "简称是必需的", "prefixRequired": "支付代码前缀是必需的", "suffixFromMin": "最小长度必须大于0", "suffixToGreater": "最大长度必须大于或等于最小长度", "transactionAmountMin": "交易数量必须大于或等于0"}}}, "database": {"title": "数据库集成"}}}