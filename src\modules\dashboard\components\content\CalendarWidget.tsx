import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Input, Icon } from '@/shared/components/common';
import { BaseWidgetProps } from '../../types';

interface CalendarEvent {
  id: string;
  title: string;
  date: string; // YYYY-MM-DD format
  color?: string;
  description?: string;
}

interface CalendarWidgetProps extends BaseWidgetProps {
  initialEvents?: CalendarEvent[];
  editable?: boolean;
  showWeekNumbers?: boolean;
  highlightToday?: boolean;
  compactMode?: boolean;
}

/**
 * Widget lịch với event highlighting và date picker
 */
const CalendarWidget: React.FC<CalendarWidgetProps> = ({
  className,
  initialEvents = [
    { id: '1', title: 'Meeting', date: '2024-01-15', color: '#3b82f6' },
    { id: '2', title: 'Deadline', date: '2024-01-20', color: '#ef4444' },
    { id: '3', title: 'Event', date: '2024-01-25', color: '#10b981' },
  ],
  editable = true,
  showWeekNumbers = false,
  highlightToday = true,
  compactMode = false,
  onPropsChange,
  ...props
}) => {
  const { t } = useTranslation(['dashboard', 'common']);
  
  // Use values from props if available
  const currentEvents = (props.events as CalendarEvent[]) || initialEvents;
  const currentSettings = {
    showWeekNumbers: (props.showWeekNumbers as boolean) ?? showWeekNumbers,
    highlightToday: (props.highlightToday as boolean) ?? highlightToday,
    compactMode: (props.compactMode as boolean) ?? compactMode,
  };

  const [currentDate, setCurrentDate] = useState(new Date());
  const [events, setEvents] = useState<CalendarEvent[]>(currentEvents);
  const [isEditing, setIsEditing] = useState(false);
  const [tempEvents, setTempEvents] = useState<CalendarEvent[]>(currentEvents);
  const [tempSettings, setTempSettings] = useState(currentSettings);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [newEventTitle, setNewEventTitle] = useState('');

  // Sync with props changes
  useEffect(() => {
    const newEvents = (props.events as CalendarEvent[]) || initialEvents;
    if (JSON.stringify(newEvents) !== JSON.stringify(events)) {
      setEvents(newEvents);
      setTempEvents(newEvents);
    }
  }, [props.events, initialEvents, events]);

  const handleEdit = useCallback(() => {
    setTempEvents([...events]);
    setTempSettings(currentSettings);
    setIsEditing(true);
  }, [events, currentSettings]);

  const handleSave = useCallback(() => {
    setEvents(tempEvents);
    setIsEditing(false);
    
    // Save to widget props
    if (onPropsChange) {
      onPropsChange({
        events: tempEvents,
        ...tempSettings,
      });
    }
  }, [tempEvents, tempSettings, onPropsChange]);

  const handleCancel = useCallback(() => {
    setTempEvents([...events]);
    setTempSettings(currentSettings);
    setSelectedDate(null);
    setNewEventTitle('');
    setIsEditing(false);
  }, [events, currentSettings]);

  const addEvent = useCallback(() => {
    if (!selectedDate || !newEventTitle.trim()) return;
    
    const newEvent: CalendarEvent = {
      id: Date.now().toString(),
      title: newEventTitle.trim(),
      date: selectedDate,
      color: '#3b82f6',
    };
    
    setTempEvents(prev => [...prev, newEvent]);
    setNewEventTitle('');
    setSelectedDate(null);
  }, [selectedDate, newEventTitle]);

  const removeEvent = useCallback((id: string) => {
    setTempEvents(prev => prev.filter(event => event.id !== id));
  }, []);



  const getDaysInMonth = useCallback((date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  }, []);

  const getFirstDayOfMonth = useCallback((date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  }, []);

  const getEventsForDate = useCallback((dateStr: string) => {
    return events.filter(event => event.date === dateStr);
  }, [events]);

  const formatDate = useCallback((year: number, month: number, day: number) => {
    return `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  }, []);

  const isToday = useCallback((year: number, month: number, day: number) => {
    const today = new Date();
    return year === today.getFullYear() && 
           month === today.getMonth() && 
           day === today.getDate();
  }, []);

  const navigateMonth = useCallback((direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  }, []);

  const monthNames = [
    'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
    'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
  ];

  const dayNames = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];

  if (isEditing) {
    return (
      <div className={`w-full h-full p-4 ${className || ''}`}>
        <div className="h-full flex flex-col">
          <div className="space-y-4 flex-1 overflow-y-auto">
            {/* Settings */}
            <div className="space-y-2">
              <Typography variant="body2">
                {t('dashboard:widgets.calendar.settings', 'Cài đặt')}
              </Typography>
              
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.showWeekNumbers}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, showWeekNumbers: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.calendar.showWeekNumbers', 'Hiển thị số tuần')}
                </Typography>
              </label>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.highlightToday}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, highlightToday: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.calendar.highlightToday', 'Highlight ngày hôm nay')}
                </Typography>
              </label>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.compactMode}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, compactMode: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.calendar.compactMode', 'Chế độ compact')}
                </Typography>
              </label>
            </div>

            {/* Add Event */}
            <div className="border border-border rounded-lg p-3">
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.calendar.addEvent', 'Thêm sự kiện')}
              </Typography>
              
              <div className="space-y-2">
                <Input
                  type="date"
                  value={selectedDate || ''}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="w-full"
                />
                
                <div className="flex gap-2">
                  <Input
                    value={newEventTitle}
                    onChange={(e) => setNewEventTitle(e.target.value)}
                    placeholder={t('dashboard:widgets.calendar.eventTitle', 'Tiêu đề sự kiện')}
                    className="flex-1"
                  />
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={addEvent}
                    disabled={!selectedDate || !newEventTitle.trim()}
                  >
                    {t('dashboard:widgets.calendar.add', 'Thêm')}
                  </Button>
                </div>
              </div>
            </div>

            {/* Events List */}
            <div>
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.calendar.events', 'Sự kiện')} ({tempEvents.length})
              </Typography>
              
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {tempEvents.map((event) => (
                  <div key={event.id} className="flex items-center gap-2 p-2 border border-border rounded">
                    <div
                      className="w-3 h-3 rounded-full flex-shrink-0"
                      style={{ backgroundColor: event.color }}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{event.title}</div>
                      <div className="text-sm text-muted-foreground">{event.date}</div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeEvent(event.id)}
                      className="text-destructive hover:text-destructive flex-shrink-0"
                    >
                      ×
                    </Button>
                  </div>
                ))}
                
                {tempEvents.length === 0 && (
                  <div className="text-center py-4 text-muted-foreground">
                    {t('dashboard:widgets.calendar.noEvents', 'Chưa có sự kiện nào')}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              {t('common:cancel')}
            </Button>
            <Button variant="primary" size="sm" onClick={handleSave}>
              {t('common:save')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const daysInMonth = getDaysInMonth(currentDate);
  const firstDay = getFirstDayOfMonth(currentDate);

  // Generate calendar days
  const calendarDays = [];
  
  // Empty cells for days before month starts
  for (let i = 0; i < firstDay; i++) {
    calendarDays.push(null);
  }
  
  // Days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push(day);
  }

  return (
    <div 
      className={`w-full h-full p-4 relative group ${className || ''}`}
    >
      <div className="h-full flex flex-col">
        {/* Calendar Header */}
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateMonth('prev')}
          >
            <Icon name="chevron-left" size="sm" />
          </Button>
          
          <Typography variant="h3" className="font-semibold">
            {monthNames[month]} {year}
          </Typography>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateMonth('next')}
          >
            <Icon name="chevron-right" size="sm" />
          </Button>
        </div>

        {/* Calendar Grid */}
        <div className="flex-1 min-h-0">
          {/* Day headers */}
          <div className={`grid grid-cols-7 gap-1 mb-2 ${currentSettings.compactMode ? 'text-xs' : 'text-sm'}`}>
            {dayNames.map((day) => (
              <div key={day} className="text-center font-medium text-muted-foreground p-1">
                {day}
              </div>
            ))}
          </div>
          
          {/* Calendar days */}
          <div className={`grid grid-cols-7 gap-1 flex-1 ${currentSettings.compactMode ? 'text-xs' : 'text-sm'}`}>
            {calendarDays.map((day, index) => {
              if (day === null) {
                return <div key={index} className="aspect-square" />;
              }
              
              const dateStr = formatDate(year, month, day);
              const dayEvents = getEventsForDate(dateStr);
              const isTodayDate = currentSettings.highlightToday && isToday(year, month, day);
              
              return (
                <div
                  key={day}
                  className={`
                    aspect-square border border-border rounded p-1 flex flex-col
                    ${isTodayDate ? 'bg-primary/10 border-primary' : 'hover:bg-muted/50'}
                    ${currentSettings.compactMode ? 'text-xs' : 'text-sm'}
                  `}
                >
                  <div className={`font-medium ${isTodayDate ? 'text-primary' : ''}`}>
                    {day}
                  </div>
                  
                  {/* Event indicators */}
                  <div className="flex-1 flex flex-col gap-0.5 mt-1 overflow-hidden">
                    {dayEvents.slice(0, currentSettings.compactMode ? 2 : 3).map((event) => (
                      <div
                        key={event.id}
                        className="w-full h-1 rounded-full"
                        style={{ backgroundColor: event.color }}
                        title={event.title}
                      />
                    ))}
                    {dayEvents.length > (currentSettings.compactMode ? 2 : 3) && (
                      <div className="text-xs text-muted-foreground">
                        +{dayEvents.length - (currentSettings.compactMode ? 2 : 3)}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Today's Events */}
        {!currentSettings.compactMode && (
          <div className="mt-4 pt-2 border-t border-border">
            <Typography variant="body2" className="mb-2 text-muted-foreground">
              {t('dashboard:widgets.calendar.todayEvents', 'Sự kiện hôm nay')}
            </Typography>
            
            {(() => {
              const today = new Date();
              const todayStr = formatDate(today.getFullYear(), today.getMonth(), today.getDate());
              const todayEvents = getEventsForDate(todayStr);
              
              if (todayEvents.length === 0) {
                return (
                  <Typography variant="caption" className="text-muted-foreground">
                    {t('dashboard:widgets.calendar.noTodayEvents', 'Không có sự kiện nào')}
                  </Typography>
                );
              }
              
              return (
                <div className="space-y-1">
                  {todayEvents.slice(0, 3).map((event) => (
                    <div key={event.id} className="flex items-center gap-2">
                      <div
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: event.color }}
                      />
                      <Typography variant="caption" className="truncate">
                        {event.title}
                      </Typography>
                    </div>
                  ))}
                  {todayEvents.length > 3 && (
                    <Typography variant="caption" className="text-muted-foreground">
                      +{todayEvents.length - 3} more
                    </Typography>
                  )}
                </div>
              );
            })()}
          </div>
        )}

        {/* Edit Button */}
        {editable && (
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button variant="secondary" size="sm" onClick={handleEdit}>
              {t('common:edit')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CalendarWidget;
