import React, { useState, useMemo, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import { ActiveFilters } from '@/modules/components/filters';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Card, Chip, Loading } from '@/shared/components/common';
import PageWrapper from '@/shared/components/common/PageWrapper/PageWrapper';
import {
  useUserIntegratedOnlyForMyIntegrationsPage,
  IntegrationCategory,
} from '../hooks/useUserIntegrationsFilter';

// CSS cho smooth transitions
const transitionStyles = `
  .integration-container {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .integration-container.transitioning {
    opacity: 0;
    transform: translateY(8px) scale(0.98);
  }

  .integration-container.visible {
    opacity: 1;
    transform: translateY(0) scale(1);
  }

  .integration-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, opacity;
  }

  .integration-card.entering {
    opacity: 0;
    transform: translateY(16px) scale(0.95);
  }

  .integration-card.entered {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
`;

/**
 * Trang hiển thị chỉ những integrations mà user đã tích hợp
 */
const MyIntegrationsPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common', 'integration']);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Xác định category ban đầu dựa trên URL params hoặc localStorage
  const getInitialCategory = (): IntegrationCategory | 'all' => {
    // 1. Kiểm tra URL search params trước
    const categoryParam = searchParams.get('category') as IntegrationCategory | null;
    if (
      categoryParam &&
      ['all', 'bank', 'llm', 'sms', 'email', 'social', 'shipping', 'other'].includes(categoryParam)
    ) {
      return categoryParam;
    }

    // 2. Kiểm tra localStorage để lưu category cuối cùng được chọn
    const savedCategory = localStorage.getItem(
      'integration-last-category'
    ) as IntegrationCategory | null;
    if (
      savedCategory &&
      ['all', 'bank', 'llm', 'sms', 'email', 'social', 'shipping', 'other'].includes(savedCategory)
    ) {
      return savedCategory;
    }

    // 3. Mặc định là 'all'
    return 'all';
  };

  // State cho search và filter
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<IntegrationCategory | 'all'>(
    getInitialCategory()
  );

  // State cho animation
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [displayCategory, setDisplayCategory] = useState<IntegrationCategory | 'all'>(
    getInitialCategory()
  );

  // Refs cho scroll đến từng section
  const sectionRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // Sử dụng hook để lấy chỉ những integrations mà user đã tích hợp từ API mới
  // với linkTo được override để trỏ đến trang quản lý
  const { integrations: userIntegrations, isLoading } =
    useUserIntegratedOnlyForMyIntegrationsPage();

  // Effect để lưu category được chọn vào localStorage
  useEffect(() => {
    localStorage.setItem('integration-last-category', selectedCategory);
  }, [selectedCategory]);

  // Filter categories cho MenuIconBar
  const filterCategories = [
    {
      id: 'all',
      label: t('common:all'),
      icon: 'list' as const,
      onClick: () => handleCategoryClick('all'),
    },
    {
      id: 'bank',
      label: t('integration:types.bank', 'Tài khoản ngân hàng'),
      icon: 'bank' as const,
      onClick: () => handleCategoryClick('bank'),
    },
    {
      id: 'llm',
      label: t('integration:types.llm', 'LLM'),
      icon: 'openai-red' as const,
      onClick: () => handleCategoryClick('llm'),
    },
    {
      id: 'sms',
      label: t('integration:types.sms', 'SMS'),
      icon: 'mail-plus' as const,
      onClick: () => handleCategoryClick('sms'),
    },
    {
      id: 'email',
      label: t('integration:types.email', 'Email'),
      icon: 'mail' as const,
      onClick: () => handleCategoryClick('email'),
    },

    {
      id: 'social',
      label: t('integration:types.social', 'Mạng xã hội'),
      icon: 'users' as const,
      onClick: () => handleCategoryClick('social'),
    },
    {
      id: 'shipping',
      label: t('integration:types.shipping', 'Vận chuyển'),
      icon: 'truck' as const,
      onClick: () => handleCategoryClick('shipping'),
    },
    {
      id: 'calendar',
      label: t('integration:types.calendar', 'Lịch'),
      icon: 'calendar' as const,
      onClick: () => handleCategoryClick('calendar'),
    },
    {
      id: 'ads',
      label: t('integration:types.ads', 'Quảng cáo'),
      icon: 'google-ads' as const,
      onClick: () => handleCategoryClick('ads'),
    },
  ];

  // Nhóm integrations theo category
  const integrationsByCategory = useMemo(() => {
    const categories: { [key in IntegrationCategory]: typeof userIntegrations } = {
      all: [],
      bank: [],
      llm: [],
      sms: [],
      email: [],
      social: [],
      shipping: [],
      calendar: [],
      ads: [],
    };

    userIntegrations.forEach(integration => {
      categories[integration.category].push(integration);
    });

    return categories;
  }, [userIntegrations]);

  // Lọc integrations theo search term
  const filteredIntegrationsByCategory = useMemo(() => {
    if (!searchTerm.trim()) {
      return integrationsByCategory;
    }

    const filtered: { [key in IntegrationCategory]: typeof userIntegrations } = {
      all: [],
      bank: [],
      llm: [],
      sms: [],
      email: [],
      social: [],
      shipping: [],
      calendar: [],
      ads: [],
    };

    Object.entries(integrationsByCategory).forEach(([category, integrations]) => {
      filtered[category as IntegrationCategory] = integrations.filter(
        integration =>
          integration.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          integration.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    });

    return filtered;
  }, [integrationsByCategory, searchTerm]);

  // Handlers
  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
  };

  // Helper function để navigate với category parameter
  const navigateWithCategory = (path: string, category?: IntegrationCategory | 'all') => {
    const categoryToUse = category || selectedCategory;
    if (categoryToUse && categoryToUse !== 'all') {
      navigate(`${path}?category=${categoryToUse}`);
    } else {
      navigate(path);
    }
  };

  // Handler để điều hướng đến trang tất cả integrations
  const handleGoToAllIntegrations = () => {
    navigateWithCategory('/integrations');
  };

  const handleCategoryClick = (category: IntegrationCategory | 'all') => {
    if (category === selectedCategory) return; // Không làm gì nếu đã chọn category này

    // Bắt đầu transition
    setIsTransitioning(true);

    // Sau 150ms (nửa thời gian transition), thay đổi category
    setTimeout(() => {
      setSelectedCategory(category);
      setDisplayCategory(category);

      // Scroll đến section tương ứng
      if (category !== 'all') {
        const sectionElement = sectionRefs.current[category];
        if (sectionElement) {
          sectionElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest',
          });
        }
      } else {
        // Scroll về đầu trang khi chọn "Tất cả"
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }

      // Kết thúc transition sau 150ms nữa
      setTimeout(() => {
        setIsTransitioning(false);
      }, 150);
    }, 150);
  };

  // Set ref cho section
  const setSectionRef = (category: string) => (el: HTMLDivElement | null) => {
    sectionRefs.current[category] = el;
  };

  return (
    <PageWrapper>
      {/* Inject custom CSS */}
      <style dangerouslySetInnerHTML={{ __html: transitionStyles }} />

      <div className="w-full bg-background text-foreground space-y-6 pb-5">
        {/* Search và Filter Bar */}
        <MenuIconBar
          onSearch={handleSearch}
          showDateFilter={false}
          showColumnFilter={false}
          additionalIcons={[
            {
              icon: 'grid',
              tooltip: t('integration:allIntegrations', 'Tất cả tích hợp'),
              onClick: handleGoToAllIntegrations,
              className: 'shadow-md',
            },
          ]}
        />

        {/* Category Chips */}
        <div className="flex flex-wrap gap-3">
          {filterCategories.map(category => (
            <Chip
              key={category.id}
              variant={selectedCategory === category.id ? 'primary' : 'default'}
              onClick={() => handleCategoryClick(category.id as IntegrationCategory | 'all')}
              className={`cursor-pointer transition-all duration-200 ${
                selectedCategory === category.id
                  ? 'shadow-md scale-105'
                  : 'hover:shadow-sm hover:scale-102'
              }`}
              size="md"
            >
              {category.label}
            </Chip>
          ))}
        </div>

        {/* Active Filters - chỉ hiển thị search term */}
        {searchTerm && (
          <ActiveFilters
            searchTerm={searchTerm}
            onClearSearch={handleClearSearch}
            onClearAll={handleClearSearch}
          />
        )}

        {/* Integration Sections */}
        {isLoading ? (
          <Card className="p-8">
            <div className="text-center">
              <Loading size="md" />
            </div>
          </Card>
        ) : userIntegrations.length === 0 ? (
          <Card className="p-12">
            <div className="text-center">
              <div className="text-gray-500 text-lg mb-2">
                {t('integration:noUserIntegrations', 'Bạn chưa có tích hợp nào')}
              </div>
              <div className="text-gray-400 text-sm mb-4">
                {t('integration:exploreIntegrations', 'Khám phá các tích hợp có sẵn để bắt đầu')}
              </div>
              <button
                onClick={handleGoToAllIntegrations}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
              >
                {t('integration:exploreAllIntegrations', 'Khám phá tất cả tích hợp')}
              </button>
            </div>
          </Card>
        ) : (
          <div className={`integration-container ${isTransitioning ? 'transitioning' : 'visible'}`}>
            {displayCategory === 'all' ? (
              // Hiển thị tất cả integrations trong một danh sách khi chọn "Tất cả"
              <div>
                {(() => {
                  // Lấy tất cả integrations từ tất cả categories
                  const allIntegrations = Object.values(filteredIntegrationsByCategory).flat();

                  return allIntegrations.length > 0 ? (
                    <ResponsiveGrid
                      maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
                      maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
                      gap={6}
                    >
                      {allIntegrations.map((integration, index) => (
                        <div
                          key={integration.id}
                          className={`relative integration-card h-full ${
                            isTransitioning ? 'entering' : 'entered'
                          }`}
                          style={{
                            transitionDelay: isTransitioning ? '0ms' : `${index * 50}ms`,
                          }}
                        >
                          <ModuleCard
                            title={integration.title}
                            description={integration.description}
                            icon={
                              integration.icon as
                                | 'mail'
                                | 'bank'
                                | 'openai-red'
                                | 'message-circle'
                                | 'facebook'
                                | 'zalo'
                                | 'zaloIcon'
                                | 'website'
                                | 'calendar'
                                | 'truck'
                                | 'cloud'
                                | 'server'
                                | 'users'
                                | 'google-ads'
                            }
                            linkTo={integration.linkTo}
                            gradientColor={integration.gradientColor || 'primary'}
                            size="md"
                          />
                        </div>
                      ))}
                    </ResponsiveGrid>
                  ) : (
                    <Card className="p-8">
                      <div className="text-center">
                        <div className="text-gray-500 text-lg mb-2">
                          {t('common:noIntegrationsFound', 'Không tìm thấy tích hợp nào')}
                        </div>
                        <div className="text-gray-400 text-sm">
                          {t('common:tryDifferentSearch', 'Thử tìm kiếm với từ khóa khác')}
                        </div>
                      </div>
                    </Card>
                  );
                })()}
              </div>
            ) : (
              // Hiển thị theo categories khi chọn category cụ thể
              <div className="space-y-8">
                {Object.entries(filteredIntegrationsByCategory).map(([category, integrations]) => {
                  // Chỉ hiển thị category nếu có integrations hoặc không có search term
                  if (integrations.length === 0 && searchTerm.trim()) {
                    return null;
                  }

                  // Nếu có displayCategory và không phải 'all', chỉ hiển thị category được chọn
                  if ((displayCategory as string) !== 'all' && displayCategory !== category) {
                    return null;
                  }

                  return (
                    <div
                      key={category}
                      ref={setSectionRef(category)}
                      className="scroll-mt-24" // Offset cho sticky header
                    >
                      {/* Integration Cards */}
                      {integrations.length > 0 && (
                        <ResponsiveGrid
                          maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
                          maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
                          gap={6}
                        >
                          {integrations.map((integration, index) => (
                            <div
                              key={integration.id}
                              className={`relative integration-card ${
                                isTransitioning ? 'entering' : 'entered'
                              }`}
                              style={{
                                transitionDelay: isTransitioning ? '0ms' : `${index * 50}ms`,
                              }}
                            >
                              <ModuleCard
                                title={integration.title}
                                description={integration.description}
                                icon={
                                  integration.icon as
                                    | 'mail'
                                    | 'bank'
                                    | 'openai-red'
                                    | 'mail-plus'
                                    | 'facebook'
                                    | 'zalo'
                                    | 'zaloIcon'
                                    | 'website'
                                    | 'calendar'
                                    | 'truck'
                                    | 'cloud'
                                    | 'server'
                                    | 'users'
                                    | 'google-ads'
                                }
                                linkTo={integration.linkTo}
                                gradientColor={integration.gradientColor || 'primary'}
                                size="md"
                              />
                            </div>
                          ))}
                        </ResponsiveGrid>
                      )}
                    </div>
                  );
                })}

                {/* No results message for search */}
                {searchTerm.trim() &&
                  Object.values(filteredIntegrationsByCategory).every(
                    integrations => integrations.length === 0
                  ) && (
                    <Card className="p-12">
                      <div className="text-center">
                        <div className="text-gray-500 text-lg mb-2">
                          {t('common:noResultsFound', 'Không tìm thấy kết quả nào')}
                        </div>
                        <div className="text-gray-400 text-sm">
                          {t('common:tryDifferentSearch', 'Thử tìm kiếm với từ khóa khác')}
                        </div>
                      </div>
                    </Card>
                  )}
              </div>
            )}
          </div>
        )}
      </div>
    </PageWrapper>
  );
};

export default MyIntegrationsPage;
