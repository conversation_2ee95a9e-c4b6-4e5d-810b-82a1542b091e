/**
 * Dashboard Query Keys
 * Theo pattern RedAI cho TanStack Query
 */

import { QueryDashboardPageDto, DashboardPageType, OwnerType, AccessLevel } from '../types/dashboard-api.types';

// ==================== DASHBOARD PAGE QUERY KEYS ====================

export const DASHBOARD_QUERY_KEYS = {
  /**
   * Base key cho tất cả dashboard queries
   */
  ALL: ['dashboard'] as const,

  /**
   * Key cho danh sách dashboard pages
   */
  PAGES: () => [...DASHBOARD_QUERY_KEYS.ALL, 'pages'] as const,

  /**
   * Key cho danh sách dashboard pages với query params
   */
  PAGES_LIST: (params: QueryDashboardPageDto) => 
    [...DASHBOARD_QUERY_KEYS.PAGES(), 'list', params] as const,

  /**
   * Key cho dashboard page cụ thể
   */
  PAGE: (id: string) => [...DASHBOARD_QUERY_KEYS.ALL, 'page', id] as const,

  /**
   * Key cho dashboard page với options
   */
  PAGE_DETAIL: (id: string, includeWidgets: boolean = false) => 
    [...DASHBOARD_QUERY_KEYS.PAGE(id), 'detail', { includeWidgets }] as const,

  /**
   * Key cho dashboard pages của user cụ thể
   */
  USER_PAGES: (userId: number) => 
    [...DASHBOARD_QUERY_KEYS.ALL, 'user', userId] as const,

  /**
   * Key cho dashboard pages của employee cụ thể
   */
  EMPLOYEE_PAGES: (employeeId: number) => 
    [...DASHBOARD_QUERY_KEYS.ALL, 'employee', employeeId] as const,

  /**
   * Key cho dashboard page mặc định của user
   */
  DEFAULT_PAGE: (userId?: number, employeeId?: number) => 
    [...DASHBOARD_QUERY_KEYS.ALL, 'default', { userId, employeeId }] as const,

  /**
   * Key cho dashboard templates
   */
  TEMPLATES: () => [...DASHBOARD_QUERY_KEYS.ALL, 'templates'] as const,

  /**
   * Key cho system templates
   */
  SYSTEM_TEMPLATES: (targetAudience?: 'USER' | 'EMPLOYEE') => 
    [...DASHBOARD_QUERY_KEYS.TEMPLATES(), 'system', targetAudience] as const,

  /**
   * Key cho user templates
   */
  USER_TEMPLATES: () => [...DASHBOARD_QUERY_KEYS.TEMPLATES(), 'user'] as const,

  /**
   * Key cho admin templates
   */
  ADMIN_TEMPLATES: () => [...DASHBOARD_QUERY_KEYS.TEMPLATES(), 'admin'] as const,

  /**
   * Key cho dashboard pages theo type
   */
  PAGES_BY_TYPE: (pageType: DashboardPageType) => 
    [...DASHBOARD_QUERY_KEYS.PAGES(), 'type', pageType] as const,

  /**
   * Key cho dashboard pages theo owner type
   */
  PAGES_BY_OWNER: (ownerType: OwnerType) => 
    [...DASHBOARD_QUERY_KEYS.PAGES(), 'owner', ownerType] as const,

  /**
   * Key cho dashboard pages theo access level
   */
  PAGES_BY_ACCESS: (accessLevel: AccessLevel) => 
    [...DASHBOARD_QUERY_KEYS.PAGES(), 'access', accessLevel] as const,

  /**
   * Key cho shared dashboard pages
   */
  SHARED_PAGES: () => [...DASHBOARD_QUERY_KEYS.ALL, 'shared'] as const,

  /**
   * Key cho public dashboard pages
   */
  PUBLIC_PAGES: () => [...DASHBOARD_QUERY_KEYS.ALL, 'public'] as const,

  /**
   * Key cho dashboard statistics
   */
  STATISTICS: () => [...DASHBOARD_QUERY_KEYS.ALL, 'statistics'] as const,

  /**
   * Key cho dashboard usage analytics
   */
  ANALYTICS: (timeRange?: string) => 
    [...DASHBOARD_QUERY_KEYS.ALL, 'analytics', timeRange] as const,
} as const;

// ==================== DASHBOARD WIDGET QUERY KEYS ====================

export const DASHBOARD_WIDGET_QUERY_KEYS = {
  /**
   * Base key cho tất cả widget queries
   */
  ALL: ['dashboard', 'widgets'] as const,

  /**
   * Key cho widgets của dashboard page cụ thể
   */
  PAGE_WIDGETS: (pageId: string) => 
    [...DASHBOARD_WIDGET_QUERY_KEYS.ALL, 'page', pageId] as const,

  /**
   * Key cho widget cụ thể
   */
  WIDGET: (widgetId: string) => 
    [...DASHBOARD_WIDGET_QUERY_KEYS.ALL, 'widget', widgetId] as const,

  /**
   * Key cho widget data
   */
  WIDGET_DATA: (widgetId: string, params?: Record<string, unknown>) => 
    [...DASHBOARD_WIDGET_QUERY_KEYS.WIDGET(widgetId), 'data', params] as const,

  /**
   * Key cho widget statistics
   */
  STATISTICS: () => [...DASHBOARD_WIDGET_QUERY_KEYS.ALL, 'statistics'] as const,
} as const;

// ==================== HELPER FUNCTIONS ====================

/**
 * Invalidate tất cả dashboard queries
 */
export const invalidateAllDashboardQueries = () => DASHBOARD_QUERY_KEYS.ALL;

/**
 * Invalidate dashboard pages queries
 */
export const invalidateDashboardPagesQueries = () => DASHBOARD_QUERY_KEYS.PAGES();

/**
 * Invalidate dashboard page cụ thể
 */
export const invalidateDashboardPageQuery = (id: string) => DASHBOARD_QUERY_KEYS.PAGE(id);

/**
 * Invalidate user dashboard queries
 */
export const invalidateUserDashboardQueries = (userId: number) => DASHBOARD_QUERY_KEYS.USER_PAGES(userId);

/**
 * Invalidate employee dashboard queries
 */
export const invalidateEmployeeDashboardQueries = (employeeId: number) => DASHBOARD_QUERY_KEYS.EMPLOYEE_PAGES(employeeId);

// ==================== EXPORT ====================

export type DashboardQueryKeys = typeof DASHBOARD_QUERY_KEYS;
export type DashboardWidgetQueryKeys = typeof DASHBOARD_WIDGET_QUERY_KEYS;
