import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Modal,
  Typography,
  CollapsibleCard,
  Checkbox,
  ScrollArea,
  Button,
} from '@/shared/components/common';
import { MenuIconBar } from '@/modules/components/menu-bar';
import ActiveFilters from '@/modules/components/filters/ActiveFilters';
import { DashboardWidget } from '../types';
import { WidgetType } from '../constants/widget-types';
import { WIDGET_CONFIGS } from '../registry/widgetConfigs';

interface AddWidgetModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddWidget: (widget: DashboardWidget) => void;
  existingWidgets: DashboardWidget[];
}

interface WidgetOption {
  id: string;
  title: string;
  description: string;
  icon: string;
  type: WidgetType;
  category: string;
  defaultSize: {
    w: number;
    h: number;
    minW?: number;
    minH?: number;
    maxW?: number;
    maxH?: number;
  };
}

const AddWidgetModal: React.FC<AddWidgetModalProps> = ({
  isOpen,
  onClose,
  onAddWidget,
  existingWidgets,
}) => {
  const { t } = useTranslation(['dashboard', 'common']);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedWidgets, setSelectedWidgets] = useState<Set<string>>(new Set());

  // Tạo danh sách widget sections từ WIDGET_CONFIGS
  const widgetSections = useMemo(() => {
    const sections: { [key: string]: WidgetOption[] } = {};

    // Gom nhóm widgets theo category
    WIDGET_CONFIGS.forEach(config => {
      const category = config.category;

      if (!sections[category]) {
        sections[category] = [];
      }

      sections[category].push({
        id: config.id,
        title: config.title,
        description: config.description || '',
        icon: config.icon?.name || 'square', // Fallback icon
        type: config.type,
        category: config.category,
        defaultSize: config.defaultSize,
      });
    });

    return sections;
  }, []);

  // Filter sections based on search and category
  const filteredSections = useMemo(() => {
    const filtered: { [key: string]: WidgetOption[] } = {};

    Object.entries(widgetSections).forEach(([sectionId, widgets]) => {
      const filteredWidgets = widgets.filter(widget => {
        const matchesSearch =
          widget.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          widget.description.toLowerCase().includes(searchQuery.toLowerCase());
        const matchesCategory = selectedCategory === 'all' || widget.category === selectedCategory;

        // Cho phép thêm nhiều widget cùng loại
        // Không kiểm tra widget đã tồn tại để cho phép duplicate

        return matchesSearch && matchesCategory;
      });

      if (filteredWidgets.length > 0) {
        filtered[sectionId] = filteredWidgets;
      }
    });

    return filtered;
  }, [widgetSections, searchQuery, selectedCategory, existingWidgets]);

  // Categories for filter
  const categories = useMemo(() => {
    const allWidgets = Object.values(widgetSections).flat();
    const categorySet = new Set(allWidgets.map(w => w.category));
    return [
      { id: 'all', label: t('dashboard:categories.all') },
      ...Array.from(categorySet).map(cat => ({
        id: cat,
        label: t(`dashboard:categories.${cat}`),
      })),
    ];
  }, [widgetSections, t]);

  // Section titles mapping - sử dụng category làm key
  const sectionTitles: { [key: string]: string } = {
    business: t('dashboard:categories.business'),
    integration: t('dashboard:categories.integration'),
    marketing: t('dashboard:categories.marketing'),
    data: t('dashboard:categories.data'),
    'ai-agents': t('dashboard:categories.ai-agents'),
    affiliate: t('dashboard:categories.affiliate'),
  };

  // Xử lý chọn/bỏ chọn widget
  const handleWidgetSelect = (widgetId: string, checked: boolean) => {
    setSelectedWidgets(prev => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(widgetId);
      } else {
        newSet.delete(widgetId);
      }
      return newSet;
    });
  };

  // Xử lý chọn tất cả widget trong một section
  const handleSelectAllInSection = (widgets: WidgetOption[], checked: boolean) => {
    setSelectedWidgets(prev => {
      const newSet = new Set(prev);
      widgets.forEach(widget => {
        if (checked) {
          newSet.add(widget.id);
        } else {
          newSet.delete(widget.id);
        }
      });
      return newSet;
    });
  };

  // Thêm các widget đã chọn
  const handleAddSelectedWidgets = () => {
    const allWidgets = Object.values(filteredSections).flat();
    const widgetsToAdd = allWidgets.filter(widget => selectedWidgets.has(widget.id));

    let maxY = existingWidgets.length > 0 ? Math.max(...existingWidgets.map(w => w.y + w.h)) : 0;

    widgetsToAdd.forEach(widgetOption => {
      // Tìm config gốc từ WIDGET_CONFIGS
      const originalConfig = WIDGET_CONFIGS.find(config => config.id === widgetOption.id);

      // Tạo ID duy nhất với timestamp và random string để tránh trùng lặp
      const uniqueId = `${widgetOption.id}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      const newWidget: DashboardWidget = {
        id: uniqueId,
        title: widgetOption.title,
        type: widgetOption.type,
        x: 0,
        y: maxY,
        w: widgetOption.defaultSize.w,
        h: widgetOption.defaultSize.h,
        minW: widgetOption.defaultSize.minW,
        minH: widgetOption.defaultSize.minH,
        maxW: widgetOption.defaultSize.maxW,
        maxH: widgetOption.defaultSize.maxH,
        isEmpty: false,
        config: originalConfig, // Lưu config gốc để sử dụng sau này
      };

      onAddWidget(newWidget);
      maxY += widgetOption.defaultSize.h;
    });

    handleCloseModal();
  };

  // Xử lý đóng modal và reset state
  const handleCloseModal = () => {
    setSelectedWidgets(new Set());
    setSearchQuery('');
    setSelectedCategory('all');
    onClose();
  };

  // Xử lý click vào widget - chỉ toggle checkbox
  const handleWidgetClick = (widgetId: string) => {
    const isCurrentlySelected = selectedWidgets.has(widgetId);
    handleWidgetSelect(widgetId, !isCurrentlySelected);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleCloseModal}
      title={t('dashboard:addWidget.title')}
      size="xl"
      footer={
        <div className="flex justify-between items-center">
          {selectedWidgets.size > 0 ? (
            <Typography variant="body2" className="text-muted-foreground">
              {t('dashboard:addWidget.selectedCount', { count: selectedWidgets.size })}
            </Typography>
          ) : (
            <div></div>
          )}
          <div className="flex gap-2">
            {selectedWidgets.size > 0 && (
              <></>
            )}
            {selectedWidgets.size > 0 ? (
              <Button variant="primary" onClick={handleAddSelectedWidgets}>
                {t('dashboard:addWidget.addSelected')}
              </Button>
            ) : (
              <></>
            )}
          </div>
        </div>
      }
    >
      <div className="space-y-4">
        {/* Search and Filter using MenuIconBar */}
        <MenuIconBar
          onSearch={setSearchQuery}
          showSearchButton={true}
          items={categories.map(category => ({
            id: category.id,
            label: category.label,
            icon: category.id === 'all' ? 'list' : 'tag',
            onClick: () => setSelectedCategory(category.id),
            keepOpen: true, // Giữ menu mở khi click
          }))}
          showColumnFilter={false}
        />

        {/* Active Filters */}
        <ActiveFilters
          searchTerm={searchQuery}
          onClearSearch={() => setSearchQuery('')}
          filterValue={selectedCategory !== 'all' ? selectedCategory : undefined}
          filterLabel={categories.find(cat => cat.id === selectedCategory)?.label}
          onClearFilter={() => setSelectedCategory('all')}
        />

        {/* Widget Sections với ScrollArea */}
        <ScrollArea maxHeight="400px" className="space-y-4">
          {Object.keys(filteredSections).length === 0 ? (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {searchQuery || selectedCategory !== 'all'
                  ? t('dashboard:addWidget.noResults')
                  : t('dashboard:addWidget.noWidgetsAvailable')}
              </Typography>
            </div>
          ) : (
            <div className="space-y-4">
              {Object.entries(filteredSections).map(([sectionId, widgets]) => {
                const allSelected = widgets.every(widget => selectedWidgets.has(widget.id));
                const someSelected = widgets.some(widget => selectedWidgets.has(widget.id));

                return (
                  <CollapsibleCard
                    key={sectionId}
                    title={
                      <div className="flex items-center gap-3">
                        <Checkbox
                          checked={allSelected}
                          indeterminate={someSelected && !allSelected}
                          onChange={checked => handleSelectAllInSection(widgets, checked)}
                        />
                        <Typography variant="h6" className="font-medium">
                          {sectionTitles[sectionId] || sectionId}
                        </Typography>
                      </div>
                    }
                    defaultOpen={true}
                  >
                    <div className="space-y-2">
                      {widgets.map(widget => (
                        <div
                          key={widget.id}
                          className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer"
                          onClick={() => handleWidgetClick(widget.id)}
                        >
                          <Checkbox
                            checked={selectedWidgets.has(widget.id)}
                            onChange={checked => handleWidgetSelect(widget.id, checked)}
                          />
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <Typography variant="body1" className="font-medium">
                                  {widget.title}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  className="text-muted-foreground text-sm"
                                >
                                  {widget.description}
                                </Typography>
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {widget.defaultSize.w}×{widget.defaultSize.h}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CollapsibleCard>
                );
              })}
            </div>
          )}
        </ScrollArea>
      </div>
    </Modal>
  );
};

export default AddWidgetModal;
