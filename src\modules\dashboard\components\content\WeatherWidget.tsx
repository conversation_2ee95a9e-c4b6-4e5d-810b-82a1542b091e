import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Input, Icon } from '@/shared/components/common';
import { BaseWidgetProps } from '../../types';

interface WeatherData {
  location: string;
  temperature: number;
  description: string;
  humidity: number;
  windSpeed: number;
  icon: string;
  forecast?: {
    day: string;
    high: number;
    low: number;
    icon: string;
  }[];
}

interface WeatherWidgetProps extends BaseWidgetProps {
  initialLocation?: string;
  editable?: boolean;
  showForecast?: boolean;
  temperatureUnit?: 'celsius' | 'fahrenheit';
  refreshInterval?: number; // in minutes
}

/**
 * Widget hiển thị thời tiết với OpenWeatherMap API
 */
const WeatherWidget: React.FC<WeatherWidgetProps> = ({
  className,
  initialLocation = 'Ho Chi Minh City',
  editable = true,
  showForecast = false,
  temperatureUnit = 'celsius',
  refreshInterval = 30,
  onPropsChange,
  ...props
}) => {
  const { t } = useTranslation(['dashboard', 'common']);
  
  // Use values from props if available
  const currentLocation = (props.location as string) || initialLocation;
  const currentSettings = {
    showForecast: (props.showForecast as boolean) ?? showForecast,
    temperatureUnit: (props.temperatureUnit as typeof temperatureUnit) || temperatureUnit,
    refreshInterval: (props.refreshInterval as number) ?? refreshInterval,
  };

  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [tempLocation, setTempLocation] = useState(currentLocation);
  const [tempSettings, setTempSettings] = useState(currentSettings);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Mock weather data for demo (replace with real API)
  const getMockWeatherData = useCallback((location: string): WeatherData => {
    const mockData: Record<string, WeatherData> = {
      'ho chi minh city': {
        location: 'Ho Chi Minh City',
        temperature: 32,
        description: 'Sunny',
        humidity: 65,
        windSpeed: 12,
        icon: '☀️',
        forecast: [
          { day: 'Today', high: 34, low: 26, icon: '☀️' },
          { day: 'Tomorrow', high: 33, low: 25, icon: '⛅' },
          { day: 'Wed', high: 31, low: 24, icon: '🌧️' },
          { day: 'Thu', high: 30, low: 23, icon: '⛈️' },
          { day: 'Fri', high: 32, low: 25, icon: '☀️' },
        ],
      },
      'hanoi': {
        location: 'Hanoi',
        temperature: 28,
        description: 'Partly Cloudy',
        humidity: 70,
        windSpeed: 8,
        icon: '⛅',
        forecast: [
          { day: 'Today', high: 30, low: 22, icon: '⛅' },
          { day: 'Tomorrow', high: 29, low: 21, icon: '🌧️' },
          { day: 'Wed', high: 27, low: 20, icon: '🌧️' },
          { day: 'Thu', high: 26, low: 19, icon: '⛈️' },
          { day: 'Fri', high: 28, low: 21, icon: '⛅' },
        ],
      },
      'da nang': {
        location: 'Da Nang',
        temperature: 30,
        description: 'Clear',
        humidity: 60,
        windSpeed: 15,
        icon: '☀️',
        forecast: [
          { day: 'Today', high: 32, low: 24, icon: '☀️' },
          { day: 'Tomorrow', high: 31, low: 23, icon: '☀️' },
          { day: 'Wed', high: 29, low: 22, icon: '⛅' },
          { day: 'Thu', high: 28, low: 21, icon: '🌧️' },
          { day: 'Fri', high: 30, low: 23, icon: '☀️' },
        ],
      },
    };

    const key = location.toLowerCase();
    return mockData[key] || {
      location: location,
      temperature: 25,
      description: 'Unknown',
      humidity: 50,
      windSpeed: 10,
      icon: '❓',
      forecast: [],
    };
  }, []);

  const fetchWeatherData = useCallback(async (location: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // For demo, use mock data
      const data = getMockWeatherData(location);
      setWeatherData(data);
      setLastUpdated(new Date());
    } catch (err) {
      setError(t('dashboard:widgets.weather.fetchError', 'Không thể lấy dữ liệu thời tiết'));
    } finally {
      setIsLoading(false);
    }
  }, [getMockWeatherData, t]);

  // Initial load and auto-refresh
  useEffect(() => {
    fetchWeatherData(currentLocation);
    
    const interval = setInterval(() => {
      fetchWeatherData(currentLocation);
    }, currentSettings.refreshInterval * 60 * 1000);

    return () => clearInterval(interval);
  }, [currentLocation, currentSettings.refreshInterval, fetchWeatherData]);

  const handleEdit = useCallback(() => {
    setTempLocation(currentLocation);
    setTempSettings(currentSettings);
    setIsEditing(true);
  }, [currentLocation, currentSettings]);

  const handleSave = useCallback(() => {
    if (tempLocation.trim() === '') {
      setError(t('dashboard:widgets.weather.emptyLocation', 'Vị trí không được để trống'));
      return;
    }

    setIsEditing(false);
    
    // Fetch weather for new location
    fetchWeatherData(tempLocation);
    
    // Save to widget props
    if (onPropsChange) {
      onPropsChange({
        location: tempLocation,
        ...tempSettings,
      });
    }
  }, [tempLocation, tempSettings, onPropsChange, fetchWeatherData, t]);

  const handleCancel = useCallback(() => {
    setTempLocation(currentLocation);
    setTempSettings(currentSettings);
    setError(null);
    setIsEditing(false);
  }, [currentLocation, currentSettings]);

  const handleRefresh = useCallback(() => {
    fetchWeatherData(currentLocation);
  }, [currentLocation, fetchWeatherData]);

  const convertTemperature = useCallback((temp: number, unit: typeof temperatureUnit): number => {
    if (unit === 'fahrenheit') {
      return Math.round((temp * 9/5) + 32);
    }
    return temp;
  }, []);

  const getTemperatureUnit = useCallback((unit: typeof temperatureUnit): string => {
    return unit === 'fahrenheit' ? '°F' : '°C';
  }, []);

  const commonLocations = [
    'Ho Chi Minh City',
    'Hanoi',
    'Da Nang',
    'Can Tho',
    'Hai Phong',
    'Nha Trang',
  ];

  if (isEditing) {
    return (
      <div className={`w-full h-full p-4 ${className || ''}`}>
        <div className="h-full flex flex-col">
          <div className="space-y-4 flex-1">
            <div>
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.weather.location', 'Vị trí')}
              </Typography>
              <Input
                value={tempLocation}
                onChange={(e) => setTempLocation(e.target.value)}
                placeholder={t('dashboard:widgets.weather.locationPlaceholder', 'Nhập tên thành phố...')}
                className="w-full"
              />
              {error && (
                <Typography variant="caption" className="text-destructive mt-1">
                  {error}
                </Typography>
              )}
            </div>

            <div>
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.weather.commonLocations', 'Vị trí phổ biến')}
              </Typography>
              <div className="grid grid-cols-2 gap-2">
                {commonLocations.map((location, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="sm"
                    onClick={() => setTempLocation(location)}
                    className="justify-start text-left"
                  >
                    {location}
                  </Button>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Typography variant="body2">
                {t('dashboard:widgets.weather.settings', 'Cài đặt')}
              </Typography>
              
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.showForecast}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, showForecast: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.weather.showForecast', 'Hiển thị dự báo')}
                </Typography>
              </label>

              <div>
                <Typography variant="body2" className="mb-1">
                  {t('dashboard:widgets.weather.temperatureUnit', 'Đơn vị nhiệt độ')}
                </Typography>
                <div className="flex gap-2">
                  <label className="flex items-center gap-1">
                    <input
                      type="radio"
                      name="temperatureUnit"
                      value="celsius"
                      checked={tempSettings.temperatureUnit === 'celsius'}
                      onChange={(e) => setTempSettings(prev => ({ ...prev, temperatureUnit: e.target.value as typeof temperatureUnit }))}
                    />
                    <Typography variant="body2">°C</Typography>
                  </label>
                  <label className="flex items-center gap-1">
                    <input
                      type="radio"
                      name="temperatureUnit"
                      value="fahrenheit"
                      checked={tempSettings.temperatureUnit === 'fahrenheit'}
                      onChange={(e) => setTempSettings(prev => ({ ...prev, temperatureUnit: e.target.value as typeof temperatureUnit }))}
                    />
                    <Typography variant="body2">°F</Typography>
                  </label>
                </div>
              </div>

              <div>
                <Typography variant="body2" className="mb-1">
                  {t('dashboard:widgets.weather.refreshInterval', 'Tự động cập nhật (phút)')}
                </Typography>
                <Input
                  type="number"
                  value={tempSettings.refreshInterval}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, refreshInterval: parseInt(e.target.value) || 30 }))}
                  min="5"
                  max="120"
                  step="5"
                  className="w-full"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              {t('common:cancel')}
            </Button>
            <Button variant="primary" size="sm" onClick={handleSave}>
              {t('common:save')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading && !weatherData) {
    return (
      <div className={`w-full h-full p-4 ${className || ''}`}>
        <div className="h-full flex flex-col items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-2"></div>
          <Typography variant="body2" className="text-muted-foreground">
            {t('dashboard:widgets.weather.loading', 'Đang tải thời tiết...')}
          </Typography>
        </div>
      </div>
    );
  }

  if (error && !weatherData) {
    return (
      <div 
        className={`w-full h-full p-4 ${className || ''} ${editable ? 'cursor-pointer hover:bg-muted/50 transition-colors' : ''}`}
        onClick={editable ? handleEdit : undefined}
      >
        <div className="h-full flex flex-col items-center justify-center border border-destructive/30 rounded-lg bg-destructive/5">
          <Icon name="alert-circle" size="lg" className="text-destructive mb-2" />
          <Typography variant="body2" className="text-destructive text-center mb-2">
            {error}
          </Typography>
          {editable && (
            <Button variant="ghost" size="sm" onClick={handleEdit}>
              {t('common:edit')}
            </Button>
          )}
        </div>
      </div>
    );
  }

  if (!weatherData) {
    return (
      <div 
        className={`w-full h-full p-4 ${className || ''} ${editable ? 'cursor-pointer hover:bg-muted/50 transition-colors' : ''}`}
        onClick={editable ? handleEdit : undefined}
      >
        <div className="h-full flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/30 rounded-lg">
          <Icon name="cloud" size="lg" className="text-muted-foreground mb-2" />
          <Typography variant="body2" className="text-muted-foreground text-center">
            {editable 
              ? t('dashboard:widgets.weather.empty', 'Click để cấu hình thời tiết')
              : t('dashboard:widgets.weather.noData', 'Không có dữ liệu thời tiết')
            }
          </Typography>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`w-full h-full p-4 relative group ${className || ''}`}
    >
      <div className="h-full flex flex-col">
        {/* Current Weather */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="text-4xl">{weatherData.icon}</div>
            <div>
              <Typography variant="h3" className="font-bold">
                {convertTemperature(weatherData.temperature, currentSettings.temperatureUnit)}
                {getTemperatureUnit(currentSettings.temperatureUnit)}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {weatherData.description}
              </Typography>
            </div>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
            className="opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <Icon name="refresh-cw" size="sm" className={isLoading ? 'animate-spin' : ''} />
          </Button>
        </div>

        {/* Location & Details */}
        <div className="mb-4">
          <Typography variant="body1" className="font-medium mb-2">
            {weatherData.location}
          </Typography>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Icon name="droplets" size="sm" className="text-blue-500" />
              <span>{weatherData.humidity}%</span>
            </div>
            <div className="flex items-center gap-2">
              <Icon name="wind" size="sm" className="text-gray-500" />
              <span>{weatherData.windSpeed} km/h</span>
            </div>
          </div>
        </div>

        {/* Forecast */}
        {currentSettings.showForecast && weatherData.forecast && weatherData.forecast.length > 0 && (
          <div className="flex-1">
            <Typography variant="body2" className="mb-2 text-muted-foreground">
              {t('dashboard:widgets.weather.forecast', 'Dự báo')}
            </Typography>
            <div className="grid grid-cols-5 gap-1 text-xs">
              {weatherData.forecast.slice(0, 5).map((day, index) => (
                <div key={index} className="text-center p-1 rounded bg-muted/30">
                  <div className="mb-1">{day.day}</div>
                  <div className="text-lg mb-1">{day.icon}</div>
                  <div className="font-medium">
                    {convertTemperature(day.high, currentSettings.temperatureUnit)}°
                  </div>
                  <div className="text-muted-foreground">
                    {convertTemperature(day.low, currentSettings.temperatureUnit)}°
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Last Updated */}
        {lastUpdated && (
          <div className="mt-auto pt-2">
            <Typography variant="caption" className="text-muted-foreground">
              {t('dashboard:widgets.weather.lastUpdated', 'Cập nhật lúc')}: {lastUpdated.toLocaleTimeString()}
            </Typography>
          </div>
        )}

        {/* Edit Button */}
        {editable && (
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button variant="secondary" size="sm" onClick={handleEdit}>
              {t('common:edit')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default WeatherWidget;
