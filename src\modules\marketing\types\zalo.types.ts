/**
 * Types cho tích hợp Zalo OA/ZNS
 */

/**
 * Trạng thái tài khoản Zalo OA
 */
export enum ZaloOAStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  ERROR = 'error',
}

/**
 * Trạng thái template ZNS
 */
export enum ZNSTemplateStatus {
  ENABLE = 'enabel',
  PENDING_REVIEW = 'pending_review',
  REJECT = 'rejected',
  DISABLE = 'disable',
  APPROVED = 'approved',
}

/**
 * Trạng thái gửi ZNS
 */
export enum ZNSMessageStatus {
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
  PENDING = 'pending',
}

/**
 * Loại tin nhắn Zalo
 */
export enum ZaloMessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  TEMPLATE = 'template',
  LIST = 'list',
  BUTTON = 'button',
}

/**
 * Trạng thái nhóm Zalo
 */
export enum ZaloGroupStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  ARCHIVED = 'archived',
}

/**
 * DTO tài khoản Zalo OA
 */
export interface ZaloOAAccountDto {
  id: number;
  userId: number;
  oaId: string;
  name: string;
  avatar?: string;
  accessToken: string;
  refreshToken: string;
  status: ZaloOAStatus;
  followersCount: number;
  expireTime: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO người theo dõi Zalo OA
 */
export interface ZaloFollowerDto {
  id: string;
  userId: number;
  oaId: number;
  followerId: string;
  displayName: string;
  avatar?: string;
  phone?: string;
  gender?: 'male' | 'female' | 'unknown';
  status: 'ACTIVE' | 'BLOCKED' | 'UNFOLLOWED';
  tags: string[];
  followedAt: number;
  lastInteractionAt?: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO nhóm Zalo
 */
export interface ZaloGroupDto {
  id: number;
  userId: number;
  oaId: number;
  groupId: string;
  name: string;
  description?: string;
  avatar?: string;
  memberCount: number;
  status: ZaloGroupStatus;
  isOwner: boolean;
  permissions: string[];
  tags: string[];
  lastActivity?: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO tin nhắn Zalo
 */
export interface ZaloMessageDto {
  id: number;
  userId: number;
  oaId: number;
  followerId: string;
  messageId: string;
  type: ZaloMessageType;
  content: string;
  attachments?: ZaloAttachmentDto[];
  isFromUser: boolean;
  sentAt: number;
  createdAt: number;
}

/**
 * DTO đính kèm tin nhắn Zalo
 */
export interface ZaloAttachmentDto {
  type: string;
  url: string;
  name?: string;
  size?: number;
  thumbnailUrl?: string;
}

/**
 * DTO template ZNS parameter
 */
export interface ZNSTemplateParamDto {
  name: string;
  type: string;
  require: boolean;
  maxLength: number;
  minLength: number;
  acceptNull: boolean;
}

/**
 * DTO template ZNS
 */
export interface ZNSTemplateDto {
  id: number;
  userId: number;
  oaId: string;
  templateId: string;
  templateName: string;
  templateContent: string;
  templateType: string;
  status: ZNSTemplateStatus;
  timeout: string;
  previewUrl: string;
  templateQuality: string | null;
  templateTag: string;
  price: string;
  applyTemplateQuota: boolean;
  reason: string;
  listParams: ZNSTemplateParamDto[];
  createdAt: string;
  updatedAt: string;
}

/**
 * DTO tin nhắn ZNS
 */
export interface ZNSMessageDto {
  id: number;
  userId: number;
  oaId: number;
  templateId: number;
  phone: string;
  params: Record<string, string>;
  status: ZNSMessageStatus;
  errorCode?: string;
  errorMessage?: string;
  sentAt: number;
  deliveredAt?: number;
  readAt?: number;
  createdAt: number;
}

/**
 * Trạng thái chiến dịch ZNS
 */
export enum ZNSCampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  RUNNING = 'running',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  FAILED = 'failed',
}

/**
 * Loại đối tượng mục tiêu
 */
export enum ZNSCampaignTargetType {
  ALL_FOLLOWERS = 'all_followers',
  TAGS = 'tags',
  CUSTOM_LIST = 'custom_list',
  PHONE_LIST = 'phone_list',
}

/**
 * DTO chiến dịch ZNS nâng cao
 */
export interface ZNSCampaignDto {
  id: number;
  userId: number;
  oaId: string;
  name: string;
  description?: string;
  templateId: string;
  templateData?: Record<string, any>;
  personalizationConfig?: any;
  phoneList?: string[];
  segmentId?: number;
  audienceIds?: number[];
  status: string;
  errorMessage?: string;

  // Scheduling
  scheduledAt?: string;
  startedAt?: string;
  completedAt?: string;

  // Statistics
  totalMessages: number;
  sentMessages: number;
  failedMessages: number;

  // Job tracking
  jobIds?: any;

  // Integration
  integrationId: string;

  // Metadata
  createdAt: string;
  updatedAt: string;
}

/**
 * DTO tham số truy vấn tài khoản Zalo OA
 */
export interface ZaloOAAccountQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  status?: ZaloOAStatus;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tham số truy vấn người theo dõi Zalo
 */
export interface ZaloFollowerQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  oaId?: number;
  status?: 'ACTIVE' | 'BLOCKED' | 'UNFOLLOWED';
  tags?: string[];
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tham số truy vấn tin nhắn Zalo
 */
export interface ZaloMessageQueryDto {
  page?: number;
  limit?: number;
  oaId: number;
  followerId: string;
  startDate?: number;
  endDate?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tham số truy vấn template ZNS
 */
export interface ZNSTemplateQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  templateName?: string;
  status?: ZNSTemplateStatus;
  integrationId?: string;
  templateTag?: string;
  [key: string]: unknown;
}

/**
 * DTO tham số truy vấn nhóm Zalo
 */
export interface ZaloGroupQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  oaId?: number;
  status?: ZaloGroupStatus;
  tags?: string[];
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  [key: string]: unknown;
}

/**
 * DTO tạo tài khoản Zalo OA
 */
export interface CreateZaloOAAccountDto {
  oaId: string;
  accessToken: string;
  refreshToken: string;
  name: string;
  avatar?: string;
}

/**
 * DTO cập nhật tài khoản Zalo OA
 */
export interface UpdateZaloOAAccountDto {
  name?: string;
  status?: ZaloOAStatus;
}

/**
 * DTO tạo template ZNS
 */
export interface CreateZNSTemplateDto {
  oaId: number;
  name: string;
  content: string;
  params: string[];
}

/**
 * DTO gửi tin nhắn ZNS
 */
export interface SendZNSMessageDto {
  oaId: number;
  templateId: number;
  phone: string;
  params: Record<string, string>;
}

/**
 * DTO tạo chiến dịch ZNS nâng cao
 */
export interface CreateZNSCampaignDto {
  oaId: number;
  name: string;
  description?: string;
  templateId: number;

  // Target audience
  targetType: ZNSCampaignTargetType;
  targetTags?: string[];
  targetPhones?: string[];
  targetFollowerIds?: string[];

  // Scheduling
  scheduleType: 'immediate' | 'scheduled';
  scheduledAt?: number;
  timezone?: string;

  // Campaign parameters
  campaignParams?: Record<string, string>;
}

/**
 * DTO cập nhật chiến dịch ZNS
 */
export interface UpdateZNSCampaignDto {
  name?: string;
  description?: string;
  status?: ZNSCampaignStatus;
  scheduledAt?: number;
  campaignParams?: Record<string, string>;
}

/**
 * DTO tham số truy vấn chiến dịch ZNS
 */
export interface ZNSCampaignQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  oaId?: number;
  status?: string;
  targetType?: ZNSCampaignTargetType;
  templateId?: number;
  startDate?: number;
  endDate?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  integrationId?: string;
}

/**
 * DTO thống kê chiến dịch ZNS
 */
export interface ZNSCampaignStatsDto {
  totalCampaigns: number;
  activeCampaigns: number;
  completedCampaigns: number;
  totalMessagesSent: number;
  totalMessagesDelivered: number;
  totalMessagesRead: number;
  totalMessagesFailed: number;
  averageDeliveryRate: number;
  averageReadRate: number;
  averageFailureRate: number;
}

/**
 * DTO kết nối Official Account
 */
export interface ConnectOfficialAccountDto {
  accessToken: string;
  refreshToken: string;
}

/**
 * DTO response Official Account
 */
export interface OfficialAccountResponseDto {
  id: number;
  userId: number;
  oaId: string;
  name: string;
  avatar?: string;
  accessToken: string;
  refreshToken: string;
  status: ZaloOAStatus;
  followersCount: number;
  expireTime: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO Official Account cho paginated API
 */
export interface ZaloOfficialAccountDto {
  id: number;
  oaId: string;
  name: string;
  description?: string;
  avatarUrl?: string;
  status: 'active' | 'inactive' | 'pending' | 'error';
  createdAt: string;
  updatedAt: string;
}

/**
 * DTO tham số truy vấn cho paginated API
 */
export interface ZaloOfficialAccountQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  name?: string;
  status?: 'active' | 'inactive' | 'pending' | 'error';
}

/**
 * DTO chi tiết Official Account từ Zalo API
 */
export interface ZaloOADetailDto {
  oa_id: string;
  name: string;
  description: string;
  oa_alias: string;
  is_verified: boolean;
  oa_type: number;
  cate_name: string;
  num_follower: number;
  avatar: string;
  cover: string;
  package_name: string;
  package_valid_through_date: string;
  package_auto_renew_date: string;
  linked_zca: string;
}

/**
 * Response wrapper cho OA detail từ Zalo API
 */
export interface ZaloOADetailResponseDto {
  data: ZaloOADetailDto;
  error: number;
  message: string;
}

/**
 * DTO request cho quota message
 */
export interface ZaloQuotaMessageRequestDto {
  quotaOwner: string;
  productType: string;
  quotaType: string;
}

/**
 * DTO response cho quota message
 */
export interface ZaloQuotaMessageDto {
  asset_id: string;
  product_type: string;
  quota_type: string;
  valid_through: string;
  total: number;
  remain: number;
}

/**
 * DTO request cho bulk delete
 */
export interface ZaloBulkDeleteRequestDto {
  ids: string[];
}

// PagingResponseDto đã được định nghĩa trong shared types