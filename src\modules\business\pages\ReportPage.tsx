import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Tabs, Typography, Table, DoubleDatePicker } from '@/shared/components/common';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer } from 'recharts';
import { ListOverviewCard } from '@/shared/components/widgets';
import {
  useReportOverview,
  useTopSellingProducts,
  usePotentialCustomers,
  useSalesChart,
  useCustomersChart,
  useProductsChart
} from '../hooks/useReportQuery';
import {
  ReportPeriodEnum,
  TopSellingProductDto,
  PotentialCustomerDto
} from '../types/report.types';

/**
 * Trang báo cáo kinh doanh
 */
const ReportPage: React.FC = () => {
  const { t } = useTranslation('business');
  const [activeTab, setActiveTab] = useState('sales');
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

  // Query parameters for reports
  const reportParams = useMemo(() => ({
    period: ReportPeriodEnum.MONTH,
  }), []);

  // API calls
  const { data: overviewData, isLoading: overviewLoading } = useReportOverview(reportParams);
  const { data: topProductsData, isLoading: topProductsLoading } = useTopSellingProducts({
    limit: 10,
  });
  const { data: potentialCustomersData, isLoading: potentialCustomersLoading } = usePotentialCustomers({
    limit: 10,
  });
  const { data: salesChartData, isLoading: salesChartLoading } = useSalesChart({});
  const { data: customersChartData, isLoading: customersChartLoading } = useCustomersChart({});
  const { data: productsChartData, isLoading: productsChartLoading } = useProductsChart({
    limit: 10,
  });

  // Định dạng tiền tệ
  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  }, []);

  // Định dạng số
  const formatNumber = useCallback((num: number) => {
    return new Intl.NumberFormat('vi-VN').format(num);
  }, []);

  // Columns cho bảng khách hàng tiềm năng
  const potentialCustomersColumns = useMemo(() => [
    {
      key: 'rank',
      title: '#',
      render: (_: unknown, __: PotentialCustomerDto, index: number) => (
        <span className="text-sm font-medium text-muted-foreground">#{index + 1}</span>
      ),
      width: 60,
    },
    {
      key: 'customerName',
      title: t('business:customer.name'),
      dataIndex: 'customerName',
      render: (value: unknown, record: PotentialCustomerDto) => (
        <div>
          <Typography variant="body2" className="font-medium">
            {String(value)}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            {record.email}
          </Typography>
        </div>
      ),
    },
    {
      key: 'totalOrders',
      title: t('business:customer.totalOrders'),
      dataIndex: 'totalOrders',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-center">
          {formatNumber(Number(value))}
        </Typography>
      ),
      width: 120,
    },
    {
      key: 'totalSpent',
      title: t('business:customer.totalSpent'),
      dataIndex: 'totalSpent',
      render: (value: unknown) => (
        <Typography variant="body2" className="font-medium text-right">
          {formatCurrency(Number(value))}
        </Typography>
      ),
      width: 150,
    },
    {
      key: 'potentialScore',
      title: t('business:customer.potentialScore'),
      dataIndex: 'potentialScore',
      render: (value: unknown) => {
        const score = Number(value);
        return (
          <div className="text-center">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              score >= 80 ? 'bg-green-100 text-green-800' :
              score >= 60 ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
            }`}>
              {score}
            </span>
          </div>
        );
      },
      width: 100,
    },
  ], [t, formatCurrency, formatNumber]);

  // Chuyển đổi dữ liệu customers chart từ Record<string, number> thành array
  const transformedCustomersChartData = useMemo(() => {
    if (!customersChartData?.data) return [];

    return Object.entries(customersChartData.data).map(([period, value]) => ({
      period,
      customers: value,
      // Tạo label hiển thị đẹp hơn
      displayPeriod: period.replace('2025-W', 'Tuần ').replace('2025-', '')
    }));
  }, [customersChartData]);

  // Component biểu đồ doanh thu
  const SalesChart = () => {
    if (salesChartLoading) {
      return (
        <div className="h-80 flex items-center justify-center">
          <Typography variant="h5" className="text-muted-foreground">
            {t('business:report.loading', 'Đang tải...')}
          </Typography>
        </div>
      );
    }

    if (!salesChartData?.data || salesChartData.data.length === 0) {
      return (
        <div className="h-80 flex items-center justify-center">
          <Typography variant="h5" className="text-muted-foreground">
            {t('business:report.noData', 'Không có dữ liệu')}
          </Typography>
        </div>
      );
    }

    return (
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={salesChartData.data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="period"
              tick={{ fontSize: 12 }}
            />
            <YAxis
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => formatCurrency(value)}
            />
            <RechartsTooltip
              formatter={(value: number) => [formatCurrency(value), t('business:report.charts.labels.revenue', 'Doanh thu')]}
              labelFormatter={(label) => `${t('business:report.charts.labels.timeLabel', 'Thời gian')}: ${label}`}
            />
            <Line
              type="monotone"
              dataKey="revenue"
              stroke="#2563eb"
              strokeWidth={2}
              dot={{ fill: '#2563eb', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    );
  };

  // Component biểu đồ khách hàng
  const CustomersChart = () => {
    if (customersChartLoading) {
      return (
        <div className="h-80 flex items-center justify-center">
          <Typography variant="h5" className="text-muted-foreground">
            {t('business:report.loading', 'Đang tải...')}
          </Typography>
        </div>
      );
    }

    if (!transformedCustomersChartData || transformedCustomersChartData.length === 0) {
      return (
        <div className="h-80 flex items-center justify-center">
          <Typography variant="h5" className="text-muted-foreground">
            {t('business:report.noData', 'Không có dữ liệu')}
          </Typography>
        </div>
      );
    }

    return (
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={transformedCustomersChartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="displayPeriod"
              tick={{ fontSize: 12 }}
            />
            <YAxis
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => formatNumber(value)}
            />
            <RechartsTooltip
              formatter={(value: number, name: string) => [
                formatNumber(value),
                name === 'customers' ? t('business:report.charts.labels.customers', 'Khách hàng') : t('business:report.charts.labels.customers', 'Khách hàng')
              ]}
              labelFormatter={(label) => `${t('business:report.charts.labels.timeLabel', 'Thời gian')}: ${label}`}
            />
            <Line
              type="monotone"
              dataKey="customers"
              stroke="#10b981"
              strokeWidth={2}
              dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6 }}
              name="newCustomers"
            />
            <Line
              type="monotone"
              dataKey="totalCustomers"
              stroke="#3b82f6"
              strokeWidth={2}
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6 }}
              name="totalCustomers"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    );
  };

  // Component biểu đồ sản phẩm
  const ProductsChart = () => {
    if (productsChartLoading) {
      return (
        <div className="h-80 flex items-center justify-center">
          <Typography variant="h5" className="text-muted-foreground">
            {t('business:report.loading', 'Đang tải...')}
          </Typography>
        </div>
      );
    }

    if (!productsChartData?.data || productsChartData.data.length === 0) {
      return (
        <div className="h-80 flex items-center justify-center">
          <Typography variant="h5" className="text-muted-foreground">
            {t('business:report.noData', 'Không có dữ liệu')}
          </Typography>
        </div>
      );
    }

    return (
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={productsChartData.data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="period"
              tick={{ fontSize: 12 }}
            />
            <YAxis
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => formatNumber(value)}
            />
            <RechartsTooltip
              formatter={(value: number, name: string) => {
                const labels = {
                  totalProducts: t('business:report.charts.labels.totalProducts', 'Tổng sản phẩm'),
                  newProducts: t('business:report.charts.labels.newProducts', 'Sản phẩm mới'),
                  soldProducts: t('business:report.charts.labels.soldProducts', 'Sản phẩm đã bán')
                };
                return [formatNumber(value), labels[name as keyof typeof labels] || name];
              }}
              labelFormatter={(label) => `${t('business:report.charts.labels.timeLabel', 'Thời gian')}: ${label}`}
            />
            <Line
              type="monotone"
              dataKey="totalProducts"
              stroke="#3b82f6"
              strokeWidth={2}
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6 }}
              name="totalProducts"
            />
            <Line
              type="monotone"
              dataKey="newProducts"
              stroke="#10b981"
              strokeWidth={2}
              dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6 }}
              name="newProducts"
            />
            <Line
              type="monotone"
              dataKey="soldProducts"
              stroke="#f59e0b"
              strokeWidth={2}
              dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6 }}
              name="soldProducts"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    );
  };

  // Dữ liệu cho overview cards
  const overviewCards = useMemo(() => {
    if (!overviewData) {
      return [
        {
          title: t('business:report.totalRevenue', 'Tổng doanh thu'),
          value: '--',
          description: t('business:report.loading', 'Đang tải...'),
          color: 'blue' as const,
        },
        {
          title: t('business:report.totalOrders', 'Tổng đơn hàng'),
          value: '--',
          description: t('business:report.loading', 'Đang tải...'),
          color: 'green' as const,
        },
        {
          title: t('business:report.newCustomers', 'Khách hàng mới'),
          value: '--',
          description: t('business:report.loading', 'Đang tải...'),
          color: 'orange' as const,
        },
      ];
    }

    return [
      {
        title: t('business:report.totalRevenue', 'Tổng doanh thu'),
        value: formatCurrency(overviewData.totalRevenue),
        description: `${overviewData.startDate} - ${overviewData.endDate}`,
        color: 'blue' as const,
      },
      {
        title: t('business:report.totalOrders', 'Tổng đơn hàng'),
        value: formatNumber(overviewData.totalOrders),
        description: `${overviewData.startDate} - ${overviewData.endDate}`,
        color: 'green' as const,
      },
      {
        title: t('business:report.newCustomers', 'Khách hàng mới'),
        value: formatNumber(overviewData.newCustomers),
        description: `${overviewData.startDate} - ${overviewData.endDate}`,
        color: 'orange' as const,
      },
    ];
  }, [overviewData, t, formatCurrency, formatNumber]);

  return (
    <div className="space-y-6">
      <ListOverviewCard
        items={overviewCards}
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        gap={6}
        isLoading={overviewLoading}
      />

      <Card>
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <Typography variant="h5">
              {t('business:report.charts.title', 'Biểu đồ thống kê')}
            </Typography>
            <DoubleDatePicker
              value={dateRange}
              onChange={setDateRange}
              placeholder={t('business:report.selectDateRange', 'Chọn khoảng thời gian')}
            />
          </div>
        </div>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'sales',
              label: t('business:report.tabs.sales', 'Doanh thu'),
              children: (
                <div className="p-4">
                  <SalesChart />
                </div>
              ),
            },
            {
              key: 'orders',
              label: t('business:report.tabs.orders', 'Đơn hàng'),
              children: (
                <div className="p-4">
                  <div className="h-80 flex items-center justify-center">
                    <Typography variant="h5" className="text-muted-foreground">
                      {t(
                        'business:report.charts.ordersPlaceholder',
                        'Biểu đồ đơn hàng sẽ được hiển thị ở đây'
                      )}
                    </Typography>
                  </div>
                </div>
              ),
            },
            {
              key: 'customers',
              label: t('business:report.tabs.customers', 'Khách hàng'),
              children: (
                <div className="p-4">
                  <CustomersChart />
                </div>
              ),
            },
            {
              key: 'products',
              label: t('business:report.tabs.products', 'Sản phẩm'),
              children: (
                <div className="p-4">
                  <ProductsChart />
                </div>
              ),
            },
          ]}
        />
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
        <Card>
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <Typography variant="h6">
                {t('business:report.topSellingProducts', 'Sản phẩm bán chạy')}
              </Typography>
              <DoubleDatePicker
                value={dateRange}
                onChange={setDateRange}
                placeholder={t('business:report.selectDateRange', 'Chọn khoảng thời gian')}
                size="sm"
              />
            </div>
          </div>
          <div className="px-4 pb-4">
            {topProductsLoading ? (
              <div className="h-60 flex items-center justify-center">
                <Typography variant="h5" className="text-muted-foreground">
                  {t('business:report.loading', 'Đang tải...')}
                </Typography>
              </div>
            ) : topProductsData?.products && topProductsData.products.length > 0 ? (
              <div className="space-y-3">
                {topProductsData.products.slice(0, 5).map((product: TopSellingProductDto, index: number) => (
                  <div key={product.id} className="flex items-center justify-between p-2 border-b">
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-muted-foreground">#{index + 1}</span>
                      <div>
                        <Typography variant="body2" className="font-medium">
                          {product.name}
                        </Typography>
                        <Typography variant="caption" className="text-muted-foreground">
                          SKU: {product.sku}
                        </Typography>
                      </div>
                    </div>
                    <div className="text-right">
                      <Typography variant="body2" className="font-medium">
                        {formatCurrency(product.revenue)}
                      </Typography>
                      <Typography variant="caption" className="text-muted-foreground">
                        {formatNumber(product.quantity)} sản phẩm
                      </Typography>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="h-60 flex items-center justify-center">
                <Typography variant="h5" className="text-muted-foreground">
                  {t('business:report.noData', 'Không có dữ liệu')}
                </Typography>
              </div>
            )}
          </div>
        </Card>

        <Card title={t('business:report.potentialCustomers', 'Khách hàng tiềm năng')}>
          <div className="overflow-hidden">
            <Table
              columns={potentialCustomersColumns}
              data={potentialCustomersData?.data || []}
              rowKey="customerId"
              loading={potentialCustomersLoading}
              pagination={false}
              size="sm"
              className="min-h-[100px]"
            />
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ReportPage;
