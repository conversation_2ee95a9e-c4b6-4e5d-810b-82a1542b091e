/**
 * Types cho Business Report Module
 */

/**
 * Enum cho các khoảng thời gian báo cáo
 */
export enum ReportPeriodEnum {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  QUARTER = 'quarter',
  YEAR = 'year'
}

/**
 * Enum cho loại dữ liệu biểu đồ line-chart chính
 */
export enum BusinessReportTypeEnum {
  ORDER = 'ORDER',
  CUSTOMER = 'CUSTOMER',
  PRODUCT = 'PRODUCT',
  REVENUE = 'REVENUE',
  NEW_CUSTOMERS = 'NEW_CUSTOMERS',
  RETURNING_CUSTOMERS = 'RETURNING_CUSTOMERS',
  TOTAL_CUSTOMERS = 'TOTAL_CUSTOMERS',
  AVERAGE_ORDER_VALUE = 'AVERAGE_ORDER_VALUE'
}

/**
 * DTO cho query parameters của API line-chart chính (frontend)
 */
export interface LineChartQueryDto {
  begin?: string; // <PERSON><PERSON><PERSON> bắt đầu (YYYY-MM-DD)
  end?: string;   // Ng<PERSON>y kết thúc (YYYY-MM-DD)
  type: BusinessReportTypeEnum; // Loại dữ liệu
}

/**
 * DTO cho query parameters gửi xuống API (backend) với bigint timestamps
 */
export interface LineChartApiQueryDto {
  begin?: string; // Bigint timestamp as string
  end?: string;   // Bigint timestamp as string
  type: BusinessReportTypeEnum; // Loại dữ liệu
}

/**
 * DTO cho response API line-chart chính
 */
export interface LineChartResponseDto {
  data: Record<string, number>; // Object với key là ngày, value là giá trị
  period: string; // Khoảng thời gian được tính toán tự động (hour/day/week/month/year)
}

/**
 * DTO cho query parameters của API tổng quan báo cáo
 */
export interface ReportOverviewQueryDto {
  startDate?: string;
  endDate?: string;
  period?: ReportPeriodEnum;
}

/**
 * DTO cho dữ liệu so sánh với kỳ trước
 */
export interface PreviousPeriodDataDto {
  totalRevenue: number;
  totalOrders: number;
  newCustomers: number;
  revenueGrowth: number;
  ordersGrowth: number;
  customersGrowth: number;
}

/**
 * DTO cho response API tổng quan báo cáo
 */
export interface ReportOverviewResponseDto {
  totalRevenue: number;
  totalOrders: number;
  newCustomers: number;
  period: string;
  startDate: string;
  endDate: string;
  previousPeriod?: PreviousPeriodDataDto;
}

/**
 * DTO cho query parameters của API biểu đồ doanh thu
 */
export interface SalesChartQueryDto {
  startDate?: string;
  endDate?: string;
}

/**
 * DTO cho dữ liệu điểm biểu đồ doanh thu
 */
export interface SalesChartDataPoint {
  period: string;
  revenue: number;
  orders: number;
  date: string;
}

/**
 * DTO cho response API biểu đồ doanh thu
 */
export interface SalesChartResponseDto {
  data: SalesChartDataPoint[];
  totalRevenue: number;
  totalOrders: number;
  period: string;
  groupBy: string;
}

/**
 * DTO cho query parameters của API biểu đồ đơn hàng
 */
export interface OrdersChartQueryDto {
  startDate?: string;
  endDate?: string;
  groupBy?: 'day' | 'week' | 'month' | 'quarter' | 'year';
  status?: string[];
}

/**
 * DTO cho dữ liệu điểm biểu đồ đơn hàng (match với API response thực tế)
 */
export interface OrdersChartDataPoint {
  period: string;
  date: string;
  totalOrders: number;
  pendingOrders: number;
  preparingOrders: number;
  shippedOrders: number;
  inTransitOrders: number;
  deliveredOrders: number;
  cancelledOrders: number;
}

/**
 * DTO cho status breakdown trong response API biểu đồ đơn hàng
 */
export interface OrdersStatusBreakdown {
  pending: number;
  preparing: number;
  shipped: number;
  inTransit: number;
  delivered: number;
  cancelled: number;
}

/**
 * DTO cho response API biểu đồ đơn hàng (match với API response thực tế)
 */
export interface OrdersChartResponseDto {
  data: OrdersChartDataPoint[];
  statusBreakdown: OrdersStatusBreakdown;
}

/**
 * Enum cho loại khách hàng trong biểu đồ
 */
export enum CustomersChartTypeEnum {
  NEW_CUSTOMERS = 'NEW_CUSTOMERS',
  RETURNING_CUSTOMERS = 'RETURNING_CUSTOMERS',
  TOTAL_CUSTOMERS = 'TOTAL_CUSTOMERS'
}

/**
 * DTO cho query parameters của API biểu đồ khách hàng
 */
export interface CustomersChartQueryDto {
  begin?: number; // Thời gian bắt đầu (timestamp)
  end?: number;   // Thời gian kết thúc (timestamp)
  type?: CustomersChartTypeEnum; // Loại khách hàng
}

/**
 * DTO cho response API biểu đồ khách hàng (theo API response thực tế)
 */
export interface CustomersChartResponseDto {
  data: Record<string, number>; // Dữ liệu theo format "2025-W24": 13
  period: string; // Loại period như "week", "month", etc.
}

/**
 * DTO cho query parameters của API biểu đồ sản phẩm
 */
export interface ProductsChartQueryDto {
  startDate?: string;
  endDate?: string;
  limit?: number;
}

/**
 * DTO cho dữ liệu điểm biểu đồ sản phẩm
 */
export interface ProductsChartDataPoint {
  period: string;
  totalProducts: number;
  newProducts: number;
  soldProducts: number;
  date: string;
}

/**
 * DTO cho response API biểu đồ sản phẩm
 */
export interface ProductsChartResponseDto {
  data: ProductsChartDataPoint[];
  totalProducts: number;
  newProducts: number;
  soldProducts: number;
  period: string;
  groupBy: string;
}

/**
 * DTO cho query parameters của API sản phẩm bán chạy
 */
export interface TopSellingProductsQueryDto {
  startDate?: string;
  endDate?: string;
  limit?: number;
}

/**
 * DTO cho dữ liệu sản phẩm bán chạy
 */
export interface TopSellingProductDto {
  id: number;
  name: string;
  sku: string;
  image?: string;
  revenue: number;
  quantity: number;
  orders: number;
  rank: number;
}

/**
 * DTO cho response API sản phẩm bán chạy
 */
export interface TopSellingProductsResponseDto {
  products: TopSellingProductDto[];
  totalRevenue: number;
  totalQuantity: number;
  period: string;
}

/**
 * DTO cho query parameters của API khách hàng tiềm năng
 */
export interface PotentialCustomersQueryDto {
  startDate?: string;
  endDate?: string;
  limit?: number;
}

/**
 * DTO cho dữ liệu khách hàng tiềm năng
 */
export interface PotentialCustomerDto {
  customerId: number;
  customerName: string;
  email: string;
  phone?: string;
  avatarUrl?: string;
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  lastOrderDate?: string;
  daysSinceLastOrder: number;
  potentialScore: number;
  tags: string[];
}

/**
 * DTO cho response API khách hàng tiềm năng
 */
export interface PotentialCustomersResponseDto {
  data: PotentialCustomerDto[];
  meta: {
    totalItems: number;
    averagePotentialScore: number;
  };
}

/**
 * DTO cho query parameters của API biểu đồ tròn loại sản phẩm
 */
export interface ProductTypePieChartQueryDto {
  begin?: number; // Thời gian bắt đầu (timestamp)
  end?: number;   // Thời gian kết thúc (timestamp)
}

/**
 * DTO cho dữ liệu một loại sản phẩm trong biểu đồ tròn
 */
export interface ProductTypeDataDto {
  key: string;        // Mã loại sản phẩm (PHYSICAL, DIGITAL, COMBO, SERVICE, EVENT)
  label: string;      // Tên hiển thị loại sản phẩm
  value: number;      // Số lượng sản phẩm
  percentage: number; // Phần trăm
  color: string;      // Màu sắc hiển thị
}

/**
 * DTO cho thông tin tổng kết biểu đồ tròn loại sản phẩm
 */
export interface ProductTypeSummaryDto {
  totalItems: number;           // Tổng số sản phẩm
  totalCategories: number;      // Tổng số loại sản phẩm
  mostPopularKey: string;       // Mã loại sản phẩm phổ biến nhất
  mostPopularLabel: string;     // Tên loại sản phẩm phổ biến nhất
}

/**
 * DTO cho response API biểu đồ tròn loại sản phẩm
 */
export interface ProductTypePieChartResponseDto {
  data: ProductTypeDataDto[];   // Dữ liệu các loại sản phẩm
  summary: ProductTypeSummaryDto; // Thông tin tổng kết
  startDate: string;            // Ngày bắt đầu (ISO string)
  endDate: string;              // Ngày kết thúc (ISO string)
}
