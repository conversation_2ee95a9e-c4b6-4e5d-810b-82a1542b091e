{"integration": {"title": "Integration", "breadcrumb": {"home": "Home", "integrations": "Integrations", "googleAds": "Google Ads", "facebookAds": "Facebook Ads", "gmail": "Gmail", "calendar": "Google Calendar"}, "myIntegrations": {"title": "My Integrations"}, "allIntegrations": "All Integrations", "types": {"bank": "Bank", "llm": "LLM", "sms": "SMS", "email": "Email", "database": "Database", "social": "Social", "shipping": "Shipping", "calendar": "Calendar", "ads": "Ads", "other": "Other"}, "bankAccounts": {"title": "Bank Accounts Management"}, "googleAds": {"title": "Google Ads Integration", "description": "Integrate with Google Ads to manage advertising campaigns", "connectTitle": "Connect to Google Ads", "connectDescription": "Connect to Google Ads to manage advertising campaigns", "connectButton": "Connect with Google Ads"}, "facebookAds": {"title": "Facebook Ads Integration", "description": "Integrate with Facebook Ads to manage advertising campaigns", "connectTitle": "Connect to Facebook Ads", "connectDescription": "Connect to Facebook Ads to manage advertising campaigns", "connectButton": "Connect with Facebook Ads", "management": {"title": "Facebook Ads Management", "description": "Manage your Facebook Ads integrations"}}, "cards": {"banking": {"mb": {"description": "Integration with MB Bank "}, "acb": {"description": "Integration with ACB Bank"}, "ocb": {"description": "Integration with OCB Bank"}, "kienlong": {"description": "Integration with Kien Long Bank"}}, "llm": {"openai": {"description": "Integration with OpenAI GPT models"}, "anthropic": {"description": "Integration with Anthropic Claude <PERSON>"}, "gemini": {"description": "Integration with Google Gemini Pro"}, "deepseek": {"description": "Integration with DeepSeek AI models"}, "xai": {"description": "Integration with XAI Grok models"}}, "sms": {"twilio": {"description": "Integration with Twilio for SMS sending", "title": "Twilio SMS Integration", "form": {"fields": {"integrationName": "Integration Name", "integrationNameHelp": "Name to identify this integration", "integrationNamePlaceholder": "e.g., Twilio Production SMS", "authToken": "<PERSON><PERSON><PERSON>", "authTokenHelp": "<PERSON><PERSON> from <PERSON><PERSON><PERSON>", "authTokenPlaceholder": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "baseDomain": "Twilio Base Domain", "baseDomainHelp": "Base domain for Twilio API (e.g., api.twilio.com)", "baseDomainPlaceholder": "Enter Twilio Base Domain"}, "placeholders": {"integrationName": "Enter integration name", "authToken": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "baseDomain": "Enter Twilio Base Domain"}, "create": "Create Integration", "cancel": "Cancel", "saving": "Saving...", "required": "Required"}, "validation": {"integrationName": {"required": "Integration name is required", "maxLength": "Integration name cannot exceed 100 characters"}, "authToken": {"required": "<PERSON><PERSON><PERSON> is required", "minLength": "<PERSON><PERSON><PERSON> must be at least 10 characters"}, "baseDomain": {"required": "Twilio Base Domain is required", "minLength": "Twilio Base Domain must be at least 3 characters"}}}, "fpt": {"description": "Integration with FPT SMS", "descriptionManagement": "Manage FPT SMS configurations", "breadcrumb": "FPT SMS", "title": "FPT SMS Integration", "form": {"fields": {"integrationName": "Integration Name", "integrationNameHelp": "Name to identify this integration", "integrationNamePlaceholder": "e.g., FPT SMS - Company ABC", "clientId": "Client ID", "clientIdHelp": "Client ID from FPT SMS for authentication", "clientIdPlaceholder": "Enter Client ID from FPT SMS", "clientSecret": "Client Secret", "clientSecretHelp": "Client Secret from FPT SMS for authentication", "clientSecretPlaceholder": "Enter Client Secret from FPT SMS", "brandName": "Brandname", "brandNameHelp": "Brandname for this integration", "brandNamePlaceholder": "Enter Brandname"}, "placeholders": {"integrationName": "Enter integration name", "clientId": "Enter Client ID from FPT SMS", "clientSecret": "Enter Client Secret from FPT SMS", "brandName": "Enter Brandname"}, "validation": {"integrationName": {"required": "Integration name is required", "maxLength": "Integration name cannot exceed 100 characters"}, "clientId": {"required": "Client ID is required", "maxLength": "Client ID cannot exceed 255 characters"}, "clientSecret": {"required": "Client Secret is required", "maxLength": "Client Secret cannot exceed   255 characters"}, "brandName": {"required": "Brandname is required", "maxLength": "Brandname cannot exceed 255 characters"}}, "create": "Create Integration", "cancel": "Cancel", "saving": "Saving...", "required": "Required", "editTitle": "Edit FPT SMS Integration", "editDescription": "Update FPT SMS integration information", "createTitle": "Create FPT SMS Integration", "createDescription": "Fill in the information to create a new FPT SMS integration"}, "list": {"title": "FPT SMS Management", "description": "Manage FPT SMS configurations", "columns": {"integrationName": "Integration Name", "brandName": "Brandname", "clientId": "Client ID", "endpoint": "Endpoint", "createdAt": "Created At", "status": "Status", "actions": "Actions"}}, "actions": {"edit": "Edit", "delete": "Delete", "testConnection": "Test Connection", "moreActions": "More Actions"}, "confirmations": {"deleteTitle": "Confirm Delete", "delete": "Are you sure you want to delete this configuration?"}, "error": {"createFailed": "FPT SMS configuration failed", "createFailedDescription": "An error occurred during configuration. Please try again.", "updateFailed": "Configuration update failed", "deleteFailed": "Configuration deletion failed", "invalidConfig": "Configuration information is invalid"}}}, "social": {"zalo": {"description": "Integration with Zalo Official Account", "breadcrumb": "Zalo OA", "title": "Zalo Official Account", "list": {"title": "Zalo Official Account Management", "description": "Manage Zalo Official Accounts", "columns": {"name": "Name", "description": "Description", "status": "Status", "createdAt": "Created At", "actions": "Actions"}}, "status": {"active": "Active", "inactive": "Inactive", "suspended": "Suspended", "error": "Error"}, "actions": {"disconnect": "Disconnect", "delete": "Delete", "moreActions": "More Actions"}}}, "shipping": {"viettelPost": {"description": "Integration with Viettel Post", "breadcrumb": "Viettel Post"}, "vnpost": {"description": "Integration with VNPost", "breadcrumb": "VNPost"}, "jnt": {"description": "Integration with J&T Express", "breadcrumb": "J&T Express"}, "list": {"title": "Shipping Management", "description": "Manage shipping configurations", "columns": {"integrationName": "Integration Name", "providerName": "Provider Name", "providerType": "Provider Type", "shopId": "Shop ID", "default": "<PERSON><PERSON><PERSON>", "status": "Status", "actions": "Actions"}}, "ghtk": {"description": "Integration with Giao Hang Tiet Kiem (GHTK)", "breadcrumb": "Economy Shipping"}, "ghn": {"description": "Integration with Giao <PERSON> (GHN)", "breadcrumb": "Fast Shipping"}, "ahamove": {"description": "Integration with Ahamove - Fast delivery", "breadcrumb": "Ahamove - Fast Delivery"}}}, "googleCalendar": {"title": "Google Calendar Integration", "description": "Integrate with Google Calendar", "addCalendar": "Add Google Calendar", "editCalendar": "Edit Google Calendar", "viewCalendar": "View Google Calendar", "addFirstCalendar": "Add First Google Calendar", "management": {"title": "Google Calendar Management", "description": "Manage Google Calendar configurations"}, "table": {"columns": {"name": "Name", "user": "User", "status": "Status", "calendars": "Calendars", "lastSync": "Last Sync"}}, "card": {"connected": "Connected", "connectTitle": "Connect Google Calendar", "connectDescription": "Connect with Google Calendar to sync calendars and events, manage time more efficiently", "disconnect": "Disconnect", "connect": "Connect Google Calendar", "connecting": "Connecting...", "securityNote": "Secure and safe connection", "calendarsCount": "calendars", "features": {"syncEvents": "Sync events", "manageSchedule": "Manage schedule", "autoReminder": "Auto reminder", "shareCalendar": "Share calendar"}}, "form": {"title": "Google Calendar Configuration", "displayName": "Display Name", "displayNameHelp": "Display name for this Google Calendar configuration", "displayNamePlaceholder": "e.g., Main Google Calendar Configuration", "clientId": "Client ID", "clientIdHelp": "Client ID from Google Cloud Console", "clientIdPlaceholder": "Enter Client ID", "clientSecret": "Client Secret", "clientSecretHelp": "Client Secret from Google Cloud Console", "clientSecretPlaceholder": "Enter Client Secret", "refreshToken": "Refresh <PERSON>", "refreshTokenHelp": "Refresh <PERSON> to keep connection alive", "refreshTokenPlaceholder": "Enter Refresh <PERSON>", "calendarId": "Calendar ID", "calendarIdHelp": "Specific Calendar ID to sync (optional)", "calendarIdPlaceholder": "Enter Calendar ID (optional)", "isActive": "Is Active", "isActiveHelp": "Enable/Disable this integration"}}, "gmail": {"title": "Gmail Integration", "description": "Connect with Gmail to send and receive emails", "addGmail": "Add Gmail", "editGmail": "Edit Gmail", "viewGmail": "View Gmail", "addFirstGmail": "Add First Gmail", "management": {"title": "Gmail Management", "description": "Manage Gmail configurations"}, "table": {"columns": {"name": "Name", "email": "Email", "status": "Status", "createdAt": "Created At"}}, "form": {"title": "Gmail Configuration", "displayName": "Display Name", "displayNameHelp": "Display name for this Gmail configuration", "displayNamePlaceholder": "e.g., Main Gmail Configuration", "isActive": "Is Active", "isActiveHelp": "Enable/Disable this integration"}, "card": {"description": "Connect with Gmail to send and receive emails automatically", "connectedAndActive": "Connected and active", "disconnect": "Disconnect", "connect": "Connect Gmail", "connecting": "Connecting..."}}, "calendar": {"title": "Google Calendar Integration", "description": "Integrate with Google Calendar", "addCalendar": "Add Google Calendar", "editCalendar": "Edit Google Calendar", "viewCalendar": "View Google Calendar", "addFirstCalendar": "Add First Google Calendar", "form": {"title": "Google Calendar Configuration", "displayName": "Display Name", "displayNameHelp": "Display name for this Google Calendar configuration", "displayNamePlaceholder": "e.g., Main Google Calendar Configuration", "clientId": "Client ID", "clientIdHelp": "Client ID from Google Cloud Console", "clientIdPlaceholder": "Enter Client ID", "clientSecret": "Client Secret", "clientSecretHelp": "Client Secret from Google Cloud Console", "clientSecretPlaceholder": "Enter Client Secret", "refreshToken": "Refresh <PERSON>", "refreshTokenHelp": "Refresh <PERSON> to keep connection alive", "refreshTokenPlaceholder": "Enter Refresh <PERSON>", "calendarId": "Calendar ID", "calendarIdHelp": "Specific Calendar ID to sync (optional)", "calendarIdPlaceholder": "Enter Calendar ID (optional)", "isActive": "Is Active", "isActiveHelp": "Enable/Disable this integration"}}, "table": {"integrationName": "Integration Name", "type": "Type", "status": "Status", "actions": "Actions"}, "dashboard": {"totalIntegrations": "Total Integrations", "totalIntegrationsDesc": "Total number of configured integrations", "activeIntegrations": "Active Integrations", "activeIntegrationsDesc": "Number of integrations currently working normally", "websites": "Websites", "websitesDesc": "Number of integrated websites", "apiCallsToday": "API Calls Today", "apiCallsTodayDesc": "Number of API calls today", "webhooks": "Webhooks", "webhooksDesc": "Number of active webhooks", "integrationErrors": "Integration Errors", "integrationErrorsDesc": "Number of integrations with errors that need attention"}, "externalAgents": {"title": "External Agents Management", "description": "Integrate with external agents through MCP, REST API, WebSocket", "overview": "Overview", "protocolDistribution": "Protocol Distribution", "quickActions": "Quick Actions", "recentActivity": "Recent Activity", "gettingStarted": "Getting Started", "gettingStartedDescription": "No external agents configured yet. Get started by creating your first agent.", "createDescription": "Create a new external agent with custom protocol configuration", "manageDescription": "Manage all existing external agents and their settings", "protocolsDescription": "View and configure supported protocols", "analyticsDescription": "View performance analytics and usage statistics", "noRecentActivity": "No recent activity", "activityWillAppear": "External agent activity will appear here", "step1": "Choose integration protocol (MCP, Google Agent, REST API, etc.)", "step2": "Configure endpoint and authentication information", "step3": "Test connection and start using"}, "boxChat": {"configTitle": "Box Chat Configuration", "welcomeText": "Welcome Message", "welcomeTextPlaceholder": "Enter welcome message", "avatar": "Avatar", "avatarError": "Avatar Error", "avatarErrorDesc": "Only image files are accepted", "avatarSizeError": "File size must not exceed 2MB", "avatarUpdateSuccess": "Avatar updated successfully!", "avatarUpdateSuccessDesc": "Box chat avatar has been updated.", "avatarUpdateError": "Avatar update failed!", "avatarUpdateErrorDesc": "Please try again later.", "clickToChangeAvatar": "Click on image to change avatar", "clickToUploadAvatar": "Click on frame to upload avatar", "avatarDescription": "Accept image files (JPEG, PNG, WebP), max 2MB", "placeholderMessage": "Placeholder Message", "placeholderMessagePlaceholder": "Enter your message...", "displayMode": "Display Mode", "sideMode": "Side Mode", "colorPrimary": "Primary Color", "icon": "Icon", "iconError": "Icon Error", "iconErrorDesc": "Only image files are accepted", "iconSizeError": "File size must not exceed 2MB", "clickToChangeIcon": "Click on image to change icon", "clickToUploadIcon": "Click on frame to upload icon", "iconDescription": "Accept image files (JPEG, PNG, WebP), max 2MB", "selectedFormat": "Selected Format", "bannerMediaIds": "Banner Media", "addBannerMedia": "Add Banner Media", "banners": "Banner Images", "bannerError": "<PERSON>r", "bannerErrorDesc": "Only image files are accepted", "bannerSizeError": "File size must not exceed 10MB", "editBanner": "Edit Banner", "bannerSlot": "Banner {{index}}", "addBanner": "Add Banner", "quickMessages": "Quick Messages", "quickMessagePlaceholder": "Quick message", "addQuickMessage": "Add Quick Message", "components": "Components", "componentPlaceholder": "Component name", "addComponent": "Add Component", "iframeWidth": "Wid<PERSON> (px)", "iframeHeight": "Height (px)", "textColor": "Text Color", "backgroundColor": "Background Color", "updateSuccess": "Update successful!", "updateSuccessDesc": "Box chat configuration has been updated.", "updateError": "Update failed!", "updateErrorDesc": "Please try again later.", "updating": "Updating...", "update": "Update", "corner": "Corner", "center": "Center", "selectDisplayMode": "Select display mode", "floating": "Floating", "fixed": "Fixed", "selectSideMode": "Select side mode", "selectMedia": "Select media"}, "providerModel": {"title": "Provider Model Management", "create": "Create Provider Model", "form": {"view": "View Provider Model", "edit": "Edit Provider Model", "create": "Create Provider Model", "viewDescription": "View detailed information of Provider Model", "editDescription": "Edit Provider Model information. Only name and API key can be changed.", "createDescription": "Create new Provider Model to integrate with AI providers", "cannotChange": "Cannot be changed", "fields": {"type": "Provider Type", "name": "Provider Model Name", "namePlaceholder": "Enter provider model name", "apiKey": "API Key", "apiKeyPlaceholder": "Enter API key", "apiKeyPlaceholderEdit": "Leave empty if you don't want to change API key..."}}, "actions": {"cancel": "Cancel", "save": "Save", "create": "Create Provider Model", "edit": "Edit", "delete": "Delete", "retry": "Retry"}, "empty": {"title": "No Provider Models yet", "description": "Add Provider Model to get started."}, "error": {"title": "Error loading data", "description": "An error occurred while loading Provider Model list. Please try again."}, "confirmations": {"deleteTitle": "Confirm Delete", "delete": "Are you sure you want to delete this Provider Model?", "bulkDelete": "Are you sure you want to delete {{count}} selected provider models?", "noItemsSelected": "No items selected"}, "validation": {"name": {"required": "Name is required", "maxLength": "Name cannot exceed 255 characters"}, "type": {"invalid": "Invalid provider type"}, "apiKey": {"required": "API key is required", "minLength": "API key must be at least 10 characters", "format": "API key can only contain letters, numbers, hyphens, underscores and dots"}}, "provider": "Provider", "name": "Display Name", "apiKey": "API Key", "nameRequired": "Name is required", "apiKeyRequired": "API Key is required", "createSuccess": "Created successfully", "createSuccessMessage": "Provider model has been created successfully", "createError": "Error creating provider model", "createErrorMessage": "An error occurred while creating provider model", "metaNamePlaceholder": "Example: My Meta Key", "metaApiKeyPlaceholder": "meta-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "metaApiKeyFormat": "Meta API Key must start with \"meta-\"", "createMetaButton": "Create Meta Provider Model", "xaiNamePlaceholder": "Example: My XAI Key", "xaiApiKeyPlaceholder": "xai-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "xaiApiKeyFormat": "XAI API Key must start with \"xai-\"", "createXaiButton": "Create XAI Provider Model"}, "ai": {"openai": {"title": "Integrate with OpenAI"}, "anthropic": {"title": "Integrate with Anthropic"}, "google": {"title": "Integrate with Google Gemini"}, "meta": {"title": "Integrate with Meta Llama"}, "deepseek": {"title": "Integrate with DeepSeek"}, "xai": {"title": "Integrate with XAI"}}, "openai": {"title": "List of OpenAI Keys", "provider": "Provider", "description": "Manage integrated OpenAI API keys", "addKey": "Add OpenAI Key", "systemModelsTitle": "System Models", "userModelsTitle": "User Models", "editKey": "Edit OpenAI Key", "deleteKey": "Delete OpenAI Key", "keyName": "Display Name", "keyNamePlaceholder": "e.g., My OpenAI Key", "keyNameHelp": "Name to distinguish different API keys", "apiKey": "API Key", "apiKeyPlaceholder": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "apiKeyHelp": "Get API key from https://platform.openai.com/api-keys", "status": "Status", "createdAt": "Created Date", "actions": "Actions", "viewUserModels": "View User Models", "viewSystemModels": "View System Models", "confirmDelete": "Are you sure you want to delete this API key?", "deleteSuccess": "OpenAI key deleted successfully", "deleteError": "Error occurred while deleting OpenAI key", "createSuccess": "OpenAI key created successfully", "createError": "Error occurred while creating OpenAI key", "loadError": "Unable to load OpenAI keys list", "noKeys": "No OpenAI keys yet", "noKeysDescription": "Add your first API key to start using OpenAI"}, "anthropic": {"title": "List of Anthropic <PERSON>", "description": "Manage integrated Anthropic Claude API keys", "provider": "Provider", "addKey": "Add Anthropic Key", "editKey": "Edit Anthrop<PERSON> Key", "deleteKey": "Delete Anthropic Key", "keyName": "Display Name", "keyNamePlaceholder": "e.g., My Anthropic Key", "keyNameHelp": "Name to distinguish different API keys", "apiKey": "API Key", "apiKeyPlaceholder": "sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "apiKeyHelp": "Get API key from https://console.anthropic.com/", "status": "Status", "createdAt": "Created Date", "actions": "Actions", "viewUserModels": "View User Models", "viewSystemModels": "View System Models", "confirmDelete": "Are you sure you want to delete this API key?", "deleteSuccess": "Anthropic key deleted successfully", "deleteError": "Error occurred while deleting Anthropic key", "createSuccess": "Anthropic key created successfully", "createError": "Error occurred while creating Anthropic key", "loadError": "Unable to load Anthropic keys list", "noKeys": "No Anthropic keys yet", "noKeysDescription": "Add your first API key to start using Anthropic Claude"}, "gemini": {"title": "List of Google Gemini Keys", "description": "Manage integrated Google Gemini API keys", "provider": "Provider", "addKey": "Add Gemini Key", "editKey": "Edit Gemini Key", "deleteKey": "Delete Gemini Key", "keyName": "Display Name", "keyNamePlaceholder": "e.g., My Gemini Key", "keyNameHelp": "Name to distinguish different API keys", "apiKey": "API Key", "apiKeyPlaceholder": "AIzaxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "apiKeyHelp": "Get API key from Google AI Studio", "viewUserModels": "View User Models", "viewSystemModels": "View System Models", "status": "Status", "createdAt": "Created Date", "actions": "Actions", "confirmDelete": "Are you sure you want to delete this API key?", "deleteSuccess": "Gemini key deleted successfully", "deleteError": "Error occurred while deleting Gemini key", "createSuccess": "Gemini key created successfully", "createError": "Error occurred while creating Gemini key", "loadError": "Unable to load Gemini keys list", "noKeys": "No Gemini keys yet", "noKeysDescription": "Add your first API key to start using Google Gemini"}, "deepseek": {"title": "List of DeepSeek Keys", "description": "Manage integrated DeepSeek API keys", "provider": "Provider", "addKey": "Add DeepSeek Key", "editKey": "Edit DeepSeek Key", "deleteKey": "Delete DeepSeek Key", "keyName": "Display Name", "keyNamePlaceholder": "e.g., My DeepSeek Key", "keyNameHelp": "Name to distinguish different API keys", "apiKey": "API Key", "apiKeyPlaceholder": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "apiKeyHelp": "Get API key from https://platform.deepseek.com/", "viewUserModels": "View User Models", "viewSystemModels": "View System Models", "status": "Status", "createdAt": "Created Date", "actions": "Actions", "confirmDelete": "Are you sure you want to delete this API key?", "deleteSuccess": "DeepSeek key deleted successfully", "deleteError": "Error occurred while deleting DeepSeek key", "createSuccess": "DeepSeek key created successfully", "createError": "Error occurred while creating DeepSeek key", "loadError": "Unable to load DeepSeek keys list", "noKeys": "No DeepSeek keys yet", "noKeysDescription": "Add your first API key to start using DeepSeek"}, "xaiGrok": {"title": "List of XAI Grok Keys", "description": "Manage integrated XAI Grok API keys", "provider": "Provider", "addKey": "Add XAI Grok Key", "editKey": "Edit XAI Grok Key", "deleteKey": "Delete XAI Grok Key", "keyName": "Display Name", "keyNamePlaceholder": "e.g., My XAI Grok Key", "keyNameHelp": "Name to distinguish different API keys", "apiKey": "API Key", "apiKeyPlaceholder": "xai-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "apiKeyHelp": "Get API key from xAI platform", "viewUserModels": "View User Models", "viewSystemModels": "View System Models", "status": "Status", "createdAt": "Created Date", "actions": "Actions", "confirmDelete": "Are you sure you want to delete this API key?", "deleteSuccess": "XAI Grok key deleted successfully", "deleteError": "Error occurred while deleting XAI Grok key", "createSuccess": "XAI Grok key created successfully", "createError": "Error occurred while creating XAI Grok key", "loadError": "Unable to load XAI Grok keys list", "noKeys": "No XAI Grok keys yet", "noKeysDescription": "Add your first API key to start using XAI Grok"}, "shipping": {"title": "Shipping Management", "description": "Integrate with shipping providers GHN, GHTK,...", "addProvider": "Add Shipping Provider", "editProvider": "Edit Shipping Provider", "viewProvider": "View Shipping Provider", "addFirstProvider": "Add First Shipping Provider", "selectProviderType": "Select Shipping Provider", "testConnection": "Test Connection", "test": "Test", "runTest": "Run Test", "rateCalculator": "Rate Calculator", "create": "Create", "default": "<PERSON><PERSON><PERSON>", "ghtk": {"title": "Economy Shipping (GHTK)", "description": "Integrate with economy shipping service - low cost, 3-5 days delivery time", "breadcrumb": "Economy Shipping"}, "ghn": {"title": "Express Shipping (GHN)", "description": "Integrate with express shipping service - 1-2 days delivery, higher cost", "breadcrumb": "Express Shipping"}, "providers": {"ghtk": {"form": {"title": "GHTK Configuration", "displayName": "Display Name", "username": "Username", "password": "Password", "usernameHelp": "Username from GHTK for authentication", "usernamePlaceholder": "Enter Username from GHTK", "usernamePlaceholderEdit": "Enter new Username (leave empty if no change)", "usernamePlaceholderReadonly": "Data has been encrypted", "passwordHelp": "Password from GHTK for authentication", "passwordPlaceholder": "Enter Password from GHTK", "passwordPlaceholderEdit": "Enter new Password (leave empty if no change)", "passwordPlaceholderReadonly": "Data has been encrypted", "displayNameHelp": "Display name for this GHTK configuration", "displayNamePlaceholder": "e.g., Main GHTK Configuration", "token": "Token", "tokenHelp": "API Token from GHTK for authentication", "tokenPlaceholder": "<PERSON><PERSON> from GHTK", "tokenPlaceholderEdit": "Enter new Token (leave empty if no change)", "tokenPlaceholderReadonly": "Data has been encrypted", "timeout": "Timeout (ms)", "timeoutHelp": "Maximum wait time for each request (default: 30000ms)", "timeoutPlaceholder": "30000", "testConnection": "Test Connection", "testConnectionResult": "GHTK Connection Test Result", "testConnectionSuccess": "✅ Connection Successful", "testConnectionFailed": "❌ Connection Failed", "testConnectionError": "❌ Connection Error", "testConnectionSuccessDetails": "• API endpoint is accessible\n• Token authentication is valid\n• Ready to use", "testConnectionErrorDetails": "• Check if Token is correct\n• Ensure stable internet connection\n• Contact GHTK if issue persists", "testConnectionErrorDefault": "Unable to connect to GHTK", "testConnectionRequired": "Token is required for connection test", "testMode": "Test Mode", "createButton": "Create GHTK Configuration"}}, "ghn": {"form": {"title": "GHN Configuration", "displayName": "Display Name", "displayNameHelp": "Display name for this GHN configuration", "displayNamePlaceholder": "e.g., Main GHN Configuration", "token": "Token", "tokenHelp": "API Token from GHN for authentication", "tokenPlaceholder": "<PERSON><PERSON> from GHN", "tokenPlaceholderEdit": "Enter new Token (leave empty if no change)", "tokenPlaceholderReadonly": "Data has been encrypted", "shopId": "Shop ID", "shopIdHelp": "Shop ID from GHN to identify the store", "shopIdPlaceholder": "Enter Shop ID from GHN", "shopIdPlaceholderEdit": "Enter new Shop ID (leave empty if no change)", "shopIdPlaceholderReadonly": "Data has been encrypted", "timeout": "Timeout (ms)", "timeoutHelp": "Maximum wait time for each request (default: 30000ms)", "timeoutPlaceholder": "30000", "testConnection": "Test Connection", "testConnectionResult": "GHN Connection Test Result", "testConnectionSuccess": "✅ Connection Successful", "testConnectionFailed": "❌ Connection Failed", "testConnectionError": "❌ Connection Error", "testConnectionSuccessDetails": "• API endpoint is accessible\n• Token and Shop ID authentication is valid\n• Ready to use", "testConnectionErrorDetails": "• Check if Token and Shop ID are correct\n• Ensure stable internet connection\n• Contact GHN if issue persists", "testConnectionErrorDefault": "Unable to connect to GHN", "testConnectionTokenRequired": "Token is required for connection test", "testConnectionShopIdRequired": "Shop ID is required for connection test", "testMode": "Test Mode", "createButton": "Create GHN Configuration"}}, "ahamove": {"form": {"title": "Ahamove Configuration", "displayName": "Display Name", "displayNameHelp": "Display name for this Ahamove configuration", "displayNamePlaceholder": "e.g., Main Ahamove Configuration", "mobile": "Mobile Number", "mobileHelp": "Mobile number from Ahamove for authentication", "mobilePlaceholder": "Enter Mobile Number from Ahamove", "mobilePlaceholderEdit": "Enter new Mobile Number (leave empty if no change)", "mobilePlaceholderReadonly": "Data has been encrypted", "testMode": "Test Mode", "createButton": "Create Ahamove Configuration"}}}, "form": {"createTitle": "Add Shipping Provider", "editTitle": "Edit Shipping Provider"}, "list": {"columns": {"providerName": "Provider Name", "providerType": "Type", "shopId": "Shop ID", "default": "<PERSON><PERSON><PERSON>", "status": "Status", "actions": "Actions"}}, "filters": {"providerType": "Provider Type", "status": "Status"}, "actions": {"edit": "Edit", "test": "Test", "setDefault": "Set as <PERSON><PERSON><PERSON>", "delete": "Delete"}, "confirmations": {"deleteTitle": "Delete Shipping Provider", "delete": "Are you sure you want to delete this shipping provider?"}, "empty": {"title": "No shipping providers", "description": "You haven't added any shipping providers. Add your first shipping provider."}, "validation": {"providerType": {"invalid": "Invalid shipping provider type"}, "providerName": {"required": "Provider name is required", "maxLength": "Provider name cannot exceed 100 characters"}, "apiKey": {"required": "API Key is required", "maxLength": "API Key cannot exceed 255 characters"}, "apiSecret": {"required": "API Secret is required", "maxLength": "API Secret cannot exceed 255 characters", "minLength": "API Secret must be at least 6 characters"}, "shopId": {"required": "Shop ID is required", "maxLength": "Shop ID cannot exceed 999999999"}, "clientId": {"required": "Client ID is required", "maxLength": "Client ID cannot exceed 255 characters"}, "settings": {"invalidJson": "Settings must be valid JSON format"}, "name": {"required": "Display name is required", "maxLength": "Display name cannot exceed 100 characters"}, "token": {"required": "Token is required", "maxLength": "Token cannot exceed 255 characters"}, "username": {"required": "Username is required", "maxLength": "Username cannot exceed 255 characters"}, "password": {"required": "Password is required", "minLength": "Password must be at least 6 characters", "maxLength": "Password cannot exceed 255 characters"}, "mobile": {"required": "Phone number is required", "minLength": "Phone number must be at least 10 characters", "maxLength": "Phone number cannot exceed 15 characters", "format": "Invalid phone number format"}, "timeout": {"min": "Timeout must be at least 1000ms", "max": "Timeout cannot exceed 300000ms"}, "phone": {"required": "Phone number is required", "maxLength": "Phone number cannot exceed 20 characters"}, "address": {"required": "Address is required"}, "weight": {"min": "Minimum weight is 0.1kg"}, "serviceType": {"required": "Service type is required"}}}, "common": {"refresh": "Refresh"}, "sms": {"title": "SMS Integration", "description": "Configure integration with SMS providers", "configName": "Configuration Name", "configNameHelp": "Example: SMS Marketing, SMS Notifications", "configNamePlaceholder": "Enter configuration name", "isActive": "Active", "provider": "SMS Provider", "selectProvider": "Select provider", "otherProvider": "Other", "fromPhone": "From Phone Number", "fromPhoneHelp": "Phone number displayed when sending SMS", "apiKey": "API Key", "apiSecret": "API Secret", "testConnection": "Test Connection", "testResult": "Connection Test Result", "testSuccess": "Successfully connected to SMS provider", "testError": "Could not connect to SMS provider. Please check your configuration.", "saveSuccess": "SMS configuration saved successfully", "saveError": "Error saving SMS configuration", "fpt": {"title": "FPT SMS Brandname", "description": "Integrate with FPT Telecom SMS Brandname service", "list": {"title": "FPT SMS Brandname Management", "description": "Manage FPT SMS Brandname configurations", "columns": {"integrationName": "Integration Name", "brandName": "Brandname", "clientId": "Client ID", "endpoint": "Endpoint", "createdAt": "Created At", "status": "Status", "actions": "Actions"}}, "actions": {"edit": "Edit", "delete": "Delete", "testConnection": "Test Connection", "moreActions": "More Actions"}, "confirmations": {"deleteTitle": "Confirm Delete", "delete": "Are you sure you want to delete this configuration?"}, "form": {"createTitle": "Create FPT SMS Brandname Configuration", "editTitle": "Edit FPT SMS Brandname Configuration", "createDescription": "Fill in the information to create a new FPT SMS Brandname integration", "editDescription": "Update FPT SMS Brandname configuration information", "integrationName": "Integration Name", "integrationNameHelp": "Name to identify this integration", "integrationNamePlaceholder": "e.g., FPT SMS Brandname - Company ABC", "clientId": "Client ID", "clientIdHelp": "Client ID from FPT SMS for authentication", "clientIdPlaceholder": "Enter Client ID from FPT SMS", "clientSecret": "Client Secret", "clientSecretHelp": "Client Secret from FPT SMS for authentication", "clientSecretPlaceholder": "Enter Client Secret from FPT SMS", "brandName": "Brandname", "brandNameHelp": "Brand name displayed when sending SMS", "brandNamePlaceholder": "Enter brandname", "testConnection": "Test Connection", "testConnectionDescription": "Test connection with FPT SMS API", "testNow": "Test Now", "testing": "Testing...", "create": "Create Configuration", "cancel": "Cancel", "saving": "Saving...", "required": "Required"}, "test": {"saveFirst": "Please save the configuration before testing connection", "failed": "Connection test failed"}, "success": {"created": "FPT SMS configuration successful!", "createdDescription": "Integration \"{{name}}\" has been created successfully.", "updated": "Configuration updated successfully!", "deleted": "Configuration deleted successfully!"}, "error": {"createFailed": "FPT SMS configuration failed", "createFailedDescription": "An error occurred during configuration. Please try again.", "updateFailed": "Configuration update failed", "deleteFailed": "Configuration deletion failed", "invalidConfig": "Configuration information is invalid"}}}, "email": {"providers": {"outlook": "Microsoft Outlook", "yahoo": "Yahoo Mail", "sendgrid": "SendGrid", "mailchimp": "Mailchimp Transactional", "amazonSes": "Amazon SES", "mailgun": "Mailgun", "gmail": "Gmail"}}, "social": {"title": "Social Media Integration", "description": "Manage integration with social media platforms", "facebook": "Facebook", "instagram": "Instagram", "twitter": "Twitter", "youtube": "YouTube", "tiktok": "TikTok", "connect": "Connect", "disconnect": "Disconnect", "connected": "Connected", "notConnected": "Not Connected", "connectSuccess": "Successfully connected to {{platform}}", "connectError": "Error connecting to {{platform}}", "disconnectSuccess": "Successfully disconnected from {{platform}}", "disconnectError": "Error disconnecting from {{platform}}", "networkAriaLabel": "{{name}} social network"}, "zalo": {"personal": {"title": "Zalo Personal Integration", "description": "Connect your personal Zalo account to use marketing features", "qrTitle": "Scan QR Code to Connect", "qrInstruction": "Use Zalo app to scan QR code", "qrDescription": "Open Zalo app > Scan QR > Scan the code below", "qrNote": "QR code will expire in 5 minutes", "qrGenerated": "QR Code generated", "qrError": "Unable to generate QR Code", "connected": "Successfully connected to <PERSON><PERSON>", "statusTitle": "Connection Status", "status": {"disconnected": "Not connected", "connecting": "Waiting for connection...", "connected": "Successfully connected"}, "instructionsTitle": "Connection Instructions", "step1": "Open Zalo app on your phone", "step2": "Select QR code scanner icon", "step3": "Scan the QR code displayed on the left", "step4": "Confirm connection in Zalo app", "featuresTitle": "Features after connection", "feature1": "Send automatic messages", "feature2": "Manage friend list", "feature3": "Create marketing campaigns", "feature4": "Performance analytics"}}, "facebook": {"title": "Facebook", "description": "Manage linked Facebook accounts", "addPage": "Add Facebook Page", "connecting": "Connecting...", "processing": "Processing Facebook connection...", "search": "Search Facebook Page...", "loadError": "Unable to load Facebook Pages list", "noPages": "No Facebook Pages yet", "noPagesDescription": "You haven't linked any Facebook Pages yet. Add a Facebook Page to get started.", "confirmDelete": "Are you sure you want to delete this Facebook Page?", "connectAgent": "Connect Agent", "disconnectAgent": "Disconnect Agent", "status": {"label": "Status", "active": "Active", "inactive": "Inactive", "error": "Error"}, "agent": {"label": "Agent"}, "pageName": "Page Name", "personalName": "Personal Name", "pageId": "Page ID", "isActive": "Is Active", "hasError": "<PERSON>"}, "accounts": {"title": "Linked Accounts", "description": "Manage accounts linked to the system", "addAccount": "Add Account", "removeAccount": "Remove Account", "accountName": "Account Name", "accountType": "Account Type", "linkedDate": "Linked Date", "noAccounts": "No accounts linked yet", "confirmRemove": "Are you sure you want to remove this account?", "removeSuccess": "Account removed successfully", "removeError": "Error removing account", "defaultAccount": "De<PERSON><PERSON> Account", "failedToLoad": "Failed to load accounts"}, "website": {"title": "Website Integration", "description": "Manage integration with websites", "domain": "Domain", "apiKey": "API Key", "secretKey": "Secret Key", "webhookUrl": "Webhook URL", "generateKey": "Generate New Key", "copyKey": "Copy", "keyCopied": "Copied to clipboard", "saveSuccess": "Website configuration saved successfully", "saveError": "Error saving website configuration", "confirmActivate": "Are you sure you want to activate this website?", "confirmDeactivate": "Are you sure you want to deactivate this website?", "host": "Host", "status": "Status", "verified": "Verified", "notVerified": "Not Verified", "connected": "Connected", "notConnected": "Not Connected", "agent": "Agent", "noAgent": "No Agent Connected", "createdAt": "Created At", "actions": "Actions", "widgetScript": "Widge<PERSON>", "widgetScriptDesc": "Copy and paste this script into your website to integrate the chat widget.", "createTitle": "Add New Website", "createSuccess": "Website created successfully!", "createSuccessDesc": "Website has been added to the list.", "createError": "Failed to create website!", "createErrorDesc": "Please try again later.", "creating": "Creating...", "create": "Create Website", "deleteSuccess": "Website deleted successfully!", "deleteSuccessDesc": "Website has been removed from the list.", "deleteError": "Failed to delete website!", "deleteErrorDesc": "Please try again later.", "copySuccess": "Copied!", "copySuccessDesc": "Script has been copied to clipboard.", "copyScript": "Copy", "noWebsites": "No Websites Yet", "noWebsitesDescription": "You haven't added any websites yet. Add a website to get started.", "addWebsite": "Add Website", "noSearchResults": "No Results Found", "noSearchResultsDescription": "No websites match your search criteria.", "clearFilters": "Clear Filters", "confirmDelete": "Confirm Delete", "confirmDeleteDesc": "Are you sure you want to delete this website? This action cannot be undone.", "deleting": "Deleting...", "form": {"websiteName": "Website Name", "websiteNamePlaceholder": "Enter website name", "host": "Host/Domain", "hostPlaceholder": "redai.vn or https://www.redai.vn", "hostDescription": "Enter domain or full URL. System will automatically normalize.", "logo": "Website Logo", "clickToUploadLogo": "Click on the frame to upload logo", "clickToChangeLogo": "Click on the image to change logo", "logoDescription": "Accepts image files (JPEG, PNG, WebP), max 5MB"}, "filter": {"websiteName": "Website Name", "host": "Host", "createdAt": "Created At", "verify": "Verification Status", "asc": "Ascending", "desc": "Descending", "all": "All", "verified": "Verified", "unverified": "Unverified"}}, "bankAccount": {"kienlong": {"title": "List of Kien Long Bank Accounts", "description": "List of Kien Long Bank accounts integrated with the system"}, "mb": {"title": "List of MB Bank Accounts", "description": "List of MB Bank accounts integrated with the system"}, "acb": {"title": "List of ACB Bank Accounts", "description": "List of ACB Bank accounts integrated with the system"}, "ocb": {"title": "List of OCB Bank Accounts", "description": "List of OCB Bank accounts integrated with the system"}, "createVirtualAccount": "Create Virtual Account", "addAccount": "Add Account", "disableVA": "Disable Virtual", "enableVA": "Enable Virtual", "realAccount": "Real Account", "enterOtpToDelete": "Enter OTP code to delete account", "otpSentMessage": "OTP code has been sent to the phone number", "accountType": "Account Type", "reconnectOtpSentMessage": "OTP code has been sent to the phone number", "enterOtpToReconnect": "Enter OTP code to reconnect account", "reconnectAccount": "Reconnect Account", "confirmReconnect": "Confirm Reconnect", "createVirtualAccountFromReal": "Create Virtual Account ", "forceDeleteWarning": "Are you sure you want to delete this bank account? This account is not connected to the bank API so it will be deleted immediately.", "confirmDelete": "Confirm Force Delete", "balance": "Balance", "title": "Bank Account Integration", "description": "Manage bank accounts integrated with the system", "createTitle": "Add Bank Account", "createDescription": "Enter bank account information to integrate with the system", "bankName": "Bank", "reconnect": "Reconnect", "selectBank": "Select bank", "accountNumber": "Account Number", "accountNumberPlaceholder": "Enter account number", "accountNumberHelp": "Enter bank account number (6-20 digits)", "accountName": "Account Name", "connectionStatus": "Connection Status", "connected": "Connected", "notConnected": "Not Connected", "activated": "Activated", "notActivated": "Not Activated", "label": "Label", "bankApiConnected": "Bank API Connected", "accountHolderName": "Account Holder Name", "idNumber": "ID Number", "idNumberPlaceholder": "Enter ID number", "idNumberHelp": "Enter ID number registered with the bank", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter phone number", "phoneNumberHelp": "Phone number registered with the bank to receive OTP", "storeName": "Store Name", "storeNamePlaceholder": "Enter store name", "storeNameHelp": "Your store/business name", "storeAddress": "Store Address", "storeAddressPlaceholder": "Enter store address", "storeAddressHelp": "Detailed address of your store/business", "create": "Create Account", "bankInfo": "Bank Information", "status": "Status", "statusActive": "Active", "statusPending": "Pending Verification", "statusInactive": "Inactive", "statusSuspended": "Suspended", "statusExpired": "Expired", "virtualAccount": "Virtual Account", "createVA": "Create Virtual Account", "createSuccess": "Account created successfully", "createSuccessDescription": "Bank account has been created successfully", "createError": "Failed to create account", "createErrorDescription": "Unable to create bank account. Please check the information.", "createCompleteSuccess": "Account creation completed", "createCompleteSuccessDescription": "Bank account has been created and activated successfully", "deleteSuccess": "Deleted successfully", "deleteSuccessDescription": "Bank account has been deleted successfully", "deleteError": "Failed to delete", "deleteErrorDescription": "Unable to delete bank account. Please try again.", "deleteConfirmTitle": "Confirm account deletion", "deleteConfirmMessage": "Are you sure you want to delete this bank account? This action cannot be undone.", "bulkDeleteSuccess": "Deleted successfully", "bulkDeleteSuccessDescription": "Deleted {{count}} bank accounts", "bulkDeleteError": "Failed to delete", "bulkDeleteErrorDescription": "Unable to delete bank accounts. Please try again.", "bulkDeleteConfirmTitle": "Confirm multiple account deletion", "bulkDeleteConfirmMessage": "Are you sure you want to delete {{count}} selected bank accounts? This action cannot be undone.", "selectAccountsToDelete": "Please select at least one account to delete", "createVASuccess": "Virtual account created successfully", "createVASuccessDescription": "Virtual account has been created successfully", "createVAError": "Failed to create virtual account", "createVAErrorDescription": "Unable to create virtual account. Please try again.", "activateSuccess": "Activated successfully", "activateSuccessDescription": "Bank account has been activated successfully", "otpVerification": "OTP Verification", "otpVerificationDescription": "Enter the OTP code sent to the phone number registered with {{bankName}}", "enterOtp": "Enter OTP code", "otpSent": "OTP code sent", "otpSentDescription": "Please check SMS messages on your phone", "otpSendError": "Failed to send OTP", "otpSendErrorDescription": "Unable to send OTP code. Please try again later.", "resendOtp": "Resend OTP", "resendOtpCountdown": "Resend code in {{countdown}} seconds", "verify": "Verify", "otpVerifySuccess": "Verification successful", "otpVerifySuccessDescription": "Bank account has been activated successfully", "otpVerifyError": "Verification failed", "otpVerifyErrorDescription": "OTP code is incorrect or expired. Please try again.", "invalidOtpLength": "Incorrect OTP length", "invalidOtpLengthDescription": "OTP code must be {{length}} characters", "acbOtpDescription": "Enter the OTP code sent to the phone number registered with ACB to complete the connection", "ocbOtpVerification": "OCB OTP Verification", "ocbOtpDescription": "Enter the OTP code sent to the phone number registered with OCB to complete the connection", "ocbOtpLengthError": "OTP code must be 6 characters", "klbOtpVerification": "Kien Long Bank OTP Verification", "klbOtpDescription": "Enter the OTP code sent to the phone number registered with Kien Long Bank to complete the connection", "mbOtpDescription": "Enter the OTP code sent to the phone number registered with MB Bank to complete the connection", "klbOtpLengthError": "OTP code must be 6 characters", "resendOtpIn": "Resend code in {{seconds}} seconds", "otpExpired": "OTP code has expired", "resendOtpSuccess": "OTP resent successfully", "resendOtpSuccessDescription": "A new OTP code has been sent to the registered phone number", "resendOtpError": "Failed to resend OTP", "activatingConnection": "Activating connection with bank...", "connectionCompleted": "Connection has been established successfully!", "pleaseWait": "Please wait...", "setupComplete": "Setup complete!", "otpProcessError": "An error occurred during verification process", "connectionSuccess": "Connection successful", "mbConnectionSuccessDescription": "MB Bank account has been linked and activated successfully", "acbConnectionSuccessDescription": "ACB account has been linked and activated successfully", "ocbConnectionSuccessDescription": "OCB account has been linked and activated successfully", "klbConnectionSuccessDescription": "Kien Long Bank account has been linked and activated successfully", "otpError": "OTP Error", "otpLengthError": "OTP code must be 8 characters"}, "banking": {"bankAccount": "Bank Account", "bank": "Bank", "connectionMethod": "Connection Method", "estimatedBalance": "Estimated Balance", "accountName": "Account Name", "accountNumber": "Account Number", "connectApi": "Connect Banking API", "acb": {"title": "Link ACB Account", "description": "Enter ACB bank account information to link with the system", "accountHolderName": "Account Holder Name", "accountHolderNamePlaceholder": "Enter ACB account holder name", "accountNumber": "Account Number", "accountNumberPlaceholder": "Enter ACB account number", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter registered phone number with ACB", "label": "Label", "labelPlaceholder": "Enter label (optional)", "saveSuccess": "ACB account information saved successfully", "saveError": "Error occurred while saving ACB account information", "submitError": "Error occurred while saving account information", "otpVerificationTitle": "ACB OTP Verification"}, "mb": {"title": "Link MB Bank Account", "description": "Enter MB Bank account information to link with the system", "accountHolderName": "Account Holder Name", "accountHolderNamePlaceholder": "Name will be automatically fetched from API", "accountNumber": "Account Number", "accountNumberPlaceholder": "Enter MB Bank account number", "identificationNumber": "ID Number", "identificationNumberPlaceholder": "Enter ID number registered with MB Bank", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter phone number registered with MB Bank", "label": "Label", "labelPlaceholder": "Enter label (optional)", "fetchNameSuccess": "Account holder name fetched successfully", "fetchNameError": "Unable to fetch account holder name", "saveSuccess": "MB Bank account information saved successfully", "saveError": "Error occurred while saving MB Bank account information", "submitError": "Error occurred while saving account information"}, "ocb": {"title": "Link OCB Account", "description": "Enter OCB bank account information to link with the system", "accountHolderName": "Account Holder Name", "accountHolderNamePlaceholder": "Name will be automatically fetched from API", "accountNumber": "Account Number", "accountNumberPlaceholder": "Enter OCB account number", "identificationNumber": "ID Number", "identificationNumberPlaceholder": "Enter ID number registered with OCB", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter phone number registered with OCB", "label": "Label", "labelPlaceholder": "Enter label (optional)", "fetchNameSuccess": "Account holder name fetched successfully", "fetchNameError": "Unable to fetch account holder name", "saveSuccess": "OCB account information saved successfully", "saveError": "Error occurred while saving OCB account information", "submitError": "Error occurred while saving account information"}, "kienlong": {"title": "<PERSON> Bank Account", "description": "Enter Kien Long Bank account information to link with the system", "accountHolderName": "Account Holder Name", "accountHolderNamePlaceholder": "Enter Kien Long Bank account holder name", "accountNumber": "Account Number", "accountNumberPlaceholder": "Enter Kien Long Bank account number", "identificationNumber": "ID Number", "identificationNumberPlaceholder": "Enter ID number registered with account", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter phone number registered with Kien Long Bank", "label": "Label", "labelPlaceholder": "Enter label (optional)", "saveSuccess": "Kien Long Bank account information saved successfully", "saveError": "Error occurred while saving Kien Long Bank account information", "submitError": "Error occurred while saving account information"}, "validation": {"bankId": {"required": "Bank ID is required"}, "accountNumber": {"required": "Account number is required", "minLength": "Account number must be at least 6 characters", "maxLength": "Account number must not exceed 20 characters", "numbersOnly": "Account number must contain only numbers"}, "accountHolderName": {"required": "Account holder name is required", "minLength": "Account holder name must be at least 2 characters", "maxLength": "Account holder name must not exceed 100 characters", "lettersOnly": "Account holder name must contain only letters and spaces", "invalidChars": "Account holder name contains invalid characters"}, "phoneNumber": {"required": "Phone number is required", "minLength": "Phone number must be at least 10 characters", "maxLength": "Phone number must not exceed 20 characters", "numbersOnly": "Phone number must contain only numbers"}, "identificationNumber": {"required": "Identification number is required", "minLength": "Identification number must be at least 9 characters", "maxLength": "Identification number must not exceed 100 characters", "numbersOnly": "Identification number must contain only numbers"}, "label": {"lengthRange": "Label must be between 2 and 100 characters", "required": "Label is required"}}}, "emailSMTP": {"title": "SMTP Configuration", "titleManagement": "Email SMTP Management", "description": "Configure SMTP server for sending emails", "descriptionManagement": "Manage SMTP configurations", "configName": "Configuration Name", "configNameHelp": "Example: Company Email, Personal Gmail", "configNamePlaceholder": "Enter configuration name", "senderEmail": "Sender <PERSON><PERSON>", "senderName": "Sender Name", "senderNameHelp": "Name displayed when recipient views email", "sendTest": "Send Test"}, "apiKeys": {"title": "API Keys Management", "description": "Description", "id": "ID", "apiKey": "API Key", "scope": "Access Scope", "environment": "Environment", "expiredAt": "Expiration Date", "status": "Status", "actions": "Actions", "active": "Active", "inactive": "Inactive", "addNew": "Create New API Key", "createNew": "Create New API Key", "list": "API Keys List", "descriptionPlaceholder": "Enter description for API Key", "selectDate": "Select expiration date", "noData": "No data available", "searchPlaceholder": "Search by description...", "filterByStatus": "Filter by status", "allStatuses": "All statuses", "enable": "Enable", "disable": "Disable", "confirmDeleteMessage": "Are you sure you want to delete this API Key?", "createSuccess": "Created new API Key with description: {{description}}", "deleteSuccess": "Deleted API Key: {{api<PERSON>ey}}", "toggleSuccess": "{{action}} API Key: {{api<PERSON>ey}}"}, "cloudStorage": {"title": "Cloud Storage Integration", "description": "Manage integrations with cloud storage services", "providers": {"google-drive": "Google Drive", "onedrive": "Microsoft OneDrive", "dropbox": "Dropbox", "box": "Box"}, "form": {"createTitle": "Add Cloud Storage", "editTitle": "Edit Cloud Storage", "providerType": {"label": "Provider", "placeholder": "Select provider", "helpText": "Choose the cloud storage service to integrate"}, "providerName": {"label": "Configuration Name", "placeholder": "Enter configuration name", "helpText": "Name to identify this configuration"}, "clientId": {"label": "Client ID", "placeholder": "Enter Client ID", "helpText": "Client ID from provider console"}, "clientSecret": {"label": "Client Secret", "placeholder": "Enter Client Secret", "helpText": "Client Secret from provider console"}, "refreshToken": {"label": "Refresh <PERSON>", "placeholder": "Enter Refresh <PERSON>", "helpText": "Token to maintain connection"}, "rootFolderId": {"label": "Root Folder ID", "placeholder": "Enter Root Folder ID (optional)", "helpText": "Root folder ID, leave empty to use main folder"}, "isActive": {"label": "Active", "helpText": "Enable/disable this integration"}, "autoSync": {"label": "Auto Sync", "helpText": "Automatically sync files"}, "syncFolders": {"label": "Sync Folders", "placeholder": "Enter folder list (JSON)", "helpText": "List of folders to sync (JSON array format)"}, "testConnection": "Test Connection", "connectWithOAuth": "Connect with {{provider}}", "authSuccess": "Authentication successful", "authFailed": "Authentication failed"}, "list": {"title": "Cloud Storage List", "description": "Manage cloud storage integrations", "createNew": "Add Cloud Storage", "searchPlaceholder": "Search by name...", "resultsCount": "Showing {{count}} of {{total}} results"}, "filters": {"all": "All", "active": "Active", "inactive": "Inactive", "byProvider": "By Provider"}, "status": {"active": "Active", "inactive": "Inactive", "syncEnabled": "Sync Enabled", "syncDisabled": "Sync Disabled", "syncing": "Syncing", "error": "Error"}, "actions": {"test": "Test", "sync": "Sync", "browse": "Browse Files", "upload": "Upload", "download": "Download", "share": "Share", "delete": "Delete", "createFolder": "Create Folder"}, "details": {"provider": "Provider", "clientId": "Client ID", "lastSync": "Last Sync", "neverSynced": "Never synced", "created": "Created", "updated": "Updated", "storageQuota": "Storage Quota", "usedSpace": "Used", "availableSpace": "Available", "fileName": "File Name", "fileSize": "Size", "lastModified": "Last Modified"}, "modal": {"editTitle": "Edit Cloud Storage", "deleteTitle": "Delete Cloud Storage", "deleteConfirm": "Confirm Delete", "deleteDescription": "Are you sure you want to delete configuration {{name}}? This action cannot be undone."}, "empty": {"noConfigurations": "No configurations", "noConfigurationsDescription": "You haven't added any cloud storage configurations", "noResults": "No results found", "noResultsDescription": "No configurations match the filters", "createFirst": "Add first configuration", "clearFilters": "Clear filters", "noFiles": "No files", "noFilesDescription": "This folder is empty"}, "error": {"loadFailed": "Failed to load list", "createFailed": "Failed to create configuration", "updateFailed": "Failed to update configuration", "deleteFailed": "Failed to delete configuration", "testFailed": "Connection test failed", "syncFailed": "Sync failed", "authFailed": "Authentication failed", "uploadFailed": "Upload failed", "downloadFailed": "Download failed"}, "success": {"created": "Configuration created successfully", "updated": "Configuration updated successfully", "deleted": "Configuration deleted successfully", "testPassed": "Connection test successful", "syncCompleted": "Sync completed", "authCompleted": "Authentication successful", "uploaded": "Upload successful", "downloaded": "Download successful"}, "validation": {"providerType": {"invalid": "Invalid provider"}, "providerName": {"required": "Configuration name is required", "maxLength": "Configuration name cannot exceed 100 characters"}, "clientId": {"required": "Client ID is required", "maxLength": "Client ID cannot exceed 255 characters"}, "clientSecret": {"required": "Client Secret is required", "maxLength": "Client Secret cannot exceed 255 characters"}, "refreshToken": {"required": "Refresh Token is required"}, "syncFolders": {"invalidJson": "Folder list must be a valid JSON array"}, "testFolderName": {"maxLength": "Test folder name cannot exceed 100 characters"}, "testFileName": {"maxLength": "Test file name cannot exceed 100 characters"}, "fileName": {"required": "File name is required"}, "folderName": {"required": "Folder name is required"}, "query": {"required": "Search query is required"}, "operation": {"invalid": "Invalid operation"}, "fileIds": {"required": "File list is required"}, "fileId": {"required": "File ID is required"}, "permission": {"invalid": "Invalid permission"}}}, "sepayHub": {"title": "Sepay-Hub Organization Configuration", "description": "Manage Sepay-Hub bank accounts and transactions", "overview": {"totalBankAccounts": "Total Bank Accounts", "totalVAAccounts": "Total VA Accounts", "totalTransactions": "Total Transactions", "availableTransactions": "Available Transactions"}, "table": {"id": "ID", "companyId": "Company ID", "bankId": "Bank ID", "accountHolderName": "Account Holder Name", "accountNumber": "Account Number", "accumulated": "Balance", "label": "Label", "bankApiConnected": "API Status", "lastTransaction": "Last Transaction", "createdAt": "Created At", "updatedAt": "Updated At", "connected": "Connected", "notConnected": "Not Connected"}, "actions": {"buyMoreTransactions": "Buy More Transactions"}, "companyConfig": {"title": "Company Configuration", "companyInfo": "Company Information", "paymentConfig": "Payment Configuration", "fullName": "Full Name", "fullNamePlaceholder": "Enter full name", "status": "Status", "organizationName": "Organization Name", "organizationNamePlaceholder": "Enter organization name", "shortName": "Short Name", "shortNamePlaceholder": "Enter short name", "paymentCode": {"label": "Payment Code Recognition Configuration", "on": "On", "off": "Off"}, "paymentCodePrefix": "Payment Code Prefix Configuration", "paymentCodePrefixPlaceholder": "Enter payment code prefix", "paymentCodeSuffixFrom": "Minimum Payment Code Suffix Length Configuration", "paymentCodeSuffixTo": "Maximum Payment Code Suffix Length Configuration", "paymentCodeSuffixCharacterType": "Payment Code Suffix Character Type Configuration", "characterType": {"numberAndLetter": "Allow letters and numbers", "numberOnly": "Numbers only"}, "transactionAmount": "Transaction Amount Configuration", "transactionAmountPlaceholder": "Enter transaction amount or 'Unlimited'", "availableTransactions": "Available Transactions", "validation": {"organizationNameRequired": "Organization name is required", "shortNameRequired": "Short name is required", "prefixRequired": "Payment code prefix is required", "suffixFromMin": "Minimum length must be greater than 0", "suffixToGreater": "Maximum length must be greater than or equal to minimum length", "transactionAmountMin": "Transaction amount must be greater than or equal to 0"}}}, "database": {"title": "Database Integration"}}}