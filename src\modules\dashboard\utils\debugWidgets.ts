import { widgetRegistry } from '../registry';
import { WIDGET_CONFIGS } from '../registry/widgetConfigs';

/**
 * Debug utilities cho widget system
 */
export const debugWidgets = {
  /**
   * <PERSON><PERSON>m tra trạng thái widget registry
   */
  checkRegistry: () => {
    console.log('🔍 Widget Registry Debug Info:');
    console.log('📊 Total configs:', WIDGET_CONFIGS.length);
    console.log('📋 Available widget types:', WIDGET_CONFIGS.map(c => c.type));
    
    const registeredWidgets = widgetRegistry.getAllWidgets();
    console.log('✅ Registered widgets:', Object.keys(registeredWidgets));
    
    // Kiểm tra customers-chart cụ thể
    const customersChartConfig = WIDGET_CONFIGS.find(c => c.type === 'customers-chart');
    console.log('🎯 Customers Chart Config:', customersChartConfig);

    const isCustomersChartRegistered = widgetRegistry.hasWidget('customers-chart');
    console.log('📝 Customers Chart Registered:', isCustomersChartRegistered);

    if (isCustomersChartRegistered) {
      const customersChartComponent = widgetRegistry.getComponent('customers-chart');
      console.log('🧩 Customers Chart Component:', customersChartComponent);
    }
    
    return {
      totalConfigs: WIDGET_CONFIGS.length,
      registeredCount: Object.keys(registeredWidgets).length,
      customersChartRegistered: isCustomersChartRegistered,
      customersChartConfig,
    };
  },

  /**
   * Kiểm tra widget cụ thể
   */
  checkWidget: (type: string) => {
    console.log(`🔍 Checking widget: ${type}`);
    
    const config = WIDGET_CONFIGS.find(c => c.type === type);
    console.log('📋 Config:', config);
    
    const isRegistered = widgetRegistry.hasWidget(type as any);
    console.log('✅ Registered:', isRegistered);
    
    if (isRegistered) {
      const component = widgetRegistry.getComponent(type as any);
      console.log('🧩 Component:', component);
    }
    
    return { config, isRegistered };
  },

  /**
   * Force register customers chart widget
   */
  forceRegisterCustomersChart: async () => {
    try {
      const customersChartConfig = WIDGET_CONFIGS.find(c => c.type === 'customers-chart');
      if (customersChartConfig) {
        console.log('🔄 Force registering customers-chart widget...');
        widgetRegistry.register(customersChartConfig, { override: true });
        console.log('✅ Customers chart widget registered successfully');
        return true;
      } else {
        console.error('❌ Customers chart config not found');
        return false;
      }
    } catch (error) {
      console.error('❌ Failed to register customers chart:', error);
      return false;
    }
  },

  /**
   * List all business widgets
   */
  listBusinessWidgets: () => {
    const businessWidgets = WIDGET_CONFIGS.filter(c => c.category === 'business');
    console.log('🏢 Business Widgets:');
    businessWidgets.forEach(widget => {
      const isRegistered = widgetRegistry.hasWidget(widget.type);
      console.log(`  ${isRegistered ? '✅' : '❌'} ${widget.type}: ${widget.title}`);
    });
    return businessWidgets;
  }
};

// Expose to window for debugging
if (typeof window !== 'undefined') {
  (window as any).debugWidgets = debugWidgets;
}

export default debugWidgets;
