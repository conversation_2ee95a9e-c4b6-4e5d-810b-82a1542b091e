import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Typography,
  Card,
  ScrollArea,
  FormItem,
  Select,
} from '@/shared/components/common';
import { usePDFExport } from '../hooks/usePDFExport';

import {
  Download,
  Image,
} from 'lucide-react';

interface PDFExportModalProps {
  isOpen: boolean;
  onClose: () => void;
}



// Preview Section Component
const PreviewSection: React.FC<{ config: any }> = ({ config }) => {
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);
  const [lastConfigHash, setLastConfigHash] = useState<string>('');

  // Generate config hash to detect changes
  const getConfigHash = (config: any) => {
    return JSON.stringify({
      filename: config.filename,
      format: config.format,
      colorMode: config.colorMode,
    });
  };

  const generatePreview = async () => {
    setIsGeneratingPreview(true);
    try {
      // Find dashboard container
      const dashboardElement = document.querySelector('[data-dashboard-container]') as HTMLElement;

      if (!dashboardElement) {
        throw new Error('Dashboard container not found');
      }

      // Import html2canvas dynamically
      const html2canvas = (await import('html2canvas')).default;

      // Generate preview with current config
      const canvas = await html2canvas(dashboardElement, {
        scale: 1, // Lower scale for preview
        useCORS: true,
        allowTaint: true,
        backgroundColor: config.includeBackground ? '#ffffff' : null,
        logging: false,
        width: dashboardElement.scrollWidth,
        height: dashboardElement.scrollHeight,
        scrollX: 0,
        scrollY: 0,
        windowWidth: dashboardElement.scrollWidth,
        windowHeight: dashboardElement.scrollHeight,
        ignoreElements: (element) => {
          // Apply same filtering as export
          if (element.classList.contains('no-pdf') ||
              element.tagName === 'SCRIPT' ||
              element.tagName === 'STYLE') {
            return true;
          }

          // Không filter theo exportType nữa - hiển thị tất cả nội dung trong preview
          return false;
        },
      });

      // Convert to data URL
      const dataUrl = canvas.toDataURL('image/jpeg', 0.8);
      setPreviewImage(dataUrl);
    } catch (error) {
      console.error('Failed to generate preview:', error);
    } finally {
      setIsGeneratingPreview(false);
    }
  };

  // Check if config changed
  const currentConfigHash = getConfigHash(config);
  const configChanged = lastConfigHash && lastConfigHash !== currentConfigHash;

  const handleGeneratePreview = () => {
    generatePreview();
    setLastConfigHash(currentConfigHash);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3">
        <Typography variant="h6">Preview Dashboard</Typography>
        <div className="flex items-center gap-3">
          {configChanged && (
            <Typography variant="caption" color="muted" className="text-orange-600 flex items-center gap-1">
              ⚠️ Cài đặt đã thay đổi
            </Typography>
          )}
          <Button
            onClick={handleGeneratePreview}
            disabled={isGeneratingPreview}
            isLoading={isGeneratingPreview}
            variant={configChanged ? "primary" : "outline"}
          >
            {isGeneratingPreview ? 'Đang tạo...' : configChanged ? 'Cập nhật preview' : 'Tạo preview'}
          </Button>
        </div>
      </div>

      <Card className="p-4 lg:p-6">
        <div className="space-y-4">
          <Typography variant="body2" color="muted">
            Cài đặt hiện tại:
          </Typography>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 text-sm">
            <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
              <div className="font-medium text-gray-600 dark:text-gray-400 text-xs mb-1">Định dạng</div>
              <div className="font-semibold">{config.format?.toUpperCase()} {config.orientation}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
              <div className="font-medium text-gray-600 dark:text-gray-400 text-xs mb-1">Chất lượng</div>
              <div className="font-semibold">{Math.round((config.quality || 0.95) * 100)}%</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
              <div className="font-medium text-gray-600 dark:text-gray-400 text-xs mb-1">Tỷ lệ</div>
              <div className="font-semibold">{config.scale || 2}x</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
              <div className="font-medium text-gray-600 dark:text-gray-400 text-xs mb-1">Lề</div>
              <div className="font-semibold">{config.margin || 10}mm</div>
            </div>
          </div>

          <div>
            <Typography variant="body2" color="muted" className="mb-2">
              Nội dung bao gồm:
            </Typography>
            <div className="flex flex-wrap gap-2">
              <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-xs font-medium">📈 Biểu đồ</span>
              <span className="px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full text-xs font-medium">📋 Bảng</span>
              <span className="px-3 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 rounded-full text-xs font-medium">🖼️ Hình ảnh</span>
              <span className="px-3 py-1 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 rounded-full text-xs font-medium">📝 Văn bản</span>
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                config.colorMode === 'color'
                  ? 'bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200'
                  : 'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200'
              }`}>
                {config.colorMode === 'color' ? '🎨 Màu' : '⚫ Đen trắng'}
              </span>
            </div>
          </div>
        </div>
      </Card>

      {previewImage ? (
        <Card className="p-4 lg:p-6">
          <div className="flex items-center justify-between mb-4">
            <Typography variant="body2" color="muted">
              Preview dashboard:
            </Typography>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.open(previewImage, '_blank')}
              className="text-xs"
            >
              🔍 Xem full size
            </Button>
          </div>
          <div className="border rounded-lg overflow-hidden bg-white dark:bg-gray-900 shadow-sm">
            <img
              src={previewImage}
              alt="Dashboard Preview"
              className="w-full h-auto cursor-pointer hover:opacity-90 transition-opacity"
              onClick={() => window.open(previewImage, '_blank')}
              style={{
                maxHeight: window.innerWidth < 1024 ? '300px' : '500px',
                objectFit: 'contain',
                filter: config.colorMode === 'grayscale' ? 'grayscale(100%)' : 'none'
              }}
            />
          </div>
          <Typography variant="caption" color="muted" className="mt-3 block text-center">
            💡 Preview dashboard sẽ được xuất thành báo cáo PDF
          </Typography>
        </Card>
      ) : (
        <Card className="p-8 lg:p-12 text-center">
          <div className="flex flex-col items-center gap-4">
            <div className="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
              <Image className="w-8 h-8 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <Typography variant="body2" color="muted">
                Click "Tạo preview" để xem trước dashboard sẽ được xuất
              </Typography>
              <Typography variant="caption" color="muted">
                Preview sẽ hiển thị toàn bộ nội dung dashboard
              </Typography>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

const PDFExportModal: React.FC<PDFExportModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation(['dashboard', 'common']);
  const { exportToPDF, isExporting } = usePDFExport();

  const [config, setConfig] = useState({
    // Basic options
    filename: 'redai-bao-cao',
    quality: 0.95,
    format: 'a4' as const,
    orientation: 'landscape' as const,
    margin: 10,
    scale: 2,
    includeBackground: true,
    colorMode: 'color' as 'color' | 'grayscale', // Thêm option in màu
  });



  // Handle Escape key and body scroll
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);



  const handleExport = async () => {
    try {
      await exportToPDF({
        filename: config.filename,
        quality: config.quality,
        format: config.format,
        orientation: config.orientation,
        margin: config.margin,
        scale: config.scale,
        includeBackground: config.includeBackground,
        colorMode: config.colorMode, // Thêm colorMode vào export options
      });
      onClose();
    } catch (error) {
      console.error('Export failed:', error);
    }
  };



  return (
    <div className={`fixed inset-0 z-[9500] ${isOpen ? 'block' : 'hidden'}`}>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 animate-fade-in"
        onClick={onClose}
      />

      {/* Modal Container - Centered */}
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <div
          className="bg-white dark:bg-dark-light rounded-lg shadow-xl w-[95vw] max-w-[95vw] h-[90vh] max-h-[90vh] animate-slide-in flex flex-col"
          onClick={(e) => e.stopPropagation()}
        >
        {/* Header */}
        <div className="flex items-center gap-3 p-4 lg:p-6 pb-4 border-b shrink-0 bg-background">
          <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
            <Download className="w-5 h-5 text-primary" />
          </div>
          <div className="min-w-0 flex-1">
            <Typography variant="h5" className="font-semibold">
              {t('dashboard:export.pdf.configTitle', 'Cấu hình xuất báo cáo')}
            </Typography>
            <Typography variant="body2" color="muted" className="hidden sm:block">
              {t('dashboard:export.pdf.configDescription', 'Tùy chỉnh các tùy chọn xuất báo cáo theo nhu cầu')}
            </Typography>
          </div>
          {/* Close Button */}
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            aria-label="Close"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Scrollable Content */}
        <ScrollArea className="flex-1 p-4 lg:p-6" height="100%" autoHide={true}>
          <div className="space-y-6">
            {/* Color Mode Selection */}
            <Card className="p-4">
              <Typography variant="h6" className="mb-4">Tùy chọn xuất báo cáo</Typography>
              <FormItem label="Chế độ màu" name="colorMode">
                <Select
                  value={config.colorMode}
                  onChange={(value) => setConfig(prev => ({ ...prev, colorMode: value as 'color' | 'grayscale' }))}
                  options={[
                    { value: 'color', label: '🎨 In màu (Color)' },
                    { value: 'grayscale', label: '⚫ Đen trắng (Grayscale)' },
                  ]}
                />
              </FormItem>
            </Card>

            {/* Preview Section */}
            <PreviewSection config={config} />
          </div>
        </ScrollArea>

        {/* Footer - Fixed at bottom */}
        <div className="shrink-0 border-t bg-background">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3 p-4 lg:p-6">
            <Typography variant="body2" color="muted" className="text-xs sm:text-sm">
              File: {config.filename}-{new Date().toISOString().slice(0, 10)}.pdf
            </Typography>

            <div className="flex gap-3 lg:ml-auto">
              <Button variant="outline" onClick={onClose} disabled={isExporting} className="flex-1 lg:flex-none">
                {t('common:cancel')}
              </Button>
              <Button
                onClick={handleExport}
                disabled={isExporting}
                isLoading={isExporting}
                className="min-w-[140px] flex-1 lg:flex-none"
              >
                {isExporting ? 'Đang xuất...' : 'Xuất báo cáo'}
              </Button>
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>
  );
};

export default PDFExportModal;
