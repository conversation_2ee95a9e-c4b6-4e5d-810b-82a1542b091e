import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

/**
 * Export types - chỉ chọn 1 loại
 */
export type ExportType = 'full-dashboard' | 'charts-only' | 'tables-only' | 'images-only' | 'text-only';

/**
 * Options cho việc export PDF
 */
export interface ExportPDFOptions {
  filename?: string;
  quality?: number;
  format?: 'a4' | 'a3' | 'letter';
  orientation?: 'portrait' | 'landscape';
  margin?: number;
  scale?: number;
  includeBackground?: boolean;
  colorMode?: 'color' | 'grayscale'; // Thêm option in màu

  // Export type - chỉ chọn 1 loại
  exportType?: ExportType;

  // Style options
  includeColors?: boolean;
  compactMode?: boolean;
  showHeaders?: boolean;
  showFooters?: boolean;
  pageNumbers?: boolean;
}

/**
 * Default options cho export PDF
 */
const DEFAULT_OPTIONS: Required<ExportPDFOptions> = {
  filename: 'dashboard-export',
  quality: 0.95,
  format: 'a4',
  orientation: 'landscape', // Landscape phù hợp hơn cho dashboard
  margin: 10,
  scale: 2,
  includeBackground: true,
  colorMode: 'color', // Mặc định in màu

  // Export type default
  exportType: 'full-dashboard',

  // Style options defaults
  includeColors: true,
  compactMode: false,
  showHeaders: true,
  showFooters: true,
  pageNumbers: true,
};

/**
 * Utility class cho việc export dashboard thành PDF
 */
export class DashboardPDFExporter {
  /**
   * Export dashboard element thành PDF
   */
  static async exportToPDF(
    element: HTMLElement,
    options: ExportPDFOptions = {}
  ): Promise<void> {
    const opts = { ...DEFAULT_OPTIONS, ...options };
    
    try {
      // Hiển thị loading state
      const loadingToast = this.showLoadingToast();

      // Capture element thành canvas
      const canvas = await this.captureElement(element, opts);
      
      // Tạo PDF từ canvas
      const pdf = this.createPDFFromCanvas(canvas, opts);
      
      // Download PDF
      const filename = this.generateFilename(opts.filename);
      pdf.save(filename);
      
      // Ẩn loading và hiển thị success
      this.hideLoadingToast(loadingToast);
      this.showSuccessToast(filename);
      
    } catch (error) {
      console.error('Failed to export PDF:', error);
      this.showErrorToast();
      throw error;
    }
  }

  /**
   * Capture element thành canvas với html2canvas
   */
  private static async captureElement(
    element: HTMLElement,
    options: Required<ExportPDFOptions>
  ): Promise<HTMLCanvasElement> {
    // Scroll to top để capture toàn bộ dashboard
    const originalScrollTop = window.pageYOffset;
    window.scrollTo(0, 0);

    try {
      const canvas = await html2canvas(element, {
        scale: options.scale,
        useCORS: true,
        allowTaint: true,
        backgroundColor: options.includeBackground ? '#ffffff' : null,
        logging: false,
        width: element.scrollWidth,
        height: element.scrollHeight,
        scrollX: 0,
        scrollY: 0,
        windowWidth: element.scrollWidth,
        windowHeight: element.scrollHeight,
        // Ignore elements based on options
        ignoreElements: (element) => {
          // Always ignore these elements
          if (element.classList.contains('no-pdf') ||
              element.tagName === 'SCRIPT' ||
              element.tagName === 'STYLE') {
            return true;
          }

          // Không filter theo exportType nữa - xuất tất cả nội dung
          return false;
        },
      });

      // Áp dụng grayscale filter nếu cần
      if (options.colorMode === 'grayscale') {
        this.applyGrayscaleFilter(canvas);
      }

      return canvas;
    } finally {
      // Restore original scroll position
      window.scrollTo(0, originalScrollTop);
    }
  }

  /**
   * Áp dụng grayscale filter vào canvas
   */
  private static applyGrayscaleFilter(canvas: HTMLCanvasElement): void {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Chuyển đổi mỗi pixel thành grayscale
    for (let i = 0; i < data.length; i += 4) {
      const red = data[i];
      const green = data[i + 1];
      const blue = data[i + 2];

      // Sử dụng công thức luminance để tính grayscale
      const gray = Math.round(0.299 * red + 0.587 * green + 0.114 * blue);

      data[i] = gray;     // Red
      data[i + 1] = gray; // Green
      data[i + 2] = gray; // Blue
      // Alpha (data[i + 3]) giữ nguyên
    }

    ctx.putImageData(imageData, 0, 0);
  }

  /**
   * Tạo PDF từ canvas
   */
  private static createPDFFromCanvas(
    canvas: HTMLCanvasElement,
    options: Required<ExportPDFOptions>
  ): jsPDF {
    const imgData = canvas.toDataURL('image/jpeg', options.quality);
    
    // Tính toán kích thước PDF
    const { pdfWidth, pdfHeight } = this.calculatePDFDimensions(options.format, options.orientation);
    
    // Tạo PDF instance
    const pdf = new jsPDF({
      orientation: options.orientation,
      unit: 'mm',
      format: options.format,
    });

    // Tính toán kích thước image để fit vào PDF
    const imgWidth = pdfWidth - (options.margin * 2);
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    
    // Nếu image cao hơn page, chia thành nhiều trang
    if (imgHeight > pdfHeight - (options.margin * 2)) {
      this.addMultiPageImage(pdf, imgData, canvas, options, imgWidth, pdfWidth, pdfHeight);
    } else {
      // Single page
      pdf.addImage(
        imgData,
        'JPEG',
        options.margin,
        options.margin,
        imgWidth,
        imgHeight
      );
    }

    // Thêm metadata
    this.addPDFMetadata(pdf);

    return pdf;
  }

  /**
   * Thêm image vào nhiều trang PDF
   */
  private static addMultiPageImage(
    pdf: jsPDF,
    _imgData: string,
    canvas: HTMLCanvasElement,
    options: Required<ExportPDFOptions>,
    imgWidth: number,
    _pdfWidth: number,
    pdfHeight: number
  ): void {
    const pageHeight = pdfHeight - (options.margin * 2);
    const ratio = canvas.width / imgWidth;
    const canvasPageHeight = pageHeight * ratio;
    
    let remainingHeight = canvas.height;
    let position = 0;
    let pageNumber = 1;

    while (remainingHeight > 0) {
      const currentPageHeight = Math.min(canvasPageHeight, remainingHeight);
      const currentImgHeight = currentPageHeight / ratio;

      if (pageNumber > 1) {
        pdf.addPage();
      }

      // Create a temporary canvas for this page
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = canvas.width;
      tempCanvas.height = currentPageHeight;
      
      const tempCtx = tempCanvas.getContext('2d');
      if (tempCtx) {
        tempCtx.drawImage(
          canvas,
          0, position,
          canvas.width, currentPageHeight,
          0, 0,
          canvas.width, currentPageHeight
        );

        const tempImgData = tempCanvas.toDataURL('image/jpeg', options.quality);
        pdf.addImage(
          tempImgData,
          'JPEG',
          options.margin,
          options.margin,
          imgWidth,
          currentImgHeight
        );
      }

      remainingHeight -= currentPageHeight;
      position += currentPageHeight;
      pageNumber++;
    }
  }

  /**
   * Tính toán kích thước PDF
   */
  private static calculatePDFDimensions(
    format: 'a4' | 'a3' | 'letter',
    orientation: 'portrait' | 'landscape'
  ): { pdfWidth: number; pdfHeight: number } {
    const dimensions = {
      a4: { width: 210, height: 297 },
      a3: { width: 297, height: 420 },
      letter: { width: 216, height: 279 },
    };

    const { width, height } = dimensions[format];
    
    return orientation === 'landscape'
      ? { pdfWidth: height, pdfHeight: width }
      : { pdfWidth: width, pdfHeight: height };
  }

  /**
   * Thêm metadata vào PDF
   */
  private static addPDFMetadata(pdf: jsPDF): void {
    pdf.setProperties({
      title: 'Báo cáo Dashboard',
      subject: 'Báo cáo RedAI Dashboard',
      author: 'RedAI System',
      creator: 'RedAI Dashboard',
    });
  }

  /**
   * Generate filename với timestamp
   */
  private static generateFilename(baseName: string): string {
    const now = new Date();
    const timestamp = now.toISOString().slice(0, 19).replace(/[:.]/g, '-');
    return `${baseName}-${timestamp}.pdf`;
  }

  /**
   * Console logging for debugging
   */
  private static showLoadingToast(): any {
    console.log('📄 Đang xuất báo cáo...');
    return null;
  }

  private static hideLoadingToast(_toast: any): void {
    // No-op for console logging
  }

  private static showSuccessToast(filename: string): void {
    console.log(`✅ Đã xuất báo cáo thành công: ${filename}`);
  }

  private static showErrorToast(): void {
    console.error('❌ Lỗi khi xuất báo cáo');
  }
}

/**
 * Helper function để export dashboard hiện tại
 */
export const exportDashboardToPDF = async (options?: ExportPDFOptions): Promise<void> => {
  // Tìm dashboard container
  const dashboardElement = document.querySelector('[data-dashboard-container]') as HTMLElement;
  
  if (!dashboardElement) {
    console.error('Dashboard container not found');
    return;
  }

  await DashboardPDFExporter.exportToPDF(dashboardElement, options);
};

export default DashboardPDFExporter;
