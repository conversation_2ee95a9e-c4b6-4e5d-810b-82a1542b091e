import React from 'react';
import DashboardCard from './DashboardCard';
import { DashboardWidget } from '../types';

interface DashboardWorkspaceProps {
  widgets: DashboardWidget[];
  onLayoutChange?: (layout: any[], layouts: { [key: string]: any[] }) => void;
  onRemoveWidget: (widgetId: string) => void;
  onWidgetTitleChange: (widgetId: string, newTitle: string) => void;
  onWidgetPropsChange?: (widgetId: string, newProps: Record<string, unknown>) => void;
  isDraggable: boolean;
  isResizable: boolean;
  mode: 'view' | 'edit';
  displayMode?: 'normal' | 'minimal' | 'ultra-minimal';
  smartLayoutMode?: boolean;
  autoHeightMode?: boolean;
}

const DashboardWorkspace: React.FC<DashboardWorkspaceProps> = ({
  widgets,
  onLayoutChange,
  onRemoveWidget,
  onWidgetTitleChange,
  onWidgetPropsChange,
  isDraggable,
  isResizable,
  mode,
  displayMode = 'normal',
  smartLayoutMode = false,
  autoHeightMode = true,
}) => {
  return (
    <div className="w-full">
      {/* Drag & Drop Workspace */}
      <div className="p-6">
        <DashboardCard
          widgets={widgets}
          onLayoutChange={onLayoutChange}
          onRemoveWidget={onRemoveWidget}
          onWidgetTitleChange={onWidgetTitleChange}
          onWidgetPropsChange={onWidgetPropsChange}
          isDraggable={isDraggable}
          isResizable={isResizable}
          mode={mode}
          displayMode={displayMode}
          smartLayoutMode={smartLayoutMode}
          autoHeightMode={autoHeightMode}
          className="dashboard-grid-container"
        />
      </div>
    </div>
  );
};

export default DashboardWorkspace;
