/**
 * Dashboard Management Service
 * Business logic layer cho dashboard operations
 */

import {
  getDashboardPages,
  getDashboardPage,
  createDashboardPage,
  updateDashboardPage,
  deleteDashboardPage,
  setDefaultDashboardPage,
  getDefaultDashboardPage,
  withDashboardErrorHandling,
} from '../api/dashboard-management.api';

import {
  CreateDashboardPageDto,
  UpdateDashboardPageDto,
  QueryDashboardPageDto,
  DashboardPageResponseDto,
  DashboardSaveOptions,
  DashboardLoadOptions,
  PageResult,
} from '../types/dashboard-api.types';

import { DashboardTabsState } from '../types';

// ==================== CONSTANTS ====================

const STORAGE_KEY = 'redai-dashboard-tabs';
const BACKUP_STORAGE_KEY = 'redai-dashboard-backup';
const MAX_BACKUP_COUNT = 5;

// ==================== DASHBOARD MANAGEMENT SERVICE ====================

export class DashboardManagementService {
  /**
   * Lấy danh sách dashboard pages với business logic
   */
  static async getDashboardPagesWithBusinessLogic(
    params?: QueryDashboardPageDto
  ): Promise<PageResult<DashboardPageResponseDto>> {
    const defaultParams: QueryDashboardPageDto = {
      page: 1,
      limit: 20,
      isActive: true,
      sortBy: 'sortOrder',
      sortOrder: 'ASC',
      ...params,
    };

    // Validate limit
    if (defaultParams.limit && defaultParams.limit > 100) {
      throw new Error('Limit không được vượt quá 100');
    }

    // Validate page
    if (defaultParams.page && defaultParams.page < 1) {
      throw new Error('Page phải lớn hơn 0');
    }

    return withDashboardErrorHandling(
      () => getDashboardPages(defaultParams),
      '/user/dashboard/pages'
    );
  }

  /**
   * Lấy dashboard page với business logic
   */
  static async getDashboardPageWithBusinessLogic(
    id: string,
    options?: DashboardLoadOptions
  ): Promise<DashboardPageResponseDto> {
    const { includeWidgets = false } = options || {};

    return withDashboardErrorHandling(
      () => getDashboardPage(id, includeWidgets),
      `/user/dashboard/pages/${id}`
    );
  }

  /**
   * Tạo dashboard page với validation
   */
  static async createDashboardPageWithValidation(
    data: CreateDashboardPageDto
  ): Promise<DashboardPageResponseDto> {
    // Validate required fields
    if (!data.name?.trim()) {
      throw new Error('Tên dashboard không được để trống');
    }

    if (!data.slug?.trim()) {
      throw new Error('Slug không được để trống');
    }

    // Validate slug format
    const slugRegex = /^[a-z0-9-]+$/;
    if (!slugRegex.test(data.slug)) {
      throw new Error('Slug chỉ được chứa chữ thường, số và dấu gạch ngang');
    }

    // Set default values
    const processedData: CreateDashboardPageDto = {
      sortOrder: 0,
      isActive: true,
      isDefault: false,
      ...data,
    };

    return withDashboardErrorHandling(
      () => createDashboardPage(processedData),
      '/user/dashboard/pages'
    );
  }

  /**
   * Cập nhật dashboard page với validation
   */
  static async updateDashboardPageWithValidation(
    id: string,
    data: UpdateDashboardPageDto
  ): Promise<DashboardPageResponseDto> {
    // Validate slug format if provided
    if (data.slug && !/^[a-z0-9-]+$/.test(data.slug)) {
      throw new Error('Slug chỉ được chứa chữ thường, số và dấu gạch ngang');
    }

    return withDashboardErrorHandling(
      () => updateDashboardPage(id, data),
      `/user/dashboard/pages/${id}`
    );
  }

  /**
   * Xóa dashboard page với business logic
   */
  static async deleteDashboardPageWithBusinessLogic(id: string): Promise<{ success: boolean }> {
    return withDashboardErrorHandling(() => deleteDashboardPage(id), `/user/dashboard/pages/${id}`);
  }

  /**
   * Đặt dashboard mặc định với business logic
   */
  static async setDefaultDashboardWithBusinessLogic(id: string): Promise<DashboardPageResponseDto> {
    return withDashboardErrorHandling(
      () => setDefaultDashboardPage(id),
      `/user/dashboard/pages/${id}/set-default`
    );
  }

  // ==================== DATA TRANSFORMATION ====================

  /**
   * Transform DashboardTabsState to backend format
   */
  static transformTabsStateToBackend(tabsState: DashboardTabsState): DashboardTabsState {
    return {
      currentTabId: tabsState.currentTabId,
      tabs: tabsState.tabs.map(tab => ({
        ...tab,
        updatedAt: new Date().toISOString(),
      })),
    };
  }

  /**
   * Transform backend data to DashboardTabsState
   */
  static transformBackendToTabsState(
    backendData: DashboardPageResponseDto
  ): DashboardTabsState | null {
    if (!backendData.tabsConfig) {
      return null;
    }

    return {
      currentTabId: backendData.tabsConfig.currentTabId,
      tabs: backendData.tabsConfig.tabs,
    };
  }

  // ==================== LOCAL STORAGE MANAGEMENT ====================

  /**
   * Lưu dashboard state vào localStorage
   */
  static saveToLocalStorage(tabsState: DashboardTabsState): void {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(tabsState));
    } catch (error) {
      console.warn('Failed to save dashboard to localStorage:', error);
    }
  }

  /**
   * Lấy dashboard state từ localStorage
   */
  static loadFromLocalStorage(): DashboardTabsState | null {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        return JSON.parse(saved) as DashboardTabsState;
      }
    } catch (error) {
      console.warn('Failed to load dashboard from localStorage:', error);
    }
    return null;
  }

  /**
   * Tạo backup của dashboard state
   */
  static createBackup(tabsState: DashboardTabsState): void {
    try {
      const backups = this.getBackups();
      const newBackup = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        data: tabsState,
      };

      backups.unshift(newBackup);

      // Giữ tối đa MAX_BACKUP_COUNT backups
      if (backups.length > MAX_BACKUP_COUNT) {
        backups.splice(MAX_BACKUP_COUNT);
      }

      localStorage.setItem(BACKUP_STORAGE_KEY, JSON.stringify(backups));
    } catch (error) {
      console.warn('Failed to create dashboard backup:', error);
    }
  }

  /**
   * Lấy danh sách backups
   */
  static getBackups(): Array<{
    id: string;
    timestamp: string;
    data: DashboardTabsState;
  }> {
    try {
      const saved = localStorage.getItem(BACKUP_STORAGE_KEY);
      if (saved) {
        return JSON.parse(saved);
      }
    } catch (error) {
      console.warn('Failed to load dashboard backups:', error);
    }
    return [];
  }

  /**
   * Khôi phục từ backup
   */
  static restoreFromBackup(backupId: string): DashboardTabsState | null {
    const backups = this.getBackups();
    const backup = backups.find(b => b.id === backupId);
    return backup?.data || null;
  }

  // ==================== MIGRATION UTILITIES ====================

  /**
   * Migrate data từ localStorage sang server
   */
  static async migrateLocalToServer(
    localData: DashboardTabsState,
    name: string = 'My Dashboard'
  ): Promise<DashboardPageResponseDto> {
    const createData: CreateDashboardPageDto = {
      name,
      slug: `dashboard-${Date.now()}`,
      description: 'Migrated from local storage',
      tabsConfig: localData,
      isDefault: true,
    };

    return this.createDashboardPageWithValidation(createData);
  }

  /**
   * Sync dashboard state với server
   */
  static async syncWithServer(
    dashboardId: string,
    localData: DashboardTabsState,
    options?: DashboardSaveOptions
  ): Promise<DashboardPageResponseDto> {
    const { saveToLocal = true, createBackup = true } = options || {};

    // Tạo backup trước khi sync
    if (createBackup) {
      this.createBackup(localData);
    }

    // Transform data và update server
    const transformedData = this.transformTabsStateToBackend(localData);
    const result = await this.updateDashboardPageWithValidation(dashboardId, {
      tabsConfig: transformedData,
    });

    // Save to localStorage if requested
    if (saveToLocal) {
      this.saveToLocalStorage(localData);
    }

    return result;
  }

  /**
   * Load dashboard với fallback strategy
   */
  static async loadDashboardWithFallback(options?: DashboardLoadOptions): Promise<{
    data: DashboardTabsState | null;
    source: 'server' | 'local' | 'default';
    serverData?: DashboardPageResponseDto;
  }> {
    const { preferServer = true, fallbackToLocal = true } = options || {};

    let serverData: DashboardPageResponseDto | null = null;
    let localData: DashboardTabsState | null = null;

    // Try to load from server first if preferred
    if (preferServer) {
      try {
        serverData = await getDefaultDashboardPage();
        if (serverData?.tabsConfig) {
          return {
            data: serverData.tabsConfig,
            source: 'server',
            serverData,
          };
        }
      } catch (error) {
        console.warn('Failed to load dashboard from server:', error);
      }
    }

    // Fallback to localStorage
    if (fallbackToLocal) {
      localData = this.loadFromLocalStorage();
      if (localData) {
        return {
          data: localData,
          source: 'local',
        };
      }
    }

    // Return default if nothing found
    return {
      data: null,
      source: 'default',
    };
  }
}
