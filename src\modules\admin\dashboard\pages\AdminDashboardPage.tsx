import React, { useCallback, useMemo, useState, useEffect } from 'react';
import { DashboardSidebar, DashboardCard, DashboardTabs } from '@/modules/dashboard/components';
import { DashboardWidget, MenuItem } from '@/modules/dashboard/types';
import { useDashboardTabs } from '@/modules/dashboard/hooks/useDashboardTabs';
import { IconCard } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { cleanupDashboardTabs } from '@/modules/dashboard/utils/cleanupWidgets';
import '@/modules/dashboard/styles/dashboard.css';

const AdminDashboardPage: React.FC = () => {
  const { t } = useTranslation(['dashboard']);
  const [smartLayoutMode, setSmartLayoutMode] = useState(true);
  const [autoHeightMode, setAutoHeightMode] = useState(true);

  // Use tab system instead of direct widgets state
  const {
    tabsState,
    currentTab,
    switchToTab,
    createTab,
    renameTab,
    deleteTab,
    reorderTabs,
    changeTabMode,
    addWidgetToCurrentTab,
    removeWidgetFromCurrentTab,
    updateWidgetPropsInCurrentTab,
    updateTabWidgets,
    saveToStorage,
  } = useDashboardTabs();

  // Clean up invalid widgets on mount
  useEffect(() => {
    const hasCleanedUp = cleanupDashboardTabs();
    if (hasCleanedUp) {
      console.log('🧹 Admin dashboard cleanup completed - page will reload');
      window.location.reload();
    }
  }, []);

  // Get current tab widgets
  const widgets = useMemo(() => currentTab?.widgets || [], [currentTab?.widgets]);

  // Handle menu item click to add widget
  const handleMenuItemClick = useCallback(
    (menuItem: MenuItem) => {
      if (!currentTab) return;

      // Create new widget from menu item
      const newWidget: DashboardWidget = {
        id: `${menuItem.id}-${Date.now()}`,
        title: menuItem.title,
        type: 'data-count', // Default type since MenuItem doesn't have type
        x: 0,
        y: 0,
        w: 6, // Default width
        h: 4, // Default height
        isEmpty: true,
      };

      addWidgetToCurrentTab(newWidget);
    },
    [currentTab, addWidgetToCurrentTab]
  );

  // Handle layout change
  const handleLayoutChange = useCallback(
    (layout: any[]) => {
      if (!currentTab) return;

      // Update widgets with new layout positions
      const updatedWidgets = widgets.map(widget => {
        const layoutItem = layout.find(item => item.i === widget.id);
        if (layoutItem) {
          return {
            ...widget,
            x: layoutItem.x,
            y: layoutItem.y,
            w: layoutItem.w,
            h: layoutItem.h,
          };
        }
        return widget;
      });

      // Update current tab widgets using the hook
      updateTabWidgets(updatedWidgets);
    },
    [currentTab, widgets, updateTabWidgets]
  );

  // Handle widget removal
  const handleRemoveWidget = useCallback(
    (widgetId: string) => {
      removeWidgetFromCurrentTab(widgetId);
    },
    [removeWidgetFromCurrentTab]
  );

  const handleToggleSmartLayout = useCallback(() => {
    setSmartLayoutMode(prev => !prev);
  }, []);

  const handleToggleAutoHeight = useCallback(() => {
    setAutoHeightMode(prev => !prev);
  }, []);

  // Handle save action
  const handleSave = useCallback(() => {
    // Save current state to storage - call without parameters as the hook manages state internally
    saveToStorage();

    // You can add additional save logic here if needed
    console.log('Dashboard saved successfully');
  }, [saveToStorage]);

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Navbar - with smooth transition */}
      <div
        className={`navbar-container overflow-hidden ${
          currentTab?.mode === 'edit' ? 'navbar-visible max-h-20' : 'navbar-hidden max-h-0'
        }`}
      >
        <DashboardSidebar onMenuItemClick={handleMenuItemClick} />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Dashboard Tabs */}
        <DashboardTabs
          tabs={tabsState.tabs}
          currentTabId={tabsState.currentTabId}
          onTabChange={switchToTab}
          onTabCreate={createTab}
          onTabRename={renameTab}
          onTabDelete={deleteTab}
          onTabReorder={reorderTabs}
          onModeChange={changeTabMode}
          onSave={handleSave}
        />

        {/* Content Area */}
        <div className="flex-1 flex flex-col min-h-0">
          {/* Layout Controls */}
          <div className="flex justify-end gap-2 p-2 border-b">
            <IconCard
              icon="maximize"
              variant={autoHeightMode ? "primary" : "ghost"}
              onClick={handleToggleAutoHeight}
              title={autoHeightMode ? t('dashboard:autoHeight.disable', 'Tắt tự động điều chỉnh chiều cao') : t('dashboard:autoHeight.enable', 'Bật tự động điều chỉnh chiều cao')}
            />
            <IconCard
              icon={smartLayoutMode ? "grid" : "layout"}
              variant={smartLayoutMode ? "primary" : "ghost"}
              onClick={handleToggleSmartLayout}
              title={smartLayoutMode ? t('dashboard:smartLayout.disable') : t('dashboard:smartLayout.enable')}
            />
          </div>

          {/* Scrollable Dashboard Grid */}
          <div
            className="flex-1 overflow-auto dashboard-main-scroll scrollbar-hide"
            style={{
              maxHeight: '70vh',
            }}
          >
            <div className="p-6 pt-2">
              <div className="dashboard-container">
                <DashboardCard
                  widgets={widgets}
                  onLayoutChange={handleLayoutChange}
                  onRemoveWidget={handleRemoveWidget}
                  onWidgetPropsChange={updateWidgetPropsInCurrentTab}
                  isDraggable={true}
                  isResizable={true}
                  mode={currentTab?.mode || 'edit'}
                  smartLayoutMode={smartLayoutMode}
                  autoHeightMode={autoHeightMode}
                  className="dashboard-grid-container"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboardPage;
