{"business": {"title": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>h do<PERSON>h", "description": "<PERSON><PERSON><PERSON><PERSON> lý các ho<PERSON>t động kinh doanh của doanh nghi<PERSON>p", "common": {"moduleTitle": "<PERSON><PERSON>", "createAt": "<PERSON><PERSON><PERSON>", "selected": "<PERSON><PERSON> ch<PERSON>n", "continue": "<PERSON><PERSON><PERSON><PERSON>", "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "pending": "Chờ xử lý", "processing": "<PERSON><PERSON> lý", "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "cancelled": "<PERSON><PERSON> hủy", "blocked": "Đã chặn", "draft": "<PERSON><PERSON><PERSON>"}, "orderStatus": {"pending": "Chờ xử lý", "processing": "<PERSON><PERSON> lý", "confirmed": "<PERSON><PERSON> x<PERSON>c <PERSON>n", "shipped": "<PERSON><PERSON> gửi hàng", "delivered": "Đ<PERSON> giao hàng", "cancelled": "<PERSON><PERSON> hủy", "completed": "<PERSON><PERSON><PERSON> th<PERSON>"}, "paymentStatus": {"paid": "<PERSON><PERSON> thanh toán", "pending": "Chờ thanh toán", "unpaid": "<PERSON><PERSON><PERSON> to<PERSON>", "failed": "<PERSON><PERSON> to<PERSON> thất bại", "partiallyPaid": "<PERSON><PERSON> <PERSON><PERSON> một ph<PERSON>n", "refunded": "<PERSON><PERSON> hoàn tiền"}, "shippingStatus": {"title": "<PERSON><PERSON><PERSON><PERSON> thái vận chuyển", "pending": "<PERSON><PERSON> vận chuyển", "processing": "<PERSON><PERSON> bị", "shipped": "<PERSON><PERSON> gửi hàng", "delivered": "Đ<PERSON> giao hàng", "cancelled": "<PERSON><PERSON> hủy", "sorting": "Đang phân loại", "pickedUp": "<PERSON><PERSON> l<PERSON>y hàng", "inTransit": "<PERSON><PERSON> vận chuy<PERSON>n", "failed": "<PERSON><PERSON><PERSON> hàng thất b<PERSON>i", "returned": "<PERSON><PERSON> hoàn trả"}, "actions": {"save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "edit": "Chỉnh sửa", "create": "<PERSON><PERSON><PERSON> mới", "back": "Quay lại", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "submit": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "sort": "<PERSON><PERSON><PERSON>p", "add": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Xóa", "upload": "<PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "view": "Xem", "details": "<PERSON> ti<PERSON>", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "form": {"name": "Họ và tên", "email": "Email", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "address": "Địa chỉ", "description": "<PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "tags": "<PERSON><PERSON>ã<PERSON>", "notes": "<PERSON><PERSON><PERSON>"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "deleteError": "Có lỗi xảy ra khi xóa", "bulkDeleteSuccess": "<PERSON><PERSON><PERSON> thành công {{count}} mục", "bulkDeleteError": "Có lỗi xảy ra khi xóa", "loadError": "Lỗi khi tải dữ liệu"}}, "company": {"title": "<PERSON><PERSON><PERSON><PERSON> lý công ty", "description": "<PERSON><PERSON><PERSON><PERSON> lý thông tin công ty (tổ chức)", "create": "<PERSON><PERSON><PERSON> công ty", "createTitle": "<PERSON><PERSON><PERSON> công ty mới", "createDescription": "<PERSON><PERSON><PERSON> một công ty (tổ chức) thu<PERSON><PERSON> quyền quản lý của <PERSON>", "form": {"fullName": "<PERSON>ên đ<PERSON>y đủ công ty", "fullNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên đầy đủ công ty (tổ chức)", "fullNameRequired": "<PERSON>ên đ<PERSON>y đủ công ty là bắt buộc", "fullNameMaxLength": "<PERSON>ên đầy đủ công ty không đ<PERSON><PERSON><PERSON> vư<PERSON><PERSON> quá 200 ký tự", "shortName": "<PERSON><PERSON><PERSON> vi<PERSON>t tắt công ty", "shortNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên viết tắt công ty (tổ chức)", "shortNameRequired": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> tắt công ty là bắt buộc", "shortNameMaxLength": "<PERSON><PERSON><PERSON> viết tắt công ty không đ<PERSON><PERSON><PERSON> vư<PERSON><PERSON> quá 20 ký tự", "submit": "<PERSON><PERSON><PERSON> công ty", "submitting": "<PERSON><PERSON> tạo...", "cancel": "<PERSON><PERSON><PERSON>"}, "configuration": {"title": "<PERSON><PERSON><PERSON> hình công ty", "paymentCode": "<PERSON><PERSON> thanh toán", "paymentCodePlaceholder": "<PERSON><PERSON>n trạng thái mã thanh toán", "paymentCodeOn": "<PERSON><PERSON><PERSON>", "paymentCodeOff": "Tắt", "paymentCodePrefix": "<PERSON><PERSON><PERSON><PERSON> tố mã thanh toán", "paymentCodePrefixPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tiền tố mã thanh toán", " paymentCodeSuffixFrom": "<PERSON><PERSON><PERSON> tố từ", "paymentCodeSuffixFromPlaceholder": "<PERSON><PERSON><PERSON><PERSON> hậu tố từ", "paymentCodeSuffixTo": "<PERSON><PERSON><PERSON> tố đến", "paymentCodeSuffixToPlaceholder": "<PERSON><PERSON><PERSON><PERSON> hậu tố đến", "paymentCodeSuffixCharacterType": "<PERSON><PERSON><PERSON> ký tự hậu tố", "paymentCodeSuffixCharacterTypePlaceholder": "<PERSON><PERSON><PERSON> kiểu ký tự hậu tố", "numberAndLetter": "Cho phép chữ cái và số", "numberOnly": "Chỉ cho phép chữ số"}, "createSuccess": "<PERSON><PERSON> tạo công ty (tổ chức) thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo công ty", "updateSuccess": "<PERSON><PERSON><PERSON> nhật thông tin công ty thành công", "updateError": "<PERSON><PERSON> lỗi xảy ra khi cập nhật thông tin công ty", "loadError": "Lỗi khi tải thông tin công ty", "validation": {"fullNameRequired": "<PERSON>ên đ<PERSON>y đủ công ty là bắt buộc", "fullNameMaxLength": "Tên đầy đủ công ty không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá {{max}} ký tự", "shortNameRequired": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> tắt công ty là bắt buộc", "shortNameMaxLength": "<PERSON><PERSON><PERSON> viết tắt công ty không đ<PERSON><PERSON><PERSON> vư<PERSON><PERSON> quá {{max}} ký tự"}, "information": {"title": "Thông tin công ty", "fullName": "<PERSON>ên đ<PERSON>y đủ công ty", "shortName": "<PERSON><PERSON><PERSON> vi<PERSON>t tắt công ty", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "statusPlaceholder": "<PERSON><PERSON><PERSON> trạng thái", "statusOptions": {"pending": "Chờ xử lý", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "suspended": "<PERSON><PERSON><PERSON>", "terminated": "<PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON> hủy", "fraud": "<PERSON><PERSON>"}}}, "shop": {"title": "<PERSON><PERSON><PERSON><PERSON> lý địa chỉ cửa hàng", "description": "<PERSON><PERSON><PERSON>n lý địa chỉ cửa hàng và thông tin vận chuyển", "form": {"shopId": "<PERSON> c<PERSON><PERSON> h<PERSON>ng", "shopName": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng", "shopNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên c<PERSON><PERSON> hàng", "shopPhone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "shopPhonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại cửa hàng", "shopAddress": "Đ<PERSON><PERSON> chỉ chi tiết", "shopAddressPlaceholder": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, s<PERSON>, v.v", "shopProvince": "Tỉnh/Thành phố", "shopProvincePlaceholder": "Nhập tỉnh/thành phố", "shopDistrict": "Quận/Huyện", "shopDistrictPlaceholder": "<PERSON><PERSON><PERSON><PERSON> quận/huyện", "shopWard": "Phường/Xã", "shopWardPlaceholder": "<PERSON><PERSON><PERSON><PERSON> phường/xã", "isDefault": "Mặc định", "createTitle": "<PERSON><PERSON><PERSON> địa chỉ cửa hàng", "editTitle": "<PERSON><PERSON><PERSON> nh<PERSON>t địa chỉ cửa hàng", "description": "<PERSON><PERSON><PERSON><PERSON> địa chỉ cửa hàng để quản lý đơn hàng và vận chuyển", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "addressShop": "<PERSON><PERSON><PERSON> chỉ c<PERSON>a hàng"}, "filter": {"recent": "<PERSON><PERSON><PERSON><PERSON>"}, "empty": {"title": "<PERSON><PERSON><PERSON> có cửa hàng nào", "description": "<PERSON><PERSON><PERSON> cửa hàng đầu tiên để bắt đầu quản lý đơn hàng và vận chuyển", "action": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> thông tin cửa hàng thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo thông tin cửa hàng", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin cửa hàng thành công", "updateError": "<PERSON><PERSON> lỗi xảy ra khi cập nhật thông tin cửa hàng", "loadError": "Lỗi khi tải thông tin cửa hàng", "noShopInfo": "<PERSON><PERSON>a có thông tin cửa hàng. <PERSON><PERSON> lòng tạo thông tin cửa hàng để có thể quản lý đơn hàng.", "bulkDeleteSuccess": "X<PERSON><PERSON> {{count}} c<PERSON>a hàng thành công", "bulkDeleteError": "Lỗi khi xóa nhiều cửa hàng", "confirmDelete": "Bạn có chắc chắn muốn xóa địa chỉ cửa hàng này không?"}, "actions": {"setDefault": "Đặt làm mặc định"}, "selectToDelete": "<PERSON><PERSON> lòng chọn ít nhất một cửa hàng để xóa", "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa {{count}} cửa hàng đã chọn?"}, "customer": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý thông tin khách hàng", "add": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> h<PERSON>ng", "edit": "Chỉnh sửa kh<PERSON>ch hàng", "view": "<PERSON>em chi tiết kh<PERSON>ch hàng", "addForm": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch hàng mới", "editForm": "Chỉnh sửa thông tin khách hàng", "detailForm": "<PERSON> tiết kh<PERSON>ch hàng", "totalCustomers": "Tổng số khách hàng", "manage": "<PERSON><PERSON><PERSON><PERSON> lý kh<PERSON>ch hàng", "platform": "<PERSON><PERSON><PERSON> t<PERSON>", "timezone": "<PERSON><PERSON><PERSON> giờ", "name": "<PERSON><PERSON><PERSON> h<PERSON>ng", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "email": "Email", "address": "Địa chỉ", "customFields": "Trường tùy chỉnh", "totalOrders": "<PERSON><PERSON><PERSON> đơn hàng", "totalSpent": "T<PERSON>ng chi tiêu", "potentialScore": "<PERSON><PERSON><PERSON><PERSON> tiềm năng", "list": "<PERSON><PERSON> s<PERSON>ch kh<PERSON>ch hàng", "noCustomers": "<PERSON><PERSON><PERSON> có khách hàng nào", "addFirst": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch hàng đầu tiên", "form": {"namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> họ và tên khách hàng", "emailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email", "phonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "tagsPlaceholder": "Nhập tag và nhấn Enter", "addressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "addressNote": "<PERSON><PERSON> chú địa chỉ", "addressNotePlaceholder": "Ví dụ: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>h<PERSON>...", "avatar": "Ảnh đại diện", "agent": "Agent hỗ trợ"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo khách hàng", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t khách hàng thành công", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật kh<PERSON>ch hàng", "deleteSuccess": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng thành công", "deleteError": "Có lỗi xảy ra khi xóa khách hàng", "bulkDeleteSuccess": "<PERSON><PERSON><PERSON> thành công {{count}} kh<PERSON>ch hàng", "bulkDeleteError": "Có lỗi xảy ra khi xóa khách hàng"}, "errors": {"phoneExists": "<PERSON><PERSON> điện thoại đã tồn tại", "createFailed": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng thất bại"}, "loadError": "Lỗi khi tải thông tin khách hàng", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy khách hàng", "notFoundDescription": "<PERSON><PERSON><PERSON><PERSON> hàng bạn đang tìm kiếm không tồn tại hoặc đã bị xóa", "merge": {"title": "<PERSON><PERSON><PERSON> h<PERSON>ng", "action": "<PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng thành công", "error": "<PERSON><PERSON> lỗi xảy ra khi gộp kh<PERSON>ch hàng", "originalCustomer": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>c", "recommendedCustomer": "<PERSON><PERSON><PERSON><PERSON> hàng đ<PERSON><PERSON> g<PERSON>", "similarity": "<PERSON><PERSON> tương đồng", "high": "<PERSON>", "medium": "<PERSON>rung bình", "low": "<PERSON><PERSON><PERSON><PERSON>", "recommendations": {"title": "Gợ<PERSON> ý g<PERSON>p kh<PERSON>ch hàng", "count": "<PERSON><PERSON><PERSON> thấy {{count}} gợi ý gộp khách hàng", "noData": "<PERSON><PERSON><PERSON>ng có gợi ý gộp khách hàng nào"}, "search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm kh<PERSON>ch hàng..."}, "manualSelection": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng thủ công", "manualSubtitle": "<PERSON>ọn và gộp khách hàng thủ công", "recommendationSubtitle": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng từ gợi ý hệ thống", "aiRecommendation": "<PERSON><PERSON><PERSON> ý từ AI", "highSimilarity": "<PERSON><PERSON> tương đồng cao", "customer1": "<PERSON><PERSON><PERSON><PERSON> hàng thứ nhất", "customer2": "<PERSON><PERSON><PERSON><PERSON> hàng thứ hai", "searchCustomer1": "<PERSON><PERSON><PERSON> kiếm khách hàng thứ nhất...", "searchCustomer2": "<PERSON><PERSON><PERSON> kiếm kh<PERSON>ch hàng thứ hai...", "helpTitle": "Hướng dẫn", "helpText": "Chọn hai khách hàng bạn muốn gộp. <PERSON><PERSON> thống sẽ hiển thị form so sánh để bạn chọn thông tin muốn giữ lại.", "step1": "<PERSON><PERSON><PERSON> kh<PERSON> h<PERSON>ng", "step2": "<PERSON><PERSON><PERSON> liệu", "readyToMerge": "Sẵn sàng g<PERSON>p kh<PERSON>ch hàng", "mergeWarning": "<PERSON><PERSON> tác này không thể hoàn tác", "confirmMerge": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> kh<PERSON> hàng", "validation": {"selectSourceCustomer": "<PERSON><PERSON> lòng chọn khách hàng nguồn", "selectTargetCustomer": "<PERSON><PERSON> lòng chọn khách hàng đích", "nameMinLength": "<PERSON>ên kh<PERSON>ch hàng phải có ít nhất 2 ký tự", "phoneRequired": "<PERSON><PERSON> điện tho<PERSON><PERSON> không đư<PERSON>c để trống", "invalidEmail": "<PERSON><PERSON> h<PERSON> l<PERSON>", "differentCustomers": "<PERSON><PERSON><PERSON><PERSON> hàng đích phải khác khách hàng nguồn"}}, "bulkDeleteConfirmation": "Bạn có chắc chắn muốn xóa {{count}} khách hàng đã chọn?", "detail": {"generalInfo": "Th<PERSON>ng tin chung", "social": "Mạng xã hội", "customFields": "Trường tùy chỉnh", "orders": "<PERSON><PERSON><PERSON> hàng", "activities": "<PERSON><PERSON><PERSON> đ<PERSON>", "overview": "<PERSON><PERSON><PERSON> quan", "interactions": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> tác", "totalOrders": "<PERSON><PERSON><PERSON> đơn hàng", "totalRevenue": "<PERSON><PERSON>ng doanh thu", "totalSpent": "T<PERSON>ng chi tiêu", "averageOrderValue": "<PERSON><PERSON><PERSON> trị đơn hàng trung bình", "potentialScore": "<PERSON><PERSON><PERSON><PERSON> tiềm năng", "lastOrderDate": "<PERSON><PERSON><PERSON> hàng <PERSON>", "customerSince": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng từ", "interactionChannels": "<PERSON><PERSON><PERSON> t<PERSON> tác", "orderHistory": "<PERSON><PERSON><PERSON> sử mua hàng", "activityLog": "<PERSON><PERSON><PERSON><PERSON> ký hoạt động", "socialProfiles": "<PERSON><PERSON> sơ mạng xã hội", "customFieldValues": "<PERSON><PERSON><PERSON> trị trường tùy chỉnh", "noData": "Chưa có dữ liệu", "noOrders": "<PERSON><PERSON><PERSON> có đơn hàng nào", "noOrdersDesc": "<PERSON><PERSON><PERSON><PERSON> hàng này chưa thực hiện đơn hàng nào", "noActivities": "Chưa có hoạt động nào", "noActivitiesDesc": "<PERSON><PERSON><PERSON><PERSON> hàng này chưa có hoạt động nào đư<PERSON><PERSON> ghi nhận", "noInteractions": "<PERSON><PERSON><PERSON> có tương tác nào", "noInteractionsDesc": "<PERSON><PERSON><PERSON><PERSON> hàng này chưa có tương tác nào đư<PERSON><PERSON> ghi nhận", "allActivities": "<PERSON><PERSON><PERSON> cả hoạt động", "revenue": "<PERSON><PERSON>h thu", "topChannels": "Top kênh nhận tin nhắn", "topDevices": "Top thiết bị khách hàng sử dụng", "interactedFlows": "Flow đã tương tác", "interactedCampaigns": "Campaign đã tương tác", "orderList": "<PERSON><PERSON><PERSON> sử mua hàng", "orderCode": "<PERSON><PERSON> đơn hàng", "orderDate": "Ngày đặt", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "deliveryStatus": "<PERSON><PERSON><PERSON><PERSON> thái vận chuyển", "source": "<PERSON><PERSON><PERSON><PERSON>", "totalAmount": "<PERSON><PERSON><PERSON> tiền", "orderStatus": "<PERSON><PERSON><PERSON><PERSON> thái đơn hàng", "paymentStatus": "<PERSON>r<PERSON><PERSON> thái thanh toán", "shippingStatus": "<PERSON><PERSON><PERSON><PERSON> thái vận chuyển", "flowName": "Tên flow", "lastInteraction": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c cu<PERSON>i", "campaignName": "Tên campaign", "interactionType": "<PERSON><PERSON><PERSON> tư<PERSON> tác", "sent": "Đ<PERSON> gửi", "opened": "Đã nhận", "clicked": "Đã click", "platforms": "<PERSON><PERSON><PERSON> tảng", "channels": "k<PERSON><PERSON>", "channelStats": "<PERSON><PERSON><PERSON><PERSON> kê kênh", "allInteractions": "<PERSON><PERSON><PERSON> cả tương tác", "interactionSummary": "Tổng cộng {{total}} tương tác qua {{channels}} kênh, {{completed}} ho<PERSON>n thành", "noSocialProfiles": "Chưa có mạng xã hội", "noSocialProfilesDesc": "<PERSON><PERSON><PERSON><PERSON> hàng này chưa có thông tin mạng xã hội nào", "items": "<PERSON><PERSON><PERSON> p<PERSON>m", "itemsUnit": "s<PERSON><PERSON> p<PERSON>m"}, "activity": {"type": "<PERSON><PERSON><PERSON> ho<PERSON>t động", "date": "<PERSON><PERSON><PERSON>", "details": "<PERSON> ti<PERSON>", "moreDetails": "chi ti<PERSON><PERSON>c", "types": {"order": "<PERSON><PERSON><PERSON> hàng", "login": "<PERSON><PERSON><PERSON>", "support": "Hỗ trợ", "review": "Đánh giá"}}, "order": {"cancel": "<PERSON><PERSON><PERSON> đơn hàng"}, "overview": {"flowCount": "Số flow", "campaignCount": "Số campaign", "sequenceCount": "Số sequence", "interactions": "Số tương tác", "revenue": "<PERSON><PERSON>h thu", "flows": "Flow", "campaigns": "Campaign", "sequences": "Sequence", "noChannelData": "<PERSON><PERSON>a có dữ liệu kênh", "noDeviceData": "<PERSON><PERSON><PERSON> có dữ liệu thiết bị", "topChannels": "Top kênh nhận tin nhắn", "topDevices": "Top thiết bị khách hàng sử dụng", "sessions": "<PERSON><PERSON><PERSON><PERSON>", "messages": "tin nhắn"}, "social": {"platform": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "link": "Link", "editDescription": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin mạng xã hội của khách hàng", "save": "<PERSON><PERSON><PERSON>", "addProfiles": "<PERSON><PERSON><PERSON><PERSON> mạng xã hội"}, "interaction": {"type": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "channel": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "date": "<PERSON><PERSON><PERSON>", "types": {"email": "Email", "phone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "chat": "Cha<PERSON>", "social": "Mạng xã hội", "meeting": "<PERSON><PERSON><PERSON><PERSON>"}, "statuses": {"completed": "<PERSON><PERSON><PERSON> th<PERSON>", "pending": "<PERSON><PERSON> lý", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}}, "import": {"title": "Import kh<PERSON>ch hàng", "steps": {"upload": "<PERSON><PERSON><PERSON>", "productType": "<PERSON><PERSON><PERSON> lo<PERSON>i sản phẩm", "mapping": "<PERSON><PERSON> cột", "preview": "<PERSON><PERSON>", "importing": "Đang import"}, "productType": {"title": "<PERSON><PERSON><PERSON> lo<PERSON>i sản phẩm", "description": "<PERSON><PERSON> lòng chọn loại sản phẩm phù hợp để cấu hình import tối ưu"}, "upload": {"title": "<PERSON><PERSON><PERSON> lên file Excel", "description": "Chọn file Excel hoặc nhập URL để import danh sách khách hàng", "fromFile": "Từ file", "fromUrl": "Từ URL", "dragDrop": "Kéo thả file vào đây hoặc click để chọn", "supportedFormats": "Hỗ trợ: .xlsx, .xls, .csv (tối đa 10MB)", "selectFile": "<PERSON><PERSON><PERSON> file", "urlTitle": "Import từ URL", "urlPlaceholder": "Nhập URL file Excel...", "excelUrl": "URL file Excel", "sheetName": "Tên sheet", "sheetNamePlaceholder": "Nhập tên sheet (để trống sẽ lấy sheet đầu tiên)", "sheetNameHelp": "<PERSON><PERSON> trống sẽ sử dụng sheet đầu tiên trong file", "hasHeader": "File có dòng tiêu đề", "loadFromUrl": "<PERSON><PERSON><PERSON> từ URL", "dragDropTitle": "Kéo thả file vào đây", "dragDropDescription": "hoặc click để chọn file từ máy tính", "loading": "<PERSON><PERSON> tả<PERSON>..."}, "mapping": {"title": "<PERSON><PERSON> xạ cột dữ liệu", "description": "<PERSON><PERSON><PERSON> cột Excel tương ứng với từng trườ<PERSON> kh<PERSON>ch hàng", "columnMapping": "<PERSON><PERSON> cột", "selectField": "<PERSON><PERSON><PERSON> tr<PERSON>", "skipColumn": "Bỏ qua cột này", "requiredField": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "dataPreview": "<PERSON><PERSON> tr<PERSON><PERSON> dữ liệu", "previewData": "Hiển thị dữ liệu mẫu", "previewNote": "<PERSON><PERSON><PERSON> thị 5 dòng đầu tiên", "validationErrors": "Lỗi validation", "errors": {"requiredFieldMissing": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> b<PERSON><PERSON> buộ<PERSON>: {{field}}", "duplicateMapping": "<PERSON><PERSON> trường được ánh xạ trùng lặp"}}, "preview": {"title": "Xem trước dữ liệu import", "description": "<PERSON><PERSON>m tra dữ liệu trước khi import vào hệ thống", "totalRows": "Tổng số dòng", "validRows": "<PERSON><PERSON><PERSON> h<PERSON>p l<PERSON>", "invalidRows": "Dòng lỗi", "validationWarnings": "<PERSON><PERSON><PERSON> báo validation", "dataPreview": "<PERSON><PERSON> tr<PERSON><PERSON> dữ liệu", "showingFirst10": "<PERSON><PERSON><PERSON> thị 10 dòng đầu tiên", "importOptions": "T<PERSON>y <PERSON> import", "skipInvalidRows": "Bỏ qua các dòng lỗi", "sendWelcomeEmail": "Gửi email chào mừng", "startImport": "Bắt đầu import", "row": "Dòng"}, "progress": {"importing": "Đang import d<PERSON> liệu", "pleaseWait": "<PERSON><PERSON> lòng đợi trong khi hệ thống xử lý dữ liệu", "processing": "<PERSON><PERSON> lý", "imported": "Đã import", "errors": "Lỗi", "recentErrors": "Lỗi gần đây"}, "complete": {"title": "Import hoàn thành", "description": "Quá trình import đã hoàn thành thành công", "totalProcessed": "Tổng đã xử lý", "successfullyImported": "Import thành công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "errorDetails": "<PERSON> tiết lỗi", "nextSteps": "<PERSON><PERSON><PERSON><PERSON> tiế<PERSON> theo", "reviewCustomers": "<PERSON><PERSON> lại danh s<PERSON>ch kh<PERSON>ch hàng", "setupSegments": "<PERSON><PERSON><PERSON><PERSON> lập phân khúc khách hàng", "createCampaigns": "Tạo chiến dịch marketing", "viewCustomers": "<PERSON><PERSON> h<PERSON>ng", "successMessage": "Đã import thành công {{count}} khách hàng!"}, "validation": {"nameRequired": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng là bắt buộc", "invalidEmail": "<PERSON><PERSON> h<PERSON> l<PERSON>", "invalidPhone": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ"}, "errors": {"emptyFile": "File rỗng hoặc không có dữ liệu", "invalidFile": "File không hợp lệ hoặc bị lỗi", "readError": "Lỗi khi đọc file", "invalidFileType": "Định dạng file không được hỗ trợ", "fileTooLarge": "File quá lớn (tối đa 10MB)", "parseError": "Lỗi khi phân tích dữ liệu", "urlRequired": "URL là bắt buộc", "urlFetchError": "<PERSON><PERSON><PERSON><PERSON> thể tải file từ URL", "urlLoadError": "Lỗi khi tải file từ URL"}}}, "customField": {"configId": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> đ<PERSON>nh danh", "title": "Trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON><PERSON> lý các trường tùy chỉnh", "adminDescription": "<PERSON><PERSON><PERSON><PERSON> lý các trường tùy chỉnh của hệ thống", "add": "Thêm trường tùy chỉnh", "edit": "Chỉnh sửa trường tùy chỉnh", "addForm": "Thêm trường tùy chỉnh mới", "editForm": "Chỉnh sửa trường tùy chỉnh", "component": "<PERSON><PERSON><PERSON> thành phần", "components": {"input": "Ô nhập liệu", "textarea": "Ô văn bản", "select": "<PERSON><PERSON>n", "checkbox": "<PERSON><PERSON><PERSON>", "radio": "Nút radio", "date": "<PERSON><PERSON><PERSON>", "number": "Số", "file": "<PERSON><PERSON>p tin", "multiSelect": "<PERSON><PERSON><PERSON>"}, "type": "<PERSON><PERSON><PERSON> dữ liệu", "type.string": "<PERSON><PERSON><PERSON>", "type.number": "Số", "type.boolean": "Có/<PERSON>hông", "type.date": "<PERSON><PERSON><PERSON>", "type.object": "<PERSON><PERSON><PERSON>", "type.array": "<PERSON><PERSON><PERSON>", "types": {"text": "<PERSON><PERSON><PERSON>", "number": "Số", "boolean": "Có/<PERSON>hông", "date": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "object": "<PERSON><PERSON><PERSON>", "array": "<PERSON><PERSON><PERSON>", "string": "<PERSON><PERSON><PERSON>"}, "name": "<PERSON><PERSON><PERSON> tr<PERSON>", "label": "<PERSON><PERSON>ã<PERSON>", "placeholder": "Placeholder", "defaultValue": "<PERSON><PERSON><PERSON> trị mặc định", "options": "<PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "validation": {"minLength": "<PERSON><PERSON> dài tối thiểu", "maxLength": "<PERSON><PERSON> dài tối đa", "pattern": "Mẫu kiểm tra", "min": "<PERSON><PERSON><PERSON> trị tối thiểu", "max": "<PERSON><PERSON><PERSON> trị tối đa"}, "form": {"componentRequired": "<PERSON><PERSON> lòng chọn loại thành phần", "labelRequired": "<PERSON><PERSON> lòng nh<PERSON><PERSON> nh<PERSON>n", "typeRequired": "<PERSON><PERSON> lòng chọn kiểu dữ liệu", "idRequired": "<PERSON><PERSON> lòng nhập tên trường định danh", "labelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>n hiển thị", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho trường này", "placeholderPlaceholder": "<PERSON>h<PERSON>p placeholder", "defaultValuePlaceholder": "<PERSON><PERSON><PERSON><PERSON> giá trị mặc định", "arrayDefaultPlaceholder": "<PERSON><PERSON><PERSON><PERSON> giá trị và nhấn Enter để thêm", "optionsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> các tù<PERSON>, ph<PERSON> cách bằng dấu phẩy hoặc định dạng JSON", "selectOptionsPlaceholder": "<PERSON>h<PERSON>p giá trị theo cấu trúc: Name|Value, Mỗi cặp giá trị trên 1 dòng. VD:\na|1\nb|2", "booleanDefaultPlaceholder": "<PERSON><PERSON><PERSON> giá trị mặc định", "dateDefaultPlaceholder": "<PERSON><PERSON><PERSON> ngày mặc định", "description": "<PERSON><PERSON>", "showAdvancedSettings": "<PERSON><PERSON>n thị cài đặt nâng cao", "labelTagRequired": "<PERSON><PERSON> lòng thêm ít nhất một nhãn", "fieldIdLabel": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> đ<PERSON>nh danh", "fieldIdPlaceholder": "text-input-001", "displayNameLabel": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> hiển thị", "displayNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên hiển thị cho trường này", "displayNameRequired": "<PERSON><PERSON> lòng nhập tên trường hiển thị", "labelInputPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>n và nhấn Enter", "tagsCount": "nh<PERSON>n đã thêm", "patternSuggestions": "Gợi ý pattern phổ biến:", "defaultValue": "<PERSON><PERSON><PERSON> trị mặc định", "minLength": "<PERSON><PERSON> dài tối thiểu", "maxLength": "<PERSON><PERSON> dài tối đa", "pattern": "Mẫu kiểm tra", "options": "<PERSON><PERSON><PERSON>", "min": "<PERSON><PERSON><PERSON> trị tối thiểu", "max": "<PERSON><PERSON><PERSON> trị tối đa", "placeholder": "Placeholder", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "labels": "<PERSON><PERSON>ã<PERSON>"}, "createSuccess": "Tạo trường tùy chỉnh thành công", "createError": "Lỗi khi tạo trường tùy chỉnh", "updateSuccess": "<PERSON><PERSON><PERSON> nhật trường tùy chỉnh thành công", "updateError": "Lỗi khi cập nhật trường tùy chỉnh", "deleteSuccess": "<PERSON>óa trường tùy chỉnh thành công", "deleteError": "Lỗi khi xóa trường tùy chỉnh", "loadError": "Lỗi khi tải trường tùy chỉnh", "booleanValues": {"true": "<PERSON><PERSON>", "false": "K<PERSON>ô<PERSON>"}, "patterns": {"email": "Email", "phoneVN": "Số điện thoại VN", "phoneIntl": "<PERSON><PERSON> điện thoại quốc tế", "postalCodeVN": "<PERSON><PERSON> b<PERSON><PERSON> ch<PERSON> VN", "lettersOnly": "Chỉ chữ cái", "numbersOnly": "Chỉ số", "alphanumeric": "Chữ và số", "noSpecialChars": "<PERSON><PERSON><PERSON><PERSON> có ký tự đặc biệt", "url": "URL", "ipv4": "IPv4", "strongPassword": "<PERSON><PERSON><PERSON><PERSON> mạnh", "vietnameseName": "<PERSON><PERSON><PERSON> (c<PERSON>)", "studentId": "<PERSON><PERSON> sinh viên", "nationalId": "CMND/CCCD", "taxCode": "<PERSON><PERSON> số thuế", "dateFormat": "<PERSON>ày (dd/mm/yyyy)", "timeFormat": "Giờ (hh:mm)", "hexColor": "Hex color", "base64": "Base64", "uuid": "UUID", "filename": "Tên file", "urlSlug": "Slug URL", "variableName": "<PERSON><PERSON><PERSON>", "creditCard": "Số thẻ tín dụng", "qrCode": "Mã QR", "gpsCoordinate": "Tọa độ GPS", "rgbColor": "Mã màu RGB", "domain": "<PERSON><PERSON><PERSON>", "decimal": "<PERSON><PERSON> thập phân", "barcode": "Mã vạch"}, "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa trường tùy chỉnh này?", "bulkDeleteSuccess": "<PERSON><PERSON> xóa thành công {{count}} trường tùy chỉnh", "bulkDeleteError": "C<PERSON> lỗi xảy ra khi xóa trường tùy chỉnh", "bulkDeleteConfirmMessage": "Bạn có chắc chắn muốn xóa {{count}} trường tùy chỉnh đã chọn?", "selectedItems": "<PERSON><PERSON> chọn {{count}} mục", "totalFields": "Tổng số trường tùy chỉnh", "manage": "<PERSON><PERSON><PERSON><PERSON> lý trường tùy chỉnh"}, "customGroupForm": {"title": "Nhóm trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON><PERSON> lý các nhóm trường tùy chỉnh", "addForm": "Thêm nhóm trường tùy chỉnh", "editForm": "Chỉnh sửa nhóm trường tùy chỉnh", "createSuccess": "<PERSON><PERSON><PERSON> nhóm trường tùy chỉnh thành công", "createError": "Lỗi khi tạo nhóm trường tùy chỉnh", "updateSuccess": "<PERSON><PERSON><PERSON> nhật nhóm trường tùy chỉnh thành công", "updateError": "Lỗi khi cập nhật nhóm trường tùy chỉnh", "deleteSuccess": "<PERSON><PERSON><PERSON> nhóm trường tùy chỉnh thành công", "deleteError": "Lỗi khi xóa nhóm trường tùy chỉnh", "loadError": "Lỗi khi tải nhóm trường tùy chỉnh", "form": {"label": "<PERSON><PERSON><PERSON>", "labelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên nhóm trường tùy chỉnh", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho nhóm trường tùy chỉnh", "fieldCount": "Số trường", "fields": "tr<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "subtitle": "Tạo và quản lý nhóm các trường tùy chỉnh để sử dụng trong sản phẩm", "validation": {"labelRequired": "<PERSON><PERSON><PERSON> n<PERSON> là bắ<PERSON> bu<PERSON>c", "labelMinLength": "Tên nhóm ph<PERSON>i có ít nhất 2 ký tự", "labelMaxLength": "<PERSON>ên nhóm không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 255 ký tự", "descriptionMaxLength": "<PERSON><PERSON> tả không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 500 ký tự"}}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "draft": "<PERSON><PERSON><PERSON>"}}, "order": {"title": "<PERSON><PERSON><PERSON> hàng", "description": "<PERSON><PERSON><PERSON><PERSON> lý đơn hàng", "adminDescription": "<PERSON><PERSON><PERSON><PERSON> lý và theo dõi đơn hàng của người dùng", "createOrder": "<PERSON><PERSON><PERSON> đơn hàng mới", "editOrder": "Chỉnh sửa đơn hàng", "viewOrder": "<PERSON>em chi tiết đơn hàng", "orderNumber": "<PERSON><PERSON> đơn hàng", "customerInfo": "Thông tin khách hàng", "customerName": "<PERSON><PERSON><PERSON> h<PERSON>ng", "customerEmail": "Email", "customerPhone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "customerAddress": "Địa chỉ", "items": "<PERSON><PERSON><PERSON> phẩm trong đơn hàng", "noItems": "<PERSON><PERSON><PERSON><PERSON> có sản phẩm nào trong đơn hàng", "quantity": "Số lượng", "totalAmount": "<PERSON><PERSON><PERSON> tiền", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "paymentMethods": {"cash": "Tiền mặt", "creditCard": "Thẻ tín dụng", "bankTransfer": "<PERSON><PERSON><PERSON><PERSON>", "digitalWallet": "<PERSON><PERSON> điện tử"}, "status": {"title": "<PERSON><PERSON><PERSON><PERSON> thái đơn hàng", "pending": "Chờ xử lý", "processing": "<PERSON><PERSON> lý", "confirmed": "<PERSON><PERSON> x<PERSON>c <PERSON>n", "shipped": "<PERSON><PERSON> gửi hàng", "delivered": "Đ<PERSON> giao hàng", "cancelled": "<PERSON><PERSON> hủy", "completed": "<PERSON><PERSON><PERSON> th<PERSON>"}, "paymentStatus": {"title": "<PERSON>r<PERSON><PERSON> thái thanh toán", "paid": "<PERSON><PERSON> thanh toán", "pending": "Chờ thanh toán", "unpaid": "<PERSON><PERSON><PERSON> to<PERSON>", "failed": "<PERSON><PERSON> to<PERSON> thất bại", "refunded": "<PERSON><PERSON> hoàn tiền", "partiallyPaid": "<PERSON><PERSON> <PERSON><PERSON> một ph<PERSON>n"}, "payment": {"method": {"cash": "Tiền mặt", "banking": "Chuy<PERSON>n kho<PERSON>n ngân hàng", "creditCard": "Thẻ tín dụng", "eWallet": "<PERSON><PERSON> điện tử"}}, "shippingStatus": {"title": "<PERSON><PERSON><PERSON><PERSON> thái vận chuyển", "pending": "Chờ xử lý", "preparing": "<PERSON><PERSON> bị", "shipped": "<PERSON><PERSON> gửi hàng", "inTransit": "<PERSON><PERSON> vận chuy<PERSON>n", "sorting": "Đang phân loại", "delivered": "Đ<PERSON> giao hàng", "deliveryFailed": "<PERSON><PERSON><PERSON> hàng thất b<PERSON>i", "returning": "<PERSON><PERSON> trả lại", "cancelled": "<PERSON><PERSON> hủy"}, "totalPrice": "<PERSON><PERSON><PERSON><PERSON> tiền", "price": "Đơn giá", "orderSummary": "<PERSON><PERSON><PERSON> tắt đơn hàng", "additionalInfo": "<PERSON><PERSON><PERSON><PERSON> tin bổ sung", "shippingInfo": "Thông tin vận chuyển", "paymentInfo": "Thông tin thanh toán", "selectCustomer": "<PERSON><PERSON><PERSON> kh<PERSON> h<PERSON>ng", "createNewCustomer": "<PERSON><PERSON><PERSON> k<PERSON>ch hàng mới", "searchCustomerPlaceholder": "<PERSON><PERSON><PERSON> kiếm kh<PERSON>ch hàng...", "searchResults": "<PERSON><PERSON><PERSON> qu<PERSON> tìm kiếm", "noCustomersFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy khách hàng", "selectProducts": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "searchProductPlaceholder": "<PERSON><PERSON><PERSON> kiếm sản phẩm...", "selectedProducts": "<PERSON><PERSON><PERSON> phẩm đã chọn", "noProductsSelected": "<PERSON><PERSON><PERSON> chọn sản phẩm nào", "noProductsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm", "searchToAddProducts": "<PERSON><PERSON><PERSON> kiếm để thêm sản phẩm vào đơn hàng", "print": "In đơn hàng", "uploading": "<PERSON><PERSON> tải lên...", "summaryNote": "<PERSON><PERSON> lòng kiểm tra kỹ thông tin trước khi tạo đơn hàng", "createSuccessDescription": "Đơn hàng đã được tạo thành công và sẽ được xử lý sớm nhất", "createErrorDescription": "<PERSON><PERSON> lỗi xảy ra trong quá trình tạo đơn hàng, vui lòng thử lại", "steps": {"customer": "<PERSON><PERSON><PERSON><PERSON>", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "delivery": "<PERSON><PERSON><PERSON>", "shipping": "<PERSON><PERSON><PERSON> ch<PERSON>", "payment": "<PERSON><PERSON> toán", "review": "<PERSON><PERSON>"}, "deliveryConfiguration": "<PERSON><PERSON><PERSON> hình giao hàng", "products": "s<PERSON><PERSON> p<PERSON>m", "physicalShippingTitle": "<PERSON><PERSON><PERSON> chuyển sản phẩm vật lý", "digitalDeliveryTitle": "<PERSON><PERSON><PERSON> h<PERSON> sản phẩm số", "serviceDeliveryTitle": "Thông tin dịch vụ", "eventDeliveryTitle": "Thông tin sự kiện", "selectProductsFirstDescription": "<PERSON><PERSON> lòng chọn sản phẩm để cấu hình giao hàng", "deliveryAddress": "Đ<PERSON>a chỉ giao hàng", "useExistingAddress": "Sử dụng địa chỉ có sẵn", "addNewAddress": "<PERSON><PERSON><PERSON><PERSON> địa chỉ mới", "existingAddressNotImplemented": "<PERSON><PERSON><PERSON> năng chọn địa chỉ có sẵn chưa được triển khai", "pleaseUseNewAddress": "<PERSON><PERSON> lòng sử dụng địa chỉ mới", "recipientName": "<PERSON><PERSON><PERSON>n", "recipientNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON> nhận", "recipientPhone": "<PERSON><PERSON> điện tho<PERSON>i người nhận", "recipientPhonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại người nhận", "address": "Địa chỉ", "addressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ chi tiết", "province": "Tỉnh/Thành phố", "selectProvince": "Chọn tỉnh/thành phố", "district": "Quận/Huyện", "selectDistrict": "<PERSON><PERSON><PERSON> quận/huyện", "ward": "Phường/Xã", "selectWard": "Chọn phường/xã", "shippingCalculation": "<PERSON><PERSON><PERSON> phí vận chuyển", "manualShippingNote": "Bạn có thể nhập thông tin vận chuyển thủ công hoặc sử dụng tính năng tự động ở trên", "shipping": {"self": "<PERSON>ự vận chuyển", "ghn": "<PERSON><PERSON><PERSON>", "ghtk": "<PERSON><PERSON><PERSON>"}, "shippingService": "<PERSON><PERSON><PERSON> v<PERSON> vận chuy<PERSON>n", "shippingServicePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên dịch vụ vận chuyển", "selectProductsFirst": "<PERSON><PERSON> lòng chọn sản phẩm trước", "multipleProductTypes": "<PERSON><PERSON><PERSON> hàng có nhiều loại sản phẩm", "multipleProductTypesDescription": "Đơn hàng này chứa nhiều loại sản phẩm khác <PERSON>hau, vui lòng điền thông tin giao hàng cho từng loại.", "deliverySummary": "<PERSON><PERSON><PERSON> tắt giao hàng", "physicalShippingRequired": "<PERSON>ần vận chuyển sản phẩm vật lý", "digitalDeliveryRequired": "<PERSON><PERSON><PERSON> giao hàng sản phẩm số", "serviceDeliveryRequired": "<PERSON><PERSON><PERSON> thông tin dịch vụ", "eventDeliveryRequired": "<PERSON><PERSON><PERSON> thông tin sự kiện", "addTag": "<PERSON><PERSON><PERSON><PERSON>", "removeTag": "<PERSON><PERSON><PERSON>", "form": {"customerNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên kh<PERSON>ch hàng", "customerEmailPlaceholder": "Nhập email kh<PERSON>ch hàng", "customerPhonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện tho<PERSON><PERSON> kh<PERSON>ch hàng", "customerAddressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ khách hàng", "notesPlaceholder": "<PERSON><PERSON><PERSON><PERSON> ghi chú cho đơn hàng", "tagsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>n và nhấn Enter"}, "createSuccess": "<PERSON><PERSON><PERSON> đơn hàng thành công", "createError": "Lỗi khi tạo đơn hàng", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t đơn hàng thành công", "updateError": "Lỗi khi cập nhật đơn hàng", "deleteSuccess": "<PERSON><PERSON><PERSON> đơn hàng thành công", "deleteError": "Lỗi khi xóa đơn hàng", "bulkDeleteSuccess": "Xóa {{count}} đ<PERSON>n hàng thành công", "bulkDeleteError": "Lỗi khi xóa nhiều đơn hàng", "selectToDelete": "<PERSON><PERSON> lòng chọn ít nhất một đơn hàng để xóa", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa đơn hàng này?", "estimatedDelivery": "<PERSON><PERSON><PERSON><PERSON> gian giao hàng dự kiến", "tax": "<PERSON><PERSON><PERSON>", "printInProgress": "<PERSON><PERSON> chuẩn bị in đơn hàng...", "tracking": "<PERSON> đ<PERSON> hàng", "carrier": "Đơn vị vận chuyển", "trackingNumber": "<PERSON>ã vận đơn", "currentStatus": "<PERSON><PERSON><PERSON><PERSON> thái hiện tại", "loadingTracking": "<PERSON><PERSON> tải thông tin theo dõi...", "trackingError": "Lỗi khi tải thông tin theo dõi", "trackingErrorGeneric": "<PERSON><PERSON> xảy ra lỗi khi tải thông tin tracking", "trackingTimeline": "<PERSON><PERSON><PERSON> trình vận chuy<PERSON>n", "detailedTracking": "Th<PERSON>ng tin theo dõi chi tiết", "noTrackingInfo": "<PERSON><PERSON><PERSON> có thông tin theo dõi", "printSuccess": "In đơn hàng thành công", "printSuccessDescription": "Đơn hàng đã đư<PERSON><PERSON> chuẩn bị để in", "printError": "Lỗi in đơn hàng", "printErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể in đơn hàng. <PERSON><PERSON> lòng thử lại.", "batchPrintSuccess": "In hàng loạt thành công", "batchPrintSuccessDescription": "Đã in thành công {{count}} đơn hàng", "batchPrintError": "Lỗi in hàng loạt", "batchPrintErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể in hàng loạt đơn hàng. <PERSON><PERSON> lòng thử lại.", "fetchPrintableOrdersError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách đơn hàng có thể in", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy đơn hàng", "notFoundDescription": "Đơn hàng không tồn tại hoặc bạn không có quyền xem.", "backToList": "Quay lại danh s<PERSON>ch", "loading": "<PERSON><PERSON> tải chi tiết đơn hàng...", "createdAt": "<PERSON><PERSON><PERSON>", "productInfo": "Thông tin sản phẩm", "subtotal": "<PERSON><PERSON><PERSON>", "shippingFee": "<PERSON><PERSON> vận chuy<PERSON>n", "total": "<PERSON><PERSON><PERSON> cộng", "recipient": "<PERSON><PERSON><PERSON><PERSON>n", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "shippingNote": "<PERSON><PERSON> chú vận chuyển", "selectCarrier": "<PERSON><PERSON><PERSON> đơn vị vận chuyển", "loadingCarriers": "<PERSON><PERSON> tải danh sách nhà vận chuyển...", "carriersLoadError": "Lỗi tải danh sách nhà vận chuyển", "noCarriersConfigured": "<PERSON><PERSON><PERSON> có nhà vận chuyển nào đ<PERSON><PERSON><PERSON> cấu hình", "noCarriersDescription": "<PERSON><PERSON> lòng cấu hình ít nhất một nhà vận chuyển để sử dụng tính năng tính phí vận chuyển tự động.", "configureCarriers": "<PERSON><PERSON><PERSON> hình nhà vận chuyển", "calculatingShipping": "<PERSON><PERSON> t<PERSON>h phí vận chuyển...", "recalculate": "<PERSON><PERSON><PERSON> lại", "carrierSelected": "Đơn vị vận chuyển đã chọn", "noCarriersAvailable": "<PERSON><PERSON><PERSON><PERSON> có đơn vị vận chuyển khả dụng", "checkDeliveryAddress": "<PERSON><PERSON> lòng kiểm tra lại địa chỉ giao hàng", "serviceType": "<PERSON><PERSON><PERSON> v<PERSON>", "estimatedTime": "<PERSON><PERSON><PERSON><PERSON> gian", "notAvailable": "<PERSON><PERSON><PERSON><PERSON> khả dụng", "apiConnectionError": "Lỗi kết nối API", "shippingFeeCalculation": "<PERSON><PERSON><PERSON> phí vận chuyển", "calculatingShippingFee": "<PERSON><PERSON> t<PERSON>h phí vận chuyển...", "shippingFeeCalculated": "<PERSON><PERSON> vận chuyển đã đ<PERSON><PERSON><PERSON> t<PERSON>h", "calculationError": "Lỗi tính phí vận chuyển", "estimatedDeliveryTime": "<PERSON><PERSON><PERSON><PERSON> gian giao hàng", "enhancedOrderForm": {"title": "<PERSON><PERSON><PERSON> đơn hàng mới", "editTitle": "Chỉnh sửa đơn hàng", "stepTitles": {"customer": "Thông tin khách hàng", "products": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "delivery": "<PERSON><PERSON><PERSON> hình giao hàng", "payment": "Thông tin thanh toán", "review": "<PERSON><PERSON> lại đơn hàng"}, "navigation": {"previous": "Quay lại", "continue": "<PERSON><PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "cancel": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON> đơn hàng", "update": "<PERSON><PERSON><PERSON> nh<PERSON>t đơn hàng"}, "validation": {"customerRequired": "<PERSON><PERSON> lòng chọn kh<PERSON>ch hàng", "productsRequired": "<PERSON><PERSON> lòng chọn ít nhất một sản phẩm", "paymentMethodRequired": "<PERSON><PERSON> lòng chọn ph<PERSON><PERSON><PERSON> thức thanh toán"}, "summary": {"title": "<PERSON><PERSON><PERSON> tắt đơn hàng", "customer": "<PERSON><PERSON><PERSON><PERSON>", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "subtotal": "<PERSON><PERSON><PERSON>", "shipping": "<PERSON><PERSON> vận chuy<PERSON>n", "total": "<PERSON><PERSON><PERSON> cộng", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "notes": "<PERSON><PERSON><PERSON>"}}, "digitalDelivery": {"title": "<PERSON><PERSON><PERSON> h<PERSON> sản phẩm số", "method": "<PERSON><PERSON><PERSON><PERSON> thức giao hàng", "recipient": "<PERSON><PERSON><PERSON><PERSON>n", "message": "<PERSON>", "scheduledDelivery": "<PERSON><PERSON><PERSON><PERSON> gian giao hàng", "emailAddress": "Đ<PERSON>a chỉ email", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "telegramUsername": "<PERSON><PERSON><PERSON> dùng Telegram", "downloadEmail": "Email nhận link tải", "emailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email", "phonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "telegramPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON>i dùng Telegram", "downloadLinkPlaceholder": "Nhập email để nhận link tải", "recipientPlaceholder": "<PERSON><PERSON><PERSON><PERSON> thông tin người nhận", "messagePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tin nhắn kèm theo (tù<PERSON> chọn)", "methods": {"email": "Email", "sms": "SMS", "zalo": "<PERSON><PERSON>", "telegram": "Telegram", "whatsapp": "WhatsApp", "downloadLink": "<PERSON> t<PERSON>"}}, "serviceDelivery": {"title": "Thông tin dịch vụ", "serviceDate": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> hi<PERSON>n d<PERSON>ch vụ", "serviceDatePlaceholder": "<PERSON><PERSON><PERSON> ngày thực hiện dịch vụ", "serviceLocation": "<PERSON><PERSON><PERSON> đi<PERSON><PERSON> thự<PERSON> hi<PERSON>n", "serviceLocationPlaceholder": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> điểm thực hiện dịch vụ", "contactPerson": "<PERSON><PERSON><PERSON><PERSON> li<PERSON>n hệ", "contactPersonPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON>i liên hệ", "contactPhone": "<PERSON><PERSON> điện thoại liên hệ", "contactPhonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại liên hệ", "serviceNotes": "<PERSON><PERSON> chú d<PERSON>ch vụ", "serviceNotesPlaceholder": "<PERSON><PERSON><PERSON><PERSON> ghi chú về dịch vụ", "instructions": "Hướng dẫn", "instructionsText": "<PERSON><PERSON> lòng cung cấp đầy đủ thông tin để chúng tôi có thể liên hệ và thực hiện dịch vụ tốt nhất."}, "eventDelivery": {"title": "Thông tin sự kiện", "eventInfo": "Thông tin sự kiện", "eventDate": "<PERSON><PERSON><PERSON> kiện", "eventDatePlaceholder": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> diễn ra sự kiện", "eventLocation": "<PERSON><PERSON><PERSON> điểm sự kiện", "eventLocationPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên địa điểm sự kiện", "eventAddress": "Địa chỉ sự kiện", "eventAddressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ chi tiết của sự kiện", "ticketDelivery": "Giao vé", "ticketDeliveryMethod": "<PERSON><PERSON><PERSON><PERSON> thức giao vé", "ticketRecipient": "Ngư<PERSON><PERSON> n<PERSON>n vé", "ticketMethods": {"email": "<PERSON><PERSON><PERSON> qua Email", "sms": "<PERSON><PERSON><PERSON> qua SMS", "pickup": "<PERSON><PERSON><PERSON><PERSON> tại chỗ", "mail": "<PERSON><PERSON><PERSON> qua bưu đi<PERSON>n"}, "attendeeInfo": "Th<PERSON>ng tin người tham dự", "attendeeName": "<PERSON><PERSON><PERSON> tham dự", "attendeeNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON> tham dự", "attendeePhone": "<PERSON><PERSON> điện thoại người tham dự", "attendeePhonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại người tham dự", "attendeeEmail": "<PERSON><PERSON> tham dự", "attendeeEmailPlaceholder": "<PERSON><PERSON>ập email ngư<PERSON>i tham dự", "eventNotes": "<PERSON><PERSON> chú sự kiện", "eventNotesPlaceholder": "<PERSON><PERSON><PERSON><PERSON> ghi chú về sự kiện", "emailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email", "phonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "recipientPlaceholder": "<PERSON><PERSON><PERSON><PERSON> thông tin người nhận", "instructions": "Hướng dẫn", "instructionsText": "<PERSON><PERSON> lòng cung cấp đầy đủ thông tin để chúng tôi có thể gửi vé và thông báo về sự kiện."}}, "conversion": {"title": "<PERSON><PERSON><PERSON><PERSON> đổi", "description": "<PERSON> và quản lý các chuyển đổi", "adminDescription": "<PERSON> và quản lý các bản ghi chuyển đổi", "totalConversions": "Tổng số chuyển đổi", "manage": "<PERSON><PERSON><PERSON><PERSON> lý chuyển đổi", "id": "ID", "customerId": "ID khách hàng", "userId": "ID người dùng", "type": "<PERSON><PERSON><PERSON> chuyển đổi", "name": "<PERSON><PERSON><PERSON>", "source": "<PERSON><PERSON><PERSON><PERSON>", "destination": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON> trị", "date": "<PERSON><PERSON><PERSON>", "status": {"completed": "<PERSON><PERSON><PERSON> th<PERSON>", "pending": "<PERSON><PERSON> lý", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "sourceOptions": {"website": "Website", "social_media": "Mạng xã hội", "event": "<PERSON><PERSON> kiện"}}, "warehouseCustomField": {"title": "Trường tùy chỉnh kho", "description": "<PERSON><PERSON><PERSON><PERSON> lý các trường tùy chỉnh của kho"}, "report": {"charts": {"multiLine": {"title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> kinh doanh"}}}, "productType": {"chart": {"title": "<PERSON><PERSON><PERSON><PERSON> đồ loại sản phẩm", "totalProducts": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "categories": "lo<PERSON><PERSON>", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "mostPopular": "<PERSON><PERSON> biến nhất", "period": "<PERSON><PERSON><PERSON><PERSON> gian", "allTime": "<PERSON><PERSON><PERSON> c<PERSON>"}}, "inventory": {"title": "<PERSON><PERSON><PERSON><PERSON> lý kho", "description": "<PERSON><PERSON><PERSON><PERSON> lý hàng tồn kho và nhập xuất kho", "totalItems": "Tổng số mặt hàng", "totalProducts": "Tổng số sản phẩm", "manage": "<PERSON><PERSON><PERSON><PERSON> lý kho", "currentQuantity": "S<PERSON> l<PERSON> hiện tại", "availableQuantity": "<PERSON><PERSON> lượng có sẵn", "reservedQuantity": "Số lượng đã đặt trước", "defectiveQuantity": "S<PERSON> lượng hỏng", "totalQuantity": "Tổng số lượng", "updateQuantity": "<PERSON><PERSON><PERSON> nh<PERSON>t số lư<PERSON>", "addProduct": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "noProducts": "<PERSON><PERSON><PERSON> có sản phẩm nào trong kho", "noProductsDescription": "<PERSON>ho này hiện tại chưa có sản phẩm nào. <PERSON><PERSON><PERSON> thêm sản phẩm vào kho để bắt đầu quản lý.", "customerProducts": "<PERSON><PERSON><PERSON> ph<PERSON>m kh<PERSON>ch hàng", "createFirst": "<PERSON><PERSON><PERSON> sản phẩm đầu tiên", "warehouseProducts": "<PERSON><PERSON> s<PERSON>ch sản phẩm trong kho", "noWarehouseProducts": "<PERSON><PERSON><PERSON> có sản phẩm nào trong kho", "noWarehouseProductsDescription": "<PERSON>ho này hiện tại chưa có sản phẩm nào. <PERSON><PERSON><PERSON> thêm sản phẩm vào kho để bắt đầu quản lý.", "status": {"inStock": "<PERSON><PERSON><PERSON> hàng", "lowStock": "<PERSON><PERSON><PERSON>", "outOfStock": "<PERSON><PERSON><PERSON>"}, "confirmBulkDeleteTitle": "<PERSON><PERSON><PERSON> nh<PERSON>n x<PERSON>a tồn kho", "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa {{count}} sản phẩm tồn kho đã chọn? Hành động này không thể hoàn tác.", "bulkDeleteSuccess": "<PERSON><PERSON><PERSON> {{count}} sản phẩm tồn kho thành công", "bulkDeleteError": "Lỗi khi xóa sản phẩm tồn kho", "selectToDelete": "<PERSON><PERSON> lòng chọn ít nhất một sản phẩm để xóa"}, "product": {"title": "<PERSON><PERSON><PERSON> p<PERSON>m", "description": "<PERSON><PERSON><PERSON><PERSON> lý danh sách sản phẩm", "productContent": "<PERSON><PERSON><PERSON> dung sản phẩm", "adminDescription": "<PERSON><PERSON><PERSON><PERSON> lý và theo dõi sản phẩm của người dùng", "totalProducts": "Tổng số sản phẩm", "manage": "<PERSON><PERSON><PERSON><PERSON> lý sản phẩm", "name": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "image": "Ảnh", "tags": "Thẻ", "price": "Giá", "createProduct": "<PERSON><PERSON><PERSON> s<PERSON>n ph<PERSON>m", "editProduct": "Chỉnh s<PERSON>a sản phẩm", "productList": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m", "productDetails": "<PERSON> tiết sản phẩm", "productInfo": "Thông tin sản phẩm", "productAttributes": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> sản ph<PERSON>m", "productImages": "<PERSON><PERSON><PERSON> sản phẩm", "listPrice": "<PERSON><PERSON><PERSON>", "enterListPrice": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> y<PERSON>", "enterSalePrice": "<PERSON><PERSON><PERSON><PERSON> g<PERSON> bán", "salePrice": "<PERSON><PERSON><PERSON> b<PERSON>", "currency": "Đơn vị tiền tệ", "priceDescription": "<PERSON>ô tả giá", "quantity": "Số lượng", "quantityNotManaged": "<PERSON><PERSON><PERSON><PERSON> quản lý số lượng", "outOfStock": "<PERSON><PERSON><PERSON>", "createSuccess": "<PERSON><PERSON><PERSON> sản phẩm thành công", "createError": "Lỗi khi tạo sản phẩm", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm thành công", "updateError": "Lỗi khi cập nh<PERSON>t sản phẩm", "deleteSuccess": "<PERSON><PERSON><PERSON> sản phẩm thành công", "deleteError": "Lỗi khi xóa sản phẩm", "bulkDeleteSuccess": "<PERSON><PERSON><PERSON> {{count}} sản phẩm thành công", "bulkDeleteError": "Lỗi khi xóa nhiều sản phẩm", "selectToDelete": "<PERSON><PERSON> lòng chọn ít nhất một sản phẩm để xóa", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa sản phẩm này?", "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa {{count}} sản phẩm đã chọn?", "priceType": {"title": "Loại giá", "hasPrice": "<PERSON><PERSON> giá cố định", "stringPrice": "<PERSON><PERSON><PERSON> dạng mô tả", "noPrice": "Không có giá"}, "productType": {"title": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "physical": "<PERSON><PERSON><PERSON>", "digital": "Số", "service": "<PERSON><PERSON><PERSON> v<PERSON>", "event": "<PERSON><PERSON> kiện", "combo": "Combo"}, "types": {"physical": {"title": "<PERSON><PERSON><PERSON> ph<PERSON>m vật lý", "description": "<PERSON><PERSON><PERSON> phẩm có thể sờ đ<PERSON>, cần vận chuyển", "examples": "Ví dụ: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> tử, <PERSON><PERSON><PERSON>, <PERSON><PERSON> gia dụng"}, "digital": {"title": "<PERSON><PERSON><PERSON> ph<PERSON>m số", "description": "File, kh<PERSON><PERSON> học, ebook, ph<PERSON>n mềm", "examples": "<PERSON><PERSON> dụ: Ebook, <PERSON><PERSON><PERSON><PERSON> online, Template, <PERSON><PERSON><PERSON> mềm"}, "service": {"title": "<PERSON><PERSON><PERSON> v<PERSON>", "description": "<PERSON><PERSON>, là<PERSON> đẹp, b<PERSON><PERSON> trì, l<PERSON><PERSON> đặt", "examples": "Ví dụ: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> trì, Hỗ trợ kỹ thuật"}, "event": {"title": "<PERSON><PERSON> kiện", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, bu<PERSON><PERSON> bi<PERSON><PERSON>n", "examples": "Ví dụ: <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> t<PERSON>, <PERSON><PERSON>n"}, "combo": {"title": "Combo", "description": "<PERSON><PERSON><PERSON> sản phẩm kết hợp nhi<PERSON>u lo<PERSON>i"}}, "typeSelector": {"title": "<PERSON><PERSON><PERSON> lo<PERSON>i sản phẩm"}, "status": {"active": "<PERSON><PERSON> bán", "inactive": "<PERSON><PERSON><PERSON> b<PERSON>", "outOfStock": "<PERSON><PERSON><PERSON>", "draft": "<PERSON><PERSON><PERSON>"}, "statusTitle": "<PERSON><PERSON><PERSON><PERSON> thái", "statusPending": "Chờ xử lý", "statusApproved": "Đ<PERSON>", "statusRejected": "<PERSON><PERSON> chối", "createSimpleTitle": "<PERSON><PERSON><PERSON> sản phẩm đơn giản", "fields": {"name": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "price": "Giá", "priceType": "Loại giá", "priceTypes": {"yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "regularPrice": "<PERSON><PERSON><PERSON>", "salePrice": "<PERSON><PERSON><PERSON> mãi", "priceNote": "<PERSON><PERSON> chú về giá", "brand": "<PERSON><PERSON><PERSON><PERSON>", "url": "URL", "description": "<PERSON><PERSON>", "attributes": "<PERSON><PERSON><PERSON><PERSON>", "attributeName": "<PERSON><PERSON><PERSON>", "attributeType": "<PERSON><PERSON><PERSON> dữ liệu", "attributeValue": "<PERSON><PERSON><PERSON> trị mặc định"}, "attributeTypes": {"text": "<PERSON><PERSON><PERSON>", "number": "Số", "date": "<PERSON><PERSON><PERSON>", "boolean": "Có/<PERSON>hông", "list": "<PERSON><PERSON>"}, "images": {"addImages": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>nh sản phẩm", "image": "Ảnh", "url": "URL", "video": "Video", "uploadImage": "<PERSON><PERSON><PERSON>", "enterImageUrl": "Nhập URL hình ảnh", "enterVideoUrl": "Nhập URL video", "recommendedSize": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> nghị: 800x600px, tối đa 2MB", "addToList": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>o danh s<PERSON>ch", "uploadedImages": "<PERSON><PERSON> t<PERSON> lên", "urlImages": "<PERSON><PERSON> s<PERSON> từ URL", "videoList": "<PERSON><PERSON> video", "setCover": "Đặt làm ảnh bìa", "coverImage": "Ảnh bìa", "uploadedFromComputer": "<PERSON><PERSON><PERSON> lên từ máy t<PERSON>h", "dragAndDrop": "<PERSON><PERSON>o thả hoặc click để tải lên ảnh sản phẩm"}, "actions": {"createProduct": "<PERSON><PERSON><PERSON> s<PERSON>n ph<PERSON>m", "saveProduct": "<PERSON><PERSON><PERSON> p<PERSON>m", "deleteProduct": "<PERSON><PERSON><PERSON> p<PERSON>m", "cancelCreation": "<PERSON><PERSON><PERSON>"}, "messages": {"productCreated": "<PERSON><PERSON><PERSON> sản phẩm thành công", "productUpdated": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm thành công", "productDeleted": "<PERSON><PERSON><PERSON> sản phẩm thành công", "confirmDelete": "Bạn có chắc chắn muốn xóa sản phẩm này không?"}, "import": {"title": "Import sản ph<PERSON>m", "steps": {"upload": "<PERSON><PERSON><PERSON> l<PERSON> file", "productType": "<PERSON><PERSON><PERSON> lo<PERSON>i sản phẩm", "mapping": "<PERSON><PERSON> cột", "preview": "<PERSON><PERSON> tr<PERSON><PERSON> dữ liệu", "importing": "Đang import"}, "productType": {"title": "<PERSON><PERSON><PERSON> lo<PERSON>i sản phẩm", "description": "<PERSON><PERSON> lòng chọn loại sản phẩm phù hợp để cấu hình import tối ưu"}, "mapping": {"title": "<PERSON><PERSON> cột", "description": "<PERSON><PERSON> x<PERSON> các cột Excel với trường sản phẩm", "columnMapping": "<PERSON><PERSON> cột", "skipColumn": "Bỏ qua cột này", "requiredField": "Trư<PERSON>ng này là bắ<PERSON> buộc", "dataPreview": "<PERSON><PERSON> tr<PERSON><PERSON> dữ liệu", "validationErrors": "Lỗi xác thực", "errors": {"duplicateMapping": "Trường này đã được ánh xạ với cột khác", "requiredFieldMissing": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> buộ<PERSON> {{field}} ch<PERSON><PERSON> đ<PERSON><PERSON><PERSON> ánh xạ"}}, "upload": {"title": "<PERSON><PERSON><PERSON> lên file sản phẩm", "description": "Chọn file Excel hoặc nhập URL để import danh sách sản phẩm", "fromFile": "Từ file", "fromUrl": "Từ URL", "supportedFormats": "Hỗ trợ: .xlsx, .xls, .csv (tối đa 10MB)", "hasHeader": "File có dòng tiêu đề", "excelUrl": "URL file Excel", "urlPlaceholder": "Nhập URL file Excel...", "loading": "<PERSON><PERSON> tả<PERSON>...", "loadFromUrl": "<PERSON><PERSON><PERSON> từ URL"}, "errors": {"parseError": "Lỗi phân tích file", "urlRequired": "URL là bắt buộc", "urlFetchError": "Lỗi tải file từ URL", "urlLoadError": "Lỗi tải file từ URL"}, "progress": {"importing": "Đang import sản phẩm", "pleaseWait": "<PERSON><PERSON> lòng đợi trong khi hệ thống xử lý dữ liệu", "processing": "<PERSON><PERSON> lý", "imported": "Đã import", "errors": "Lỗi"}, "complete": {"title": "Import hoàn thành", "description": "Quá trình import sản phẩm đã hoàn thành thành công", "totalProcessed": "Tổng đã xử lý", "successfullyImported": "Import thành công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "errorDetails": "<PERSON> tiết lỗi", "nextSteps": "<PERSON><PERSON><PERSON><PERSON> tiế<PERSON> theo", "reviewProducts": "<PERSON><PERSON> lại danh s<PERSON>ch sản phẩm", "updateInventory": "<PERSON><PERSON><PERSON> nh<PERSON>t mức tồn kho", "setupCategories": "<PERSON><PERSON><PERSON><PERSON> lập danh mục sản phẩm", "viewProducts": "<PERSON><PERSON> p<PERSON>m"}, "preview": {"title": "Xem trước dữ liệu import", "description": "Xem lại và xác thực dữ liệu trư<PERSON><PERSON> khi import", "totalRows": "Tổng số dòng", "validRows": "<PERSON><PERSON><PERSON> h<PERSON>p l<PERSON>", "invalidRows": "Dòng lỗi", "validationWarnings": "<PERSON><PERSON><PERSON> b<PERSON>o x<PERSON>c thực", "dataPreview": "<PERSON><PERSON> tr<PERSON><PERSON> dữ liệu", "showingFirst10": "<PERSON><PERSON><PERSON> thị 10 dòng đầu tiên", "importOptions": "T<PERSON>y <PERSON> import", "skipInvalidRows": "Bỏ qua các dòng lỗi", "updateExisting": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm hiện có", "sendNotification": "<PERSON><PERSON><PERSON> thông báo", "startImport": "Bắt đầu import", "row": "Dòng"}, "validation": {"nameRequired": "<PERSON><PERSON><PERSON> sản phẩm là bắt buộc", "skuRequired": "SKU là bắt buộc", "priceRequired": "<PERSON><PERSON><PERSON> là b<PERSON> bu<PERSON>c", "invalidPrice": "<PERSON><PERSON><PERSON> ph<PERSON>i là số dương", "invalidStock": "<PERSON><PERSON><PERSON> kho phải là số không âm"}}, "form": {"title": "<PERSON><PERSON><PERSON><PERSON> sản phẩm mới", "name": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "description": "<PERSON><PERSON>", "price": "Giá", "category": "<PERSON><PERSON>", "sku": "Mã SKU", "statusField": "<PERSON><PERSON><PERSON><PERSON> thái", "submit": "<PERSON><PERSON><PERSON> p<PERSON>m", "cancel": "<PERSON><PERSON><PERSON>", "createTitle": "<PERSON><PERSON><PERSON><PERSON> sản phẩm vật lý", "createDigitalTitle": "<PERSON><PERSON><PERSON> sản phẩm số", "createServiceTitle": "<PERSON><PERSON><PERSON> vụ", "createEventTitle": "<PERSON><PERSON><PERSON> s<PERSON> kiện", "createComboTitle": "Tạo combo sản phẩm", "editTitle": "Chỉnh s<PERSON>a sản phẩm", "updating": "<PERSON><PERSON> cập nhật...", "nameLabel": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "descriptionLabel": "<PERSON><PERSON> t<PERSON> sản phẩm", "productTypeLabel": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên sản phẩm", "serviceNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên d<PERSON> vụ", "eventNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên sự kiện", "comboNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên combo sản phẩm", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả sản phẩm", "productTypePlaceholder": "<PERSON><PERSON><PERSON> lo<PERSON>i sản phẩm", "serviceDescriptionPlaceholder": "<PERSON><PERSON> tả chi tiết về dịch vụ", "eventDescriptionPlaceholder": "<PERSON><PERSON> tả chi tiết về sự kiện", "comboDescriptionPlaceholder": "<PERSON><PERSON> tả chi tiết về combo sản phẩm", "pricePlaceholder": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> sản phẩm", "categoryPlaceholder": "<PERSON><PERSON><PERSON> danh mục sản phẩm", "skuPlaceholder": "<PERSON><PERSON>ậ<PERSON> mã SKU sản phẩm", "statusFieldPlaceholder": "<PERSON><PERSON><PERSON> trạng thái sản phẩm", "inventoryPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số lư<PERSON> tồn kho", "tagsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> thẻ sản phẩm và nhấn Enter", "mediaPlaceholder": "<PERSON><PERSON>o thả hoặc click để tải lên ảnh sản phẩm", "media": "Ảnh sản phẩm", "priceDescriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả giá", "listPricePlaceholder": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> y<PERSON>", "salePricePlaceholder": "<PERSON><PERSON><PERSON><PERSON> g<PERSON> bán", "currency": "Đơn vị tiền tệ", "listPrice": "<PERSON><PERSON><PERSON>", "salePrice": "<PERSON><PERSON><PERSON> b<PERSON>", "sections": {"generalInfo": "Th<PERSON>ng tin chung", "pricing": "<PERSON><PERSON><PERSON> s<PERSON>n ph<PERSON>m", "images": "<PERSON><PERSON><PERSON> sản phẩm", "shipping": "<PERSON><PERSON><PERSON> ch<PERSON>", "inventory": "<PERSON><PERSON><PERSON><PERSON> lý tồn kho", "variants": "Mẫu mã", "customFields": "Trường tùy chỉnh", "digitalProcessing": "<PERSON><PERSON> trình xử lý đơn hàng số", "digitalOutput": "<PERSON><PERSON><PERSON> ra sản phẩm số", "media": "<PERSON><PERSON><PERSON> sản phẩm", "serviceInfo": "Thông tin dịch vụ", "eventInfo": "Th<PERSON>ng tin tổ chức sự kiện", "ticketTypes": "Loại vé sự kiện", "comboProducts": "<PERSON><PERSON>n phẩm trong combo"}, "validation": {"nameRequired": "<PERSON><PERSON><PERSON> sản phẩm không được để trống", "descriptionRequired": "<PERSON><PERSON> tả sản phẩm không đư<PERSON><PERSON> để trống", "productTypeRequired": "<PERSON><PERSON> lòng chọn lo<PERSON>i sản phẩm", "priceTypeRequired": "<PERSON><PERSON> lòng chọn lo<PERSON>i giá", "listPriceRequired": "<PERSON><PERSON> lòng nhập gi<PERSON> ni<PERSON> yết", "salePriceRequired": "<PERSON><PERSON> lòng nh<PERSON>p gi<PERSON> bán", "currencyRequired": "<PERSON><PERSON> lòng chọn đơn vị tiền tệ", "priceDescriptionRequired": "<PERSON><PERSON> lòng nhập mô tả giá", "loginInfoRequired": "<PERSON><PERSON> lòng nhập thông tin đăng nhập cho kh<PERSON>a học online", "versionNameRequired": "<PERSON>ê<PERSON> phiên bản không được để trống", "versionPriceMin": "<PERSON><PERSON><PERSON> phiên bản ph<PERSON>i >= 0", "versionCurrencyRequired": "Đơn vị tiền tệ không đ<PERSON><PERSON><PERSON> để trống", "versionQuantityMin": "Số lượng phải >= 1", "versionMinQuantityMin": "<PERSON><PERSON> lượng tối thiểu phải >= 1", "versionMaxQuantityMin": "<PERSON><PERSON> lượng tối đa phải >= 1", "listPriceInvalid": "<PERSON><PERSON><PERSON> yết phải là số > 0", "salePriceInvalid": "<PERSON><PERSON><PERSON> b<PERSON> ph<PERSON>i là số > 0", "comboTotalPriceInvalid": "Tổng giá trị sản phẩm trong combo phải lớn hơn 0", "listPriceGreaterThanSale": "<PERSON><PERSON><PERSON> ni<PERSON>m yết phải lớn hơn giá bán", "invalidPriceType": "Loại gi<PERSON> không hợp lệ", "formValidationError": "<PERSON><PERSON> lòng kiểm tra lại thông tin đã nhập", "requiredFieldsMissing": "<PERSON><PERSON> lòng nhập tên sản phẩm và chọn loại giá", "serviceRequiredFieldsMissing": "<PERSON><PERSON> lòng nhập tên dịch vụ và chọn loại giá", "priceValidationError": "Lỗi validation giá", "serviceDurationMin": "Thời lượng dịch vụ phải lớn hơn 0", "serviceNameRequired": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> vụ không đ<PERSON><PERSON><PERSON> để trống", "eventNameRequired": "Tên sự kiện không được để trống", "eventRequiredFieldsMissing": "<PERSON><PERSON> lòng nhập tên sự kiện", "eventLocationRequired": "<PERSON><PERSON> lòng nhập đ<PERSON><PERSON> điểm sự kiện cho hình thức offline", "zoomLinkInvalid": "Link Zoom kh<PERSON>ng h<PERSON>p lệ", "ticketTypesRequired": "Phải có ít nhất 1 loại vé", "ticketNameRequired": "<PERSON>ê<PERSON> lo<PERSON>i vé không đư<PERSON>c để trống", "ticketPriceMin": "Giá vé phải >= 0", "ticketTotalMin": "Tổng số vé phải >= 1", "ticketMinQuantityMin": "Số vé tối thiểu phải >= 1", "ticketMaxQuantityMin": "Số vé tối đa phải >= 1", "ticketMaxGreaterThanMin": "Số vé tối đa phải >= số vé tối thiểu", "ticketUploadSuccess": "<PERSON><PERSON><PERSON> lên <PERSON>nh vé thành công", "ticketUploadError": "<PERSON><PERSON> lỗi xảy ra khi tải lên ảnh vé", "comboNameRequired": "Tên combo không đ<PERSON><PERSON><PERSON> để trống", "comboRequiredFieldsMissing": "<PERSON><PERSON> lòng nhập tên combo và chọn loại giá", "comboProductsRequired": "Combo phải có <PERSON>t nhất 1 sản phẩm", "comboProductIdInvalid": "<PERSON> sản ph<PERSON>m không hợp lệ", "comboProductNameRequired": "<PERSON><PERSON><PERSON> sản phẩm không được để trống", "comboProductQuantityMin": "Số lượng phải lớn hơn 0"}, "shipmentConfig": {"title": "<PERSON><PERSON><PERSON> hình vận chuyển", "widthCm": "<PERSON><PERSON>u rộng (cm)", "heightCm": "<PERSON><PERSON><PERSON> cao (cm)", "lengthCm": "<PERSON><PERSON><PERSON> dài (cm)", "weightGram": "<PERSON><PERSON><PERSON><PERSON> (gram)"}, "inventory": {"warehouse": "<PERSON><PERSON>", "warehousePlaceholder": "<PERSON><PERSON><PERSON> kho", "availableQuantity": "<PERSON><PERSON> lượng có sẵn", "sku": "SKU", "barcode": "Barcode"}, "customFields": {"title": "Trường tùy chỉnh", "selectField": "<PERSON><PERSON>n trường tùy chỉnh", "selectGroupForm": "<PERSON><PERSON><PERSON> nhóm trường tùy chỉnh", "searchPlaceholder": "T<PERSON>m kiếm trường tùy chỉnh...", "searchGroupPlaceholder": "Tìm kiếm nhóm trường tùy chỉnh...", "selectedFields": "Trường tùy chỉnh đã chọn", "selectedGroupForm": "Nhóm trường tùy chỉnh đã chọn", "addField": "Thêm trường tùy chỉnh", "addGroupForm": "Thêm nhóm trường tùy chỉnh", "valuePlaceholder": "<PERSON><PERSON><PERSON><PERSON> giá trị", "arrayPlaceholder": "<PERSON><PERSON><PERSON><PERSON> giá trị và nhấn Enter để thêm", "selectBoolean": "<PERSON><PERSON><PERSON>/Sai", "noOptions": "<PERSON><PERSON><PERSON><PERSON> có tùy chọn nào đ<PERSON><PERSON><PERSON> cấu hình", "validation": {"required": "Trư<PERSON>ng này là bắ<PERSON> buộc", "minLength": "<PERSON><PERSON><PERSON> thiểu {{min}} ký tự", "maxLength": "T<PERSON>i đa {{max}} ký tự", "pattern": "<PERSON><PERSON><PERSON> dạng không hợp lệ", "invalidNumber": "<PERSON><PERSON><PERSON> là số hợp lệ", "min": "<PERSON><PERSON><PERSON> thiểu {{min}}", "max": "<PERSON><PERSON><PERSON> đa {{max}}", "invalidEmail": "<PERSON><PERSON> h<PERSON> l<PERSON>", "invalidUrl": "URL không hợp lệ", "invalidDate": "<PERSON><PERSON><PERSON> h<PERSON> l<PERSON>"}}, "variants": {"title": "Phân loại mẫu mã", "addVariant": "<PERSON><PERSON><PERSON><PERSON> biến thể", "variant": "<PERSON><PERSON> lo<PERSON>", "noVariants": "<PERSON><PERSON><PERSON> có phân loại nào. Nhấn \"Thêm phân loại\" để bắt đầu.", "customFields": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h biến thể", "searchCustomField": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> tả biến thể", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho biến thể này", "priceDescription": "<PERSON>ô tả giá", "priceDescriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả giá cho biến thể này", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "name": "<PERSON><PERSON><PERSON> biến thể", "currency": "Đơn vị tiền tệ", "listPrice": "<PERSON><PERSON><PERSON>", "salePrice": "<PERSON><PERSON><PERSON> b<PERSON>", "inventory": "<PERSON><PERSON><PERSON><PERSON> lý tồn kho & Ảnh", "sku": "SKU biến thể", "images": "Ảnh biến thể", "imagesHelper": "<PERSON><PERSON><PERSON> lên <PERSON>nh riêng cho biến thể này"}, "versions": {"title": "<PERSON><PERSON><PERSON>", "addVersion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bản", "version": "<PERSON><PERSON><PERSON>", "noVersions": "<PERSON><PERSON><PERSON> có phiên bản nào. <PERSON><PERSON><PERSON><PERSON> \"Thêm phiên bản\" để bắt đầu.", "name": "<PERSON><PERSON><PERSON> p<PERSON>ê<PERSON> bản", "namePlaceholder": "Basic, Pro, Premium...", "price": "Giá", "currency": "Đơn vị tiền tệ", "description": "<PERSON><PERSON> t<PERSON> phiên bản", "descriptionPlaceholder": "<PERSON><PERSON> tả chi tiết về phiên bản nà<PERSON>...", "quantity": "<PERSON><PERSON> lượng có sẵn", "sku": "Mã SKU", "skuPlaceholder": "BASIC-001", "minQuantity": "<PERSON><PERSON> lượng tối thiểu mỗi lần mua", "maxQuantity": "<PERSON><PERSON> lượng tối đa mỗi lần mua", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "statusPending": "Chờ xử lý", "statusActive": "<PERSON><PERSON><PERSON> đ<PERSON>", "statusInactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "removeVersion": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>n"}, "priceTypePlaceholder": "<PERSON><PERSON>n lo<PERSON>i giá", "priceTypes": {"yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "digitalProduct": {"deliveryMethod": {"title": "<PERSON><PERSON><PERSON> giao h<PERSON>ng", "email": "Email", "dashboardDownload": "<PERSON><PERSON>i về từ dashboard", "sms": "SMS", "directMessage": "<PERSON> nh<PERSON>n trực tiếp", "zalo": "<PERSON><PERSON>", "courseActivation": "<PERSON><PERSON><PERSON> ho<PERSON> kh<PERSON><PERSON> h<PERSON>c"}, "deliveryTiming": {"title": "<PERSON><PERSON><PERSON><PERSON> điểm giao hàng", "immediate": "<PERSON>ay sau thanh toán", "delayed": "<PERSON><PERSON> thời gian chờ"}, "deliveryDelayMinutes": "<PERSON>h<PERSON>i gian chờ (phút)", "deliveryDelayPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số phút chờ", "accessStatus": {"title": "<PERSON><PERSON><PERSON> trạng truy cập", "pending": "<PERSON><PERSON> chờ", "delivered": "Đã giao", "notDelivered": "Chưa giao", "deliveryError": "Lỗi giao hàng"}, "digitalProductType": {"title": "<PERSON><PERSON><PERSON> sản phẩm số", "onlineCourse": "Khóa h<PERSON> online", "fileDownload": "File download", "licenseKey": "<PERSON><PERSON> bản quyền", "ebook": "E-book"}, "downloadLink": "<PERSON> truy c<PERSON>p", "downloadLinkPlaceholder": "Nhập link t<PERSON><PERSON> cậ<PERSON> sản phẩm số", "accessLink": "<PERSON> truy c<PERSON>p", "accessLinkPlaceholder": "Nhập link t<PERSON><PERSON> cậ<PERSON> sản phẩm số", "loginInfo": {"title": "Th<PERSON>ng tin đăng nhập kh<PERSON><PERSON> học", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "usernamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên đăng nhập", "password": "<PERSON><PERSON><PERSON>", "passwordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u"}, "usageInstructions": "Hướng dẫn sử dụng", "usageInstructionsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> hướng dẫn chi tiết cách sử dụng sản phẩm số"}, "serviceProduct": {"serviceTime": "<PERSON><PERSON><PERSON><PERSON> gian th<PERSON> hi<PERSON>n", "serviceTimePlaceholder": "<PERSON><PERSON><PERSON> ngày và giờ thực hiện dịch vụ", "serviceDuration": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "serviceDurationPlaceholder": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>i l<PERSON> d<PERSON>ch vụ", "serviceDurationUnit": "Đơn vị", "durationUnit": {"minute": "<PERSON><PERSON><PERSON>", "hour": "Giờ", "day": "<PERSON><PERSON><PERSON>", "session": "<PERSON><PERSON><PERSON><PERSON>", "time": "<PERSON><PERSON><PERSON>", "quantity": "Số lượng"}, "serviceProvider": "<PERSON><PERSON><PERSON><PERSON> th<PERSON> hi<PERSON>n", "serviceProviderPlaceholder": "<PERSON><PERSON><PERSON> ng<PERSON>/đ<PERSON><PERSON> ngũ thực hiện dịch vụ", "serviceType": {"title": "<PERSON><PERSON><PERSON> hình d<PERSON>ch vụ", "consultation": "<PERSON><PERSON> vấn", "beauty": "<PERSON><PERSON><PERSON> đẹp", "maintenance": "<PERSON><PERSON><PERSON> trì", "installation": "Lắp đặt"}, "serviceLocation": {"title": "<PERSON><PERSON><PERSON> đi<PERSON><PERSON> thự<PERSON> hi<PERSON>n", "atHome": "Tại nhà", "atCenter": "Tại trung tâm", "online": "Online"}, "defaultPackageName": "<PERSON><PERSON><PERSON> tư vấn c<PERSON> bản", "defaultPackageDescription": "<PERSON><PERSON><PERSON> tư vấn cơ bản bao gồm 3 buổi tư vấn online"}, "servicePackages": {"title": "<PERSON><PERSON> s<PERSON>ch gói d<PERSON>ch vụ", "addPackage": "<PERSON><PERSON><PERSON><PERSON>", "newPackageName": "<PERSON><PERSON><PERSON> d<PERSON> v<PERSON> mới", "noPackages": "<PERSON><PERSON><PERSON> có gói dịch vụ nào. <PERSON><PERSON><PERSON> thêm gói đầu tiên!", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên g<PERSON>i d<PERSON>ch vụ", "descriptionPlaceholder": "<PERSON><PERSON> tả chi tiết về gói dịch vụ", "features": "<PERSON><PERSON><PERSON>", "featuresPlaceholder": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h năng và nhấn Enter", "isActive": "<PERSON><PERSON><PERSON><PERSON> thái", "isLimited": "<PERSON><PERSON><PERSON><PERSON> hạn số lư<PERSON>", "quantity": "<PERSON><PERSON> l<PERSON><PERSON> tối đa"}, "eventProduct": {"eventDateTime": "<PERSON><PERSON><PERSON><PERSON> gian sự kiện", "eventDateTimePlaceholder": "<PERSON><PERSON><PERSON> ngày và giờ sự kiện", "eventEndDateTime": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc", "eventEndDateTimePlaceholder": "<PERSON><PERSON><PERSON> thời gian kết thúc sự kiện", "eventTimeZone": "<PERSON><PERSON><PERSON> giờ", "eventTimeZonePlaceholder": "<PERSON><PERSON><PERSON> múi giờ", "attendanceMode": {"title": "<PERSON><PERSON><PERSON> thức tham dự", "offline": "Offline (<PERSON>r<PERSON><PERSON> t<PERSON>)", "online": "Online (<PERSON><PERSON><PERSON><PERSON>)"}, "eventLocation": "<PERSON><PERSON><PERSON> điểm sự kiện", "eventLocationPlaceholder": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> điểm tổ chức sự kiện", "zoomLink": "Link Zoom", "zoomLinkPlaceholder": "Nhập link Zoom cho sự kiện online", "ticketTypes": {"title": "<PERSON><PERSON> s<PERSON>ch lo<PERSON>i vé", "addTicket": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON> vé", "removeTicket": "Xóa loại vé", "ticketName": "<PERSON><PERSON><PERSON> lo<PERSON> vé", "ticketNamePlaceholder": "VIP, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> viên...", "price": "Giá", "currency": "Đơn vị tiền tệ", "totalTickets": "Tổng số vé", "sku": "Mã SKU", "skuPlaceholder": "VIP-001", "saleStartTime": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đ<PERSON>u bán", "saleStartTimePlaceholder": "<PERSON><PERSON><PERSON> ngày và giờ bắt đầu bán", "saleEndTime": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc bán", "saleEndTimePlaceholder": "<PERSON><PERSON><PERSON> ngày và giờ kết thúc bán", "timeZone": "Múi giờ vé", "timeZonePlaceholder": "<PERSON><PERSON>n múi giờ cho vé", "minQuantityPerOrder": "Số vé tối thiểu/lần mua", "maxQuantityPerOrder": "Số vé tối đa/lần mua", "ticketImage": "Hình <PERSON> vé", "ticketImagePlaceholder": "Chưa có hình <PERSON>nh vé", "description": "<PERSON>ô tả loại vé", "descriptionPlaceholder": "<PERSON><PERSON> tả chi tiết về loại vé này...", "defaultTicketName": "Loại vé", "defaultTicketDescription": "<PERSON>é tham gia hội thảo c<PERSON> bản"}, "defaultEventLocation": "Trung tâm <PERSON>, <PERSON><PERSON>"}, "comboProduct": {"searchProductLabel": "T<PERSON>m kiếm sản phẩm để thêm vào combo:", "defaultQuantityLabel": "<PERSON><PERSON> lượng mặc định", "defaultQuantity": "Số lượng", "quantityPlaceholder": "<PERSON><PERSON><PERSON><PERSON>ố l<PERSON>...", "quantityNote": "<PERSON><PERSON> lượng này sẽ được áp dụng cho sản phẩm mới được thêm vào combo", "selectedProductsLabel": "<PERSON><PERSON><PERSON> phẩm đã chọn", "noProductsMessage": "<PERSON><PERSON><PERSON> có sản phẩm nào trong combo. <PERSON><PERSON><PERSON> chọn sản phẩm ở trên.", "productAlreadyExists": "<PERSON><PERSON>n phẩm này đã có trong combo", "tableHeaders": {"productName": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "quantity": "Số lượng", "originalPrice": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>"}, "totalLabel": "Tổng cộng:", "listPriceNote": "Tự động tính từ tổng giá sản phẩm trong combo", "salePriceNote": "<PERSON><PERSON><PERSON> bán thườ<PERSON> thấp hơn giá niêm yết để tạo ưu đãi", "priceDescriptionPlaceholder": "Ví dụ: <PERSON><PERSON><PERSON> hệ để báo giá combo"}, "url": {"title": "URL sản ph<PERSON>m", "selectedUrl": "URL đã chọn", "selectedUrls": "URL đã chọn", "placeholder": "Nhập URL hoặc chọn từ danh sách", "selectFromList": "<PERSON><PERSON><PERSON> từ danh sách", "addMultiple": "Thêm URL", "preview": "<PERSON><PERSON>", "selectUrl": "Chọn URL", "selectFromExisting": "<PERSON><PERSON><PERSON> từ danh sách có sẵn", "addNew": "<PERSON><PERSON><PERSON><PERSON> mới", "addNewUrl": "Thêm URL mới", "pleaseEnterAllInfo": "<PERSON><PERSON> lòng nhập đ<PERSON>y đủ thông tin", "invalidUrl": "URL không hợp lệ. <PERSON><PERSON> lòng nhập URL đúng định dạng", "createSuccess": "Tạo URL mới thành công", "createError": "Có lỗi xảy ra khi tạo URL mới"}}}, "warehouse": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý kho hàng", "name": "<PERSON><PERSON><PERSON> kho", "code": "<PERSON><PERSON> kho", "desc": "<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON> kho", "types": {"PHYSICAL": "<PERSON><PERSON><PERSON>", "VIRTUAL": "Ảo"}, "status": "<PERSON><PERSON><PERSON><PERSON> thái", "address": "Địa chỉ", "capacity": "<PERSON><PERSON><PERSON>", "contact": "<PERSON>h<PERSON>ng tin liên hệ", "add": "<PERSON><PERSON><PERSON><PERSON> kho", "edit": "Chỉnh sửa kho", "addForm": "<PERSON><PERSON><PERSON><PERSON> kho mới", "editForm": "Chỉnh sửa thông tin kho", "createSuccess": "<PERSON><PERSON><PERSON> kho thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nhật kho thành công", "deleteSuccess": "<PERSON><PERSON><PERSON> kho thành công", "createError": "Lỗi khi tạo kho", "updateError": "Lỗi khi cập nhật kho", "deleteError": "Lỗi khi xóa kho", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa kho này không?", "errors": {"nameExists": "<PERSON>ên kho đã tồn tại"}, "form": {"namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên kho", "nameRequired": "<PERSON><PERSON><PERSON> kho là b<PERSON><PERSON> buộc", "nameMaxLength": "Tên kho không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 255 ký tự", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả kho", "descriptionMaxLength": "<PERSON><PERSON> tả không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 500 ký tự", "addressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ kho", "addressRequired": "Địa chỉ là bắt buộc", "addressMaxLength": "Địa chỉ không đư<PERSON><PERSON> vư<PERSON><PERSON> quá 255 ký tự", "capacityPlaceholder": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON> ch<PERSON> kho", "capacityMin": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> ph<PERSON>i lớn hơn hoặc bằng 0", "typePlaceholder": "<PERSON><PERSON><PERSON> lo<PERSON> kho", "selectType": "<PERSON><PERSON><PERSON> lo<PERSON> kho", "create": "<PERSON><PERSON><PERSON> kho", "customFields": {"searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa và nhấn Enter để tìm trường tùy chỉnh...", "noFields": "<PERSON><PERSON><PERSON> có trường tùy chỉnh nào. Sử dụng ô tìm kiếm trên để thêm trường.", "loading": "<PERSON><PERSON> tải cấu hình trường..."}}, "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kho", "associatedSystem": "<PERSON><PERSON> thống liên kết", "purpose": "<PERSON><PERSON><PERSON> đ<PERSON>ch sử dụng", "viewDetail": "<PERSON>em chi tiết", "detailTitle": "<PERSON> tiết kho", "detail": {"customFields": "Trường tùy chỉnh"}, "virtual": {"detailTitle": "<PERSON> ti<PERSON>t kho <PERSON>o"}}, "folder": {"title": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON> th<PERSON> mục", "path": "Đường dẫn", "contents": "<PERSON><PERSON><PERSON> dung thư mục", "rootFolders": "<PERSON><PERSON><PERSON> m<PERSON>c", "noFolders": "<PERSON><PERSON><PERSON><PERSON> có thư mục nào", "create": "<PERSON><PERSON><PERSON> thư mục", "edit": "<PERSON><PERSON><PERSON> th<PERSON> mục", "delete": "<PERSON><PERSON><PERSON> th<PERSON> mục", "deleteSuccess": "<PERSON><PERSON><PERSON> thư mục thành công", "deleteConfirmTitle": "<PERSON><PERSON><PERSON> n<PERSON>n x<PERSON><PERSON> thư mục", "deleteConfirmMessage": "Bạn có chắc chắn muốn xóa {{count}} thư mục đã chọn?"}, "file": {"title": "<PERSON><PERSON>p tin", "name": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "upload": "<PERSON><PERSON><PERSON> lên t<PERSON>", "noFiles": "<PERSON><PERSON><PERSON><PERSON> có tệp nào", "deleteSuccess": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> thành công", "deleteConfirmTitle": "<PERSON><PERSON><PERSON> n<PERSON>n x<PERSON><PERSON> tệp", "deleteConfirmMessage": "Bạn có chắc chắn muốn xóa {{count}} tệp đã chọn?"}, "physicalWarehouse": {"title": "<PERSON><PERSON>", "manage": "<PERSON><PERSON><PERSON><PERSON> lý kho", "totalWarehouses": "tổng số kho", "name": "<PERSON><PERSON><PERSON> kho", "warehouse": "<PERSON><PERSON>", "address": "Địa chỉ", "capacity": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON> kho", "create": "<PERSON><PERSON><PERSON> kho", "edit": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "view": "<PERSON>em chi tiết", "search": "<PERSON><PERSON><PERSON> k<PERSON>m kho...", "noData": "<PERSON><PERSON><PERSON>ng có dữ liệu kho", "createSuccess": "<PERSON><PERSON><PERSON> kho thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nhật kho thành công", "deleteSuccess": "<PERSON><PERSON><PERSON> kho thành công", "deleteMultipleSuccess": "<PERSON><PERSON><PERSON> nhi<PERSON>u kho thành công", "createError": "<PERSON><PERSON><PERSON> kho thất bại", "updateError": "<PERSON><PERSON><PERSON> nhật kho thất bại", "deleteError": "<PERSON><PERSON><PERSON> kho thất b<PERSON>i", "deleteMultipleError": "<PERSON><PERSON><PERSON> n<PERSON>u kho thất bại", "errors": {"nameRequired": "<PERSON><PERSON><PERSON> kho là b<PERSON><PERSON> buộc", "nameExists": "<PERSON>ên kho đã tồn tại"}, "selectToDelete": "<PERSON><PERSON> lòng chọn ít nhất một kho để xóa", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa kho này?", "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa {{count}} kho đã chọn?", "detailTitle": "<PERSON> tiết kho", "information": "Thông tin kho", "customFields": "Trường tùy chỉnh", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kho", "notFoundDescription": "<PERSON>ho bạn đang tìm kiếm không tồn tại hoặc đã bị xóa", "loadError": "Lỗi khi tải thông tin kho", "type": "<PERSON><PERSON><PERSON> kho", "description": "<PERSON><PERSON>", "form": {"createTitle": "<PERSON><PERSON><PERSON> kho mới", "editTitle": "Chỉnh sửa kho", "create": "<PERSON><PERSON><PERSON> kho", "update": "<PERSON><PERSON><PERSON>", "selectWarehouse": "<PERSON><PERSON><PERSON> kho", "warehousePlaceholder": "<PERSON><PERSON><PERSON> kho để tạo", "warehouseRequired": "<PERSON><PERSON> l<PERSON> b<PERSON><PERSON> bu<PERSON>c", "addressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ kho", "addressRequired": "Địa chỉ là bắt buộc", "addressMaxLength": "Địa chỉ không đư<PERSON><PERSON> vư<PERSON><PERSON> quá 255 ký tự", "capacityPlaceholder": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON> ch<PERSON> kho", "capacityMin": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> ph<PERSON>i lớn hơn hoặc bằng 0", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên kho", "nameRequired": "<PERSON><PERSON><PERSON> kho là b<PERSON><PERSON> buộc", "nameMaxLength": "Tên kho không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 255 ký tự", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả kho", "descriptionMaxLength": "<PERSON><PERSON> tả không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 500 ký tự", "customFields": {"searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa và nhấn Enter để tìm trường tùy chỉnh...", "noFields": "<PERSON><PERSON><PERSON> có trường tùy chỉnh nào. Sử dụng ô tìm kiếm trên để thêm trường.", "loading": "<PERSON><PERSON> tải cấu hình trường..."}}}, "virtualWarehouse": {"title": "<PERSON><PERSON> <PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý kho <PERSON>o và hệ thống lưu trữ số", "manage": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "totalWarehouses": "tổng số kho <PERSON>o", "name": "<PERSON><PERSON><PERSON>", "status": {"title": "<PERSON><PERSON><PERSON><PERSON> thái", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động"}, "associatedSystem": "<PERSON><PERSON> thống liên kết", "purpose": "<PERSON><PERSON><PERSON> đ<PERSON>ch sử dụng", "actions": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "view": "<PERSON>em chi tiết", "search": "<PERSON><PERSON><PERSON> k<PERSON>m kho <PERSON>...", "noData": "<PERSON><PERSON><PERSON>ng có dữ liệu kho <PERSON>o", "createSuccess": "<PERSON><PERSON><PERSON> kho <PERSON>o thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t kho <PERSON>o thành công", "deleteSuccess": "<PERSON><PERSON><PERSON> kho <PERSON>o thành công", "deleteMultipleSuccess": "<PERSON><PERSON><PERSON> nhi<PERSON>u kho <PERSON>o thành công", "createError": "<PERSON><PERSON><PERSON> <PERSON>ho <PERSON>o thất bại", "updateError": "<PERSON><PERSON><PERSON> nh<PERSON>t kho <PERSON>o thất bại", "deleteError": "<PERSON><PERSON><PERSON>ho <PERSON>o thất b<PERSON>i", "deleteMultipleError": "<PERSON><PERSON><PERSON> n<PERSON>u kho <PERSON>o thất bại", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa kho <PERSON>o nà<PERSON>?", "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa {{count}} kho ảo đã chọn?", "form": {"createTitle": "<PERSON><PERSON><PERSON> kho <PERSON>o mới", "editTitle": "Chỉnh sửa kho <PERSON>o", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "warehousePlaceholder": "<PERSON><PERSON><PERSON> kho để tạo kho ảo", "warehouseRequired": "<PERSON><PERSON> l<PERSON> b<PERSON><PERSON> bu<PERSON>c", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả kho <PERSON>o", "descriptionMaxLength": "<PERSON><PERSON> tả không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 500 ký tự", "associatedSystemPlaceholder": "<PERSON><PERSON><PERSON><PERSON> hệ thống liên kết", "associatedSystemMaxLength": "<PERSON><PERSON> thống liên kết không đ<PERSON><PERSON><PERSON> vư<PERSON><PERSON> quá 200 ký tự", "purposePlaceholder": "<PERSON><PERSON><PERSON><PERSON> mục đ<PERSON>ch sử dụng", "purposeMaxLength": "<PERSON><PERSON><PERSON> đ<PERSON>ch sử dụng không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 300 ký tự", "statusPlaceholder": "<PERSON><PERSON><PERSON> trạng thái", "statusRequired": "<PERSON>r<PERSON><PERSON> thái là bắ<PERSON> bu<PERSON>c"}}, "bankAccount": {"title": "<PERSON><PERSON><PERSON><PERSON> lý tài khoản ngân hàng", "description": "<PERSON><PERSON><PERSON><PERSON> lý và theo dõi các tài khoản ngân hàng của doanh nghiệp", "overview": "T<PERSON>ng quan tài khoản ngân hàng", "noAccounts": "<PERSON><PERSON>a có tài khoản ngân hàng nào", "addFirst": "<PERSON><PERSON>ê<PERSON> tài khoản đầu tiên", "addAccount": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "manage": "<PERSON><PERSON><PERSON><PERSON> lý", "stats": {"totalAccounts": "<PERSON><PERSON><PERSON> tài k<PERSON> ch<PERSON>h", "totalBankAccounts": "<PERSON><PERSON>ng tài kho<PERSON>n ngân hàng", "activeAccounts": "<PERSON><PERSON><PERSON>n ho<PERSON>t động", "connectedAccounts": "<PERSON><PERSON><PERSON> k<PERSON>n đã kết nối", "pendingAccounts": "<PERSON><PERSON><PERSON> k<PERSON>n đang chờ", "virtualAccounts": "<PERSON><PERSON><PERSON>", "totalVAAccounts": "<PERSON><PERSON><PERSON>", "totalConnectedAccounts": "<PERSON><PERSON><PERSON> k<PERSON>n đã kết nối", "totalPendingAccounts": "<PERSON><PERSON><PERSON> k<PERSON>n đang chờ", "totalTransactions": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "availableTransactions": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> kh<PERSON> dụng", "totalBalance": "Tổng số dư", "connectedBanks": "<PERSON><PERSON> hàng kết n<PERSON>i"}, "actions": {"add": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "delete": "<PERSON><PERSON><PERSON> t<PERSON>", "view": "<PERSON>em chi tiết", "edit": "Chỉnh sửa", "connect": "<PERSON><PERSON><PERSON>", "disconnect": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>", "sync": "<PERSON><PERSON><PERSON> bộ"}, "deleteModal": {"title": "<PERSON><PERSON><PERSON>n x<PERSON>a tài <PERSON>n", "message": "Bạn có chắc chắn muốn xóa {{count}} tài khoản ngân hàng đã chọn? Hành động này không thể hoàn tác."}, "messages": {"deleteSuccess": "<PERSON><PERSON><PERSON> tài kho<PERSON>n ngân hàng thành công", "deleteError": "Có lỗi xảy ra khi xóa tài khoản ngân hàng", "connectSuccess": "<PERSON><PERSON><PERSON> n<PERSON>i tài khoản ngân hàng thành công", "connectError": "<PERSON><PERSON> lỗi xảy ra khi kết nối tài khoản ngân hàng", "syncSuccess": "<PERSON><PERSON><PERSON> bộ tài khoản ngân hàng thành công", "syncError": "<PERSON><PERSON> lỗi xảy ra khi đồng bộ tài khoản ngân hàng"}}}}