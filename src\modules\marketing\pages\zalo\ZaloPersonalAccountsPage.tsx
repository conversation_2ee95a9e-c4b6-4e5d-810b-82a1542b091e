import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  ActionMenu,
  ConfirmDeleteModal,
} from '@/shared/components/common';
import { MenuIconBar } from '@/modules/components/menu-bar';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import {
  useZaloPersonalIntegrations,
  useDeleteZaloPersonalIntegrations,
  useReloginZaloPersonalIntegration,
  useCheckZaloPersonalIntegrationStatus
} from '@/modules/marketing/hooks/zalo';
import type { ZaloPersonalIntegrationQueryDto } from '@/modules/marketing/types/zaloPersonal';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

interface ZaloPersonalIntegration {
  id: string;
  integrationName: string;
  createdAt: number;
  metadata: any;
  isActive: boolean;
}

const ZaloPersonalAccountsPage: React.FC = () => {
  const { t } = useTranslation(['common', 'marketing']);
  const navigate = useNavigate();
  const { showNotification } = useSmartNotification();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  // Table columns configuration
  const columns = useMemo(() => [
    {
      key: 'index',
      title: t('marketing:zalo.modules.personalAccount.table.stt'),
      dataIndex: 'index',
      width: 80,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      key: 'integrationName',
      title: t('marketing:zalo.modules.personalAccount.table.accountName'),
      dataIndex: 'integrationName',
      sortable: true,
    },
    {
      key: 'isActive',
      title: t('marketing:zalo.modules.personalAccount.table.status'),
      dataIndex: 'isActive',
      render: (value: unknown, record: any) => {
        // Nếu có trạng thái 'failed' thì hiển thị đa ngôn ngữ
        if (record.status === 'failed') {
          return (
            <span className="px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
              {t('common:failed')}
            </span>
          );
        }
        const isActive = value as boolean;
        return (
          <span className={`px-2 py-1 rounded-full text-xs ${
            isActive
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          }`}>
            {isActive ? t('common:active') : t('common:inactive')}
          </span>
        );
      },
    },
  ], [t]);

  // Data table configuration
  const dataTable = useDataTable(useDataTableConfig({
    columns,
    createQueryParams: (params) => {
      const queryParams: ZaloPersonalIntegrationQueryDto = {
        page: params.page,
        limit: params.pageSize,
      };

      if (params.searchTerm) {
        queryParams.search = params.searchTerm;
      }

      if (params.sortBy) {
        queryParams.sortBy = params.sortBy;
      }

      if (params.sortDirection) {
        queryParams.sortDirection = params.sortDirection === 'ASC' ? 'asc' : 'desc';
      }

      return queryParams;
    },
  }));

  // API hooks
  const { data, isLoading, refetch } = useZaloPersonalIntegrations(dataTable.queryParams);
  const deleteIntegrations = useDeleteZaloPersonalIntegrations();
  const reloginIntegration = useReloginZaloPersonalIntegration();
  const checkStatus = useCheckZaloPersonalIntegrationStatus();

  // Action menu items
  const getActionMenuItems = (record: ZaloPersonalIntegration) => [
    {
      id: 'relogin',
      key: 'relogin',
      label: t('marketing:zalo.modules.personalAccount.actions.relogin'),
      icon: 'refresh-cw',
      onClick: () => handleRelogin(record.id),
    },
    {
      id: 'checkStatus',
      key: 'checkStatus',
      label: t('marketing:zalo.modules.personalAccount.actions.checkStatus'),
      icon: 'check-circle',
      onClick: () => handleCheckStatus(record.id),
    },
  ];

  // Action handlers
  const handleRelogin = async (id: string) => {
    try {
      await reloginIntegration.mutateAsync(id);
      showNotification('success', t('marketing:zalo.modules.personalAccount.messages.reloginSuccess'));
    } catch (error) {
      showNotification('error', t('marketing:zalo.modules.personalAccount.messages.reloginError'));
    }
  };

  const handleCheckStatus = async (id: string) => {
    try {
      await checkStatus.mutateAsync(id);
      showNotification('success', t('marketing:zalo.modules.personalAccount.messages.statusChecked'));
    } catch (error) {
      showNotification('error', t('marketing:zalo.modules.personalAccount.messages.statusCheckError'));
    }
  };

  const handleAdd = () => {
    navigate('/marketing/zalo/zalo-personal/integration');
  };

  const handleBulkDelete = async () => {
    if (selectedIds.length === 0) return;

    try {
      await deleteIntegrations.mutateAsync(selectedIds);
      showNotification('success', t('marketing:zalo.modules.personalAccount.messages.deleteSuccess'));
      setSelectedIds([]);
      setShowDeleteModal(false);
      refetch();
    } catch (error) {
      showNotification('error', t('marketing:zalo.modules.personalAccount.messages.deleteError'));
    }
  };

  // Enhanced table columns with action menu
  const enhancedColumns = useMemo(() => [
    ...columns,
    {
      key: 'actions',
      title: t('common:actions'),
      dataIndex: 'actions',
      width: 100,
      render: (_: any, record: ZaloPersonalIntegration) => (
        <ActionMenu
          items={getActionMenuItems(record)}
        />
      ),
    },
  ], [columns, t, getActionMenuItems]);

  return (
    <div className="w-full bg-background text-foreground">
      {/* Menu Icon Bar */}
      <MenuIconBar
        onSearch={() => {}}
        onAdd={handleAdd}
        additionalIcons={[
          ...(selectedIds.length > 0 ? [{
            icon: 'trash-2' as const,
            tooltip: t('common:delete'),
            onClick: () => setShowDeleteModal(true),
            variant: 'secondary' as const,
          }] : []),
          {
            icon: 'refresh-cw' as const,
            tooltip: t('common:refresh'),
            onClick: () => refetch(),
            variant: 'default' as const,
          }
        ]}
      />

      {/* Table */}
      <Card>
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns || enhancedColumns}
          data={data?.result?.items || []}
          loading={isLoading}
          rowSelection={{
            selectedRowKeys: selectedIds,
            onChange: (selectedRowKeys: React.Key[]) => {
              setSelectedIds(selectedRowKeys.map(key => String(key)));
            },
          }}
        />
      </Card>

      {/* Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleBulkDelete}
        title={t('marketing:zalo.modules.personalAccount.deleteModal.title')}
        message={t('marketing:zalo.modules.personalAccount.deleteModal.description', {
          count: selectedIds.length
        })}
        itemCount={selectedIds.length}
        isSubmitting={deleteIntegrations.isPending}
      />
    </div>
  );
};

export default ZaloPersonalAccountsPage;
