import { Suspense } from 'react';
import { RouteObject } from 'react-router-dom';
import { Loading } from '@/shared/components/common';
import {
  DashboardPage,
  BusinessLineChartPage
} from '../pages';
import MainLayout from '@/shared/layouts/MainLayout';

export const dashboardRoutes: RouteObject[] = [
  {
    path: '/dashboard',
    element: (
      <MainLayout title="Dashboard">
        <Suspense fallback={<Loading />}>
          <DashboardPage />
        </Suspense>
      </MainLayout>
    )
  },
  {
    path: '/dashboard/business/line-charts',
    element: (
      <MainLayout title="Business Line Charts">
        <Suspense fallback={<Loading />}>
          <BusinessLineChartPage />
        </Suspense>
      </MainLayout>
    )
  }
];

export default dashboardRoutes;
