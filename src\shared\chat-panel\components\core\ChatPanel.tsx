import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AgentSimpleListDto } from '../../../../modules/ai-agents/api/agent.api';
import { ThreadsService } from '../../../../modules/threads/services/threads.service';
import { ReplyMessage } from '../../../components/common';
import { useRPointUpdate } from '../../../hooks/common/useRPointUpdate';
import { useAppSelector } from '../../../store';
import {
  selectCurrentThread
} from '../../../store/slices/threadSlice';
import { useChatPanel } from '../../hooks/composite/useChatPanel';
import { useChatNotification } from '../../hooks/core/useChatNotification';
import { useChatContext } from '../../hooks/integration/useChatContext';
import { chatConfigService } from '../../services/config/chat-config.service';
import { MessageRole } from '../../types/chat-api.types';
import { scrollToMessageWithRetry } from '../../utils/scroll-to-message.util';
import PinnedMessagesPanel from '../ui/PinnedMessagesPanel';
import ThreadLoadingIndicator from '../ui/ThreadLoadingIndicator';
import ChatContent from './ChatContent';
import ChatHeader from './ChatHeader';
import ChatInput from './ChatInput';


/**
 * ChatPanel Component Props
 *
 * @interface ChatPanelProps
 */
interface ChatPanelProps {
  /** Callback when chat panel is closed */
  onClose: () => void;

  /** Optional callback when keyword is detected in input */
  onKeywordDetected?: (keyword: string) => void;

  /** Whether the chat panel is visible */
  isVisible?: boolean;

  // Thread integration props
  /** Enable thread integration features */
  enableThreadIntegration?: boolean;

  /** Callback when a new thread is created */
  onThreadCreated?: (threadId: string, threadName: string) => void;

  /** Callback when switching between threads */
  onThreadSwitched?: (fromThreadId: string, toThreadId: string) => void;

  /** Callback when thread name is changed */
  onThreadNameChanged?: (threadId: string, newName: string) => void;

  /** Callback when a thread is deleted */
  onThreadDeleted?: (threadId: string) => void;
}

const ChatPanel = ({
  onClose,
  onKeywordDetected,
  isVisible = true,
  enableThreadIntegration = false,
  // Thread integration callbacks
  onThreadCreated,
  onThreadSwitched,
  onThreadDeleted
}: ChatPanelProps) => {
  const { notifications, addNotification, removeNotification } = useChatNotification();
  const [replyMessage, setReplyMessage] = useState<ReplyMessage | null>(null);
  const [centerNotification, setCenterNotification] = useState<{
    message: string;
    type?: 'info' | 'success' | 'warning' | 'error';
    duration?: number;
  } | null>(null);
  const [isAgentSelectorOpen, setIsAgentSelectorOpen] = useState(false);

  // State cho edit message functionality
  const [editContent, setEditContent] = useState<string | undefined>(undefined);
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);


  // State cho selected agent
  const [selectedAgent, setSelectedAgent] = useState<AgentSimpleListDto | null>(null);

  // RPoint update handler - Integrated with SSE events
  const { handleRPointUpdate } = useRPointUpdate({
    useUpdatedBalance: true, // Sử dụng updatedBalance thay vì trừ cost
    onUpdate: () => {
      // R-Point updated - could show notification if needed
    },
    onError: (error: string) => {
      // R-Point update error - show error notification
      addNotification('error', `R-Point update failed: ${error}`, 5000);
    }
  });

  // Wrapper để handle SSE RPoint events
  const wrappedHandleRPointUpdate = useCallback((rPointCost: number, updatedBalance: string, timestamp: number) => {
    handleRPointUpdate(rPointCost, updatedBalance, timestamp);
  }, [handleRPointUpdate]);

  // Auth để lấy token (unused for now)
  // const { getToken } = useAuthCommon();

  // Chat context để xác định admin/user
  const { chatConfig: contextChatConfig } = useChatContext();

  // Lấy chat config
  const chatConfig = chatConfigService.getConfig();

  // ✅ REMOVED: Unused integration hooks
  // Integration is handled in useChatPanel hook

  // Redux for thread state
  const currentThread = useAppSelector(selectCurrentThread);

  // Chat panel hook với simplified config
  const chatStream = useChatPanel({
    agentId: contextChatConfig.agentId || chatConfig.agentId,
    getAuthToken: contextChatConfig.getAuthToken,
    enableMarkdownStreaming: true,
    enableToolCallApproval: true,
    messageHistory: {
      pageSize: 20,
      autoLoad: true,
      timeout: 5000
    }
  });

  // ✅ Phase 1: Use unified messages (history + streaming)
  const messages = useMemo(() => chatStream.unifiedMessages || [], [chatStream.unifiedMessages]);

  // ✅ SIMPLIFIED: No complex integration logic needed
  // Redux state is managed by useChatPanel hook automatically

  // ✅ REMOVED: Duplicate registration logic
  // Registration is already handled in useChatPanel hook
  // This was causing infinite re-renders due to duplicate registrations

  // TODO: Register R-Point update handler với event router in Phase 3
  // Currently chatStream doesn't expose eventRouter, need to implement proper integration
  useEffect(() => {
    // Placeholder for R-Point integration
    // Will be implemented in Phase 3 when eventRouter is properly exposed

  }, [wrappedHandleRPointUpdate]);

  // Handle errors
  useEffect(() => {
    if (chatStream.error) {
      addNotification('error', chatStream.error, 5000);
    }
  }, [chatStream.error, addNotification]);

  // Thread integration callbacks - Listen for thread changes with proper tracking
  const previousThreadIdRef = useRef<string | null>(null);

  useEffect(() => {
    if (!enableThreadIntegration) return;

    const currentThreadId = currentThread.threadId;
    const previousThreadId = previousThreadIdRef.current;

    // Handle thread creation (when threadId changes from null to a value)
    if (!previousThreadId && currentThreadId && onThreadCreated) {
      onThreadCreated(currentThreadId, currentThread.threadName || 'New Thread');
    }

    // Handle thread switching (when threadId changes from one value to another)
    if (previousThreadId && currentThreadId && previousThreadId !== currentThreadId && onThreadSwitched) {
      onThreadSwitched(previousThreadId, currentThreadId);
    }

    // Update ref for next comparison
    previousThreadIdRef.current = currentThreadId;
  }, [currentThread.threadId, currentThread.threadName, enableThreadIntegration, onThreadCreated, onThreadSwitched]);

  // Handle new chat with thread integration - optimized with useCallback
  const handleNewChat = useCallback(async () => {
    try {
      const previousThreadId = currentThread.threadId;
      console.log('🔥 [ChatPanel] handleNewChat START - Previous threadId:', previousThreadId);

      // ✅ Tạo thread mới ngay lập tức
      console.log('🔥 [ChatPanel] Creating new thread immediately');
      const newThreadResponse = await ThreadsService.createThread('New Chat');

      if (!newThreadResponse?.id) {
        throw new Error('Failed to create new thread: No thread ID returned');
      }

      const newThreadId = newThreadResponse.id;
      const newThreadName = newThreadResponse.name || 'New Chat';

      console.log('🔥 [ChatPanel] New thread created:', { newThreadId, newThreadName });

      // ✅ Clear current thread using proper action (prevents auto-load first thread)
      console.log('🔥 [ChatPanel] Calling chatStream.clearCurrentThread()');
      await chatStream.clearCurrentThread();

      // ✅ Clear messages trong chat panel
      if (chatStream.clearMessages) {
        console.log('🔥 [ChatPanel] Calling chatStream.clearMessages()');
        chatStream.clearMessages();
      }

      // ✅ Switch to new thread
      console.log('🔥 [ChatPanel] Switching to new thread:', newThreadId);
      await chatStream.switchToThread(newThreadId);

      // Clear reply message and edit state
      setReplyMessage(null);
      setEditContent(undefined);
      setEditingMessageId(null);

      // Notify about thread deletion if there was a previous thread
      if (enableThreadIntegration && previousThreadId && onThreadDeleted) {
        onThreadDeleted(previousThreadId);
      }

      // Note: Thread sẽ được tạo sau khi gửi tin nhắn đầu tiên
    } catch (error: unknown) {
      setCenterNotification({
        message: 'Không thể bắt đầu cuộc hội thoại mới. Vui lòng thử lại.',
        type: 'error',
        duration: 5000
      });
    }
  }, [currentThread.threadId, enableThreadIntegration, onThreadDeleted, chatStream]);



  // Handle clear reply - optimized with useCallback
  const handleClearReply = useCallback(() => {
    setReplyMessage(null);
  }, []);

  // Handler cho agent change with type safety - optimized with useCallback
  const handleAgentChange = useCallback((agent: AgentSimpleListDto) => {
    try {
      // Validate agent object
      if (!agent || !agent.id || !agent.name) {
        addNotification('error', 'Invalid agent selected', 3000);
        return;
      }

      setSelectedAgent(agent);


    } catch (error) {
      addNotification('error', 'Failed to change agent', 3000);
    }
  }, [addNotification]);



  // Handle edit complete with cleanup - optimized with useCallback
  const handleEditComplete = useCallback(() => {
    setEditContent(undefined);
    setEditingMessageId(null);
  }, []);



  // Handle edit cancel - hiện lại các messages đã ẩn và scroll đến cuối - optimized with useCallback
  const handleEditCancel = useCallback(() => {
    setEditContent(undefined);
    setEditingMessageId(null);

    // Scroll đến cuối sau khi cancel edit
    setTimeout(() => {
      const messagesContainer = document.querySelector('[data-radix-scroll-area-viewport]');
      if (messagesContainer) {
        messagesContainer.scrollTo({
          top: messagesContainer.scrollHeight,
          behavior: 'smooth'
        });
      }
    }, 100); // Delay để đảm bảo DOM đã update
  }, []);

  // Message action handlers
  const handleReply = useCallback((messageId: string) => {
    // Tìm message được reply trong danh sách messages
    const allMessages = [...chatStream.messages, ...chatStream.historyMessages];
    const messageToReply = allMessages.find(msg => msg.messageId === messageId);

    if (messageToReply) {
      // Tạo ReplyMessage object
      const replyMsg: ReplyMessage = {
        id: messageToReply.messageId,
        content: messageToReply.messageText || 'Tin nhắn không có nội dung',
        sender: messageToReply.role === MessageRole.USER ? 'user' : 'assistant',
        timestamp: new Date(messageToReply.messageCreatedAt)
      };

      setReplyMessage(replyMsg);

      // ✅ Focus vào textarea sau khi set reply message
      if (chatStream.focusChatInput) {
        // Delay lớn hơn để đảm bảo useEffect trong ChatInput đã chạy xong
        setTimeout(() => {
          chatStream.focusChatInput();
        }, 150);
      }
    }
  }, [chatStream.messages, chatStream.historyMessages, chatStream.focusChatInput]);

  const handleCopy = useCallback((messageText: string) => {
    // Copy text to clipboard silently
    navigator.clipboard.writeText(messageText).catch(() => {
      // Silent fail
    });
  }, []);

  const handleEdit = useCallback((messageId: string) => {
    // Tìm message được edit trong danh sách messages (bao gồm cả history)
    const allMessages = [...chatStream.messages, ...chatStream.historyMessages];
    const messageToEdit = allMessages.find(msg => msg.messageId === messageId);

    if (messageToEdit && messageToEdit.role === MessageRole.USER) {
      // Set edit state
      setEditingMessageId(messageId);
      setEditContent(messageToEdit.messageText || '');
    }
  }, [chatStream.messages, chatStream.historyMessages]);

  const handleLike = useCallback((_messageId: string) => {
    // TODO: Implement like functionality
  }, []);

  const handleUnlike = useCallback((_messageId: string) => {
    // TODO: Implement unlike functionality
  }, []);

  // Handle reply click - scroll to the replied message with enhanced UX
  const handleReplyClick = useCallback(async (messageId: string) => {
    try {
      // Use enhanced scroll utility with red theme and 1000ms duration
      const success = await scrollToMessageWithRetry(
        messageId,
        chatStream.loadMoreHistory,
        chatStream.hasMoreHistory,
        chatStream.isLoadingHistory,
        2 // Max 2 retry attempts
      );

      if (!success) {
        // Enhanced error notification with helpful message
        addNotification(
          'warning',
          'Không tìm thấy tin nhắn được reply. Tin nhắn có thể đã bị xóa hoặc nằm ngoài phạm vi hiển thị.',
          4000
        );
      }

    } catch (error) {
      addNotification('error', 'Có lỗi xảy ra khi tìm tin nhắn được reply', 3000);
    }
  }, [
    addNotification,
    chatStream.loadMoreHistory,
    chatStream.hasMoreHistory,
    chatStream.isLoadingHistory
  ]);

  return (
    <div className={`flex flex-col h-full bg-white dark:bg-dark relative w-full ${isVisible ? '' : 'pointer-events-none'
      }`}>
      {/* Header cố định ở trên cùng với z-index cao */}
      <div className="sticky top-0 z-50 bg-white dark:bg-dark mb-4 flex-shrink-0">
        <ChatHeader
          onNewChat={handleNewChat}
          onClose={onClose}
          onAgentChange={handleAgentChange}
          onAgentSelectorToggle={setIsAgentSelectorOpen}
          isAgentSelectorOpen={isAgentSelectorOpen}

          {...(enableThreadIntegration && {
            // Thread integration props with validation
            threadId: currentThread.threadId || null,
            threadName: currentThread.threadName || null,
            showThreadName: true,
            isThreadSwitching: false // TODO: Add thread switching state if needed
          })}
        />
      </div>

      {/* Pinned Messages Panel */}
      <PinnedMessagesPanel
        threadId={chatStream.threadId || undefined}
        onMessageClick={(_messageId) => {
          // TODO: Scroll to message or highlight it
        }}
        onUnpinMessage={(_messageId) => {
          // TODO: Implement unpin functionality
        }}
      />

      {/* Messages area - scrollable */}
      <div className={`flex-1 overflow-hidden relative ${isAgentSelectorOpen ? 'blur-sm' : ''}`}>
        {/* Thread Loading Overlay */}
        {(chatStream.isClearingThread || chatStream.isSwitchingThread) && (
          <ThreadLoadingIndicator
            type={chatStream.isClearingThread ? 'clearing' : 'switching'}
            overlay={true}
            size="md"
          />
        )}

        <ChatContent
          messages={chatStream.unifiedMessages || messages}
          isLoading={chatStream.isLoading}
          notifications={notifications}
          onRemoveNotification={removeNotification}

          // Thread context
          threadId={chatStream.threadId || undefined}

          // ✅ Streaming message ID để chỉ hiển thị cursor cho message đang streaming
          currentStreamingMessageId={chatStream.currentStreamingMessageId}

          // Center Notification Props
          centerNotification={centerNotification}
          onCenterNotificationDismiss={() => setCenterNotification(null)}

          // ✅ Message History Props
          historyMessages={chatStream.historyMessages}
          isLoadingHistory={chatStream.isLoadingHistory}
          hasMoreHistory={chatStream.hasMoreHistory}
          historyError={chatStream.historyError}
          totalHistoryItems={chatStream.totalHistoryItems}
          onLoadMoreHistory={chatStream.loadMoreHistory}

          // Tool Call Props
          onApproveToolCall={chatStream.approveToolCall}

          // Message Actions
          onReply={handleReply}
          onCopy={handleCopy}
          onEdit={handleEdit}
          onLike={handleLike}
          onUnlike={handleUnlike}
          onReplyClick={handleReplyClick}

          // Edit mode
          editingMessageId={editingMessageId}
        />
      </div>

      {/* Input area - fixed at bottom */}
      <div className={`flex-shrink-0 ${isAgentSelectorOpen ? 'blur-sm' : ''}`}>
        <ChatInput
          {...(onKeywordDetected && { onKeywordDetected })}
          chatStream={chatStream}
          addNotification={addNotification}
          replyMessage={replyMessage}
          onClearReply={handleClearReply}
          setCenterNotification={setCenterNotification}
          editContent={editContent}
          editingMessageId={editingMessageId}
          onEditComplete={handleEditComplete}
          onEditCancel={handleEditCancel}
          selectedAgent={selectedAgent}
        />
      </div>

      {/* Agent Selector Global Backdrop */}
      {isAgentSelectorOpen && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
          onClick={() => setIsAgentSelectorOpen(false)}
        />
      )}
    </div>
  );
};

export default memo(ChatPanel);
