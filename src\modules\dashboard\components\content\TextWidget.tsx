import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Textarea } from '@/shared/components/common';
import { BaseWidgetProps } from '../../types';

interface TextWidgetProps extends BaseWidgetProps {
  initialText?: string;
  editable?: boolean;
  fontSize?: 'sm' | 'md' | 'lg' | 'xl';
  textAlign?: 'left' | 'center' | 'right';
  textColor?: string;
  backgroundColor?: string;
}

/**
 * Widget hiển thị văn bản đơn giản
 */
const TextWidget: React.FC<TextWidgetProps> = ({
  className,
  initialText = 'Nhập văn bản của bạn...',
  editable = true,
  fontSize = 'md',
  textAlign = 'left',
  textColor,
  backgroundColor,
  onPropsChange,
  ...props
}) => {
  const { t } = useTranslation(['dashboard', 'common']);

  // Use text from props if available, otherwise use initialText
  const currentText = (props.text as string) || initialText;
  const [text, setText] = useState(currentText);
  const [isEditing, setIsEditing] = useState(false);
  const [tempText, setTempText] = useState(text);

  // Sync with props changes
  useEffect(() => {
    const newText = (props.text as string) || initialText;
    if (newText !== text) {
      setText(newText);
      setTempText(newText);
    }
  }, [props.text, initialText, text]);

  const handleEdit = useCallback(() => {
    setTempText(text);
    setIsEditing(true);
  }, [text]);

  const handleSave = useCallback(() => {
    setText(tempText);
    setIsEditing(false);

    // Save to widget props
    if (onPropsChange) {
      onPropsChange({
        text: tempText,
        fontSize,
        textAlign,
        textColor,
        backgroundColor,
      });
    }
  }, [tempText, onPropsChange, fontSize, textAlign, textColor, backgroundColor]);

  const handleCancel = useCallback(() => {
    setTempText(text);
    setIsEditing(false);
  }, [text]);

  const fontSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
  };

  const textAlignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  if (isEditing) {
    return (
      <div className={`w-full h-full p-4 ${className || ''}`} style={{ backgroundColor }}>
        <div className="h-full flex flex-col">
          <Textarea
            value={tempText}
            onChange={(e) => setTempText(e.target.value)}
            placeholder={t('dashboard:widgets.text.placeholder', 'Nhập văn bản...')}
            className="flex-1 resize-none"
            rows={6}
          />
          <div className="flex justify-end gap-2 mt-3">
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              {t('common:cancel')}
            </Button>
            <Button variant="primary" size="sm" onClick={handleSave}>
              {t('common:save')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`w-full h-full p-4 ${className || ''} ${editable ? 'cursor-pointer hover:bg-muted/50 transition-colors' : ''}`}
      style={{ backgroundColor }}
      onClick={editable ? handleEdit : undefined}
    >
      <div className="h-full flex items-center justify-center">
        <Typography
          variant="body1"
          className={`${fontSizeClasses[fontSize]} ${textAlignClasses[textAlign]} whitespace-pre-wrap break-words`}
          style={{ color: textColor }}
        >
          {text || t('dashboard:widgets.text.empty', 'Click để thêm văn bản')}
        </Typography>
      </div>
      {editable && (
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button variant="ghost" size="sm" onClick={handleEdit}>
            {t('common:edit')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default TextWidget;
