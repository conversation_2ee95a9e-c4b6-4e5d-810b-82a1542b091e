import React from 'react';
import { DashboardCard } from '../../components';
import { DashboardWidget } from '../../types';

const SalesOverviewPage: React.FC = () => {
  const widgets: DashboardWidget[] = [
    {
      id: 'sales-metrics',
      title: 'Chỉ số bán hàng',
      type: 'data-count',
      x: 0,
      y: 0,
      w: 12,
      h: 3,
      minW: 6,
      minH: 2,
      isEmpty: true
    },
    {
      id: 'sales-chart',
      title: '<PERSON><PERSON><PERSON><PERSON> đồ doanh số',
      type: 'data-count',
      x: 0,
      y: 3,
      w: 8,
      h: 5,
      minW: 6,
      minH: 4,
      isEmpty: true
    },
    {
      id: 'top-products',
      title: 'Sản phẩm bán chạy',
      type: 'data-count',
      x: 8,
      y: 3,
      w: 4,
      h: 5,
      minW: 4,
      minH: 4,
      isEmpty: true
    }
  ];

  return (
    <div className="h-full">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-foreground">Tổng quan bán hàng</h1>
        <p className="text-muted-foreground mt-1">
          <PERSON><PERSON><PERSON> hi<PERSON> su<PERSON>t bán hàng và các chỉ số quan trọng
        </p>
      </div>
      
      <DashboardCard
        widgets={widgets}
        isDraggable={true}
        isResizable={true}
      />
    </div>
  );
};

export default SalesOverviewPage;
