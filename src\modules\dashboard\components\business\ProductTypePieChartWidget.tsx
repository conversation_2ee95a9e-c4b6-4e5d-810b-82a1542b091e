import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Loading, Typography, Button, DoubleDatePicker, IconCard } from '@/shared/components/common';
import { PieChart } from '@/shared/components/charts';
import { useProductTypePieChart } from '@/modules/business/hooks/useReportQuery';
import { ProductTypePieChartQueryDto } from '@/modules/business/types/report.types';
import { PieChart as PieChartIcon, RefreshCw } from 'lucide-react';
import { format } from 'date-fns';
import { type BaseWidgetProps } from '../../types';

/**
 * Widget hiển thị biểu đồ tròn loại sản phẩm
 */
const ProductTypePieChartWidget: React.FC<BaseWidgetProps> = ({
  className,
  isLoading: externalLoading = false,
}) => {
  const { t } = useTranslation(['business', 'common']);
  
  // State cho filter
  const [queryParams, setQueryParams] = useState<ProductTypePieChartQueryDto>({});

  // State cho date range picker
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

  // Gọi API với params
  const { data: pieChartData, isLoading: internalLoading, error, refetch } = useProductTypePieChart(queryParams);

  const isLoading = externalLoading || internalLoading;

  // Xử lý thay đổi date range
  const handleDateRangeChange = (newDateRange: [Date | null, Date | null]) => {
    setDateRange(newDateRange);
    
    const [startDate, endDate] = newDateRange;
    const newParams: ProductTypePieChartQueryDto = {};
    
    if (startDate) {
      newParams.begin = startDate.getTime();
    }
    if (endDate) {
      newParams.end = endDate.getTime();
    }
    
    setQueryParams(newParams);
  };

  // Xử lý refresh
  const handleRefresh = () => {
    refetch();
  };

  // Chuẩn bị dữ liệu cho PieChart
  const chartData = useMemo(() => {
    if (!pieChartData?.data) return [];

    return pieChartData.data.map(item => ({
      name: item.label,
      value: item.value,
      percentage: item.percentage,
    }));
  }, [pieChartData]);

  // Cấu hình slices cho PieChart
  const slices = useMemo(() => [{
    nameKey: 'name',
    valueKey: 'value',
  }], []);

  // Màu sắc cho biểu đồ
  const colorScheme = useMemo(() => {
    if (!pieChartData?.data) return undefined;

    // Sử dụng màu từ API hoặc màu mặc định đẹp hơn
    const defaultColors = [
      '#3b82f6', // blue-500
      '#10b981', // emerald-500
      '#f59e0b', // amber-500
      '#ef4444', // red-500
      '#8b5cf6', // violet-500
      '#06b6d4', // cyan-500
      '#84cc16', // lime-500
      '#f97316', // orange-500
      '#ec4899', // pink-500
      '#6366f1', // indigo-500
    ];

    return pieChartData.data.map((item, index) =>
      item.color || defaultColors[index % defaultColors.length]
    );
  }, [pieChartData]);

  // Nội dung hiển thị ở center
  const centerContent = useMemo(() => {
    if (!pieChartData?.summary) return null;
  
    return (
      <div className="text-center">
        <Typography variant="h4" className="font-bold text-foreground">
          {pieChartData.summary.totalItems}
        </Typography>
        <Typography variant="body2" className="text-muted-foreground">
          {t('business:productType.chart.totalProducts', 'Tổng sản phẩm')}
        </Typography>
        <Typography variant="body2" className="text-muted-foreground mt-1">
          {pieChartData.summary.totalCategories} {t('business:productType.chart.categories', 'loại')}
        </Typography>
      </div>
    );
  }, [pieChartData, t]);

  // Custom tooltip cho product type chart
  const ProductTypeTooltipContent = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3 min-w-[140px]">
          <p className="text-sm font-semibold text-foreground mb-2">
            {data.name || label}
          </p>
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">
              Số lượng: <span className="font-medium text-foreground">{data.value?.toLocaleString('vi-VN')}</span>
            </p>
            {data.payload && typeof data.payload === 'object' && 'percentage' in data.payload && (
              <p className="text-xs text-muted-foreground">
                Tỷ lệ: <span className="font-medium text-primary">{data.payload.percentage.toFixed(1)}%</span>
              </p>
            )}
          </div>
        </div>
      );
    }
    return null;
  };

  // Hiển thị loading
  if (isLoading) {
    return (
      <Card className={`p-6 ${className || ''}`}>
        <div className="flex items-center justify-center h-64">
          <Loading size="lg" />
        </div>
      </Card>
    );
  }

  // Hiển thị error
  if (error) {
    return (
      <Card className={`p-6 ${className || ''}`}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <PieChartIcon className="w-5 h-5 text-primary" />
            <Typography variant="h6" className="font-semibold">
              {t('business:productType.chart.title', 'Biểu đồ loại sản phẩm')}
            </Typography>
          </div>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Typography variant="body1" className="text-destructive mb-2">
              {t('common:error.loadData', 'Không thể tải dữ liệu')}
            </Typography>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="w-4 h-4 mr-2" />
              {t('common:action.retry', 'Thử lại')}
            </Button>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={`p-6 ${className || ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <PieChartIcon className="w-5 h-5 text-primary" />
          <Typography variant="h6" className="font-semibold">
            {t('business:productType.chart.title', 'Biểu đồ loại sản phẩm')}
          </Typography>
        </div>
        
        {/* Controls */}
        <div className="flex items-center gap-3">
          {/* Date Range Picker */}
          <DoubleDatePicker
            value={dateRange}
            onChange={handleDateRangeChange}
            triggerIcon={<IconCard icon="calendar" size="md" />}
            size="sm"
          />

          {/* Refresh Button */}
          <Button variant="ghost" size="sm" onClick={handleRefresh}>
            <RefreshCw className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Chart */}
      <div className="h-80 w-full">
        {chartData.length > 0 ? (
          <PieChart
            data={chartData}
            slices={slices}
            height={320}
            width="100%"
            showTooltip
            showLegend
            legendPosition="bottom"
            innerRadius="40%"
            outerRadius="80%"
            centerContent={centerContent}
            animated
            colorScheme={colorScheme}
            tooltipContent={ProductTypeTooltipContent}
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <Typography variant="body1" className="text-muted-foreground">
              {t('business:productType.chart.noData', 'Không có dữ liệu')}
            </Typography>
          </div>
        )}
      </div>

      {/* Summary Info */}
      {pieChartData?.summary && (
        <div className="mt-4 pt-4 border-t border-border">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <Typography variant="body2" className="text-muted-foreground">
                {t('business:productType.chart.mostPopular', 'Phổ biến nhất')}:
              </Typography>
              <Typography variant="body2" className="font-medium">
                {pieChartData.summary.mostPopularLabel}
              </Typography>
            </div>
            <div>
              <Typography variant="body2" className="text-muted-foreground">
                {t('business:productType.chart.period', 'Thời gian')}:
              </Typography>
              <Typography variant="body2" className="font-medium">
                {pieChartData.startDate && pieChartData.endDate ? (
                  `${format(new Date(pieChartData.startDate), 'dd/MM/yyyy')} - ${format(new Date(pieChartData.endDate), 'dd/MM/yyyy')}`
                ) : (
                  t('business:productType.chart.allTime', 'Tất cả')
                )}
              </Typography>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

export default ProductTypePieChartWidget;
