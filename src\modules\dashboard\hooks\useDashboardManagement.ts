/**
 * Dashboard Management Hooks
 * TanStack Query hooks cho dashboard CRUD operations
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions,
} from '@tanstack/react-query';
import { DashboardManagementService } from '../services/dashboard-management.service';
import { DASHBOARD_QUERY_KEYS } from '../constants/query-keys';
import {
  CreateDashboardPageDto,
  UpdateDashboardPageDto,
  QueryDashboardPageDto,
  DashboardPageResponseDto,
  DashboardSaveOptions,
  DashboardLoadOptions,
  PageResult,
} from '../types/dashboard-api.types';
import { DashboardTabsState } from '../types';

// ==================== QUERY HOOKS ====================

/**
 * Hook lấy danh sách dashboard pages
 */
export const useDashboardPages = (
  params?: QueryDashboardPageDto,
  options?: UseQueryOptions<PageResult<DashboardPageResponseDto>>
) => {
  return useQuery<PageResult<DashboardPageResponseDto>>({
    queryKey: DASHBOARD_QUERY_KEYS.PAGES_LIST(params || {}),
    queryFn: () => DashboardManagementService.getDashboardPagesWithBusinessLogic(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 2,
    ...options,
  });
};

/**
 * Hook lấy chi tiết dashboard page
 */
export const useDashboardPage = (
  id: string,
  options?: DashboardLoadOptions & UseQueryOptions<DashboardPageResponseDto>
) => {
  const { includeWidgets, ...queryOptions } = options || {};

  return useQuery<DashboardPageResponseDto>({
    queryKey: DASHBOARD_QUERY_KEYS.PAGE_DETAIL(id, includeWidgets),
    queryFn: () =>
      DashboardManagementService.getDashboardPageWithBusinessLogic(id, { includeWidgets }),
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    retry: 2,
    enabled: !!id,
    ...queryOptions,
  });
};

/**
 * Hook lấy dashboard page mặc định
 */
export const useDefaultDashboardPage = (
  options?: UseQueryOptions<DashboardPageResponseDto | null>
) => {
  return useQuery<DashboardPageResponseDto | null>({
    queryKey: DASHBOARD_QUERY_KEYS.DEFAULT_PAGE(),
    queryFn: async () => {
      const result = await DashboardManagementService.loadDashboardWithFallback({
        preferServer: true,
        fallbackToLocal: false,
      });
      return result.serverData || null;
    },
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    retry: 1,
    ...options,
  });
};

/**
 * Hook lấy dashboard với fallback strategy
 */
export const useDashboardWithFallback = (
  loadOptions?: DashboardLoadOptions,
  queryOptions?: UseQueryOptions<{
    data: DashboardTabsState | null;
    source: 'server' | 'local' | 'default';
    serverData?: DashboardPageResponseDto;
  }>
) => {
  return useQuery({
    queryKey: ['dashboard', 'fallback', loadOptions],
    queryFn: () => DashboardManagementService.loadDashboardWithFallback(loadOptions),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false,
    retry: 1,
    ...queryOptions,
  });
};

// ==================== MUTATION HOOKS ====================

/**
 * Hook tạo dashboard page mới
 */
export const useCreateDashboard = (
  options?: UseMutationOptions<DashboardPageResponseDto, Error, CreateDashboardPageDto>
) => {
  const queryClient = useQueryClient();

  return useMutation<DashboardPageResponseDto, Error, CreateDashboardPageDto>({
    mutationFn: data => DashboardManagementService.createDashboardPageWithValidation(data),
    onSuccess: data => {
      // Invalidate và refetch dashboard pages
      queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.PAGES() });

      // Update cache với data mới
      queryClient.setQueryData(DASHBOARD_QUERY_KEYS.PAGE(data.id), data);

      // Nếu là dashboard mặc định, invalidate default query
      if (data.isDefault) {
        queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.DEFAULT_PAGE() });
      }
    },
    ...options,
  });
};

/**
 * Hook cập nhật dashboard page
 */
export const useUpdateDashboard = (
  options?: UseMutationOptions<
    DashboardPageResponseDto,
    Error,
    { id: string; data: UpdateDashboardPageDto }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation<DashboardPageResponseDto, Error, { id: string; data: UpdateDashboardPageDto }>(
    {
      mutationFn: ({ id, data }) =>
        DashboardManagementService.updateDashboardPageWithValidation(id, data),
      onSuccess: (data, variables) => {
        // Update cache
        queryClient.setQueryData(DASHBOARD_QUERY_KEYS.PAGE(variables.id), data);

        // Invalidate pages list
        queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.PAGES() });

        // Nếu cập nhật dashboard mặc định, invalidate default query
        if (data.isDefault) {
          queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.DEFAULT_PAGE() });
        }
      },
      ...options,
    }
  );
};

/**
 * Hook xóa dashboard page
 */
export const useDeleteDashboard = (
  options?: UseMutationOptions<{ success: boolean }, Error, string>
) => {
  const queryClient = useQueryClient();

  return useMutation<{ success: boolean }, Error, string>({
    mutationFn: id => DashboardManagementService.deleteDashboardPageWithBusinessLogic(id),
    onSuccess: (_, id) => {
      // Remove từ cache
      queryClient.removeQueries({ queryKey: DASHBOARD_QUERY_KEYS.PAGE(id) });

      // Invalidate pages list
      queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.PAGES() });

      // Invalidate default page (có thể đã xóa dashboard mặc định)
      queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.DEFAULT_PAGE() });
    },
    ...options,
  });
};

/**
 * Hook đặt dashboard mặc định
 */
export const useSetDefaultDashboard = (
  options?: UseMutationOptions<DashboardPageResponseDto, Error, string>
) => {
  const queryClient = useQueryClient();

  return useMutation<DashboardPageResponseDto, Error, string>({
    mutationFn: id => DashboardManagementService.setDefaultDashboardWithBusinessLogic(id),
    onSuccess: data => {
      // Update cache
      queryClient.setQueryData(DASHBOARD_QUERY_KEYS.PAGE(data.id), data);

      // Set làm default page
      queryClient.setQueryData(DASHBOARD_QUERY_KEYS.DEFAULT_PAGE(), data);

      // Invalidate pages list để cập nhật isDefault flags
      queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.PAGES() });
    },
    ...options,
  });
};

/**
 * Hook sync dashboard với server
 */
export const useSyncDashboard = (
  options?: UseMutationOptions<
    DashboardPageResponseDto,
    Error,
    { dashboardId: string; localData: DashboardTabsState; options?: DashboardSaveOptions }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation<
    DashboardPageResponseDto,
    Error,
    { dashboardId: string; localData: DashboardTabsState; options?: DashboardSaveOptions }
  >({
    mutationFn: ({ dashboardId, localData, options: saveOptions }) =>
      DashboardManagementService.syncWithServer(dashboardId, localData, saveOptions),
    onSuccess: (data, variables) => {
      // Update cache
      queryClient.setQueryData(DASHBOARD_QUERY_KEYS.PAGE(variables.dashboardId), data);

      // Invalidate pages list
      queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.PAGES() });
    },
    ...options,
  });
};

/**
 * Hook migrate từ localStorage sang server
 */
export const useMigrateDashboard = (
  options?: UseMutationOptions<
    DashboardPageResponseDto,
    Error,
    { localData: DashboardTabsState; name?: string }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation<
    DashboardPageResponseDto,
    Error,
    { localData: DashboardTabsState; name?: string }
  >({
    mutationFn: ({ localData, name }) =>
      DashboardManagementService.migrateLocalToServer(localData, name),
    onSuccess: data => {
      // Update cache
      queryClient.setQueryData(DASHBOARD_QUERY_KEYS.PAGE(data.id), data);

      // Set làm default nếu cần
      if (data.isDefault) {
        queryClient.setQueryData(DASHBOARD_QUERY_KEYS.DEFAULT_PAGE(), data);
      }

      // Invalidate pages list
      queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.PAGES() });
    },
    ...options,
  });
};

// ==================== UTILITY HOOKS ====================

/**
 * Hook để refresh tất cả dashboard management data
 */
export const useRefreshDashboardManagement = () => {
  const queryClient = useQueryClient();

  return () => {
    queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.ALL });
  };
};

/**
 * Hook để clear dashboard cache
 */
export const useClearDashboardCache = () => {
  const queryClient = useQueryClient();

  return () => {
    queryClient.removeQueries({ queryKey: DASHBOARD_QUERY_KEYS.ALL });
  };
};

/**
 * Hook để get dashboard backups
 */
export const useDashboardBackups = () => {
  return useQuery({
    queryKey: ['dashboard', 'backups'],
    queryFn: () => DashboardManagementService.getBackups(),
    staleTime: Infinity, // Backups không thay đổi thường xuyên
    refetchOnWindowFocus: false,
  });
};
