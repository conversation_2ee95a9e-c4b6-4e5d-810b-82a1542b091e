import { apiClient } from '@/shared/api/axios';
import type {
  ZaloPersonalIntegrationQueryDto,
  ZaloPersonalIntegrationResponseDto,
  CreateZaloPersonalIntegrationDto,
  UpdateZaloPersonalIntegrationDto,
  ZaloPersonalQRCodeResponseDto,
  GenerateZaloPersonalQRCodeDto
} from '@/modules/marketing/types/zaloPersonal';

/**
 * API endpoints cho Zalo Personal Integrations
 */
const ENDPOINTS = {
  LIST: '/marketing/user/zalo-personal/integrations',
  DETAIL: (id: string) => `/marketing/user/zalo-personal/integrations/${id}`,
  CREATE: '/marketing/user/zalo-personal/integrations',
  UPDATE: (id: string) => `/marketing/user/zalo-personal/integrations/${id}`,
  DELETE: '/marketing/user/zalo-personal/integrations',
  RELOGIN: (id: string) => `/marketing/user/zalo-personal/integrations/${id}/relogin`,
  CHECK_STATUS: (id: string) => `/marketing/user/zalo-personal/integrations/${id}/status`,
  QR_CODE_GENERATE: '/marketing/user/zalo-personal/qr-code/generate',
} as const;

/**
 * Lấy danh sách tích hợp Zalo Personal
 */
export const getZaloPersonalIntegrations = async (
  params?: ZaloPersonalIntegrationQueryDto
): Promise<ZaloPersonalIntegrationResponseDto> => {
  const response = await apiClient.get(ENDPOINTS.LIST, { params });
  return response as ZaloPersonalIntegrationResponseDto;
};

/**
 * Lấy chi tiết tích hợp Zalo Personal
 */
export const getZaloPersonalIntegrationDetail = async (
  id: string
): Promise<ZaloPersonalIntegrationResponseDto> => {
  const response = await apiClient.get(ENDPOINTS.DETAIL(id));
  return response as ZaloPersonalIntegrationResponseDto;
};

/**
 * Tạo tích hợp Zalo Personal mới
 */
export const createZaloPersonalIntegration = async (
  data: CreateZaloPersonalIntegrationDto
): Promise<ZaloPersonalIntegrationResponseDto> => {
  const response = await apiClient.post(ENDPOINTS.CREATE, data);
  return response as ZaloPersonalIntegrationResponseDto;
};

/**
 * Cập nhật tích hợp Zalo Personal
 */
export const updateZaloPersonalIntegration = async (
  id: string,
  data: UpdateZaloPersonalIntegrationDto
): Promise<ZaloPersonalIntegrationResponseDto> => {
  const response = await apiClient.put(ENDPOINTS.UPDATE(id), data);
  return response as ZaloPersonalIntegrationResponseDto;
};

/**
 * Xóa tích hợp Zalo Personal
 */
export const deleteZaloPersonalIntegrations = async (
  ids: string[]
): Promise<{ success: boolean }> => {
  const response = await apiClient.delete(ENDPOINTS.DELETE, {
    data: { ids }
  });
  return (response as any).result;
};

/**
 * Đăng nhập lại tích hợp Zalo Personal
 */
export const reloginZaloPersonalIntegration = async (
  id: string
): Promise<{ success: boolean; redirectUrl?: string }> => {
  const response = await apiClient.post(ENDPOINTS.RELOGIN(id));
  return (response as any).result;
};

/**
 * Kiểm tra trạng thái đăng nhập
 */
export const checkZaloPersonalIntegrationStatus = async (
  id: string
): Promise<{ isActive: boolean; status: string; lastChecked: number }> => {
  const response = await apiClient.get(ENDPOINTS.CHECK_STATUS(id));
  return (response as any).result;
};

/**
 * Tạo QR code để đăng nhập Zalo Personal
 */
export const generateZaloPersonalQRCode = async (
  data?: GenerateZaloPersonalQRCodeDto
): Promise<ZaloPersonalQRCodeResponseDto> => {
  const response = await apiClient.post(ENDPOINTS.QR_CODE_GENERATE, data);
  return response as ZaloPersonalQRCodeResponseDto;
};
