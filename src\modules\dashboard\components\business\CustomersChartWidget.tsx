import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Loading, Typography, Button, DoubleDatePicker, Select } from '@/shared/components/common';
import { BarChart } from '@/shared/components/charts';
import { useCustomersChart } from '@/modules/business/hooks/useReportQuery';
import { CustomersChartQueryDto, CustomersChartTypeEnum } from '@/modules/business/types/report.types';
import { Users, Calendar, RefreshCw } from 'lucide-react';
import { type BaseWidgetProps } from '../../types';

/**
 * Widget hiển thị biểu đồ khách hàng theo loại
 */
const CustomersChartWidget: React.FC<BaseWidgetProps> = ({
  className,
  isLoading: externalLoading = false,
}) => {
  const { t } = useTranslation(['business', 'common']);
  
  // State cho filter
  const [queryParams, setQueryParams] = useState<CustomersChartQueryDto>({
    type: CustomersChartTypeEnum.NEW_CUSTOMERS
  });

  // State cho date range picker
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

  // State cho customer type selector
  const [customerType, setCustomerType] = useState<CustomersChartTypeEnum>(CustomersChartTypeEnum.NEW_CUSTOMERS);

  // Gọi API với params
  const { data: customersData, isLoading: internalLoading, error, refetch } = useCustomersChart(queryParams);

  const isLoading = externalLoading || internalLoading;

  // Xử lý thay đổi date range
  const handleDateRangeChange = (range: [Date | null, Date | null]) => {
    setDateRange(range);
    const [startDate, endDate] = range;
    
    const newParams: CustomersChartQueryDto = {
      ...queryParams,
      begin: startDate ? Math.floor(startDate.getTime() / 1000) : undefined,
      end: endDate ? Math.floor(endDate.getTime() / 1000) : undefined,
    };
    
    setQueryParams(newParams);
  };

  // Xử lý thay đổi loại khách hàng
  const handleCustomerTypeChange = (type: CustomersChartTypeEnum) => {
    setCustomerType(type);
    setQueryParams({
      ...queryParams,
      type
    });
  };

  // Xử lý refresh
  const handleRefresh = () => {
    refetch();
  };

  // Tùy chọn loại khách hàng
  const customerTypeOptions = [
    { 
      value: CustomersChartTypeEnum.NEW_CUSTOMERS, 
      label: t('business:customer.type.new', 'Khách hàng mới') 
    },
    { 
      value: CustomersChartTypeEnum.RETURNING_CUSTOMERS, 
      label: t('business:customer.type.returning', 'Khách hàng quay lại') 
    },
    { 
      value: CustomersChartTypeEnum.TOTAL_CUSTOMERS, 
      label: t('business:customer.type.total', 'Tổng khách hàng') 
    },
  ];

  // Chuyển đổi dữ liệu API thành format cho chart
  const chartData = useMemo(() => {
    if (!customersData?.data) return [];

    return Object.entries(customersData.data).map(([period, value]) => ({
      period,
      customers: value,
      // Tạo label hiển thị đẹp hơn
      displayPeriod: period.replace('2025-W', 'Tuần ').replace('2025-', '')
    }));
  }, [customersData]);

  // Cấu hình bars cho chart
  const barConfigs = useMemo(() => {
    const getColorByType = (type: CustomersChartTypeEnum) => {
      switch (type) {
        case CustomersChartTypeEnum.NEW_CUSTOMERS:
          return '#10B981'; // Green
        case CustomersChartTypeEnum.RETURNING_CUSTOMERS:
          return '#3B82F6'; // Blue
        case CustomersChartTypeEnum.TOTAL_CUSTOMERS:
          return '#8B5CF6'; // Purple
        default:
          return '#6B7280'; // Gray
      }
    };

    return [
      {
        dataKey: 'customers',
        name: customerTypeOptions.find(opt => opt.value === customerType)?.label || 'Khách hàng',
        color: getColorByType(customerType),
      }
    ];
  }, [customerType, customerTypeOptions, t]);

  // Hiển thị loading
  if (isLoading) {
    return (
      <div className={`w-full ${className || ''}`}>
        <Card className="h-full">
          <div className="flex items-center justify-center h-64">
            <Loading size="lg" />
          </div>
        </Card>
      </div>
    );
  }

  // Hiển thị error
  if (error) {
    return (
      <div className={`w-full ${className || ''}`}>
        <Card className="h-full">
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <Typography variant="body1" className="text-destructive mb-2">
              {t('common:error.load_data', 'Không thể tải dữ liệu')}
            </Typography>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              {t('common:action.retry', 'Thử lại')}
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className={`w-full ${className || ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Users className="w-5 h-5 text-primary" />
          <Typography variant="h6" className="font-semibold">
            {t('business:customer.chart.title', 'Biểu đồ khách hàng')}
          </Typography>
        </div>
        
        {/* Controls */}
        <div className="flex items-center gap-3">
          {/* Customer Type Selector */}
          <Select
            value={customerType}
            onChange={(val) => handleCustomerTypeChange(val as CustomersChartTypeEnum)}
            options={customerTypeOptions}
            placeholder={t('business:customer.type.select', 'Chọn loại khách hàng')}
            size="sm"
          />

          {/* Date Range Picker */}
          <DoubleDatePicker
            value={dateRange}
            onChange={handleDateRangeChange}
            triggerIcon={<Calendar className="w-4 h-4" />}
            size="sm"
          />

          {/* Refresh Button */}
          <Button variant="ghost" size="sm" onClick={handleRefresh}>
            <RefreshCw className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Summary Stats */}
      {customersData && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                <Users className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <Typography variant="body2" className="text-muted-foreground">
                  {t('business:customer.total_in_period', 'Tổng trong kỳ')}
                </Typography>
                <Typography variant="h6" className="font-semibold">
                  {Object.values(customersData.data).reduce((sum, val) => sum + val, 0).toLocaleString('vi-VN')}
                </Typography>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                <Calendar className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <Typography variant="body2" className="text-muted-foreground">
                  {t('business:customer.period_type', 'Loại thời gian')}
                </Typography>
                <Typography variant="h6" className="font-semibold">
                  {customersData.period === 'week' ? 'Tuần' : customersData.period}
                </Typography>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center">
                <Users className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <Typography variant="body2" className="text-muted-foreground">
                  {t('business:customer.data_points', 'Số điểm dữ liệu')}
                </Typography>
                <Typography variant="h6" className="font-semibold">
                  {Object.keys(customersData.data).length}
                </Typography>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Chart */}
      <Card className="overflow-hidden">
        <div className="p-4">
          <BarChart
            data={chartData}
            xAxisKey="displayPeriod"
            bars={barConfigs}
            height={300}
            showGrid
            showTooltip
            showLegend
            xAxisLabel={t('common:period.label', 'Thời gian')}
            yAxisLabel={t('business:customer.count', 'Số khách hàng')}
            legendPosition="bottom"
          />
        </div>
      </Card>
    </div>
  );
};

export default CustomersChartWidget;
