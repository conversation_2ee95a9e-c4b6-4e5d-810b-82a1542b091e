import React from 'react';
import { Typography } from '@/shared/components/common';
import { DashboardWidget } from '../types';
import WidgetFactory from '../factory/WidgetFactory';

/**
 * Renders widget content using Widget Factory
 * Hỗ trợ lazy loading, error boundaries và type safety
 */
export const renderWidgetContent = (
  widget: DashboardWidget,
  displayMode: 'normal' | 'minimal' | 'ultra-minimal' = 'normal',
  onPropsChange?: (widgetId: string, newProps: Record<string, unknown>) => void
): React.ReactNode => {
  try {
    // Sử dụng Widget Factory để tạo widget
    const widgetElement = WidgetFactory.createWidget(widget, {
      lazy: true,
      errorBoundary: true,
      displayMode, // Pass displayMode to factory
      onPropsChange, // Pass onPropsChange callback
      widgetId: widget.id, // Pass widget ID
    });

    // Nếu có widget element từ factory, wrap nó với widget-no-drag để ngăn kéo
    if (widgetElement) {
      return (
        <div className="widget-no-drag h-full w-full">
          {widgetElement}
        </div>
      );
    }

    // Fallback: Custom content
    if (widget.content) {
      if (typeof widget.content === 'string') {
        return (
          <div className="widget-no-drag p-4 h-full w-full">
            <Typography variant="body2">{widget.content}</Typography>
          </div>
        );
      } else if (React.isValidElement(widget.content)) {
        return (
          <div className="widget-no-drag h-full w-full">
            {widget.content}
          </div>
        );
      } else {
        return (
          <div className="widget-no-drag p-4 text-center text-muted-foreground h-full w-full">
            <Typography variant="body2">Invalid widget content</Typography>
          </div>
        );
      }
    }

    // Default fallback
    return (
      <div className="widget-no-drag p-4 text-center text-muted-foreground h-full w-full">
        <Typography variant="body2">Widget content</Typography>
      </div>
    );
  } catch (error) {
    return (
      <div className="widget-no-drag p-4 text-center text-red-500 h-full w-full">
        <Typography variant="body2">Widget display error</Typography>
      </div>
    );
  }
};
