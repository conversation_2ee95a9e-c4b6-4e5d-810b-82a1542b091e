{"integration": {"title": "<PERSON><PERSON><PERSON>", "breadcrumb": {"home": "Trang chủ", "integrations": "<PERSON><PERSON><PERSON>", "googleAds": "Google Ads", "facebookAds": "Facebook Ads", "gmail": "Gmail", "calendar": "Google Calendar"}, "myIntegrations": {"title": "<PERSON><PERSON><PERSON> h<PERSON> c<PERSON>a tôi"}, "allIntegrations": "<PERSON><PERSON><PERSON> cả tích hợp", "myIntegrationsDescription": "<PERSON><PERSON><PERSON><PERSON> lý các tích hợp cá nhân của bạn", "types": {"bank": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng", "llm": "LLM", "sms": "SMS", "email": "Email", "database": "C<PERSON> sở dữ liệu", "social": "Mạng xã hội", "shipping": "<PERSON><PERSON><PERSON> ch<PERSON>", "calendar": "<PERSON><PERSON><PERSON>", "ads": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>o", "other": "K<PERSON><PERSON><PERSON>"}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "pending": "<PERSON><PERSON> chờ", "error": "Lỗi"}, "account_name": "<PERSON><PERSON><PERSON> tà<PERSON>", "customer_id": "Customer ID", "currency": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "last_used": "<PERSON>ần cu<PERSON>i sử dụng", "search_placeholder": "<PERSON><PERSON><PERSON> kiếm tích hợp...", "no_search_results": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả nào", "bankAccounts": {"title": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>ài khoản ngân hàng"}, "cards": {"banking": {"mb": {"description": "<PERSON><PERSON><PERSON> h<PERSON> với ngân hàng MB Bank "}, "acb": {"description": "<PERSON><PERSON><PERSON> h<PERSON> với ngân hàng ACB"}, "ocb": {"description": "<PERSON><PERSON><PERSON> h<PERSON> với ngân hàng OCB"}, "kienlong": {"description": "<PERSON><PERSON><PERSON> h<PERSON> với ngân hàng Kiên Long Bank"}}, "llm": {"openai": {"description": "<PERSON><PERSON><PERSON> hợp với OpenAI GPT models"}, "anthropic": {"description": "<PERSON><PERSON><PERSON> h<PERSON> v<PERSON><PERSON>ic <PERSON>"}, "gemini": {"description": "<PERSON><PERSON><PERSON> h<PERSON> với Google Gemini Pro"}, "deepseek": {"description": "<PERSON><PERSON><PERSON> h<PERSON>p với DeepSeek AI models"}, "xai": {"description": "<PERSON><PERSON><PERSON> hợp với XAI Grok models"}}, "sms": {"twilio": {"description": "<PERSON><PERSON><PERSON> hợ<PERSON> v<PERSON><PERSON><PERSON> để gửi SMS", "title": "<PERSON><PERSON><PERSON><PERSON>", "form": {"fields": {"integrationName": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>", "authToken": "<PERSON><PERSON><PERSON>", "baseDomain": "Twilio Base Domain"}, "placeholders": {"integrationName": "<PERSON><PERSON><PERSON><PERSON> tên tích hợp", "authToken": "<PERSON><PERSON><PERSON><PERSON>", "baseDomain": "Nhập Twilio Base Domain"}}, "validation": {"integrationName": {"required": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> hợ<PERSON> là bắ<PERSON> bu<PERSON>c", "maxLength": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> hợp không đ<PERSON><PERSON><PERSON> quá 100 ký tự"}, "authToken": {"required": "<PERSON><PERSON><PERSON> là bắ<PERSON> bu<PERSON>", "minLength": "<PERSON><PERSON><PERSON> phải có ít nhất 10 ký tự"}, "baseDomain": {"required": "Twilio Base Domain là bắt buộc", "minLength": "Twilio Base Domain phải có ít nhất 3 ký tự"}}}, "fpt": {"description": "<PERSON><PERSON><PERSON> hợ<PERSON> với FPT để gửi  SMS", "descriptionManagement": "<PERSON><PERSON><PERSON><PERSON> lý các cấu hình FPT SMS", "title": "<PERSON><PERSON><PERSON> hợp FPT SMS", "breadcrumb": "FPT SMS", "validation": {"integrationName": {"required": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> hợ<PERSON> là bắ<PERSON> bu<PERSON>c", "maxLength": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> hợp không đ<PERSON><PERSON><PERSON> quá 100 ký tự"}, "clientId": {"required": "Client ID là b<PERSON> buộc", "maxLength": "Client ID không đ<PERSON><PERSON><PERSON> quá 255 ký tự"}, "clientSecret": {"required": "Client Secret là b<PERSON><PERSON> bu<PERSON>c", "maxLength": "Client Secret không đ<PERSON><PERSON><PERSON> quá 255 ký tự"}, "brandName": {"required": "Brandname l<PERSON> b<PERSON><PERSON> buộc", "maxLength": "Brandname kh<PERSON><PERSON> đ<PERSON><PERSON><PERSON> quá 255 ký tự"}}, "empty": {"title": "<PERSON><PERSON>a có cấu hình FPT SMS", "description": "Click vào nút bên dưới để tạo cấu hình đầu tiên"}, "form": {"fields": {"integrationName": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>", "clientId": "Client ID", "clientSecret": "Client Secret", "brandName": "Brandname"}, "placeholders": {"integrationName": "<PERSON><PERSON><PERSON><PERSON> tên tích hợp", "clientId": "Nhập Client ID", "clientSecret": "Nhập Client Secret", "brandName": "Nhập Brandname"}}, "list": {"title": "<PERSON><PERSON><PERSON><PERSON> lý FPT SMS", "description": "<PERSON><PERSON><PERSON><PERSON> lý danh sách cấu hình FPT SMS", "columns": {"integrationName": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>", "brandName": "Brandname", "clientId": "Client ID", "endpoint": "Endpoint", "createdAt": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>"}}, "actions": {"edit": "Chỉnh sửa", "delete": "Xóa", "testConnection": "Test kết n<PERSON>i", "moreActions": "<PERSON><PERSON><PERSON><PERSON> thao tác"}, "confirmations": {"deleteTitle": "<PERSON><PERSON><PERSON>n x<PERSON>a", "delete": "Bạn có chắc chắn muốn xóa cấu hình này không?"}, "error": {"createFailed": "<PERSON><PERSON><PERSON> c<PERSON>u hình thất bại", "createFailedDescription": "<PERSON><PERSON> xảy ra lỗi khi tạo cấu hình. <PERSON><PERSON> lòng thử lại.", "updateFailed": "<PERSON><PERSON><PERSON> nhật cấu hình thất bại", "deleteFailed": "<PERSON><PERSON><PERSON> cấu hình thất bại", "invalidConfig": "Thông tin cấu hình không hợp lệ"}}}, "social": {"zalo": {"description": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> Official Account", "title": "Zalo Official Account", "list": {"title": "<PERSON><PERSON><PERSON><PERSON> Official Account", "description": "<PERSON><PERSON><PERSON><PERSON> lý danh s<PERSON><PERSON> Official Account đ<PERSON> tích hợp", "columns": {"name": "Tên Official Account", "description": "<PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>"}}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "suspended": "<PERSON><PERSON><PERSON>", "error": "Lỗi"}, "actions": {"disconnect": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>", "delete": "Xóa", "moreActions": "<PERSON><PERSON><PERSON><PERSON> thao tác"}}, "linkedin": {"description": "<PERSON><PERSON><PERSON> h<PERSON> v<PERSON><PERSON> LinkedIn để quản lý profile "}}, "shipping": {"viettelPost": {"description": "<PERSON><PERSON><PERSON> h<PERSON> v<PERSON>i Viettel Post", "breadcrumb": "Viettel Post"}, "vnpost": {"description": "<PERSON><PERSON><PERSON> với VNPost", "breadcrumb": "VNPost"}, "title": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> chuyển", "description": "<PERSON><PERSON><PERSON> h<PERSON> với các nhà vận chuyển ", "breadcrumb": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> chuyển", "addProvider": "<PERSON><PERSON><PERSON><PERSON> nhà vận chuyển", "editProvider": "Chỉnh sửa nhà vận chuyển", "viewProvider": "<PERSON>em chi tiết nhà vận chuyển", "addFirstProvider": "Thê<PERSON> nhà vận chuyển đầu tiên", "selectProviderType": "<PERSON><PERSON><PERSON> nhà vận chuyển", "testConnection": "Test kết n<PERSON>i", "test": "Test", "runTest": "Chạy test", "rateCalculator": "<PERSON><PERSON><PERSON> toán phí vận chuyển", "create": "Tạo", "list": {"title": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> chuyển", "description": "<PERSON><PERSON><PERSON><PERSON> lý các cấu hình vận chuyển", "columns": {"integrationName": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>", "providerName": "<PERSON><PERSON><PERSON> nhà vận chuyển", "providerType": "<PERSON><PERSON><PERSON> nhà vận chuyển", "shopId": "Shop ID", "default": "Mặc định", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>"}}, "ghtk": {"description": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> (GHTK)", "breadcrumb": "<PERSON><PERSON><PERSON> h<PERSON>ng ti<PERSON><PERSON> k<PERSON>"}, "ghn": {"description": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> (GHN)", "breadcrumb": "<PERSON><PERSON><PERSON>h"}, "ahamove": {"description": "<PERSON><PERSON><PERSON> h<PERSON> v<PERSON><PERSON> - <PERSON><PERSON><PERSON>h", "breadcrumb": "Ahamove"}}}, "table": {"integrationName": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>", "type": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "gmail": {"title": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> nối với Gmail để gửi và nhận email tự động", "description": "<PERSON><PERSON><PERSON> nối với Gmail để gửi và nhận email", "addGmail": "<PERSON><PERSON><PERSON><PERSON>", "editGmail": "Chỉnh s<PERSON>a <PERSON>", "deleteGmail": "<PERSON>ó<PERSON>mail", "confirmDelete": "Bạn có chắc chắn muốn xóa Gmail integration này không?", "viewGmail": "<PERSON>em chi ti<PERSON><PERSON>", "addFirstGmail": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> tiên", "management": {"title": "<PERSON><PERSON><PERSON><PERSON> ", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tích hợp <PERSON> của bạn"}, "table": {"columns": {"name": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>", "email": "Email", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "<PERSON><PERSON><PERSON>"}}, "deleteModal": {"title": "Xóa Gmail Integration", "message": "Bạn có chắc chắn muốn xóa Gmail integration \"{{name}}\"? Hành động này không thể hoàn tác."}, "messages": {"deleteSuccess": "Xóa Gmail integration thành công"}, "errors": {"loadFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách Gmail integrations", "deleteFailed": "<PERSON><PERSON><PERSON>ng thể xóa Gmail integration"}, "form": {"title": "<PERSON><PERSON><PERSON> <PERSON>", "displayName": "<PERSON><PERSON><PERSON> hiển thị", "displayNameHelp": "<PERSON><PERSON>n hiển thị cho cấu hình này", "displayNamePlaceholder": "Ví dụ: <PERSON><PERSON><PERSON> <PERSON><PERSON>", "isActive": "<PERSON><PERSON><PERSON>", "isActiveHelp": "<PERSON><PERSON><PERSON>/ <PERSON><PERSON> hiệu hóa cấu hình này"}, "card": {"description": "<PERSON><PERSON><PERSON> nối với Gmail để gửi và nhận email tự động", "connectedAndActive": "Đ<PERSON> kết nối và hoạt động", "disconnect": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>", "connect": "<PERSON><PERSON><PERSON>", "connecting": "<PERSON><PERSON> kết nối..."}}, "calendar": {"title": "T<PERSON>ch hợp Google Calendar", "description": "<PERSON><PERSON><PERSON> hợp và đồng bộ lịch với Google Calendar", "form": {"createTitle": "Thêm Google Calendar", "editTitle": "Chỉnh sửa Google Calendar", "accountName": {"label": "<PERSON><PERSON><PERSON> tà<PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên tà<PERSON>n", "helpText": "<PERSON>ên để nhận diện cấu hình này"}, "clientId": {"label": "Client ID", "placeholder": "Nhập Client ID từ Google Console", "helpText": "Client ID từ Google Cloud Console"}, "clientSecret": {"label": "Client Secret", "placeholder": "Nhập Client Secret", "helpText": "Client Secret từ Google Cloud Console"}, "refreshToken": {"label": "Refresh <PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON>", "helpText": "Refresh <PERSON>ken để duy trì kết nối"}, "calendarId": {"label": "Calendar ID", "placeholder": "Nhập Calendar ID (t<PERSON><PERSON> ch<PERSON>)", "helpText": "ID của calendar cụ thể, để trống để dùng calendar chính"}, "isActive": {"label": "<PERSON><PERSON><PERSON>", "helpText": "Bật/tắt tích hợp n<PERSON>y"}, "syncEnabled": {"label": "<PERSON><PERSON>ng bộ tự động", "helpText": "Tự động đồng bộ sự kiện"}, "testConnection": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i"}, "list": {"title": "<PERSON><PERSON> s<PERSON>ch Google Calendar", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tích hợp Google Calendar", "createNew": "Thêm Google Calendar", "searchPlaceholder": "<PERSON><PERSON><PERSON> k<PERSON>ếm theo tên tài k<PERSON>n...", "resultsCount": "<PERSON><PERSON><PERSON> thị {{count}} trong tổng số {{total}} kết quả"}, "filters": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động"}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "syncEnabled": "<PERSON><PERSON><PERSON> bộ bật", "syncDisabled": "Đồng bộ tắt", "syncing": "<PERSON><PERSON> đồng bộ"}, "actions": {"test": "<PERSON><PERSON><PERSON> tra", "sync": "<PERSON><PERSON><PERSON> bộ"}, "details": {"clientId": "Client ID", "lastSync": "<PERSON><PERSON><PERSON> bộ lần cu<PERSON>i", "neverSynced": "<PERSON><PERSON><PERSON> đồng bộ", "created": "Tạo", "updated": "<PERSON><PERSON><PERSON>"}, "syncStatus": {"eventsCount": "Số sự kiện"}, "modal": {"editTitle": "Chỉnh sửa Google Calendar", "deleteTitle": "Xóa Google Calendar", "deleteConfirm": "<PERSON><PERSON><PERSON>n x<PERSON>a", "deleteDescription": "Bạn có chắc chắn muốn xóa cấu hình {{name}}? Hành động này không thể hoàn tác."}, "empty": {"noConfigurations": "<PERSON><PERSON><PERSON> có cấu hình nào", "noConfigurationsDescription": "<PERSON><PERSON>n ch<PERSON>a thêm cấu hình <PERSON> Calendar nào", "noResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "noResultsDescription": "<PERSON><PERSON><PERSON><PERSON> có cấu hình nào phù hợp với bộ lọc", "createFirst": "<PERSON><PERSON><PERSON><PERSON> cấu hình đầu tiên", "clearFilters": "Xóa bộ lọc"}, "error": {"loadFailed": "<PERSON><PERSON><PERSON> danh sách thất bại"}, "defaultCalendar": "Calendar chính", "validation": {"accountName": {"required": "<PERSON><PERSON><PERSON> tài k<PERSON>n là bắt buộc", "maxLength": "Tên tài khoản không đư<PERSON>c quá 100 ký tự"}, "clientId": {"required": "Client ID là b<PERSON> buộc", "maxLength": "Client ID không đ<PERSON><PERSON><PERSON> quá 255 ký tự"}, "clientSecret": {"required": "Client Secret là b<PERSON><PERSON> bu<PERSON>c", "maxLength": "Client Secret không đ<PERSON><PERSON><PERSON> quá 255 ký tự"}, "refreshToken": {"required": "Refresh <PERSON> là b<PERSON> buộc"}, "testEventTitle": {"maxLength": "Tiêu đề sự kiện không được quá 200 ký tự"}, "testEventDescription": {"maxLength": "<PERSON><PERSON> tả sự kiện không được quá 1000 ký tự"}, "summary": {"required": "Ti<PERSON><PERSON> đề sự kiện là bắt buộc"}, "baseDomain": {"required": "Base Domain là bắt buộc", "maxLength": "Base Domain không đư<PERSON>c quá 255 ký tự"}}}, "googleCalendar": {"management": {"title": "Quản lý Google Calendar", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tích hợp Google Calendar của bạn"}, "table": {"columns": {"name": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>", "user": "<PERSON><PERSON><PERSON><PERSON> dùng", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "calendars": "<PERSON><PERSON> lịch", "lastSync": "Đồng bộ cuối"}}, "deleteModal": {"title": "Xóa Google Calendar Integration", "message": "Bạn có chắc chắn muốn xóa Google Calendar integration \"{{name}}\"? Hành động này không thể hoàn tác."}, "messages": {"deleteSuccess": "Xóa Google Calendar integration thành công"}, "errors": {"loadFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách Google Calendar integrations", "deleteFailed": "<PERSON><PERSON><PERSON>ng thể xóa Google Calendar integration"}, "card": {"connected": "<PERSON><PERSON> kết nối", "connectTitle": "Kết nối Google Calendar", "connectDescription": "<PERSON><PERSON>t n<PERSON>i với Google Calendar để đồng bộ lịch và sự kiện, quản lý thời gian hiệu qu<PERSON> hơn", "disconnect": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>", "connect": "Kết nối Google Calendar", "connecting": "<PERSON><PERSON> kết nối...", "securityNote": "<PERSON><PERSON><PERSON> n<PERSON>i an toàn và bảo mật", "calendarsCount": "l<PERSON><PERSON>", "features": {"syncEvents": "<PERSON>ồng bộ sự kiện", "manageSchedule": "<PERSON><PERSON><PERSON><PERSON> lý lịch trình", "autoReminder": "Nhắc nhở tự động", "shareCalendar": "<PERSON><PERSON> sẻ lịch"}}}, "googleAds": {"title": "Google Ads Integration", "subtitle": "<PERSON>ết n<PERSON><PERSON> với Google Ads để quản lý chiến dịch quảng cáo", "description": "<PERSON>ết n<PERSON><PERSON> với Google Ads để quản lý chiến dịch quảng cáo", "connectTitle": "Kết nối Google Ads", "connectDescription": "<PERSON>ết n<PERSON><PERSON> với Google Ads để quản lý chiến dịch quảng cáo", "connectButton": "Kết nối Google Ads", "management": {"title": "Quản lý Google Ads", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tích hợp Google Ads của bạn"}, "table": {"columns": {"account": "<PERSON><PERSON><PERSON>", "customerId": "Customer ID", "currency": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "lastUsed": "<PERSON>ần cu<PERSON>i sử dụng"}}, "deleteModal": {"title": "Xóa Google Ads Integration", "message": "Bạn có chắc chắn muốn xóa Google Ads integration \"{{name}}\"? Hành động này không thể hoàn tác."}, "messages": {"deleteSuccess": "Xóa Google Ads integration thành công"}, "errors": {"loadFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách Google Ads integrations", "deleteFailed": "Không thể xóa Google Ads integration", "authError": "<PERSON><PERSON><PERSON><PERSON> thể khởi tạo xác thực Google Ads"}, "card": {"connected": "<PERSON><PERSON> kết nối", "connectTitle": "Kết nối Google Ads", "connectDescription": "<PERSON>ết n<PERSON><PERSON> với Google Ads để quản lý chiến dịch quảng cáo hiệu quả hơn", "disconnect": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>", "connect": "Kết nối Google Ads", "connecting": "<PERSON><PERSON> kết nối...", "securityNote": "<PERSON><PERSON><PERSON> n<PERSON>i an toàn và bảo mật", "features": {"manageCampaigns": "<PERSON><PERSON><PERSON><PERSON> lý chiến dịch", "viewReports": "<PERSON><PERSON> b<PERSON>o c<PERSON>o", "manageAds": "<PERSON><PERSON><PERSON><PERSON> lý quảng cáo", "trackPerformance": "<PERSON>"}}, "load_error": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách tích hợp Google Ads", "delete_success": "Đã xóa tích hợp Google Ads thành công", "delete_error": "<PERSON><PERSON><PERSON><PERSON> thể xóa tích hợp Google Ads", "delete_confirm_title": "Xóa tích hợp Google Ads", "delete_confirm_description": "Bạn có chắc chắn muốn xóa tích hợp này? Hành động này không thể hoàn tác.", "management_title": "Quản lý Google Ads", "add_account": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "no_integrations": "Chưa có tích hợp Google Ads nào", "auth_error": "<PERSON><PERSON><PERSON><PERSON> thể khởi tạo xác thực Google Ads"}, "facebookAds": {"title": "Facebook Ads Integration", "subtitle": "<PERSON><PERSON>t n<PERSON>i với Facebook Ads để quản lý chiến dịch quảng cáo", "description": "<PERSON><PERSON><PERSON> hợp với Facebook Ads để quản lý chiến dịch quảng cáo", "connectTitle": "<PERSON><PERSON>t <PERSON> Facebook Ads", "connectDescription": "<PERSON><PERSON>t n<PERSON>i với Facebook Ads để quản lý chiến dịch quảng cáo", "connectButton": "<PERSON><PERSON>t <PERSON> Facebook Ads", "management": {"title": "<PERSON><PERSON><PERSON>n lý Facebook Ads", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tích hợp Facebook Ads của bạn"}}, "emailSMTP": {"title": "<PERSON><PERSON><PERSON> <PERSON>ail SMTP", "titleManagement": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>ail SMTP", "description": "<PERSON><PERSON><PERSON> hình máy chủ SMTP cho gửi email đi", "descriptionManagement": "<PERSON><PERSON><PERSON><PERSON> lý các cấu hình <PERSON>ail SMTP", "configName": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "configNameHelp": "Ví dụ: <PERSON><PERSON>, <PERSON><PERSON> c<PERSON> nhân", "configNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên cấu hình", "senderEmail": "<PERSON><PERSON>", "senderName": "<PERSON><PERSON><PERSON><PERSON>i", "senderNameHelp": "<PERSON><PERSON>n hiển thị khi người nhận xem email", "senderNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON>i", "sendTest": "Gửi email test"}, "dashboard": {"totalIntegrations": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>p", "totalIntegrationsDesc": "Tổng số tích hợp đã cấu hình", "activeIntegrations": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> ho<PERSON> động", "activeIntegrationsDesc": "<PERSON><PERSON> tích hợp đang hoạt động bình thường", "websites": "Websites", "websitesDesc": "Số website đã tích hợp", "apiCallsToday": "API Calls hôm nay", "apiCallsTodayDesc": "Số lượng API calls trong ngày", "webhooks": "Webhooks", "webhooksDesc": "Số webhook đang hoạt động", "integrationErrors": "Lỗi tích hợp", "integrationErrorsDesc": "<PERSON><PERSON> tích hợp gặp lỗi cần xử lý"}, "externalAgents": {"title": "<PERSON><PERSON><PERSON>n lý External Agents", "description": "<PERSON><PERSON><PERSON> h<PERSON> v<PERSON><PERSON> c<PERSON> external agents ", "overview": "<PERSON><PERSON><PERSON> quan", "protocolDistribution": "<PERSON><PERSON> bố giao thức", "quickActions": "<PERSON><PERSON> t<PERSON> n<PERSON>h", "recentActivity": "<PERSON><PERSON><PERSON> động gần đây", "noRecentActivity": "<PERSON><PERSON><PERSON> có hoạt động gần đây", "activityWillAppear": "<PERSON><PERSON><PERSON> động của external agents sẽ xuất hiện ở đây", "gettingStarted": "<PERSON><PERSON><PERSON> đ<PERSON>u", "gettingStartedDescription": "<PERSON><PERSON>a có external agent <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> cấu hình. <PERSON><PERSON><PERSON> bắt đầu bằng cách tạo agent đầ<PERSON> tiên của bạn.", "step1": "<PERSON><PERSON><PERSON> giao thức tích hợ<PERSON> (MCP, Google Agent, REST API, v.v.)", "step2": "<PERSON><PERSON><PERSON> hình endpoint và thông tin xác thực", "step3": "<PERSON><PERSON><PERSON> tra kết nối và bắt đầu sử dụng", "createDescription": "Tạo một external agent mới với cấu hình giao thức tùy chỉnh", "manageDescription": "<PERSON><PERSON><PERSON><PERSON> lý tất cả external agents hiện có và cài đặt của chúng", "protocolsDescription": "<PERSON>em và cấu hình các giao thức được hỗ trợ", "analyticsDescription": "<PERSON>em phân tích hiệu suất và thống kê sử dụng"}, "boxChat": {"configTitle": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>", "welcomeText": "<PERSON> nhắn chào mừng", "welcomeTextPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tin nhắn chào mừng", "avatar": "Avatar", "avatarError": "Lỗi avatar", "avatarErrorDesc": "Chỉ chấp nhận file ảnh", "avatarSizeError": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> file khô<PERSON> đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 2MB", "avatarUpdateSuccess": "<PERSON><PERSON><PERSON> nhật avatar thành công!", "avatarUpdateSuccessDesc": "Avatar box chat đã đ<PERSON><PERSON><PERSON> cập nh<PERSON>t.", "avatarUpdateError": "<PERSON><PERSON><PERSON> nhật avatar thất bại!", "avatarUpdateErrorDesc": "<PERSON><PERSON> lòng thử lại sau.", "clickToChangeAvatar": "<PERSON>lick vào ảnh để thay đổi avatar", "clickToUploadAvatar": "<PERSON>lick vào khung để tải lên avatar", "avatarDescription": "<PERSON><PERSON><PERSON> nhận file ảnh (JPEG, PNG, WebP), tối đa 2MB", "placeholderMessage": "Placeholder tin <PERSON>n", "placeholderMessagePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tin nhắn của bạn...", "displayMode": "<PERSON><PERSON> độ hiển thị", "sideMode": "<PERSON><PERSON> độ bên", "colorPrimary": "<PERSON><PERSON><PERSON>", "icon": "Icon", "iconError": "Lỗi icon", "iconErrorDesc": "Chỉ chấp nhận file ảnh", "iconSizeError": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> file khô<PERSON> đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 2MB", "clickToChangeIcon": "Click vào ảnh để thay đổi icon", "clickToUploadIcon": "Click vào khung để tải lên icon", "iconDescription": "<PERSON><PERSON><PERSON> nhận file ảnh (JPEG, PNG, WebP), tối đa 2MB", "selectedFormat": "<PERSON><PERSON><PERSON> dạng đã chọn", "bannerMediaIds": "Banner Media", "addBannerMedia": "Thêm Banner Media", "banners": "Banner Images", "bannerError": "Lỗi banner", "bannerErrorDesc": "Chỉ chấp nhận file ảnh", "bannerSizeError": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> file khô<PERSON> đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 10MB", "editBanner": "Chỉnh sửa Banner", "bannerSlot": "Banner {{index}}", "addBanner": "<PERSON><PERSON><PERSON><PERSON>", "quickMessages": "<PERSON> n<PERSON> nhanh", "quickMessagePlaceholder": "<PERSON> n<PERSON> nhanh", "addQuickMessage": "<PERSON>h<PERSON><PERSON> tin nhắn nhanh", "components": "Components", "componentPlaceholder": "Tên component", "addComponent": "Thêm component", "iframeWidth": "<PERSON><PERSON><PERSON> r<PERSON> (px)", "iframeHeight": "<PERSON><PERSON><PERSON> cao (px)", "textColor": "<PERSON><PERSON><PERSON> chữ", "backgroundColor": "<PERSON><PERSON><PERSON>", "updateSuccess": "<PERSON><PERSON><PERSON> nhật thành công!", "updateSuccessDesc": "<PERSON><PERSON><PERSON> hình box chat đã đ<PERSON><PERSON><PERSON> cập nh<PERSON>t.", "updateError": "<PERSON><PERSON><PERSON> nhật thất bại!", "updateErrorDesc": "<PERSON><PERSON> lòng thử lại sau.", "updating": "<PERSON><PERSON> cập nhật...", "update": "<PERSON><PERSON><PERSON>", "corner": "<PERSON><PERSON><PERSON>", "center": "Trung tâm", "selectDisplayMode": "<PERSON><PERSON><PERSON> chế độ hiển thị", "floating": "<PERSON>ổ<PERSON>", "fixed": "<PERSON><PERSON>", "selectSideMode": "<PERSON><PERSON><PERSON> chế độ bên", "selectMedia": "Chọn media"}, "providerModel": {"title": "<PERSON><PERSON><PERSON>n lý <PERSON> keys Model", "create": "Tạo Provider Model", "form": {"view": "Xem Provider Model", "edit": "Chỉnh sửa Provider Model", "create": "Tạo Provider Model", "viewDescription": "<PERSON><PERSON> thông tin chi tiết của Provider Model", "editDescription": "Chỉnh sửa thông tin Provider Model. Chỉ có thể thay đổi tên và API key.", "createDescription": "<PERSON><PERSON>o mới Provider Model đ<PERSON> tích hợp với các nhà cung cấp AI", "cannotChange": "<PERSON><PERSON><PERSON><PERSON> thể thay đổi", "fields": {"type": "Loại Provider", "name": "T<PERSON>n Provider Model", "namePlaceholder": "Nhập tên provider model", "apiKey": "API Key", "apiKeyPlaceholder": "Nhập API key", "apiKeyPlaceholderEdit": "<PERSON><PERSON> trống nếu không muốn thay đổi API key..."}}, "actions": {"cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "create": "Tạo Provider Model", "edit": "Chỉnh sửa", "delete": "Xóa", "retry": "<PERSON><PERSON><PERSON> lại"}, "empty": {"title": "Chưa có Provider Model nào", "description": "<PERSON>h<PERSON><PERSON> Provider Model đ<PERSON> bắt đầu sử dụng."}, "error": {"title": "Lỗi tải dữ liệu", "description": "<PERSON><PERSON> lỗi xảy ra khi tải danh sách Provider Model. <PERSON><PERSON> lòng thử lại."}, "confirmations": {"deleteTitle": "<PERSON><PERSON><PERSON>n x<PERSON>a", "delete": "Bạn có chắc chắn muốn xóa Provider Model này?", "bulkDelete": "Bạn có chắc chắn muốn xóa {{count}} provider model đã chọn?", "noItemsSelected": "<PERSON><PERSON><PERSON>ng có item nào đư<PERSON> chọn"}, "validation": {"name": {"required": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "maxLength": "<PERSON>ê<PERSON> khô<PERSON> đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 255 ký tự"}, "type": {"invalid": "Loại nhà cung cấp không hợp lệ"}, "apiKey": {"required": "API key không đ<PERSON><PERSON><PERSON> để trống", "minLength": "API key ph<PERSON>i có ít nhất 10 ký tự", "format": "API key chỉ đư<PERSON><PERSON> chứa chữ cái, s<PERSON>, dấu gạch ngang, gạch dưới và dấu chấm"}}, "provider": "<PERSON><PERSON><PERSON> cung cấp", "name": "<PERSON><PERSON><PERSON> hiển thị", "apiKey": "API Key", "nameRequired": "<PERSON><PERSON><PERSON> là b<PERSON><PERSON> bu<PERSON>c", "apiKeyRequired": "API Key là bắt buộc", "createSuccess": "<PERSON><PERSON><PERSON> thành công", "createSuccessMessage": "Provider model đ<PERSON> đ<PERSON><PERSON><PERSON> tạo thành công", "createError": "Lỗi tạo provider model", "createErrorMessage": "Có lỗi xảy ra khi tạo provider model", "metaNamePlaceholder": "Ví dụ: <PERSON> Meta Key", "metaApiKeyPlaceholder": "meta-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "metaApiKeyFormat": "Meta API Key phải bắt đầu bằng \"meta-\"", "createMetaButton": "Tạo Meta Provider Model", "xaiNamePlaceholder": "Ví dụ: My XAI Key", "xaiApiKeyPlaceholder": "xai-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "xaiApiKeyFormat": "XAI API Key phải bắt đầu bằng \"xai-\"", "createXaiButton": "Tạo XAI Provider Model"}, "ai": {"openai": {"title": "<PERSON><PERSON><PERSON> h<PERSON> với OpenAI"}, "anthropic": {"title": "<PERSON><PERSON><PERSON> v<PERSON><PERSON>"}, "google": {"title": "<PERSON><PERSON><PERSON> h<PERSON> với Google Gemini"}, "meta": {"title": "<PERSON><PERSON><PERSON> h<PERSON> v<PERSON><PERSON>"}, "deepseek": {"title": "<PERSON><PERSON><PERSON> với DeepSeek"}, "xai": {"title": "<PERSON><PERSON><PERSON> h<PERSON> với XAI"}}, "openai": {"title": "<PERSON><PERSON> OpenAI Keys", "provider": "<PERSON><PERSON><PERSON> cung cấp", "description": "<PERSON>h sách API keys OpenAI đã tích hợp", "addKey": "Thêm OpenAI Key", "systemModelsTitle": "System Models", "userModelsTitle": "User Models", "editKey": "Chỉnh sửa OpenAI Key", "deleteKey": "Xóa OpenAI Key", "keyName": "<PERSON><PERSON><PERSON> hiển thị", "keyNamePlaceholder": "Ví dụ: My OpenAI Key", "keyNameHelp": "<PERSON>ên để phân biệt các API key khác nhau", "apiKey": "API Key", "apiKeyPlaceholder": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "apiKeyHelp": "Lấy API key từ https://platform.openai.com/api-keys", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "viewUserModels": "User Models", "viewSystemModels": "System Models", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>", "confirmDelete": "Bạn có chắc chắn muốn xóa API key này?", "deleteSuccess": "Xóa OpenAI key thành công", "deleteError": "Có lỗi x<PERSON>y ra khi xóa OpenAI key", "createSuccess": "Tạo OpenAI key thành công", "createError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi tạo OpenAI key", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách OpenAI keys", "noKeys": "Chưa có OpenAI key nào", "noKeysDescription": "Thêm API key đầu tiên để bắt đầu sử dụng OpenAI"}, "anthropic": {"title": "<PERSON><PERSON> s<PERSON>ch <PERSON>ic <PERSON>", "description": "<PERSON><PERSON> s<PERSON>ch <PERSON> keys An<PERSON><PERSON> <PERSON> tích hợp", "provider": "<PERSON><PERSON><PERSON> cung cấp", "addKey": "<PERSON><PERSON><PERSON><PERSON> Anthropic Key", "editKey": "Chỉnh sửa Anthropic Key", "deleteKey": "Xóa Anthropic Key", "keyName": "<PERSON><PERSON><PERSON> hiển thị", "keyNamePlaceholder": "<PERSON><PERSON> dụ: My Anthropic Key", "keyNameHelp": "<PERSON>ên để phân biệt các API key khác nhau", "apiKey": "API Key", "apiKeyPlaceholder": "sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "apiKeyHelp": "Lấy API key từ https://console.anthropic.com/", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "viewUserModels": "User Models", "viewSystemModels": "System Models", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>", "confirmDelete": "Bạn có chắc chắn muốn xóa API key này?", "deleteSuccess": "<PERSON>óa Anthropic key thành công", "deleteError": "Có lỗi x<PERSON>y ra khi xóa Anthropic key", "createSuccess": "Tạo Anthropic key thành công", "createError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi tạo Anthropic key", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách Anthropic keys", "noKeys": "<PERSON><PERSON>a có Anthropic key nào", "noKeysDescription": "Thêm API key đầu tiên để bắt đầu sử dụng Anthropic Claude"}, "gemini": {"title": "<PERSON><PERSON> s<PERSON>ch Google Gemini Keys", "description": "<PERSON>h sách API keys Google Gemini đã tích hợp", "provider": "<PERSON><PERSON><PERSON> cung cấp", "addKey": "<PERSON><PERSON><PERSON><PERSON>", "editKey": "Chỉnh sửa <PERSON> Key", "deleteKey": "Xóa Gemini Key", "keyName": "<PERSON><PERSON><PERSON> hiển thị", "keyNamePlaceholder": "<PERSON>í dụ: My Gemini Key", "keyNameHelp": "<PERSON>ên để phân biệt các API key khác nhau", "apiKey": "API Key", "apiKeyPlaceholder": "AIzaxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "apiKeyHelp": "Lấy API key từ Google AI Studio", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "viewUserModels": "User Models", "viewSystemModels": "System Models", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>", "confirmDelete": "Bạn có chắc chắn muốn xóa API key này?", "deleteSuccess": "Xóa Gemini key thành công", "deleteError": "Có lỗi x<PERSON>y ra khi xóa Gemini key", "createSuccess": "Tạo Gemini key thành công", "createError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi tạo Gemini key", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách <PERSON> keys", "noKeys": "Chưa có Gemini key nào", "noKeysDescription": "Thêm API key đầu tiên để bắt đầu sử dụng Google Gemini"}, "deepseek": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> s<PERSON>ch API keys DeepSeek đ<PERSON> tích hợp", "provider": "<PERSON><PERSON><PERSON> cung cấp", "addKey": "<PERSON><PERSON>êm DeepSeek Key", "editKey": "Chỉnh sửa DeepSeek Key", "deleteKey": "Xóa DeepSeek Key", "keyName": "<PERSON><PERSON><PERSON> hiển thị", "keyNamePlaceholder": "<PERSON><PERSON> dụ: My DeepSeek Key", "keyNameHelp": "<PERSON>ên để phân biệt các API key khác nhau", "apiKey": "API Key", "apiKeyPlaceholder": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "apiKeyHelp": "Lấy API key từ https://platform.deepseek.com/", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "viewUserModels": "User Models", "viewSystemModels": "System Models", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>", "confirmDelete": "Bạn có chắc chắn muốn xóa API key này?", "deleteSuccess": "Xóa DeepSeek key thành công", "deleteError": "Có lỗi x<PERSON>y ra khi xóa DeepSeek key", "createSuccess": "Tạo DeepSeek key thành công", "createError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi tạo DeepSeek key", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách DeepSeek keys", "noKeys": "Chưa có DeepSeek key nào", "noKeysDescription": "Thêm API key đầu tiên để bắt đầu sử dụng DeepSeek"}, "xaiGrok": {"title": "<PERSON><PERSON> s<PERSON>ch XAI Grok Keys", "description": "<PERSON>h sách API keys XAI Grok đã tích hợp", "provider": "<PERSON><PERSON><PERSON> cung cấp", "addKey": "Thêm XAI Grok Key", "editKey": "Chỉnh sửa XAI Grok Key", "deleteKey": "Xóa XAI Grok Key", "keyName": "<PERSON><PERSON><PERSON> hiển thị", "keyNamePlaceholder": "Ví dụ: My XAI Grok Key", "keyNameHelp": "<PERSON>ên để phân biệt các API key khác nhau", "apiKey": "API Key", "apiKeyPlaceholder": "xai-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "apiKeyHelp": "Lấy API key từ xAI platform", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "viewUserModels": "User Models", "viewSystemModels": "System Models", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>", "confirmDelete": "Bạn có chắc chắn muốn xóa API key này?", "deleteSuccess": "Xóa XAI Grok key thành công", "deleteError": "Có lỗi xảy ra khi xóa XAI Grok key", "createSuccess": "Tạo XAI Grok key thành công", "createError": "Có lỗi xảy ra khi tạo XAI Grok key", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách XAI Grok keys", "noKeys": "Chưa có XAI Grok key nào", "noKeysDescription": "Thêm API key đầu tiên để bắt đầu sử dụng XAI Grok"}, "shipping": {"title": "<PERSON><PERSON> sách nhà vận chuyển đã tích hợp", "description": "<PERSON><PERSON><PERSON> hợ<PERSON> với các nhà vận chuyển GHN, GHTK,...", "addProvider": "<PERSON><PERSON><PERSON><PERSON> nhà vận chuyển", "editProvider": "Chỉnh sửa nhà vận chuyển", "viewProvider": "<PERSON>em chi tiết nhà vận chuyển", "addFirstProvider": "Thê<PERSON> nhà vận chuyển đầu tiên", "selectProviderType": "<PERSON><PERSON><PERSON> nhà vận chuyển", "testConnection": "Test Connection", "test": "Test", "runTest": "Run Test", "rateCalculator": "Rate Calculator", "create": "Tạo", "default": "Mặc định", "ghtk": {"title": "Giao h<PERSON>ng ti<PERSON> k<PERSON> (GHTK)", "description": "<PERSON><PERSON><PERSON> hợp v<PERSON><PERSON> dịch vụ giao hàng tiết kiệm - chi <PERSON><PERSON><PERSON> thấp, thời gian giao hàng 3-5 ng<PERSON>y", "breadcrumb": "<PERSON><PERSON><PERSON> h<PERSON>ng ti<PERSON><PERSON> k<PERSON>"}, "ghn": {"title": "Giao <PERSON> (GHN)", "description": "<PERSON><PERSON><PERSON> hợp v<PERSON><PERSON> dịch vụ giao hàng nhanh - giao hàng trong 1-2 ng<PERSON><PERSON>, p<PERSON><PERSON> cao hơn", "breadcrumb": "<PERSON><PERSON><PERSON>h"}, "providers": {"ghtk": {"form": {"title": "<PERSON><PERSON><PERSON> h<PERSON>nh GHTK", "displayName": "<PERSON><PERSON><PERSON> hiển thị", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "password": "<PERSON><PERSON><PERSON>", "usernameHelp": "Tên đ<PERSON>ng nhập từ GHTK để xác thực", "usernamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> Tê<PERSON> đăng nhập từ GHTK", "usernamePlaceholderEdit": "<PERSON><PERSON><PERSON><PERSON> Tê<PERSON> đăng nhập mới (để trống nếu không thay đổi)", "usernamePlaceholderReadonly": "<PERSON><PERSON> liệu đã đư<PERSON><PERSON> mã hóa", "passwordHelp": "<PERSON><PERSON><PERSON> khẩu từ GHTK để xác thực", "passwordPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>h<PERSON> từ GHTK", "passwordPlaceholderEdit": "<PERSON><PERSON><PERSON><PERSON><PERSON>h<PERSON>u mới (để trống nếu không thay đổi)", "passwordPlaceholderReadonly": "<PERSON><PERSON> liệu đã đư<PERSON><PERSON> mã hóa", "displayNameHelp": "<PERSON><PERSON>n hiển thị cho cấu hình GHTK này", "displayNamePlaceholder": "Ví dụ: <PERSON><PERSON><PERSON> <PERSON>nh GHTK ch<PERSON>h", "token": "Token", "tokenHelp": "Token API từ GHTK để xác thực", "tokenPlaceholder": "<PERSON><PERSON><PERSON><PERSON> từ GHTK", "tokenPlaceholderEdit": "<PERSON><PERSON><PERSON><PERSON> mới (để trống nếu không thay đổi)", "tokenPlaceholderReadonly": "<PERSON><PERSON> liệu đã đư<PERSON><PERSON> mã hóa", "timeout": "Timeout (ms)", "timeoutHelp": "Th<PERSON>i gian chờ tối đa cho mỗi request (mặc định: 30000ms)", "timeoutPlaceholder": "30000", "testConnection": "Test kết n<PERSON>i", "testConnectionResult": "<PERSON><PERSON><PERSON> quả kiểm tra kết nối GHTK", "testConnectionSuccess": "✅ <PERSON><PERSON>t n<PERSON>i thành công", "testConnectionFailed": "❌ <PERSON><PERSON>t nối thất bại", "testConnectionError": "❌ Lỗi kết nối", "testConnectionSuccessDetails": "• API endpoint có thể truy cập được\n• Token xác thực hợp lệ\n• Sẵn sàng để sử dụng", "testConnectionErrorDetails": "• Ki<PERSON><PERSON> tra lại Token có chính xác không\n• <PERSON><PERSON><PERSON> bả<PERSON> kết nối internet ổn định\n• Liên hệ GHTK nếu vấn đề vẫn tiếp tục", "testConnectionErrorDefault": "<PERSON><PERSON><PERSON><PERSON> thể kết nối đến GHTK", "testConnectionRequired": "Token là bắt buộc để test kết nối", "testMode": "Chế độ test", "createButton": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh GHTK"}}, "ghn": {"form": {"title": "<PERSON><PERSON><PERSON> h<PERSON>nh GHN", "displayName": "<PERSON><PERSON><PERSON> hiển thị", "displayNameHelp": "<PERSON><PERSON>n hiển thị cho cấu hình GHN này", "displayNamePlaceholder": "Ví dụ: <PERSON><PERSON><PERSON> <PERSON>nh <PERSON> ch<PERSON>h", "token": "Token", "tokenHelp": "Token API từ GHN để xác thực", "tokenPlaceholder": "<PERSON><PERSON><PERSON><PERSON> từ GHN", "tokenPlaceholderEdit": "<PERSON><PERSON><PERSON><PERSON> mới (để trống nếu không thay đổi)", "tokenPlaceholderReadonly": "<PERSON><PERSON> liệu đã đư<PERSON><PERSON> mã hóa", "shopId": "Shop ID", "shopIdHelp": "Shop ID từ GHN để xác định cửa hàng", "shopIdPlaceholder": "Nhập Shop ID từ GHN", "shopIdPlaceholderEdit": "Nhập Shop ID mới (để trống nếu không thay đổi)", "shopIdPlaceholderReadonly": "<PERSON><PERSON> liệu đã đư<PERSON><PERSON> mã hóa", "timeout": "Timeout (ms)", "timeoutHelp": "Th<PERSON>i gian chờ tối đa cho mỗi request (mặc định: 30000ms)", "timeoutPlaceholder": "30000", "testConnection": "Test kết n<PERSON>i", "testConnectionResult": "<PERSON><PERSON><PERSON> quả kiểm tra kết nối GHN", "testConnectionSuccess": "✅ <PERSON><PERSON>t n<PERSON>i thành công", "testConnectionFailed": "❌ <PERSON><PERSON>t nối thất bại", "testConnectionError": "❌ Lỗi kết nối", "testConnectionSuccessDetails": "• API endpoint có thể truy cập được\n• Token và Shop ID xác thực hợp lệ\n• Sẵn sàng để sử dụng", "testConnectionErrorDetails": "• Kiểm tra lại Token và Shop ID có chính xác không\n• <PERSON><PERSON><PERSON> bảo kết nối internet ổn định\n• Liên hệ GHN nếu vấn đề vẫn tiếp tục", "testConnectionErrorDefault": "<PERSON><PERSON><PERSON><PERSON> thể kết nối đến GHN", "testConnectionTokenRequired": "Token là bắt buộc để test kết nối", "testConnectionShopIdRequired": "Shop ID là bắt buộc để test kết nối", "testMode": "Chế độ test", "createButton": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh GHN"}}, "ahamove": {"form": {"title": "<PERSON><PERSON><PERSON> <PERSON>", "displayName": "<PERSON><PERSON><PERSON> hiển thị", "displayNameHelp": "<PERSON><PERSON>n hiển thị cho cấu hình <PERSON> này", "displayNamePlaceholder": "Ví dụ: <PERSON><PERSON><PERSON> <PERSON><PERSON>", "mobile": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "mobileHelp": "<PERSON><PERSON> điện thoại từ Ahamove để xác thực", "mobilePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại từ Ahamove", "mobilePlaceholderEdit": "<PERSON><PERSON><PERSON><PERSON> số điện thoại mới (để trống nếu không thay đổi)", "mobilePlaceholderReadonly": "<PERSON><PERSON> liệu đã đư<PERSON><PERSON> mã hóa", "testMode": "Chế độ test", "createButton": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> h<PERSON>"}}}, "form": {"createTitle": "<PERSON><PERSON><PERSON><PERSON> nhà vận chuyển", "editTitle": "Chỉnh sửa nhà vận chuyển"}, "list": {"columns": {"providerName": "<PERSON><PERSON><PERSON> nhà vận chuyển", "providerType": "<PERSON><PERSON><PERSON>", "shopId": "Shop ID", "default": "Mặc định", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>"}}, "filters": {"providerType": "<PERSON><PERSON><PERSON> nhà vận chuyển", "status": "<PERSON><PERSON><PERSON><PERSON> thái"}, "actions": {"edit": "Chỉnh sửa", "test": "<PERSON><PERSON><PERSON> tra", "setDefault": "Đặt làm mặc định", "delete": "Xóa"}, "confirmations": {"deleteTitle": "<PERSON><PERSON><PERSON> nhà vận chuyển", "delete": "Bạn có chắc chắn muốn xóa nhà vận chuyển này?"}, "empty": {"title": "<PERSON><PERSON><PERSON> có nhà vận chuyển nào", "description": "<PERSON>ạn chưa thêm nhà vận chuyển nào. <PERSON><PERSON><PERSON> thêm nhà vận chuyển đầu tiên."}, "validation": {"providerType": {"invalid": "<PERSON>ạ<PERSON> nhà vận chuyển không hợp lệ"}, "providerName": {"required": "<PERSON>ên nhà vận chuyển là bắt buộc", "maxLength": "Tên nhà vận chuyển không được quá 100 ký tự"}, "apiKey": {"required": "API Key là bắt buộc", "maxLength": "API Key không đ<PERSON><PERSON><PERSON> quá 255 ký tự"}, "apiSecret": {"maxLength": "API Secret không đ<PERSON><PERSON><PERSON> quá 255 ký tự"}, "shopId": {"required": "Shop ID là bắt buộc", "maxLength": "Shop ID không đ<PERSON> quá 999999999"}, "clientId": {"required": "Client ID là b<PERSON> buộc", "maxLength": "Client ID không đ<PERSON><PERSON><PERSON> quá 255 ký tự"}, "settings": {"invalidJson": "<PERSON><PERSON><PERSON> hình không đúng định dạng JSON"}, "name": {"required": "<PERSON><PERSON><PERSON> hiển thị là bắt buộc", "maxLength": "Tên hiển thị không đư<PERSON><PERSON> quá 100 ký tự"}, "token": {"required": "<PERSON><PERSON> là b<PERSON> bu<PERSON>c", "maxLength": "Token không đư<PERSON><PERSON> quá 255 ký tự"}, "username": {"required": "<PERSON><PERSON><PERSON> l<PERSON> b<PERSON> bu<PERSON>c", "maxLength": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> quá 255 ký tự"}, "password": {"required": "Password là b<PERSON><PERSON> buộc", "minLength": "Password phải có ít nhất 6 ký tự", "maxLength": "Password không đ<PERSON><PERSON><PERSON> quá 255 ký tự"}, "mobile": {"required": "<PERSON><PERSON> điện thoại là bắt buộc", "minLength": "Số điện thoại phải có ít nhất 10 ký tự", "maxLength": "<PERSON>ố điện thoại không đ<PERSON><PERSON><PERSON> quá 15 ký tự", "format": "<PERSON><PERSON> điện tho<PERSON>i không đúng định dạng"}, "timeout": {"min": "Timeout ph<PERSON>i ít nhất 1000ms", "max": "Timeout kh<PERSON><PERSON> đ<PERSON><PERSON><PERSON> quá 300000ms"}, "phone": {"required": "<PERSON><PERSON> điện thoại là bắt buộc", "maxLength": "<PERSON>ố điện thoại không đ<PERSON><PERSON><PERSON> quá 20 ký tự"}, "address": {"required": "Địa chỉ là bắt buộc"}, "weight": {"min": "<PERSON><PERSON><PERSON><PERSON> lư<PERSON><PERSON> tối thiểu là 0.1kg"}, "serviceType": {"required": "Loại d<PERSON>ch v<PERSON> là bắ<PERSON> bu<PERSON>c"}, "timeOut": {"min": "Timeout tối thiểu là 1000ms", "max": "Timeout tối đa là 300000ms"}}}, "common": {"refresh": "<PERSON><PERSON><PERSON>"}, "sms": {"title": "<PERSON><PERSON><PERSON> hợp SMS FPT", "description": "<PERSON><PERSON><PERSON><PERSON> lý tích hợp nhà cung cấp dịch vụ SMS FPT", "providers": "<PERSON>hà cung cấp SMS", "providersDescription": "<PERSON><PERSON><PERSON><PERSON> lý các nhà cung cấp dịch vụ SMS FPT", "addProvider": "<PERSON><PERSON><PERSON><PERSON> nhà cung cấp", "editProvider": "Chỉnh sửa nhà cung cấp SMS", "deleteProvider": "<PERSON>óa nhà cung cấp", "testProvider": "Test nhà cung cấp SMS", "configName": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "configNameHelp": "<PERSON><PERSON> dụ: SMS Marketing, SMS Thông báo", "configNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên cấu hình", "name": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "nameHelp": "<PERSON>ên để nhận diện cấu hình này", "namePlaceholder": "VD: Twilio Production", "displayName": "<PERSON><PERSON><PERSON> hiển thị", "displayNameHelp": "<PERSON><PERSON><PERSON> hiển thị trong giao di<PERSON>n", "displayNamePlaceholder": "VD: SMS Chính", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "credentials": "Thông tin xác thực", "isActive": "<PERSON><PERSON><PERSON>", "isDefault": "Đặt làm mặc định", "default": "Mặc định", "provider": "<PERSON>hà cung cấp SMS", "providerType": "Loại nhà cung cấp", "selectProvider": "<PERSON><PERSON><PERSON> nhà cung cấp", "otherProvider": "K<PERSON><PERSON><PERSON>", "fromPhone": "<PERSON><PERSON> điện tho<PERSON>i g<PERSON>i", "fromPhoneHelp": "<PERSON><PERSON> điện thoại hiển thị khi gửi SMS", "fromNumber": "Số gửi", "fromName": "<PERSON><PERSON><PERSON>", "lastTested": "Test lần cuối", "apiKey": "API Key", "apiKeyHelp": "API Key từ nhà cung cấp", "apiSecret": "API Secret", "apiSecretHelp": "API Secret từ nhà cung cấp", "accountSidHelp": "Account SID từ <PERSON><PERSON><PERSON>", "authTokenHelp": "<PERSON>th Token từ <PERSON><PERSON><PERSON>", "accessKeyIdHelp": "AWS Access Key ID", "secretAccessKeyHelp": "AWS Secret Access Key", "regionHelp": "AWS Region (vd: us-east-1)", "usernameHelp": "<PERSON><PERSON><PERSON>", "passwordHelp": "Password tài <PERSON>n", "endpointHelp": "URL endpoint của API", "configurationSteps": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> c<PERSON>u hình", "testConnection": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "testResult": "<PERSON><PERSON><PERSON> quả kiểm tra kết nối", "testSuccess": "Test thành công", "testFailed": "Test thất bại", "test": "Test", "activate": "<PERSON><PERSON><PERSON>", "deactivate": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh", "update": "<PERSON><PERSON><PERSON>", "testPhoneNumber": "<PERSON><PERSON> điện tho<PERSON><PERSON> nhận", "testPhoneNumberHelp": "<PERSON><PERSON><PERSON><PERSON> số điện thoại để nhận tin nhắn test", "testMessage": "<PERSON><PERSON><PERSON> dung tin nhắn", "testMessageHelp": "Nội dung tin nhắn test (tối đa 160 ký tự)", "testMessagePlaceholder": "<PERSON><PERSON><PERSON> là tin nhắn test từ hệ thống", "sendTest": "Gửi test", "searchPlaceholder": "T<PERSON>m kiếm theo tên...", "filterByStatus": "<PERSON><PERSON><PERSON> theo trạng thái", "filterByType": "<PERSON><PERSON><PERSON> the<PERSON>", "allStatuses": "<PERSON><PERSON><PERSON> cả trạng thái", "allTypes": "<PERSON><PERSON><PERSON> c<PERSON> lo<PERSON>i", "active": "<PERSON><PERSON><PERSON> động", "showingResults": "<PERSON><PERSON><PERSON> thị {{start}}-{{end}} trong tổng số {{total}} kết quả", "loading": "<PERSON><PERSON> tả<PERSON>...", "noResultsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "tryDifferentFilters": "Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm", "noProviders": "Chưa có nhà cung cấp nào", "addFirstProvider": "<PERSON>hê<PERSON> nhà cung cấp SMS đầu tiên để bắt đầu", "deleteConfirmation": "<PERSON>ạn có chắc chắn muốn xóa nhà cung cấp \"{{name}}\"?", "deleteWarning": "<PERSON><PERSON>nh động này không thể hoàn tác. T<PERSON>t cả cấu hình liên quan sẽ bị xóa.", "createSuccess": "<PERSON><PERSON><PERSON> nhà cung cấp thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nhật nhà cung cấp thành công", "deleteSuccess": "<PERSON><PERSON><PERSON> nhà cung cấp thành công", "statusUpdateSuccess": "<PERSON><PERSON><PERSON> nhật trạng thái thành công", "testSentSuccess": "<PERSON><PERSON><PERSON> tin nhắn test thành công", "saveError": "<PERSON><PERSON> lỗi xảy ra khi lưu", "deleteError": "Có lỗi xảy ra khi xóa", "statusUpdateError": "<PERSON><PERSON> lỗi xảy ra", "testSentError": "Có lỗi xảy ra khi gửi test", "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON>", "error": "Lỗi", "testing": "Đang test", "pending": "Chờ xử lý"}, "twilio": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> t<PERSON>ch hợp v<PERSON><PERSON> dịch vụ SMS Twilio", "breadcrumb": "<PERSON><PERSON><PERSON>", "form": {"title": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> thông tin để tạo tích hợp <PERSON><PERSON><PERSON> mới", "fields": {"integrationName": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>", "integrationNameHelp": "<PERSON>ên để nhận diện tích hợp này", "integrationNamePlaceholder": "Ví dụ: <PERSON><PERSON><PERSON> Production SMS", "authToken": "<PERSON><PERSON><PERSON>", "authTokenHelp": "<PERSON>th Token từ <PERSON><PERSON><PERSON>", "authTokenPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "baseDomain": "Twilio Base Domain", "baseDomainHelp": "Domain cơ sở củ<PERSON> (ví dụ: api.twilio.com)", "baseDomainPlaceholder": "Nhập Twilio Base Domain"}}, "placeholders": {"integrationName": "<PERSON><PERSON><PERSON><PERSON> tên tích hợp", "authToken": "<PERSON><PERSON><PERSON><PERSON>", "baseDomain": "Nhập Twilio Base Domain"}, "success": {"created": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> hợ<PERSON><PERSON><PERSON> thành công", "createdDescription": "<PERSON><PERSON><PERSON> hợp \"{{name}}\" đã đư<PERSON><PERSON> tạo thành công."}, "error": {"createFailed": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> hợ<PERSON><PERSON><PERSON> thất bại", "createFailedDescription": "<PERSON><PERSON> xảy ra lỗi khi tạo tích hợp. <PERSON><PERSON> lòng thử lại."}, "validation": {"integrationName": {"required": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> hợ<PERSON> là bắ<PERSON> bu<PERSON>c", "maxLength": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> hợp không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 100 ký tự"}, "authToken": {"required": "<PERSON><PERSON><PERSON> là bắ<PERSON> bu<PERSON>", "minLength": "<PERSON><PERSON><PERSON> phải có ít nhất 10 ký tự"}, "baseDomain": {"required": "Twilio Base Domain là bắt buộc", "minLength": "Twilio Base Domain phải có ít nhất 3 ký tự"}}}, "fpt": {"title": "FPT SMS Brandname", "description": "<PERSON><PERSON><PERSON> hợp v<PERSON><PERSON> dịch vụ SMS Brandname của FPT Telecom", "list": {"title": "<PERSON><PERSON><PERSON>n lý FPT SMS Brandname", "description": "<PERSON><PERSON><PERSON><PERSON> lý danh sách cấu hình FPT SMS Brandname", "columns": {"integrationName": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>", "brandName": "Brandname", "clientId": "Client ID", "endpoint": "Endpoint", "createdAt": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON>"}}, "actions": {"edit": "Chỉnh sửa", "delete": "Xóa", "testConnection": "Test kết n<PERSON>i", "moreActions": "<PERSON><PERSON><PERSON><PERSON> thao tác"}, "confirmations": {"deleteTitle": "<PERSON><PERSON><PERSON>n x<PERSON>a", "delete": "Bạn có chắc chắn muốn xóa cấu hình này không?"}, "form": {"createTitle": "<PERSON><PERSON><PERSON> c<PERSON>u hình FPT SMS Brandname", "editTitle": "Chỉnh sửa cấu hình FPT SMS Brandname", "createDescription": "<PERSON><PERSON><PERSON><PERSON> thông tin để tạo tích hợp FPT SMS Brandname mới", "editDescription": "<PERSON><PERSON><PERSON> nhật thông tin cấu hình FPT SMS Brandname", "integrationName": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>", "integrationNameHelp": "<PERSON>ên để nhận diện tích hợp này", "integrationNamePlaceholder": "Ví dụ: FPT SMS Brandname - Công ty ABC", "clientId": "Client ID", "clientIdHelp": "Client ID từ FPT SMS để xác thực", "clientIdPlaceholder": "Nhập Client ID từ FPT SMS", "clientSecret": "Client Secret", "clientSecretHelp": "Client Secret từ FPT SMS để xác thực", "clientSecretPlaceholder": "Nhập Client Secret từ FPT SMS", "brandName": "Brandname", "brandNameHelp": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> hiệu hiển thị khi gửi SMS", "brandNamePlaceholder": "Nhập brandname", "testConnection": "Test kết n<PERSON>i", "testConnectionDescription": "<PERSON><PERSON>m tra kết nối với FPT SMS API", "testNow": "Test ngay", "testing": "Đang test...", "create": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh", "cancel": "<PERSON><PERSON><PERSON>", "saving": "<PERSON><PERSON> l<PERSON>...", "required": "<PERSON><PERSON><PERSON> b<PERSON>"}, "test": {"saveFirst": "<PERSON><PERSON> lòng lưu cấu hình trư<PERSON>c khi test kết nối", "failed": "Test kết n<PERSON>i thất bại"}, "success": {"created": "C<PERSON>u hình FPT SMS thành công!", "createdDescription": "<PERSON><PERSON><PERSON> hợp \"{{name}}\" đã đư<PERSON><PERSON> tạo thành công.", "updated": "<PERSON><PERSON><PERSON> nhật cấu hình thành công!", "deleted": "<PERSON><PERSON>a cấu hình thành công!"}, "error": {"createFailed": "<PERSON><PERSON><PERSON> hình FPT SMS thất bại", "createFailedDescription": "<PERSON><PERSON> xảy ra lỗi khi cấu hình. <PERSON><PERSON> lòng thử lại.", "updateFailed": "<PERSON><PERSON><PERSON> nhật cấu hình thất bại", "deleteFailed": "<PERSON><PERSON><PERSON> cấu hình thất bại", "invalidConfig": "Thông tin cấu hình không hợp lệ"}}}, "email": {"title": "<PERSON><PERSON><PERSON> <PERSON>ail SMTP", "description": "<PERSON><PERSON><PERSON> hình tích hợp với nhà cung cấp SMTP", "configName": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "configNameHelp": "Ví dụ: <PERSON><PERSON>, <PERSON><PERSON> c<PERSON> nhân", "configNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên cấu hình", "senderEmail": "<PERSON><PERSON>", "senderName": "<PERSON><PERSON><PERSON><PERSON>i", "senderNameHelp": "<PERSON><PERSON>n hiển thị khi người nhận xem email", "senderNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON>i", "smtpHost": "SMTP Host", "smtpHostHelp": "Ví dụ: smtp.gmail.com, smtp.office365.com", "smtpPort": "SMTP Port", "smtpPortHelp": "Ví dụ: 587 cho TLS, 465 cho SSL", "requireSSL": "<PERSON><PERSON><PERSON> c<PERSON>u SSL", "securityProtocol": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> b<PERSON><PERSON> mật", "requireAuth": "<PERSON><PERSON><PERSON> c<PERSON>u x<PERSON>c thực", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "password": "<PERSON><PERSON><PERSON>", "testConnection": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "testResult": "<PERSON><PERSON><PERSON> quả kiểm tra kết nối", "testSuccess": "<PERSON><PERSON><PERSON> n<PERSON>i thành công đến máy chủ email", "testError": "<PERSON><PERSON><PERSON><PERSON> thể kết nối đến máy chủ email. <PERSON><PERSON> lòng kiểm tra lại cấu hình.", "saveSuccess": "<PERSON><PERSON><PERSON> c<PERSON>u hình <PERSON> thành công", "saveError": "Lỗi khi lưu cấu hình email", "smtp": {"title": "<PERSON><PERSON><PERSON> h<PERSON>nh SM<PERSON>", "description": "<PERSON><PERSON><PERSON> hình máy chủ SMTP cho gửi email đi", "form": {"fields": {"serverName": "<PERSON><PERSON><PERSON> m<PERSON> chủ", "host": "Máy chủ SMTP", "port": "Cổng", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "password": "<PERSON><PERSON><PERSON>", "useSsl": "Sử dụng SSL", "useStartTls": "<PERSON>ử dụng StartTLS", "isActive": "<PERSON><PERSON><PERSON>", "additionalSettings": "<PERSON><PERSON><PERSON> đặt b<PERSON> sung (JSON)"}, "placeholders": {"serverName": "<PERSON><PERSON><PERSON><PERSON> tên máy chủ SMTP", "host": "smtp.gmail.com", "port": "587", "username": "<EMAIL>", "password": "<PERSON><PERSON><PERSON><PERSON> mật khẩu hoặc app password", "additionalSettings": "{\"timeout\": 30000}"}, "test": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "testDescription": "<PERSON><PERSON><PERSON><PERSON> email để nhận email test từ cấu hình SMTP này"}, "actions": {"save": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh", "sendTest": "Gửi test"}, "saveSuccess": "Cấu hình SMTP đã được lưu thành công!", "saveError": "<PERSON><PERSON> lỗi xảy ra khi lưu cấu hình SMTP"}}, "social": {"title": "<PERSON><PERSON><PERSON> h<PERSON>p mạng xã hội", "description": "<PERSON><PERSON><PERSON><PERSON> lý tích hợp với các mạng xã hội", "facebook": "Facebook", "instagram": "Instagram", "twitter": "Twitter", "linkedin": "LinkedIn", "youtube": "YouTube", "tiktok": "TikTok", "connect": "<PERSON><PERSON><PERSON>", "disconnect": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>", "connected": "<PERSON><PERSON> kết nối", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON>", "connectSuccess": "<PERSON><PERSON><PERSON> n<PERSON>i thành công với {{platform}}", "connectError": "Lỗi khi kết nối với {{platform}}", "disconnectSuccess": "<PERSON><PERSON> ngắt kết nối với {{platform}}", "disconnectError": "Lỗi khi ngắt kết nối với {{platform}}", "networkAriaLabel": "<PERSON><PERSON><PERSON> xã hội {{name}}"}, "zalo": {"personal": {"title": "<PERSON><PERSON><PERSON> c<PERSON>hân", "description": "<PERSON>ết nối tài k<PERSON>n <PERSON> cá nhân để sử dụng các tính năng marketing", "qrTitle": "<PERSON><PERSON><PERSON> mã QR để kết nối", "qrInstruction": "Sử dụng ứng dụng Zalo để quét mã QR", "qrDescription": "Mở ứng dụng <PERSON> > Q<PERSON><PERSON> mã QR > Quét mã bên dư<PERSON>i", "qrNote": "Mã QR sẽ hết hạn sau 5 phút", "qrGenerated": "QR Code đã đư<PERSON><PERSON> tạo", "qrError": "<PERSON><PERSON><PERSON>ng thể tạo QR Code", "connected": "<PERSON><PERSON> kết nối thành công v<PERSON><PERSON>", "statusTitle": "<PERSON><PERSON><PERSON><PERSON> thái kết n<PERSON>i", "status": {"disconnected": "<PERSON><PERSON><PERSON> k<PERSON>", "connecting": "<PERSON><PERSON> chờ kết nối...", "connected": "<PERSON><PERSON> kết nối thành công"}, "instructionsTitle": "Hướng dẫn kết nối", "step1": "Mở ứng dụng <PERSON> trên điện thoại", "step2": "<PERSON><PERSON><PERSON> biểu tư<PERSON> quét mã QR", "step3": "<PERSON><PERSON><PERSON> mã QR hiển thị bên trái", "step4": "<PERSON><PERSON><PERSON> nhận kết nối trên <PERSON>ng dụng <PERSON>", "featuresTitle": "<PERSON><PERSON><PERSON> n<PERSON>ng sau khi kết nối", "feature1": "<PERSON><PERSON><PERSON> tin nhắn tự động", "feature2": "<PERSON><PERSON><PERSON><PERSON> lý danh sách bạn bè", "feature3": "Tạo chiến dịch marketing", "feature4": "<PERSON><PERSON><PERSON><PERSON> kê hiệu quả"}}, "facebook": {"title": "Facebook", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tài k<PERSON>n Facebook đã liên kết", "addPage": "Thêm Facebook Page", "connecting": "<PERSON><PERSON> kết nối...", "processing": "<PERSON><PERSON> x<PERSON> lý kết nối Facebook...", "search": "<PERSON><PERSON><PERSON> Facebook Page...", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách Facebook Pages", "noPages": "Chưa có Facebook Page nào", "noPagesDescription": "<PERSON><PERSON>n chưa liên kết Facebook Page nào. <PERSON><PERSON><PERSON> thêm Facebook Page để bắt đầu.", "confirmDelete": "Bạn có chắc chắn muốn xóa Facebook Page này?", "connectAgent": "<PERSON><PERSON><PERSON>", "disconnectAgent": "<PERSON><PERSON><PERSON> k<PERSON>", "status": {"label": "<PERSON><PERSON><PERSON><PERSON> thái", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "error": "Lỗi"}, "agent": {"label": "Agent"}, "pageName": "<PERSON><PERSON><PERSON>", "personalName": "<PERSON><PERSON><PERSON> c<PERSON> nhân", "pageId": "Page ID", "isActive": "<PERSON><PERSON> ho<PERSON>t động", "hasError": "<PERSON><PERSON> lỗi"}, "accounts": {"title": "<PERSON><PERSON><PERSON> k<PERSON>n liên kết", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tài khoản đã liên kết với hệ thống", "addAccount": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "removeAccount": "<PERSON><PERSON><PERSON> t<PERSON>", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "accountType": "<PERSON><PERSON><PERSON> tà<PERSON>", "linkedDate": "<PERSON><PERSON><PERSON> li<PERSON>", "noAccounts": "<PERSON><PERSON><PERSON> có tài khoản nào đ<PERSON><PERSON><PERSON> liên kết", "confirmRemove": "Bạn có chắc chắn muốn xóa tài khoản này?", "removeSuccess": "<PERSON><PERSON><PERSON> tài kho<PERSON>n thành công", "removeError": "Lỗi khi xóa tài k<PERSON>n", "defaultAccount": "<PERSON><PERSON><PERSON> k<PERSON>n mặc định", "failedToLoad": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách tài k<PERSON>n"}, "website": {"title": "<PERSON><PERSON><PERSON> Website", "description": "<PERSON><PERSON><PERSON><PERSON> lý tích hợp với website", "domain": "<PERSON><PERSON><PERSON>", "apiKey": "API Key", "secretKey": "Secret Key", "webhookUrl": "Webhook URL", "generateKey": "Tạo key mới", "copyKey": "Sao chép", "keyCopied": "Đã sao chép vào clipboard", "saveSuccess": "<PERSON><PERSON><PERSON> c<PERSON>u hình website thành công", "saveError": "Lỗi khi lưu cấu hình website", "confirmActivate": "Bạn có chắc chắn muốn bật website này?", "confirmDeactivate": "Bạn có chắc chắn muốn tắt website này?", "host": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "verified": "<PERSON><PERSON> xác thực", "notVerified": "<PERSON><PERSON><PERSON> x<PERSON> thực", "connected": "<PERSON><PERSON> kết nối", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON>", "agent": "Agent", "noAgent": "<PERSON><PERSON><PERSON> k<PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "widgetScript": "Widge<PERSON>", "widgetScriptDesc": "Sao chép và dán đoạn script này vào website của bạn để tích hợp chat widget.", "createTitle": "Thêm Website Mới", "createSuccess": "Tạo website thành công!", "createSuccessDesc": "Website đã đ<PERSON><PERSON><PERSON> thêm vào danh sách.", "createError": "Tạo website thất bại!", "createErrorDesc": "<PERSON><PERSON> lòng thử lại sau.", "creating": "<PERSON><PERSON> tạo...", "create": "Tạo Website", "deleteSuccess": "Xóa website thành công!", "deleteSuccessDesc": "Website đã đư<PERSON>c xóa khỏi danh sách.", "deleteError": "Xóa website thất bại!", "deleteErrorDesc": "<PERSON><PERSON> lòng thử lại sau.", "copySuccess": "Đã sao chép!", "copySuccessDesc": "Script đã đư<PERSON>c sao chép vào clipboard.", "copyScript": "Sao chép script", "noWebsites": "Chưa có Website nào", "noWebsitesDescription": "Bạn chưa thêm Website nào. Hãy thêm Website để bắt đầu.", "addWebsite": "Thêm Website", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "noSearchResultsDescription": "Không có website nào phù hợp với tiêu chí tìm kiếm của bạn.", "clearFilters": "Xóa bộ lọc", "confirmDelete": "<PERSON><PERSON><PERSON>n x<PERSON>a", "confirmDeleteDesc": "Bạn có chắc chắn muốn xóa website này? Hành động này không thể hoàn tác.", "deleting": "Đang xóa...", "form": {"websiteName": "Tên Website", "websiteNamePlaceholder": "Nhập tên website", "host": "Host/Domain", "hostPlaceholder": "redai.vn hoặc https://www.redai.vn", "hostDescription": "Nhập domain hoặc URL đầy đủ. <PERSON><PERSON> thống sẽ tự động chuẩn hóa.", "logo": "Logo Website", "clickToUploadLogo": "Click vào khung để tải lên logo", "clickToChangeLogo": "Click vào ảnh để thay đổi logo", "logoDescription": "<PERSON><PERSON><PERSON> nhận file ảnh (JPEG, PNG, WebP), tối đa 5MB"}, "filter": {"websiteName": "Tên Website", "host": "Host", "createdAt": "<PERSON><PERSON><PERSON>", "verify": "<PERSON>r<PERSON><PERSON> thái xác thực", "asc": "<PERSON><PERSON><PERSON>", "desc": "<PERSON><PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "verified": "<PERSON><PERSON> xác thực", "unverified": "<PERSON><PERSON><PERSON> x<PERSON> thực"}, "logoError": "Lỗi logo", "logoErrorDesc": "Chỉ chấp nhận file ảnh", "logoSizeError": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> file kh<PERSON><PERSON> đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 5MB", "logoUpdateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> logo thành công!", "logoUpdateSuccessDesc": "Logo website đã đư<PERSON><PERSON> cập nh<PERSON>t.", "logoUpdateError": "<PERSON><PERSON><PERSON> nhật logo thất bại!", "logoUpdateErrorDesc": "<PERSON><PERSON> lòng thử lại sau.", "editBoxChatConfig": "<PERSON><PERSON><PERSON> h<PERSON>nh box chat", "changeLogo": "Đổi logo"}, "bankAccount": {"mb": {"title": "<PERSON>h s<PERSON>ch tài khoản MB Bank", "description": "Tài khoản MB Bank đã tích hợp "}, "acb": {"title": "<PERSON>h s<PERSON>ch tài khoản ACB Bank", "description": "Tài khoản ACB Bank đã tích hợp "}, "ocb": {"title": "Danh s<PERSON>ch tài khoản OCB Bank", "description": "Tài khoản OCB Bank đã tích hợp "}, "kienlong": {"title": "<PERSON><PERSON> s<PERSON>ch tài k<PERSON><PERSON> Ki<PERSON>n Long Bank", "description": "<PERSON><PERSON><PERSON> <PERSON><PERSON>n Long Bank đã tích hợp "}, "createVirtualAccount": "<PERSON><PERSON><PERSON> tà<PERSON> k<PERSON>o", "addAccount": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "disableVA": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "enableVA": "<PERSON><PERSON><PERSON> ", "reconnectAccount": "<PERSON><PERSON><PERSON><PERSON> lại", "reconnectOtpSentMessage": "Mã OTP đã được gửi đến số điện thoại để xác nhận kết nối lại tài khoản", "createVirtualAccountFromReal": "<PERSON><PERSON><PERSON> tà<PERSON> k<PERSON>o", "realAccount": "<PERSON><PERSON><PERSON>", "accountType": "<PERSON><PERSON><PERSON> tà<PERSON>", "forceDeleteWarning": "Bạn có chắc chắn muốn xóa tài khoản này? Tài khoản này chưa được kết nối với API ngân hàng nên sẽ được xóa ngay lập tức.", "confirmDelete": "<PERSON><PERSON><PERSON>n x<PERSON>a", "confirmReconnect": "<PERSON><PERSON><PERSON> n<PERSON>n kết n<PERSON>i lại", "otpSentMessage": "Mã OTP đã đư<PERSON><PERSON> gửi đến số điện thoại", "enterOtpToDelete": "<PERSON><PERSON><PERSON><PERSON> mã OTP để xác nhận xóa tài k<PERSON>n", "balance": "Số dư", "title": "<PERSON><PERSON><PERSON> hợp tài k<PERSON>n ngân hàng", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tài khoản ngân hàng đã tích hợp với hệ thống", "createTitle": "<PERSON><PERSON><PERSON><PERSON> tài k<PERSON>n ngân hàng", "createDescription": "<PERSON><PERSON><PERSON><PERSON> thông tin tài khoản ngân hàng để tích hợp với hệ thống", "bankName": "<PERSON><PERSON> h<PERSON>", "reconnect": "<PERSON><PERSON><PERSON><PERSON> lại", "selectBank": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "label": "<PERSON><PERSON>n g<PERSON>i nh<PERSON>", "accountNumber": "Số tài <PERSON>n", "bankApiConnected": "Kết nối API ngân hàng", "accountNumberPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số tài <PERSON>n", "accountNumberHelp": "<PERSON><PERSON><PERSON><PERSON> số tài k<PERSON>n ngân hàng (6-20 ký tự số)", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "connectionStatus": "<PERSON><PERSON><PERSON><PERSON> thái kết n<PERSON>i", "connected": "<PERSON><PERSON> kết nối", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON>", "activated": "Đ<PERSON> kích ho<PERSON>", "notActivated": "<PERSON><PERSON><PERSON> k<PERSON>", "accountHolderName": "<PERSON>ên chủ tài k<PERSON>n", "idNumber": "Số CMND/CCCD", "idNumberPlaceholder": "Nhập số CMND/CCCD", "idNumberHelp": "Nhập số CMND/CCCD đã đăng ký với ngân hàng", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "phoneNumberPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "phoneNumberHelp": "Số điện thoại đã đăng ký với ngân hàng để nhận OTP", "storeName": "<PERSON><PERSON><PERSON> đi<PERSON> bán", "storeNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên điểm bán", "storeNameHelp": "<PERSON><PERSON><PERSON> cửa hàng/đi<PERSON><PERSON> bán hàng của bạn", "storeAddress": "Đ<PERSON>a chỉ điểm bán", "storeAddressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ điểm bán", "storeAddressHelp": "Đ<PERSON>a chỉ chi tiết của cửa hàng/điểm bán hàng", "create": "<PERSON><PERSON><PERSON> t<PERSON>", "bankInfo": "Thông tin ngân hàng", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "statusActive": "<PERSON><PERSON><PERSON> đ<PERSON>", "statusPending": "<PERSON><PERSON> xác thực", "statusInactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "statusSuspended": "<PERSON><PERSON><PERSON>", "statusExpired": "<PERSON><PERSON><PERSON>", "virtualAccount": "<PERSON><PERSON><PERSON>", "createVA": "<PERSON><PERSON><PERSON> tà<PERSON> k<PERSON>o", "createSuccess": "<PERSON><PERSON><PERSON> tài k<PERSON>n thành công", "createSuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng đã đư<PERSON><PERSON> tạo thành công", "createError": "<PERSON><PERSON><PERSON> tài k<PERSON>n thất bại", "createErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể tạo tài khoản ngân hàng. <PERSON><PERSON> lòng kiểm tra lại thông tin.", "createCompleteSuccess": "<PERSON><PERSON><PERSON> tài k<PERSON>n hoàn tất", "createCompleteSuccessDescription": "<PERSON><PERSON><PERSON> kho<PERSON>n ngân hàng đã được tạo và kích hoạt thành công", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "deleteSuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng đã đư<PERSON>c xóa thành công", "deleteError": "<PERSON><PERSON><PERSON> thất b<PERSON>i", "deleteErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể xóa tài khoản ngân hàng. <PERSON><PERSON> lòng thử lại.", "deleteConfirmTitle": "<PERSON><PERSON><PERSON>n x<PERSON>a tài <PERSON>n", "deleteConfirmMessage": "Bạn có chắc chắn muốn xóa tài khoản ngân hàng này? Hành động này không thể hoàn tác.", "bulkDeleteSuccess": "<PERSON><PERSON><PERSON> thành công", "bulkDeleteSuccessDescription": "<PERSON><PERSON> xóa {{count}} tài kho<PERSON>n ngân hàng", "bulkDeleteError": "<PERSON><PERSON><PERSON> thất b<PERSON>i", "bulkDeleteErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể xóa các tài khoản ngân hàng. <PERSON><PERSON> lòng thử lại.", "bulkDeleteConfirmTitle": "<PERSON><PERSON><PERSON> n<PERSON>n x<PERSON>a nhiều tài k<PERSON>n", "bulkDeleteConfirmMessage": "Bạn có chắc chắn muốn xóa {{count}} tài khoản ngân hàng đã chọn? Hành động này không thể hoàn tác.", "selectAccountsToDelete": "<PERSON><PERSON> lòng chọn ít nhất một tài khoản để xóa", "createVASuccess": "<PERSON><PERSON><PERSON> tài k<PERSON>n <PERSON>o thành công", "createVASuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n ảo đã đư<PERSON><PERSON> tạo thành công", "createVAError": "<PERSON><PERSON><PERSON> tài k<PERSON>n <PERSON>o thất bại", "createVAErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể tạo tài kho<PERSON>n ảo. <PERSON><PERSON> lòng thử lại.", "activateSuccess": "<PERSON><PERSON><PERSON> ho<PERSON>t thành công", "activateSuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng đã đư<PERSON><PERSON> kích hoạt thành công", "otpVerification": "<PERSON><PERSON><PERSON>", "otpVerificationDescription": "<PERSON><PERSON><PERSON><PERSON> mã O<PERSON> được gửi đến số điện thoại đã đăng ký với {{bankName}}", "enterOtp": "<PERSON>hậ<PERSON> mã OTP", "otpSent": "Mã OTP đã đ<PERSON><PERSON><PERSON> g<PERSON>i", "otpSentDescription": "<PERSON><PERSON> lòng kiểm tra tin nhắn SMS trên điện thoại của bạn", "otpSendError": "Gửi OTP thất bại", "otpSendErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể gửi mã OTP. <PERSON><PERSON> lòng thử lại sau.", "resendOtp": "Gửi lại mã OTP", "resendOtpCountdown": "G<PERSON>i lại mã sau {{countdown}} giây", "verify": "<PERSON><PERSON><PERSON> th<PERSON>c", "otpVerifySuccess": "<PERSON><PERSON><PERSON> thực thành công", "otpVerifySuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng đã đư<PERSON><PERSON> kích hoạt thành công", "otpVerifyError": "<PERSON><PERSON><PERSON> thực thất bại", "otpVerifyErrorDescription": "Mã OTP không đúng hoặc đã hết hạn. <PERSON><PERSON> lòng thử lại.", "invalidOtpLength": "Mã OTP không đúng độ dài", "invalidOtpLengthDescription": "Mã OTP phải có {{length}} ký tự", "acbOtpDescription": "<PERSON><PERSON><PERSON><PERSON> mã O<PERSON> được gửi đến số điện thoại đã đăng ký với ACB để hoàn tất kết nối", "ocbOtpVerification": "<PERSON><PERSON><PERSON> thực OTP OCB", "ocbOtpDescription": "<PERSON><PERSON><PERSON><PERSON> mã O<PERSON> được gửi đến số điện thoại đã đăng ký với OCB để hoàn tất kết nối", "ocbOtpLengthError": "Mã OTP phải có 6 ký tự", "mbOtpDescription": "<PERSON>hậ<PERSON> mã O<PERSON> được gửi đến số điện thoại đã đăng ký với MB Bank để hoàn tất kết nối", "klbOtpVerification": "<PERSON><PERSON><PERSON> thực OTP Kiên Long Bank", "klbOtpDescription": "<PERSON><PERSON><PERSON><PERSON> mã O<PERSON> được gửi đến số điện thoại đã đăng ký với Kiên Long Bank để hoàn tất kết nối", "klbOtpLengthError": "Mã OTP phải có 6 ký tự", "resendOtpIn": "G<PERSON>i lại mã sau {{seconds}} giây", "otpExpired": "Mã OTP đã hết hạn", "resendOtpSuccess": "Gửi lại mã OTP thành công", "resendOtpSuccessDescription": "Mã OTP mới đã được gửi đến số điện thoại đã đăng ký", "resendOtpError": "Gửi lại mã OTP thất bại", "activatingConnection": "<PERSON><PERSON> kích hoạt kết nối với ngân hàng...", "connectionCompleted": "<PERSON>ết nối đã đư<PERSON>c thiết lập thành công!", "pleaseWait": "<PERSON>ui lòng đợi...", "setupComplete": "<PERSON><PERSON><PERSON><PERSON> lập hoàn tất!", "otpProcessError": "<PERSON><PERSON> lỗi xảy ra trong quá trình xác thực", "connectionSuccess": "<PERSON><PERSON><PERSON> n<PERSON>i thành công", "mbConnectionSuccessDescription": "Tài khoản MB Bank đã được liên kết và kích hoạt thành công", "acbConnectionSuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n ACB đã được liên kết và kích hoạt thành công", "ocbConnectionSuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n OCB đã đư<PERSON>c liên kết và kích hoạt thành công", "klbConnectionSuccessDescription": "<PERSON><PERSON><PERSON> kho<PERSON>n Kiên Long Bank đã được liên kết và kích hoạt thành công", "otpError": "Lỗi OTP", "otpLengthError": "Mã OTP phải có 8 ký tự", "enterOtpToReconnect": "<PERSON><PERSON><PERSON><PERSON> mã OTP để kết nối lại tài k<PERSON>n", "deleteOtpSentMessage": "Mã OTP đã được gửi đến số điện thoại để xác nhận xóa tài k<PERSON>n", "disableVaError": "Có lỗi xảy ra khi vô hiệu hóa tài khoản ảo", "enableVaError": "<PERSON><PERSON> lỗi xảy ra khi kích hoạt tài kho<PERSON>n ảo", "disableVATooltip": "<PERSON><PERSON> hiệu hóa tài k<PERSON>n <PERSON>o", "enableVATooltip": "<PERSON><PERSON><PERSON> ho<PERSON>t tài k<PERSON>o", "createVirtualAccountTooltip": "<PERSON><PERSON><PERSON> tà<PERSON> k<PERSON>o", "createVirtualAccountFromRealTooltip": "Tạo tài khoản ảo từ tài khoản gốc", "confirmVirtualAccount": "<PERSON><PERSON><PERSON>n tài k<PERSON>o", "vaOtpSentMessage": "Mã OTP đã đư<PERSON><PERSON> gửi đến email", "enterOtpToConfirmVa": "<PERSON><PERSON><PERSON><PERSON> mã OTP để xác nhận tạo tài kho<PERSON>n <PERSON>o", "enterEmailForVa": "<PERSON><PERSON> lòng nhập email để tạo tài kho<PERSON>n <PERSON>o", "invalidEmail": "<PERSON><PERSON> h<PERSON> l<PERSON>", "email": "Email", "confirmVa": "<PERSON><PERSON><PERSON> nh<PERSON>n tạo tài k<PERSON>n <PERSON>o", "createVaSuccess": "<PERSON><PERSON><PERSON> tài k<PERSON>n <PERSON>o thành công", "createVaError": "<PERSON><PERSON> lỗi xảy ra khi tạo tài khoản ảo"}, "banking": {"bankAccount": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng", "bank": "<PERSON><PERSON> h<PERSON>", "connectionMethod": "<PERSON><PERSON><PERSON><PERSON> thức kết n<PERSON>i", "estimatedBalance": "Số dư tạm t<PERSON>h", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "accountNumber": "Số tài <PERSON>n", "connectApi": "Kết nối API Banking", "validation": {"bankId": {"required": "Bank ID là bắt buộc"}, "accountNumber": {"required": "Số tài k<PERSON>n là bắt buộc", "minLength": "Số tài k<PERSON>n phải có ít nhất 6 ký tự", "maxLength": "Số tài khoản không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 20 ký tự", "numbersOnly": "Số tài k<PERSON>n chỉ được chứa số"}, "accountHolderName": {"required": "Tên chủ tài khoản là bắt buộc", "minLength": "Tên chủ tài khoản phải có ít nhất 2 ký tự", "maxLength": "Tên chủ tài khoản không đ<PERSON><PERSON><PERSON> vư<PERSON><PERSON> quá 100 ký tự", "lettersOnly": "Tên chủ tài khoản chỉ được chứa chữ cái và khoảng trắng"}, "phoneNumber": {"required": "<PERSON><PERSON> điện thoại là bắt buộc", "minLength": "Số điện thoại phải có ít nhất 10 chữ số", "maxLength": "<PERSON><PERSON> điện tho<PERSON>i không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 20 ký tự", "numbersOnly": "<PERSON><PERSON> điện thoại chỉ được chứa số"}, "identificationNumber": {"required": "Số CMND/CCCD là bắt buộc", "minLength": "Số CMND/CCCD phải có ít nhất 9 ký tự", "maxLength": "Số CMND/CCCD không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 12 ký tự", "numbersOnly": "Số CMND/CCCD chỉ được chứa số"}, "label": {"lengthRange": "Tên gợi nhớ phải có từ 2-100 ký tự", "required": "<PERSON>ên gợi nhớ là bắt buộc"}}, "acb": {"title": "Liên kết tài khoản ACB", "description": "<PERSON><PERSON><PERSON><PERSON> thông tin tài khoản ngân hàng ACB để liên kết với hệ thống", "accountHolderName": "<PERSON>ên chủ tài k<PERSON>n", "accountHolderNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên chủ tài kho<PERSON>n ACB", "accountNumber": "Số tài <PERSON>n", "accountNumberPlaceholder": "Nhập số tài khoản ACB", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "phoneNumberPlaceholder": "<PERSON>hập số điện thoại đăng ký ACB", "label": "<PERSON><PERSON>n g<PERSON>i nh<PERSON>", "labelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên gợi nh<PERSON> (tù<PERSON> chọn)", "saveSuccess": "<PERSON><PERSON> lưu thông tin tài khoản ACB thành công", "saveError": "<PERSON><PERSON> lỗi xảy ra khi lưu thông tin tài khoản ACB", "submitError": "<PERSON><PERSON> lỗi xảy ra khi lưu thông tin tài khoản", "otpVerificationTitle": "<PERSON><PERSON><PERSON> thực OTP ACB", "bankName": "Ngân hàng ACB"}, "mb": {"title": "Liên kết tài khoản MB Bank", "description": "<PERSON>hậ<PERSON> thông tin tài khoản ngân hàng MB Bank để liên kết với hệ thống", "accountHolderName": "<PERSON>ên chủ tài k<PERSON>n", "accountHolderNamePlaceholder": "Tên sẽ được tự động lấy từ API", "accountNumber": "Số tài <PERSON>n", "accountNumberPlaceholder": "Nhập số tài khoản MB Bank", "identificationNumber": "Số CMND/CCCD", "identificationNumberPlaceholder": "Nhập số CMND/CCCD đăng ký MB Bank", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "phoneNumberPlaceholder": "Nhập số điện thoại đăng ký MB Bank", "label": "<PERSON><PERSON>n g<PERSON>i nh<PERSON>", "labelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên gợi nh<PERSON> (tù<PERSON> chọn)", "fetchNameSuccess": "<PERSON><PERSON> lấy tên chủ tài khoản thành công", "fetchNameError": "<PERSON><PERSON><PERSON><PERSON> thể lấy tên chủ tài khoản", "saveSuccess": "<PERSON><PERSON> lưu thông tin tài khoản MB Bank thành công", "saveError": "C<PERSON> lỗi xảy ra khi lưu thông tin tài khoản MB Bank", "submitError": "<PERSON><PERSON> lỗi xảy ra khi lưu thông tin tài khoản", "bankName": "Ngân hàng MB Bank", "otpVerificationTitle": "Xác thực OTP MB Bank", "mbOtpDescription": "<PERSON>hậ<PERSON> mã O<PERSON> được gửi đến số điện thoại đã đăng ký với MB Bank để hoàn tất kết nối", "resendOtpSuccess": "Đã gửi lại mã OTP thành công", "resendOtpSuccessMessage": "Mã OTP mới đã được gửi đến số điện thoại đã đăng ký", "resendOtpError": "Có lỗi xảy ra khi gửi lại mã OTP", "resendOtpCountdown": "G<PERSON>i lại mã sau {{seconds}} giây", "otpExpired": "Mã OTP đã hết hạn", "resendOtpButton": "Gửi lại mã OTP"}, "ocb": {"title": "Liên kết tài khoản OCB", "description": "<PERSON><PERSON><PERSON><PERSON> thông tin tài khoản ngân hàng OCB để liên kết với hệ thống", "accountHolderName": "<PERSON>ên chủ tài k<PERSON>n", "accountHolderNamePlaceholder": "Tên sẽ được tự động lấy từ API", "accountNumber": "Số tài <PERSON>n", "accountNumberPlaceholder": "Nhập số tài khoản OCB", "identificationNumber": "Số CMND/CCCD", "identificationNumberPlaceholder": "Nhập số CMND/CCCD đăng ký OCB", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "phoneNumberPlaceholder": "<PERSON>hậ<PERSON> số điện thoại đăng ký OCB", "label": "<PERSON><PERSON>n g<PERSON>i nh<PERSON>", "labelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên gợi nh<PERSON> (tù<PERSON> chọn)", "fetchNameSuccess": "<PERSON><PERSON> lấy tên chủ tài khoản thành công", "fetchNameError": "<PERSON><PERSON><PERSON><PERSON> thể lấy tên chủ tài khoản", "saveSuccess": "<PERSON><PERSON> lưu thông tin tài khoản OCB thành công", "saveError": "<PERSON><PERSON> lỗi xảy ra khi lưu thông tin tài khoản OCB", "submitError": "<PERSON><PERSON> lỗi xảy ra khi lưu thông tin tài khoản", "bankName": "<PERSON>ân hàng OCB"}, "kienlong": {"title": "Liên kết tài kho<PERSON>n Kiên Long Bank", "description": "<PERSON><PERSON><PERSON><PERSON> thông tin tài khoản ngân hàng Kiên Long để liên kết với hệ thống", "accountHolderName": "<PERSON>ên chủ tài k<PERSON>n", "accountHolderNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên chủ tài kho<PERSON>n Ki<PERSON>n Long Bank", "accountNumber": "Số tài <PERSON>n", "accountNumberPlaceholder": "Nhập số tài k<PERSON>n Kiên Long Bank", "identificationNumber": "Số CMND/CCCD", "identificationNumberPlaceholder": "Nhập số CMND/CCCD đăng ký tài k<PERSON>n", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "phoneNumberPlaceholder": "Nhập số điện thoại đăng ký Kiên Long Bank", "label": "<PERSON><PERSON>n g<PERSON>i nh<PERSON>", "labelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên gợi nh<PERSON> (tù<PERSON> chọn)", "saveSuccess": "<PERSON><PERSON> lưu thông tin tài khoản Kiên Long Bank thành công", "saveError": "<PERSON><PERSON> lỗi xảy ra khi lưu thông tin tài khoản Kiên Long Bank", "submitError": "<PERSON><PERSON> lỗi xảy ra khi lưu thông tin tài khoản", "bankName": "<PERSON><PERSON> h<PERSON>"}}, "apiKeys": {"title": "<PERSON><PERSON><PERSON>n lý <PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý các <PERSON> Keys để tích hợp với các dịch vụ bên ngoài", "id": "ID", "apiKey": "API Key", "scope": "<PERSON><PERSON><PERSON><PERSON> truy cập", "environment": "<PERSON>ô<PERSON> trường", "expiredAt": "<PERSON><PERSON><PERSON> h<PERSON> hạn", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON>", "addNew": "Tạo API Key mới", "createNew": "Tạo API Key mới", "list": "<PERSON>h sách API Keys", "descriptionPlaceholder": "<PERSON>hập mô tả cho API Key", "selectDate": "<PERSON><PERSON><PERSON> ng<PERSON> hết hạn", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm theo mô tả...", "filterByStatus": "<PERSON><PERSON><PERSON> theo trạng thái", "allStatuses": "<PERSON><PERSON><PERSON> cả trạng thái", "enable": "<PERSON><PERSON><PERSON>", "disable": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa API Key này?", "createSuccess": "Đã tạo API Key mới với mô tả: {{description}}", "deleteSuccess": "Đã xóa API Key: {{apiKey}}", "toggleSuccess": "Đã {{action}} API Key: {{apiKey}}", "mockData": {"webApp": "API key cho ứng dụng web", "mobileApp": "API key cho ứng dụng di động", "thirdParty": "API key cho tích hợ<PERSON> bên thứ ba", "development": "API key cho môi trườ<PERSON> phát triển", "testing": "API key cho môi trư<PERSON><PERSON> thử nghiệm", "webhook": "API key cho webhook integration", "analytics": "API key cho analytics service"}}, "endpoint": "Endpoint", "description": "<PERSON><PERSON>", "testConnection": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "activate": "<PERSON><PERSON><PERSON>", "deactivate": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "loadError": "Lỗi khi tải danh sách tích hợp", "deleteSuccess": "<PERSON><PERSON><PERSON> tích hợp thành công", "deleteError": "Lỗi khi xóa tích hợp", "statusUpdateSuccess": "<PERSON><PERSON><PERSON> nhật trạng thái thành công", "statusUpdateError": "Lỗi khi cập nhật trạng thái", "testConnectionSuccess": "<PERSON><PERSON><PERSON> tra kết nối thành công", "testConnectionError": "Lỗi khi kiểm tra kết nối", "deleteConfirmTitle": "<PERSON><PERSON><PERSON> n<PERSON>n x<PERSON><PERSON> tích hợp", "deleteConfirmMessage": "Bạn có chắc chắn muốn xóa tích hợp \"{{name}}\"? Hành động này không thể hoàn tác.", "createIntegration": "<PERSON><PERSON><PERSON> t<PERSON>ch hợp mới", "editIntegration": "Chỉnh sửa tích hợp", "viewIntegration": "<PERSON>em chi ti<PERSON>t tí<PERSON> hợp", "createIntegrationDescription": "<PERSON><PERSON><PERSON> tích hợp mới để kết nối với dịch vụ bên ngoài", "editIntegrationDescription": "Chỉnh sửa thông tin tích hợp", "viewIntegrationDescription": "<PERSON><PERSON> thông tin chi tiết của tích hợp", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên tích hợp", "selectType": "<PERSON><PERSON><PERSON> lo<PERSON>i tích hợp", "selectStatus": "<PERSON><PERSON><PERSON> trạng thái", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho tích hợp", "endpointPlaceholder": "https://api.example.com", "createSuccess": "<PERSON><PERSON><PERSON> tích hợp thành công", "createError": "Lỗi khi tạo tích hợp", "updateSuccess": "<PERSON><PERSON><PERSON> nhật tích hợp thành công", "updateError": "Lỗi khi cập nhật tích hợp", "enterpriseStorage": {"title": "Tích hợp Enterprise Storage", "description": "<PERSON><PERSON><PERSON> hợp với AWS S3, Azure Blob Storage", "form": {"createTitle": "Thêm Enterprise Storage", "editTitle": "Chỉnh sửa Enterprise Storage"}}, "cloudStorage": {"title": "<PERSON><PERSON><PERSON> hợp Cloud Storage", "description": "<PERSON><PERSON><PERSON><PERSON> lý tích hợp vớ<PERSON> các dịch vụ lưu trữ đám mây", "providers": {"google-drive": "Google Drive", "onedrive": "Microsoft OneDrive", "dropbox": "Dropbox", "box": "Box"}, "form": {"createTitle": "Thêm Cloud Storage", "editTitle": "Chỉnh sửa Cloud Storage", "providerType": {"label": "<PERSON><PERSON><PERSON> cung cấp", "placeholder": "<PERSON><PERSON><PERSON> nhà cung cấp", "helpText": "Chọn dịch vụ cloud storage muốn tích hợp"}, "providerName": {"label": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên cấu hình", "helpText": "<PERSON>ên để nhận diện cấu hình này"}, "clientId": {"label": "Client ID", "placeholder": "Nhập Client ID", "helpText": "Client ID từ console của nhà cung cấp"}, "clientSecret": {"label": "Client Secret", "placeholder": "Nhập Client Secret", "helpText": "Client Secret từ console của nhà cung cấp"}, "refreshToken": {"label": "Refresh <PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON>", "helpText": "Token để duy trì kết nối"}, "rootFolderId": {"label": "Root Folder ID", "placeholder": "Nhập Root Folder ID (t<PERSON><PERSON> ch<PERSON>)", "helpText": "<PERSON> thư mụ<PERSON>, để trống để dùng thư mục ch<PERSON>h"}, "isActive": {"label": "<PERSON><PERSON><PERSON>", "helpText": "Bật/tắt tích hợp n<PERSON>y"}, "autoSync": {"label": "<PERSON><PERSON>ng bộ tự động", "helpText": "Tự động đồng bộ file"}, "syncFolders": {"label": "<PERSON><PERSON><PERSON> m<PERSON> đồng bộ", "placeholder": "<PERSON><PERSON><PERSON><PERSON> danh s<PERSON>ch thư mụ<PERSON> (JSON)", "helpText": "<PERSON><PERSON> s<PERSON>ch thư mục cần đồng bộ (định dạng JSON array)"}, "testConnection": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "connectWithOAuth": "<PERSON><PERSON><PERSON> n<PERSON>i với {{provider}}", "authSuccess": "<PERSON><PERSON><PERSON> thực thành công", "authFailed": "<PERSON><PERSON><PERSON> thực thất bại"}, "list": {"title": "Danh sách Cloud Storage", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tích hợp cloud storage", "createNew": "Thêm Cloud Storage", "searchPlaceholder": "T<PERSON>m kiếm theo tên...", "resultsCount": "<PERSON><PERSON><PERSON> thị {{count}} trong tổng số {{total}} kết quả"}, "filters": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "byProvider": "<PERSON> cung cấp"}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "syncEnabled": "<PERSON><PERSON><PERSON> bộ bật", "syncDisabled": "Đồng bộ tắt", "syncing": "<PERSON><PERSON> đồng bộ", "error": "Lỗi"}, "actions": {"test": "<PERSON><PERSON><PERSON> tra", "sync": "<PERSON><PERSON><PERSON> bộ", "browse": "<PERSON><PERSON><PERSON><PERSON> file", "upload": "<PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "share": "<PERSON><PERSON> sẻ", "delete": "Xóa"}, "details": {"provider": "<PERSON><PERSON><PERSON> cung cấp", "clientId": "Client ID", "lastSync": "<PERSON><PERSON><PERSON> bộ lần cu<PERSON>i", "neverSynced": "<PERSON><PERSON><PERSON> đồng bộ", "created": "Tạo", "updated": "<PERSON><PERSON><PERSON>", "storageQuota": "<PERSON><PERSON> l<PERSON>", "usedSpace": "Đã sử dụng", "availableSpace": "<PERSON>òn lại"}, "modal": {"editTitle": "Chỉnh sửa Cloud Storage", "deleteTitle": "Xóa Cloud Storage", "deleteConfirm": "<PERSON><PERSON><PERSON>n x<PERSON>a", "deleteDescription": "Bạn có chắc chắn muốn xóa cấu hình {{name}}? Hành động này không thể hoàn tác."}, "empty": {"noConfigurations": "<PERSON><PERSON><PERSON> có cấu hình nào", "noConfigurationsDescription": "Bạn chưa thêm cấu hình cloud storage nào", "noResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "noResultsDescription": "<PERSON><PERSON><PERSON><PERSON> có cấu hình nào phù hợp với bộ lọc", "createFirst": "<PERSON><PERSON><PERSON><PERSON> cấu hình đầu tiên", "clearFilters": "Xóa bộ lọc"}, "error": {"loadFailed": "<PERSON><PERSON><PERSON> danh sách thất bại", "createFailed": "<PERSON><PERSON><PERSON> c<PERSON>u hình thất bại", "updateFailed": "<PERSON><PERSON><PERSON> nhật cấu hình thất bại", "deleteFailed": "<PERSON><PERSON><PERSON> cấu hình thất bại", "testFailed": "<PERSON><PERSON><PERSON> tra kết nối thất bại", "syncFailed": "<PERSON>ồng bộ thất bại", "authFailed": "<PERSON><PERSON><PERSON> thực thất bại", "uploadFailed": "<PERSON><PERSON><PERSON> lên thất bại", "downloadFailed": "<PERSON><PERSON><PERSON> xuống thất bại"}, "success": {"created": "<PERSON><PERSON><PERSON> c<PERSON>u hình thành công", "updated": "<PERSON><PERSON><PERSON> nh<PERSON>t cấu hình thành công", "deleted": "<PERSON><PERSON><PERSON> cấu hình thành công", "testPassed": "<PERSON><PERSON><PERSON> tra kết nối thành công", "syncCompleted": "Đồng bộ hoàn tất", "authCompleted": "<PERSON><PERSON><PERSON> thực thành công", "uploaded": "<PERSON><PERSON><PERSON> lên thành công", "downloaded": "<PERSON><PERSON><PERSON> xu<PERSON>ng thành công"}, "validation": {"providerType": {"invalid": "<PERSON><PERSON><PERSON> cung cấp không hợp lệ"}, "providerName": {"required": "<PERSON><PERSON><PERSON> c<PERSON>u hình là bắt buộc", "maxLength": "<PERSON>ên cấu hình không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 100 ký tự"}, "clientId": {"required": "Client ID là b<PERSON> buộc", "maxLength": "Client ID khô<PERSON> đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 255 ký tự"}, "clientSecret": {"required": "Client Secret là b<PERSON><PERSON> bu<PERSON>c", "maxLength": "Client Secret không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 255 ký tự"}, "refreshToken": {"required": "Refresh <PERSON> là b<PERSON> buộc"}, "syncFolders": {"invalidJson": "<PERSON><PERSON> s<PERSON>ch thư mục phải là JSON array hợp lệ"}, "testFolderName": {"maxLength": "<PERSON><PERSON><PERSON> thư mục test không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 100 ký tự"}, "testFileName": {"maxLength": "Tên file test không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 100 ký tự"}, "fileName": {"required": "Tên file là bắt buộc"}, "folderName": {"required": "<PERSON><PERSON><PERSON> thư mục là bắt buộc"}, "query": {"required": "Từ khóa tìm kiếm là bắt buộc"}, "operation": {"invalid": "<PERSON><PERSON> t<PERSON>c kh<PERSON>ng h<PERSON>p lệ"}, "fileIds": {"required": "<PERSON><PERSON> s<PERSON>ch file là bắt buộc"}, "fileId": {"required": "ID file là b<PERSON><PERSON> buộc"}, "permission": {"invalid": "<PERSON><PERSON><PERSON><PERSON> truy cập không hợp lệ"}}}, "sepayHub": {"title": "<PERSON><PERSON><PERSON> hình tổ chức <PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý tài khoản ngân hàng và giao dị<PERSON><PERSON>", "overview": {"totalBankAccounts": "Tổng số tài khoản ngân hàng", "totalVAAccounts": "Tổng số Số tài k<PERSON>n VA", "totalTransactions": "Tổng số l<PERSON> giao d<PERSON>ch", "availableTransactions": "<PERSON><PERSON> l<PERSON><PERSON>t giao dịch có sẵn"}, "table": {"id": "ID", "companyId": "ID công ty", "bankId": "ID ngân hàng", "accountHolderName": "<PERSON>ên chủ tài k<PERSON>n", "accountNumber": "Số tài <PERSON>n", "accumulated": "Số dư", "label": "<PERSON><PERSON>n g<PERSON>i nh<PERSON>", "bankApiConnected": "Trạng thái API", "lastTransaction": "<PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> nh<PERSON> cu<PERSON>", "connected": "<PERSON><PERSON> liên kết", "notConnected": "<PERSON><PERSON><PERSON> li<PERSON> k<PERSON>t"}, "actions": {"buyMoreTransactions": "<PERSON><PERSON> thêm giao d<PERSON>ch"}, "companyConfig": {"title": "<PERSON><PERSON><PERSON> hình công ty", "companyInfo": "Thông tin công ty", "paymentConfig": "<PERSON><PERSON><PERSON> hình <PERSON>h toán", "fullName": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> đủ", "fullNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên đ<PERSON>y đủ", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "organizationName": "<PERSON><PERSON><PERSON> tổ chức", "organizationNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên tổ chức", "shortName": "<PERSON><PERSON><PERSON>", "shortNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên <PERSON>n", "paymentCode": {"label": "<PERSON><PERSON><PERSON> hình nhận diện mã thanh toán", "on": "<PERSON><PERSON><PERSON>", "off": "Tắt"}, "paymentCodePrefix": "<PERSON><PERSON><PERSON> hình tiền tố mã thanh toán", "paymentCodePrefixPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tiền tố mã thanh toán", "paymentCodeSuffixFrom": "<PERSON><PERSON><PERSON> hình độ dài tối thiểu hậu tố mã thanh toán", "paymentCodeSuffixTo": "<PERSON><PERSON><PERSON> hình độ dài tối đa hậu tố mã thanh toán", "paymentCodeSuffixCharacterType": "<PERSON><PERSON>u hình kiểu ký tự hậu tố mã thanh toán", "characterType": {"numberAndLetter": "Cho phép chữ cái và số", "numberOnly": "Chỉ cho phép chữ số"}, "transactionAmount": "<PERSON><PERSON><PERSON> hình số lượng giao dịch", "transactionAmountPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số lượng giao dịch hoặc 'Unlimited'", "availableTransactions": "<PERSON><PERSON> lượng giao dịch có sẵn", "validation": {"organizationNameRequired": "<PERSON><PERSON><PERSON> tổ chức là bắt buộc", "shortNameRequired": "<PERSON><PERSON><PERSON> ng<PERSON>n là b<PERSON> bu<PERSON>c", "prefixRequired": "Ti<PERSON>n tố mã thanh toán là bắt buộc", "suffixFromMin": "Đ<PERSON> dài tối thiểu phải lớn hơn 0", "suffixToGreater": "Độ dài tối đa phải lớn hơn hoặc bằng độ dài tối thiểu", "transactionAmountMin": "S<PERSON> lượng giao dịch phải lớn hơn hoặc bằng 0"}}}}}