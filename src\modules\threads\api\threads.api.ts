/**
 * Threads API - Raw API calls
 * Xử lý tất cả REST API calls cho threads management
 */

import { apiClient } from '@/shared/api';
import { GetThreadsQuery, ThreadsResponse, ThreadData, CreateThreadRequest, CreateThreadResponse } from '@/shared/types';

/**
 * Request để cập nhật thread
 */
export interface UpdateThreadRequest {
  title: string;
}

/**
 * Tạo thread mới
 * Endpoint: POST /user/chat/threads
 */
export const createThread = async (data: CreateThreadRequest) => {
  return apiClient.post<CreateThreadResponse>('/user/chat/threads', data);
};

/**
 * L<PERSON>y danh sách threads
 * Endpoint: GET /v1/user/chat/threads
 */
export const getThreads = async (query?: GetThreadsQuery) => {
  // Tạo query parameters
  const params = new URLSearchParams();
  if (query?.page) params.append('page', query.page.toString());
  if (query?.limit) params.append('limit', query.limit.toString());
  if (query?.sortBy) params.append('sortBy', query.sortBy);
  if (query?.sortDirection) params.append('sortDirection', query.sortDirection.toUpperCase());
  if (query?.search) params.append('search', query.search);

  const url = `/user/chat/threads${params.toString() ? `?${params.toString()}` : ''}`;

  return apiClient.get<ThreadsResponse>(url);
};

/**
 * Lấy chi tiết thread
 * Endpoint: GET /v1/user/chat/threads/{threadId}
 */
export const getThreadDetail = async (threadId: string) => {
  return apiClient.get<ThreadData>(`/user/chat/threads/${threadId}`);
};



/**
 * Cập nhật thread
 * Endpoint: PUT /user/chat/threads/{threadId}
 */
export const updateThread = async (threadId: string, data: UpdateThreadRequest) => {
  return apiClient.put<ThreadData>(`/user/chat/threads/${threadId}`, data);
};

/**
 * Xóa thread
 * Endpoint: DELETE /v1/user/chat/threads/{threadId}
 */
export const deleteThread = async (threadId: string) => {
  return apiClient.delete<void>(`/user/chat/threads/${threadId}`);
};
