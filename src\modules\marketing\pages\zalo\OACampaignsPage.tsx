import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, Chip, Typography, ActionMenu } from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { ConfirmDeleteModal, useSmartNotification } from '@/shared';
import CreateOACampaignForm from '../../components/zalo/CreateOACampaignForm';
import {
  useZaloOACampaigns,
  useStartZaloOACampaign,
  usePauseZaloOACampaign,
  useDuplicateZaloOACampaign,
  useBulkDeleteZaloOACampaigns,
} from '../../hooks/zalo/useZaloOACampaigns';
import {
  ZaloOACampaignDto,
  ZaloOACampaignStatus,
  ZaloOACampaignType,
} from '../../types/zalo-oa-campaigns.types';
import { formatTimestamp } from '@/shared/utils/date';

// Table column type
interface TableColumn<T> {
  key: string;
  title: string;
  sortable?: boolean;
  width?: number;
  render?: (value: any, record: T) => React.ReactNode;
}

const OA_CAMPAIGN_TYPE_LABELS = {
  [ZaloOACampaignType.ADD_MEMBER_TO_GROUP]: 'Thêm thành viên vào group',
  [ZaloOACampaignType.SEND_OA_MESSAGE]: 'Gửi tin nhắn truyền thông',
  [ZaloOACampaignType.SEND_ZNS_MESSAGE]: 'Gửi tin nhắn ZNS',
  [ZaloOACampaignType.SEND_BROADCAST_MESSAGE]: 'Gửi tin Truyền thông Broadcast (Legacy)',
  [ZaloOACampaignType.SEND_PERSONAL_MESSAGE]: 'Tin nhắn truyền thông cá nhân',
  [ZaloOACampaignType.SEND_BROADCAST_TARGET_MESSAGE]: 'Truyền thông Broadcast theo target',
  [ZaloOACampaignType.CONSULTATION_SEQUENCE]: 'Dãy tin nhắn tư vấn',
} as const;

const OA_CAMPAIGN_STATUS_LABELS = {
  [ZaloOACampaignStatus.DRAFT]: 'Nháp',
  [ZaloOACampaignStatus.ACTIVE]: 'Đang chạy',
  [ZaloOACampaignStatus.PAUSED]: 'Tạm dừng',
  [ZaloOACampaignStatus.COMPLETED]: 'Hoàn thành',
  [ZaloOACampaignStatus.FAILED]: 'Thất bại',
} as const;

const OA_CAMPAIGN_STATUS_COLORS = {
  [ZaloOACampaignStatus.DRAFT]: 'gray',
  [ZaloOACampaignStatus.ACTIVE]: 'green',
  [ZaloOACampaignStatus.PAUSED]: 'yellow',
  [ZaloOACampaignStatus.COMPLETED]: 'blue',
  [ZaloOACampaignStatus.FAILED]: 'red',
} as const;

const OACampaignsPage: React.FC = () => {
  // Sync OA Campaign Status
  const handleSyncStatus = async () => {
    try {
      showNotification('info', 'Đang cập nhật trạng thái các chiến dịch...');
      const { ZaloService } = await import('../../services/zalo.service');
      await ZaloService.syncOACampaignStatus();
      showNotification('success', 'Đã cập nhật trạng thái thành công');
      await refetch();
    } catch (error) {
      console.error('Error syncing OA campaign status:', error);
      showNotification('error', 'Có lỗi khi cập nhật trạng thái');
    }
  };
  const { t } = useTranslation(['common', 'marketing']);
  const { showNotification } = useSmartNotification();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedCampaigns, setSelectedCampaigns] = useState<React.Key[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Mutations
  const startMutation = useStartZaloOACampaign();
  const pauseMutation = usePauseZaloOACampaign();
  const duplicateMutation = useDuplicateZaloOACampaign();
  const bulkDeleteMutation = useBulkDeleteZaloOACampaigns();

  // Table columns
  const columns = useMemo(
    (): TableColumn<ZaloOACampaignDto>[] => [
      {
        key: 'name',
        title: t('marketing:zalo.oaCampaigns.table.name', 'Tên chiến dịch'),
        sortable: true,
        render: (_: any, record: ZaloOACampaignDto) => (
          <div>
            <Typography variant="body2" className="font-medium">
              {record.name}
            </Typography>
            {record.description && (
              <Typography variant="caption" className="text-muted">
                {record.description}
              </Typography>
            )}
          </div>
        ),
      },
      {
        key: 'type',
        title: t('marketing:zalo.oaCampaigns.table.type', 'Loại chiến dịch'),
        render: (_: any, record: ZaloOACampaignDto) => (
          <Typography variant="body2">{OA_CAMPAIGN_TYPE_LABELS[record.type]}</Typography>
        ),
      },
      {
        key: 'status',
        title: t('marketing:zalo.oaCampaigns.table.status', 'Trạng thái'),
        render: (_: any, record: ZaloOACampaignDto) => (
          <Chip variant={OA_CAMPAIGN_STATUS_COLORS[record.status] as any} size="sm">
            {OA_CAMPAIGN_STATUS_LABELS[record.status]}
          </Chip>
        ),
      },
      {
        key: 'progress',
        title: t('marketing:zalo.oaCampaigns.table.progress', 'Tiến độ'),
        render: (_: any, record: ZaloOACampaignDto) => {
          const totalRecipients = record.totalRecipients || 0;
          const successCount = record.successCount || 0;
          const failureCount = record.failureCount || 0;
          const processedCount = successCount + failureCount;

          const progressPercent =
            totalRecipients > 0 ? Math.round((processedCount / totalRecipients) * 100) : 0;

          return (
            <div>
              <Typography variant="body2" className="font-medium">
                {progressPercent}% ({processedCount}/{totalRecipients})
              </Typography>
              <Typography variant="caption" className="text-muted">
                Thành công: {successCount} | Thất bại: {failureCount}
              </Typography>
            </div>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('common:createdAt', 'Ngày tạo'),
        sortable: true,
        render: (_: any, record: ZaloOACampaignDto) => {
          return formatTimestamp(record.createdAt);
        },
      },
      {
        key: 'actions',
        title: t('common:actions', 'Hành động'),
        width: 120,
        render: (_: any, record: ZaloOACampaignDto) => {
          const actionItems = [
            {
              id: 'view',
              label: t('common:view', 'Xem'),
              icon: 'eye',
              onClick: () => handleView(record),
            },
            {
              id: 'edit',
              label: t('common:edit', 'Sửa'),
              icon: 'edit',
              onClick: () => handleEdit(record),
              disabled: record.status === ZaloOACampaignStatus.ACTIVE,
            },
            {
              id: 'start',
              label: t('marketing:zalo.oaCampaigns.actions.start', 'Bắt đầu'),
              icon: 'play',
              onClick: () => handleStart(record),
              disabled:
                record.status !== ZaloOACampaignStatus.DRAFT &&
                record.status !== ZaloOACampaignStatus.PAUSED,
            },
            {
              id: 'pause',
              label: t('marketing:zalo.oaCampaigns.actions.pause', 'Tạm dừng'),
              icon: 'pause',
              onClick: () => handlePause(record),
              disabled: record.status !== ZaloOACampaignStatus.ACTIVE,
            },
            {
              id: 'duplicate',
              label: t('common:duplicate', 'Nhân bản'),
              icon: 'copy',
              onClick: () => handleDuplicate(record),
            },
          ];

          return <ActionMenu items={actionItems} />;
        },
      },
    ],
    [t]
  );

  // Data table configuration
  const dataTable = useDataTable(
    useDataTableConfig({
      columns: columns as any,
      createQueryParams: (params: any) => ({
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm,
        sortBy: params.sortBy,
        sortDirection: params.sortDirection,
      }),
    })
  );

  // API call to get campaigns
  const { data: rawCampaignsData, isLoading, refetch } = useZaloOACampaigns(dataTable.queryParams);

  // Normalize status: map 'running' to ZaloOACampaignStatus.ACTIVE
  const campaignsData = useMemo(() => {
    if (!rawCampaignsData) return undefined;
    const items =
      rawCampaignsData.result?.items?.map((c: any) => ({
        ...c,
        status: c.status === 'running' ? ZaloOACampaignStatus.ACTIVE : c.status,
      })) || [];
    return {
      ...rawCampaignsData,
      result: {
        ...rawCampaignsData.result,
        items,
      },
    };
  }, [rawCampaignsData]);

  // Event handlers
  const handleAdd = () => {
    setShowCreateForm(true);
  };

  const handleCreateSuccess = () => {
    setShowCreateForm(false);
    showNotification(
      'success',
      t('marketing:zalo.oaCampaigns.messages.createSuccess', 'Tạo chiến dịch thành công')
    );
  };

  const handleView = (campaign: ZaloOACampaignDto) => {
    showNotification('info', `Xem chi tiết chiến dịch: ${campaign.name}`);
  };

  const handleEdit = (campaign: ZaloOACampaignDto) => {
    showNotification('info', `Chỉnh sửa chiến dịch: ${campaign.name}`);
  };

  const handleStart = (campaign: ZaloOACampaignDto) => {
    startMutation.mutate(String(campaign.id));
  };

  const handlePause = (campaign: ZaloOACampaignDto) => {
    pauseMutation.mutate(String(campaign.id));
  };

  const handleDuplicate = (campaign: ZaloOACampaignDto) => {
    duplicateMutation.mutate(String(campaign.id));
  };

  const handleReload = async () => {
    try {
      showNotification('info', t('common:reloading', 'Đang tải lại dữ liệu...'));
      await refetch();
      showNotification(
        'success',
        t('marketing:zalo.oaCampaigns.reloadSuccess', 'Đã tải lại danh sách chiến dịch thành công')
      );
    } catch (error) {
      console.error('Error reloading campaigns:', error);
      showNotification(
        'error',
        t('marketing:zalo.oaCampaigns.reloadError', 'Có lỗi xảy ra khi tải lại danh sách')
      );
    }
  };

  // Handle row selection
  const handleSelectionChange = (selectedRowKeys: React.Key[]) => {
    setSelectedCampaigns(selectedRowKeys);
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedCampaigns.length === 0) {
      showNotification('warning', 'Vui lòng chọn ít nhất một chiến dịch để xóa');
      return;
    }
    setShowDeleteModal(true);
  };

  // Confirm bulk delete
  const handleConfirmBulkDelete = async () => {
    try {
      const campaignIds = selectedCampaigns.map(id => String(id));
      await bulkDeleteMutation.mutateAsync(campaignIds);
      setSelectedCampaigns([]);
      setShowDeleteModal(false);
    } catch (error) {
      console.error('Error bulk deleting campaigns:', error);
      // Error is handled by the mutation hook
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      {/* Create Form SlideIn */}
      <SlideInForm isVisible={showCreateForm}>
        <CreateOACampaignForm
          onSuccess={handleCreateSuccess}
          onCancel={() => setShowCreateForm(false)}
        />
      </SlideInForm>

      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'refresh-cw',
            tooltip: t('common:reload', 'Tải lại dữ liệu'),
            variant: 'secondary',
            onClick: () => handleReload(),
            className: 'text-blue-500',
          },
          {
            icon: 'folder-sync',
            tooltip: 'Đồng bộ trạng thái chiến dịch Zalo',
            variant: 'secondary',
            onClick: handleSyncStatus,
            className: 'text-green-500',
          },
          ...(selectedCampaigns.length > 0
            ? [
                {
                  icon: 'trash' as const,
                  tooltip: t('common:delete') + ` (${selectedCampaigns.length})`,
                  onClick: handleBulkDelete,
                  variant: 'primary' as const,
                  className: 'text-red-500',
                },
              ]
            : []),
        ]}
      />

      {/* Campaigns Table */}
      <Card className="overflow-hidden">
        <Table<ZaloOACampaignDto>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={campaignsData?.result?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          rowSelection={{
            selectedRowKeys: selectedCampaigns,
            onChange: handleSelectionChange,
            getCheckboxProps: record => ({
              disabled: record.status === ZaloOACampaignStatus.ACTIVE, // Không cho phép chọn campaign đang chạy
            }),
          }}
          pagination={{
            current: campaignsData?.result?.meta?.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: campaignsData?.result?.meta?.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleConfirmBulkDelete}
        isSubmitting={bulkDeleteMutation.isPending}
        title="Xác nhận xóa chiến dịch"
        mode="delete"
        message={`Bạn có chắc chắn muốn xóa ${selectedCampaigns.length} chiến dịch đã chọn?`}
      />
    </div>
  );
};

export default OACampaignsPage;
