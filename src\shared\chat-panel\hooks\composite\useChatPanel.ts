/**
 * use<PERSON>hat<PERSON>anel Hook - Main Composite Hook
 * <PERSON>ết hợp tất cả chat panel functionality
 */

import { THREADS_QUERY_KEYS } from '@/modules/threads/constants/threads-query-keys';
import { ThreadsService } from '@/modules/threads/services/threads.service';
import { useRPointUpdate } from '@/shared/hooks/common/useRPointUpdate';
import { AuthType } from '@/shared/hooks/useAuthCommon';
import { useAppDispatch, useAppSelector } from '@/shared/store';
import { selectCurrentThreadId } from '@/shared/store/slices/threadSlice';
import { useQueryClient } from '@tanstack/react-query';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { streamingMessageService } from '../../services/streaming/streaming-message.service';
import type { UnifiedMessage } from '../../types/message.types';
import type { RunComplete } from '../../types/stream-events.types';
import { useChatAPI } from '../core/useChatAPI';
import { useChatConfig } from '../core/useChatConfig';
import { useChatNotification } from '../core/useChatNotification';
import { useChatSSE } from '../core/useChatSSE';
import { useChatMessages } from '../features/useChatMessages';
import { useChatThread } from '../features/useChatThread';
import { useChatToolCalls } from '../features/useChatToolCalls';
import { useThreadState } from '../features/useThreadState';
import { useChatContext } from '../integration/useChatContext';
import { useChatPanelIntegration } from '../integration/useChatPanelIntegration';
import { useSimpleStreamingState } from '../streaming/useSimpleStreamingState';
import type {
  ChatConfig,
  UseChatPanelConfig,
  UseChatPanelReturn
} from '../types';

import type { MarkdownStreamProps } from '@/shared/components/common/MarkdownStream/types';
import type {
  Message,
  MessageAttachment,
  MessageRequestDto
} from '../../types';
import { AttachmentContentType, AttachmentType, MessageContentType, MessageRole, ToolCallDecision } from '../../types/chat-api.types';


// Type for markdown handler
interface MarkdownHandler {
  processDelta: (delta: string) => void;
}

/**
 * useChatPanel Hook - Main Composite Hook
 * Tích hợp tất cả chat panel functionality
 */
export const useChatPanel = (config: UseChatPanelConfig = {}): UseChatPanelReturn => {
  const {
    agentId,
    authType,
    getAuthToken,
    enableMarkdownStreaming = true,
    enableThreadIntegration = true,
    enableToolCallApproval = true, // TODO: Implement tool call approval
    messageHistory = {
      pageSize: 20,
      autoLoad: true,
      timeout: 5000
    },
    // onRPointUpdate, // TODO: Implement R-Point updates
    // onThreadCreated, // TODO: Implement thread creation callback
    // onThreadSwitched, // TODO: Implement thread switching callback
  } = config;

  // Suppress unused variable warnings for future implementation
  void enableToolCallApproval;

  // Refs
  const currentStreamingMessageIdRef = useRef<string | null>(null);
  const markdownHandlersRef = useRef<Map<string, MarkdownHandler>>(new Map());
  const messagesRef = useRef<any>(null); // ✅ Ref to access messages in callbacks

  // ✅ R-Point update hook for handling point deduction after stream completion
  const rPointUpdate = useRPointUpdate({
    useUpdatedBalance: false, // Use deduction method instead of setting balance
    onUpdate: (newBalance, cost, timestamp) => {
      console.log('[useChatPanel] R-Point updated after stream completion:', {
        newBalance,
        cost,
        timestamp
      });
    },
    onError: (error) => {
      console.error('[useChatPanel] R-Point update error:', error);
    }
  });

  // ✅ PHASE 3: Simple Streaming State Management
  const simpleStreamingState = useSimpleStreamingState({
    onStreamingStart: (messageId) => {
      currentStreamingMessageIdRef.current = messageId;
    },
    onStreamingEnd: () => {
      currentStreamingMessageIdRef.current = null;
    },
    onStreamingError: () => {
      currentStreamingMessageIdRef.current = null;
    },
    debug: true
  });

  // ✅ PHASE 3: Simplified streaming state management

  // ✅ Phase 1: Unified Message State
  const [streamingMessages, setStreamingMessages] = useState<UnifiedMessage[]>([]);
  const [currentStreamingMessageId, setCurrentStreamingMessageId] = useState<string | null>(null);

  // ✅ PHASE 2: Remove EventRouter - Use direct streaming instead
  // const eventRouter = useEventRouter({
  //   streamingStateManager
  // });

  const handleTextMessageStart = useCallback((event: any) => {
    simpleStreamingState.startStreaming(event.messageId);

    // Start direct streaming
    streamingMessageService.startMessage(event.messageId);

    // ✅ FIX: DON'T create message immediately on text_message_start
    // Just mark that streaming has started and wait for actual content
    setCurrentStreamingMessageId(event.messageId);
    currentStreamingMessageIdRef.current = event.messageId;


  }, [simpleStreamingState]);

  const handleTextMessageContent = useCallback((event: any) => {
    streamingMessageService.addDelta(event.messageId, event.delta);

    // ✅ FIX: Create message only when we receive actual content
    setStreamingMessages(prev => {
      const existingMessage = prev.find(msg => msg.messageId === event.messageId);

      if (existingMessage) {
        // Update existing message
        return prev.map(msg =>
          msg.messageId === event.messageId
            ? {
              ...msg,
              messageText: msg.messageText + event.delta,
              streamingState: 'content' as const,
              sseEvents: [...(msg.sseEvents || []), event]
            }
            : msg
        );
      } else {
        // Create new message when we receive first content
        const newStreamingMessage: UnifiedMessage = {
          messageId: event.messageId,
          messageText: event.delta,
          role: MessageRole.ASSISTANT,
          messageCreatedAt: Date.now(),
          hasAttachments: false,
          attachments: [],
          isToolCallConfirm: false,
          isStreaming: true,
          streamingState: 'content',
          sseEvents: [event],
          agentId: event.agentId // ✅ Add agentId from SSE event
        };


        return [...prev, newStreamingMessage];
      }
    });
  }, []);

  const handleTextMessageEnd = useCallback((event: any) => {
    if (event.finalContent) {
      streamingMessageService.finalizeMessage(event.messageId, event.finalContent);
    }
    simpleStreamingState.endStreaming(event.messageId);

    // ✅ FIX: Only finalize message if it exists (was created during content phase)
    setStreamingMessages(prev => {
      const existingMessage = prev.find(msg => msg.messageId === event.messageId);

      if (!existingMessage) {
        // No message was created (only tool calls, no text content)

        return prev;
      }

      return prev.map(msg => {
        if (msg.messageId === event.messageId) {
          const finalContent = event.finalContent || streamingMessageService.getCurrentContent(event.messageId);

          // ✅ FIX: If finalContent is empty, don't create persistent message
          if (!finalContent || finalContent.trim() === '') {

            return null;
          }

          const finalizedMessage = {
            ...msg,
            messageText: finalContent,
            isStreaming: false,
            streamingState: 'complete' as const,
            sseEvents: [...(msg.sseEvents || []), event]
          };

          // Add to persistent messages store
          const persistentMessage: Message = {
            messageId: finalizedMessage.messageId,
            messageText: finalizedMessage.messageText,
            role: finalizedMessage.role,
            messageCreatedAt: finalizedMessage.messageCreatedAt,
            hasAttachments: finalizedMessage.hasAttachments,
            isToolCallConfirm: finalizedMessage.isToolCallConfirm,
            attachments: finalizedMessage.attachments,
            avatar: finalizedMessage.avatar,
            agentId: finalizedMessage.agentId // ✅ Add agentId to persistent message
          };

          // Add to persistent messages store using ref
          if (messagesRef.current?.addMessage) {
            messagesRef.current.addMessage(persistentMessage);
          }

          return finalizedMessage;
        }
        return msg;
      }).filter((msg): msg is UnifiedMessage => msg !== null);
    });

    setCurrentStreamingMessageId(null);
    currentStreamingMessageIdRef.current = null;

    // ✅ FIX: Clear loading state when text message ends
    setIsMessageSending(false);
  }, [simpleStreamingState]);

  const handleRunComplete = useCallback((event: RunComplete) => {
    if (currentStreamingMessageIdRef.current) {
      simpleStreamingState.endStreaming(currentStreamingMessageIdRef.current);

      // ✅ Phase 3: Enhanced run completion - cleanup streaming state
      setStreamingMessages(prev => prev.map(msg =>
        msg.messageId === currentStreamingMessageIdRef.current
          ? {
            ...msg,
            isStreaming: false,
            streamingState: 'complete' as const,
            sseEvents: [...(msg.sseEvents || []), event as any]
          }
          : msg
      ));

      // Clear streaming state
      setCurrentStreamingMessageId(null);
      currentStreamingMessageIdRef.current = null;

      // ✅ FIX: Clear loading state when run completes
      setIsMessageSending(false);
    }

    // ✅ Update R-Point after stream completion
    if (event?.totalCost && typeof event.totalCost === 'number' && event.totalCost > 0) {
      console.log('[useChatPanel] Stream completed with cost:', event.totalCost);

      // Use handleRPointUpdate to deduct points
      rPointUpdate.handleRPointUpdate(
        event.totalCost, // rPointCost
        '', // updatedBalance (not used when useUpdatedBalance = false)
        Date.now() // timestamp
      );
    }
  }, [simpleStreamingState, rPointUpdate]);

  const handleRunError = useCallback((event: any) => {
    if (currentStreamingMessageIdRef.current) {
      simpleStreamingState.handleStreamingError(currentStreamingMessageIdRef.current, event.error || 'Unknown error');

      // ✅ Phase 3: Enhanced error handling - update streaming message state
      setStreamingMessages(prev => prev.map(msg =>
        msg.messageId === currentStreamingMessageIdRef.current
          ? {
            ...msg,
            isStreaming: false,
            streamingState: 'complete' as const,
            messageText: msg.messageText || 'Có lỗi xảy ra trong quá trình xử lý.',
            sseEvents: [...(msg.sseEvents || []), event]
          }
          : msg
      ));

      // Clear streaming state
      setCurrentStreamingMessageId(null);
      currentStreamingMessageIdRef.current = null;

      // ✅ FIX: Clear loading state when run errors
      setIsMessageSending(false);
    }
  }, [simpleStreamingState]);



  // Get context info
  const { contextType, chatConfig: contextChatConfig } = useChatContext();

  // Chat config management
  const { config: chatConfig, updateConfig } = useChatConfig();

  // Initialize config once on mount - prevent infinite loop
  const configInitializedRef = useRef(false);

  useEffect(() => {
    // Only initialize once
    if (configInitializedRef.current) {
      return;
    }

    const updates: Partial<ChatConfig> = {};

    if (agentId) {
      updates.agentId = agentId;
    }

    if (authType) {
      updates.authType = authType;
    }

    if (getAuthToken) {
      updates.getAuthToken = getAuthToken;
    }

    if (Object.keys(updates).length > 0) {
      updateConfig(updates);
      configInitializedRef.current = true;
    }
  }, [agentId, authType, getAuthToken, updateConfig]); // ✅ Empty dependency array - only run once on mount

  // Determine final auth config with proper type safety
  const finalAuthType = useMemo(() => {
    if (authType) return authType;
    if (chatConfig.authType) return chatConfig.authType;
    return contextType === 'admin' ? AuthType.ADMIN : AuthType.USER;
  }, [authType, chatConfig.authType, contextType]);

  // ✅ Stable reference để tránh re-initialization - only depend on getAuthToken
  const finalGetAuthToken = useCallback(() => {
    const tokenFn = getAuthToken || chatConfig.getAuthToken || contextChatConfig.getAuthToken;
    return tokenFn ? tokenFn() : '';
  }, [getAuthToken]); // ❌ Remove chatConfig dependencies to prevent infinite loop



  // Core hooks - memoize config to prevent re-initialization
  const chatAPIConfig = useMemo(() => ({
    authType: finalAuthType,
    getAuthToken: finalGetAuthToken,
    timeout: messageHistory.timeout
  }), [finalAuthType, finalGetAuthToken, messageHistory.timeout]);

  const chatAPI = useChatAPI(chatAPIConfig);

  // ✅ Tool call event handlers - defined before chatSSE
  const handleToolCallStart = useCallback(() => {
    // Tool call started - just show loading, don't create message
    // Set loading state but don't create message
    setIsMessageSending(true);
  }, []);

  const handleToolCallArgs = useCallback(() => {
    // Tool call arguments streaming - just tool execution, not a message
    // Keep showing loading state
  }, []);

  const handleToolCallEnd = useCallback(() => {
    // Tool call ended - clear loading if no text message is streaming
    // Only clear loading if no text message is currently streaming
    if (!currentStreamingMessageIdRef.current) {
      setIsMessageSending(false);
    }
  }, []);

  // ✅ Tool call interrupt handler - defined before chatSSE
  const [toolCallInterruptEvent, setToolCallInterruptEvent] = useState<any>(null);

  const handleToolCallInterrupt = useCallback((event: any) => {
    // Tool call interrupt - show confirmation UI


    // Store event to process after toolCalls is available
    setToolCallInterruptEvent(event);
  }, []);

  const chatSSE = useChatSSE({
    getAuthToken: finalGetAuthToken, // ✅ Truyền getAuthToken cho SSE
    callbacks: {
      // ✅ PHASE 2: Direct handlers instead of EventRouter
      onTextMessageStart: handleTextMessageStart,
      onTextMessageContent: handleTextMessageContent,
      onTextMessageEnd: handleTextMessageEnd,
      onToolCallStart: handleToolCallStart,
      onToolCallArgs: handleToolCallArgs,
      onToolCallEnd: handleToolCallEnd,
      onToolCallInterrupt: handleToolCallInterrupt, // ✅ Add tool call interrupt handler
      onRunStarted: () => {
        // Handle run started if needed
      },
      onRunComplete: handleRunComplete,
      onRunError: handleRunError
    }
  });

  const notifications = useChatNotification();

  // ✅ PHASE 2: Use centralized thread state management
  const threadState = useThreadState({
    autoLoadFirstThread: enableThreadIntegration,
    enabled: enableThreadIntegration
  });

  // Feature hooks
  const thread = useChatThread({
    autoSwitch: enableThreadIntegration
  });

  const messages = useChatMessages({
    threadId: threadState.currentThreadId || thread.threadId || undefined,
    pageSize: messageHistory.pageSize,
    autoLoad: messageHistory.autoLoad,
    enableHistory: true,
    timeout: messageHistory.timeout,
    isNewChatMode: threadState.isNewChatMode // ✅ Pass new chat mode flag to prevent auto-loading
  });

  // ✅ Update messages ref for callback access
  messagesRef.current = messages;

  // ✅ Clear streaming messages when thread changes
  const currentThreadIdRef = useRef(threadState.currentThreadId || thread.threadId);
  useEffect(() => {
    const newThreadId = threadState.currentThreadId || thread.threadId;
    if (currentThreadIdRef.current !== newThreadId) {
      // Thread changed - clear streaming messages
      setStreamingMessages([]);
      setCurrentStreamingMessageId(null);
      currentStreamingMessageIdRef.current = null;
      setToolCallInterruptData(null);
      currentThreadIdRef.current = newThreadId;
    }
  }, [threadState.currentThreadId, thread.threadId]);

  // ✅ Phase 1: Merge history and streaming messages
  const unifiedMessages = useMemo(() => {
    const historyMessages: UnifiedMessage[] = messages.messages.map(msg => ({
      ...msg,
      isStreaming: false,
      streamingState: 'complete' as const,
      sseEvents: []
    }));

    // ✅ FIX: Include ALL streaming messages, not just active ones
    // This ensures completed streaming messages are still displayed
    const allStreamingMessages = streamingMessages;

    // ✅ FIX: Remove duplicates - if a message exists in both history and streaming,
    // prefer the streaming version (more up-to-date)
    const historyMessageIds = new Set(historyMessages.map(msg => msg.messageId));
    const uniqueStreamingMessages = allStreamingMessages.filter(msg =>
      !historyMessageIds.has(msg.messageId)
    );

    const merged = [...historyMessages, ...uniqueStreamingMessages]
      .sort((a, b) => a.messageCreatedAt - b.messageCreatedAt);

    return merged;
  }, [messages.messages, streamingMessages]);

  // ✅ PHASE 2: Removed EventRouter registration - using direct handlers instead

  const toolCalls = useChatToolCalls({
    autoApprove: false,
    autoDismissOnSuccess: true
  });

  // ✅ Store tool call interrupt data for UI display
  const [toolCallInterruptData, setToolCallInterruptData] = useState<any>(null);

  // ✅ Process tool call interrupt event when toolCalls is available
  useEffect(() => {
    if (toolCallInterruptEvent && toolCalls) {
      const event = toolCallInterruptEvent;

      // ✅ Create a Message for ToolCallConfirmation component with new role
      const toolCallMessage: Message = {
        messageId: `tool-call-interrupt-${Date.now()}`,
        messageText: `AI muốn thực hiện tool call: ${event.toolName || 'unknown_tool'}\n\n${event.toolDescription || 'Tool execution requires confirmation'}`,
        role: MessageRole.TOOL_CALL_INTERRUP, // ✅ Use new role instead of isToolCallConfirm flag
        messageCreatedAt: Date.now(),
        hasAttachments: false,
        attachments: [],
        isToolCallConfirm: false, // ✅ No longer needed with new role
        avatar: undefined
      };

      // ✅ Add tool call confirmation message to messages
      if (messagesRef.current?.addMessage) {
        messagesRef.current.addMessage(toolCallMessage);
      }

      // Store message ID for later removal
      setToolCallInterruptData({
        messageId: toolCallMessage.messageId,
        role: event.role || 'assistant',
        toolName: event.toolName || 'unknown_tool',
        toolDescription: event.toolDescription || 'Tool execution requires confirmation',
        parameters: event.parameters || {},
        threadId: event.threadId || threadState.currentThreadId || '',
        runId: event.runId || ''
      });

      // Clear the event after processing
      setToolCallInterruptEvent(null);
    }
  }, [toolCallInterruptEvent, toolCalls, threadState.currentThreadId, messagesRef]);

  // ✅ NEW: Detect tool call interrupt from history messages
  // Use ref to avoid circular dependency
  const toolCallInterruptDataRef = useRef(toolCallInterruptData);
  toolCallInterruptDataRef.current = toolCallInterruptData;

  useEffect(() => {
    // Scan all messages (history + streaming) for tool call interrupt
    const allMessages = [...messages.messages, ...streamingMessages];
    const toolCallInterruptMessage = allMessages.find(msg =>
      msg.role === MessageRole.TOOL_CALL_INTERRUP
    );

    const currentData = toolCallInterruptDataRef.current;

    if (toolCallInterruptMessage && !currentData) {
      // Found tool call interrupt in history, set data to lock input


      setToolCallInterruptData({
        messageId: toolCallInterruptMessage.messageId,
        role: 'assistant',
        toolName: 'unknown_tool', // Extract from message text if needed
        toolDescription: toolCallInterruptMessage.messageText || 'Tool execution requires confirmation',
        parameters: {},
        threadId: threadState.currentThreadId || thread.threadId || '',
        runId: '' // No runId for history messages
      });
    } else if (!toolCallInterruptMessage && currentData) {
      // No tool call interrupt found but data exists, clear it

      setToolCallInterruptData(null);
    }
  }, [messages.messages, streamingMessages, threadState.currentThreadId, thread.threadId]);

  // Integration
  const integration = useChatPanelIntegration();

  // Redux state management
  const dispatch = useAppDispatch();
  const currentThreadId = useAppSelector(selectCurrentThreadId);
  const queryClient = useQueryClient();

  // ✅ PHASE 2: Thread loading is now handled by useThreadState hook
  // Create wrapper to match existing interface
  const switchToThreadWrapper = useCallback(async (threadId: string): Promise<void> => {
    // Clear streaming messages when switching threads
    setStreamingMessages([]);
    setCurrentStreamingMessageId(null);
    currentStreamingMessageIdRef.current = null;

    // Use threadState for immediate Redux update
    threadState.switchToThread(threadId);
    // Also update thread hook for backward compatibility
    await thread.switchToThread(threadId);
  }, [threadState, thread]);

  // ✅ FIX: Comprehensive isLoading state that covers sendMessage + SSE streaming
  const [isMessageSending, setIsMessageSending] = useState(false);

  // ✅ Auto SSE connection state - sau 2.5s không có tin nhắn mới thì auto connect SSE
  const autoSSETimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastRunIdRef = useRef<string | null>(null); // Lưu runId cuối cùng
  const AUTO_SSE_DELAY = 2500; // 2.5 seconds

  const isLoading = useMemo(() => {
    return chatAPI.isLoading || isMessageSending || !!currentStreamingMessageIdRef.current;
  }, [chatAPI.isLoading, isMessageSending, currentStreamingMessageIdRef.current]);

  // ✅ Auto SSE connection function - connect sau 2.5s nếu không có tin nhắn mới
  const setupAutoSSEConnection = useCallback((threadId: string, runId: string) => {
    // Lưu runId cuối cùng
    lastRunIdRef.current = runId;

    // Clear existing timeout
    if (autoSSETimeoutRef.current) {
      clearTimeout(autoSSETimeoutRef.current);
    }

    // Setup new timeout - chỉ connect với runId cuối cùng
    autoSSETimeoutRef.current = setTimeout(async () => {
      try {
        // Kiểm tra xem runId này vẫn là runId cuối cùng không
        if (lastRunIdRef.current === runId) {
          console.log('[useChatPanel] Auto connecting SSE after 2.5s delay:', { threadId, runId });
          await chatSSE.connect(threadId, runId);
        } else {
          console.log('[useChatPanel] Skip auto SSE connection - newer runId exists:', {
            currentRunId: runId,
            latestRunId: lastRunIdRef.current
          });
        }
      } catch (error) {
        console.error('[useChatPanel] Auto SSE connection failed:', error);
      }
    }, AUTO_SSE_DELAY);
  }, [chatSSE]);

  // ✅ Cleanup auto SSE timeout on unmount
  useEffect(() => {
    return () => {
      // Clear SSE timeout
      if (autoSSETimeoutRef.current) {
        clearTimeout(autoSSETimeoutRef.current);
        autoSSETimeoutRef.current = null;
      }

      // Clear streaming state
      setStreamingMessages([]);
      setCurrentStreamingMessageId(null);
      currentStreamingMessageIdRef.current = null;

      // Clear tool call data
      setToolCallInterruptData(null);
    };
  }, []);

  // ✅ Helper function để update thread title với message đầu tiên
  const handleFirstMessageTitleUpdate = useCallback(async (messageText: string, targetThreadId: string) => {
    try {
      // Check xem có text content không
      if (!messageText || !messageText.trim()) {
        return;
      }

      // Check xem đây có phải message đầu tiên không bằng cách check số lượng messages
      const currentMessages = messages.messages.filter(msg => msg.role === MessageRole.USER);
      const isFirstMessage = currentMessages.length <= 1; // <= 1 vì vừa mới add message

      if (!isFirstMessage) {
        return;
      }

      // Tạo title từ message text (tối đa 255 ký tự)
      const newTitle = messageText.trim().substring(0, 255);

      console.log('[useChatPanel] Updating thread title for first message:', {
        threadId: targetThreadId,
        newTitle
      });

      // Gọi API cập nhật thread title
      await ThreadsService.updateThread(targetThreadId, newTitle);

      // ✅ ONLY invalidate specific list queries, not ALL threads queries
      // This prevents triggering useThreadsPaginated refetch when user clicks "+"
      queryClient.invalidateQueries({
        queryKey: THREADS_QUERY_KEYS.LIST(),
        refetchType: 'active'
      });

      console.log('[useChatPanel] Thread title updated successfully');

    } catch (error) {
      console.error('[useChatPanel] Failed to update thread title:', error);
      // Không throw error để không ảnh hưởng đến flow gửi message
    }
  }, [messages.messages, queryClient]);

  const sendMessage = useCallback(async (
    content: string | MessageRequestDto,
    fileMetadata?: Array<{
      fileId: string;
      name: string;
      viewUrl?: string;
      source?: string;
    }>
  ): Promise<void> => {
    try {
      // ✅ Clear any existing auto SSE timeout khi có tin nhắn mới
      if (autoSSETimeoutRef.current) {
        clearTimeout(autoSSETimeoutRef.current);
        autoSSETimeoutRef.current = null;
      }

      // ✅ FIX: Set loading state at the beginning of sendMessage
      setIsMessageSending(true);

      let request: MessageRequestDto;
      let messageText = '';

      if (typeof content === 'string') {
        messageText = content;
        request = {
          contentBlocks: {
            type: MessageContentType.TEXT,
            text: content
          },
          alwaysApproveToolCall: toolCalls.alwaysApproveToolCall,
          webSearchEnabled: false // Default value
        };
      } else {
        messageText = content.contentBlocks.text || '';
        request = content;
      }

      // ✅ PHASE 2: Use centralized thread state
      // Use threadState as primary source, fallback to thread hook
      let targetThreadId = threadState.currentThreadId || thread.threadId;

      // Check if this is the first message (no thread exists)
      if (!targetThreadId || targetThreadId.trim() === '') {
        try {
          // Create thread with message content as title (required for API)
          const threadTitle = messageText.trim().substring(0, 255) || 'New Chat';

          const createThreadResponse = await ThreadsService.createThread(threadTitle);

          if (!createThreadResponse?.id) {
            throw new Error('Thread creation failed: No thread ID returned');
          }

          targetThreadId = createThreadResponse.id;

          // ✅ Use threadState.switchToThread to reset new chat mode and update Redux
          await threadState.switchToThread(targetThreadId, threadTitle);

          // Update thread hook state for backward compatibility
          await thread.switchToThread(targetThreadId);

        } catch (threadError) {
          throw new Error(`Unable to create thread: ${threadError instanceof Error ? threadError.message : 'Unknown error'}`);
        }

        // ✅ ONLY invalidate specific list queries, not ALL threads queries
        // This prevents triggering useThreadsPaginated refetch when user clicks "+"
        queryClient.invalidateQueries({
          queryKey: THREADS_QUERY_KEYS.LIST(),
          refetchType: 'active'
        });
      }

      // ✅ Reply message will be handled by displaying reply indicator in UI

      // ✅ IMPORTANT: Chỉ tạo user message SAU KHI API thành công
      // Không tạo user message trước khi gọi API để tránh tạo message khi API fail
      const response = await chatAPI.sendMessage(targetThreadId, request);

      // Handle response - kiểm tra response
      if (!response) {
        throw new Error('No response received from sendMessage API');
      }

      // Note: Thread đã được tạo với title đúng từ đầu, không cần update

      // ✅ Check if this is a tool call decision
      const isToolCallDecision = typeof content !== 'string' &&
        content.contentBlocks.type === MessageContentType.TOOL_CALL_DECISION;



      // ✅ Only require messageId for regular messages, not tool call decisions
      if (!isToolCallDecision && !response.messageId) {
        throw new Error('No messageId received from sendMessage API');
      }

      if (!isToolCallDecision) {
        // Check if message has attachments and extract attachment info
        const hasAttachments = typeof content !== 'string' &&
          content.contentBlocks?.type === MessageContentType.ATTACHMENT &&
          content.contentBlocks.attachments &&
          content.contentBlocks.attachments.length > 0;

        // Use fileMetadata to create attachments with full information
        const attachments: MessageAttachment[] = hasAttachments && typeof content !== 'string' && content.contentBlocks?.attachments
          ? content.contentBlocks.attachments.map(att => {
            // Find corresponding file metadata
            const metadata = fileMetadata?.find(f => f.fileId === att.fileId);

            return {
              attachmentId: att.fileId,
              attachmentType: att.type === AttachmentContentType.IMAGE ? AttachmentType.IMAGE : AttachmentType.FILE,
              name: metadata?.name || `File ${att.fileId.substring(0, 8)}...`,
              viewUrl: metadata?.viewUrl
            };
          })
          : [];

        // Add user message to messages - tạo sau khi API thành công
        const userMessage: Message = {
          messageId: response.messageId, // Tạo unique ID riêng cho user message
          messageText: typeof content === 'string' ? content : content.contentBlocks.text || '',
          role: MessageRole.USER,
          messageCreatedAt: response.createdAt, // Sử dụng timestamp từ API
          hasAttachments: !!hasAttachments,
          isToolCallConfirm: false,
          attachments,
          replyMessageId: request.replyToMessageId // ✅ Set reply message ID from request
        };



        messages.addMessage(userMessage);

        // ✅ Auto-update thread title với message đầu tiên
        await handleFirstMessageTitleUpdate(messageText, targetThreadId);
      } else {

      }

      // ✅ Connect SSE after sending message
      // For tool call decisions, use existing runId if response doesn't have one
      const sseRunId = response.runId || (
        request.contentBlocks?.type === MessageContentType.TOOL_CALL_DECISION
          ? toolCallInterruptData?.runId
          : undefined
      );

      // ✅ runId sẽ được sử dụng cho auto SSE connection nếu cần

      // ✅ KHÔNG connect SSE ngay lập tức, chỉ setup auto SSE connection sau 2.5s
      if (targetThreadId && sseRunId) {
        setupAutoSSEConnection(targetThreadId, sseRunId);
      }

      // ✅ Different success message for tool call decisions
      if (isToolCallDecision) {
        notifications.success('Tool call decision sent successfully');
      } else {
        notifications.success('Message sent successfully');
      }

    } catch (error) {
      throw error;
    } finally {
      // ✅ FIX: Clear loading state when sendMessage completes (success or error)
      // Note: We don't clear here if SSE is starting, let SSE completion handle it
      if (!chatSSE.isConnected) {
        setIsMessageSending(false);
      }
    }
  }, [
    chatAPI,
    thread,
    currentThreadId,
    toolCalls.alwaysApproveToolCall,
    messages,
    chatSSE,
    notifications,
    dispatch,
    queryClient,
    setIsMessageSending, // ✅ FIX: Add setIsMessageSending to dependencies
    setupAutoSSEConnection // ✅ Add auto SSE function to dependencies
  ]);

  // Register markdown handler - simplified
  const registerMarkdownHandler = useCallback((messageId: string, handler: (delta: string) => void) => {
    if (!enableMarkdownStreaming) return;

    // Store the handler for later use
    markdownHandlersRef.current.set(messageId, { processDelta: handler });
    chatSSE.registerMarkdownHandler(messageId, handler);
  }, [enableMarkdownStreaming, chatSSE]);

  // Update message wrapper - Edit message logic
  const updateMessage = useCallback(async (messageId: string, content: string | MessageRequestDto): Promise<void> => {
    try {
      // Extract text content from different input types
      let messageText: string;
      let sendRequest: MessageRequestDto;

      if (typeof content === 'string') {
        messageText = content;
        sendRequest = {
          messageId, // ✅ Include messageId của message đang edit
          contentBlocks: {
            type: MessageContentType.TEXT,
            text: content
          }
        };
      } else if (content.contentBlocks?.type === 'text') {
        messageText = content.contentBlocks.text || '';
        sendRequest = {
          messageId, // ✅ Include messageId của message đang edit
          contentBlocks: {
            type: MessageContentType.TEXT,
            text: messageText
          }
        };
      } else {
        // Handle other content types if needed
        messageText = '';
        sendRequest = {
          messageId, // ✅ Include messageId của message đang edit
          contentBlocks: {
            type: MessageContentType.TEXT,
            text: ''
          }
        };
      }



      // ✅ Step 1: Update text của message đang edit
      messages.updateMessage(messageId, {
        messageText,
        messageCreatedAt: Date.now()
      });

      // ✅ Step 2: Xóa tất cả messages sau message đang edit
      messages.removeMessagesAfter(messageId);

      // ✅ Step 3: Call sendMessage với messageId để tạo response mới
      await sendMessage(sendRequest);


    } catch (error) {

      throw error;
    }
  }, [messages, sendMessage]);

  // Get markdown props
  const getMarkdownProps = useCallback((messageId: string): MarkdownStreamProps => {
    // Return default props - use string theme for simplicity
    return {
      messageId,
      isStreaming: currentStreamingMessageIdRef.current === messageId,
      isWaiting: false,
      showCursor: currentStreamingMessageIdRef.current === messageId,
      cursorStyle: 'blink',
      theme: 'default', // Use string theme instead of object
      plugins: { gfm: true, math: true, mermaid: true },
      className: 'chat-markdown',
      enablePerformanceMonitoring: false
    };
  }, []);

  // ✅ Simplified: Use direct tool call approval

  // Focus chat input
  const focusChatInput = useCallback(() => {
    const focusFunction = integration.getFocusChatInput();
    if (focusFunction) {
      focusFunction();
    }
  }, [integration]);

  // Clear error
  const clearError = useCallback(() => {
    chatAPI.clearError();
    chatSSE.clearError();
    messages.clearError();
    thread.clearError();
    toolCalls.clearError();
  }, [chatAPI, chatSSE, messages, thread, toolCalls]);

  // Retry last message
  const retryLastMessage = useCallback(async (): Promise<void> => {
    try {
      await chatAPI.retryLastRequest();
      notifications.success('Message retried successfully');
    } catch (error) {
      throw error;
    }
  }, [chatAPI, notifications]);

  // Determine current error
  const error = useMemo(() => {
    return chatAPI.error || chatSSE.error || messages.error || thread.error || toolCalls.error;
  }, [chatAPI.error, chatSSE.error, messages.error, thread.error, toolCalls.error]);

  // ✅ Helper function: Xóa tool call interrupt message
  const removeToolCallInterruptMessage = useCallback(async () => {
    try {


      // Tìm message với role TOOL_CALL_INTERRUP trong cả streaming và history messages
      const allMessages = [...messages.messages, ...streamingMessages];
      const toolCallMessage = allMessages.find(msg =>
        msg.role === MessageRole.TOOL_CALL_INTERRUP ||
        (msg.isToolCallConfirm && msg.messageId === toolCallInterruptData?.messageId)
      );



      if (toolCallMessage) {
        // Try to remove from messages using messagesRef
        if (messagesRef.current?.removeMessage) {
          messagesRef.current.removeMessage(toolCallMessage.messageId);

        }

        // Also remove from streaming messages if it exists there
        setStreamingMessages(prev => {
          const filtered = prev.filter(msg =>
            msg.role !== MessageRole.TOOL_CALL_INTERRUP &&
            !(msg.isToolCallConfirm && msg.messageId === toolCallInterruptData?.messageId)
          );

          if (filtered.length !== prev.length) {

          }

          return filtered;
        });
      } else {

      }

      // Clear UI data regardless

      setToolCallInterruptData(null);
    } catch (error) {

    }
  }, [messages.messages, streamingMessages, toolCallInterruptData, messagesRef]);

  // ✅ Helper function: Đảm bảo SSE connection tiếp tục
  const ensureSSEConnection = useCallback(async () => {
    try {
      // sendMessage đã handle SSE connection, chỉ cần verify connection status
      const currentThreadId = threadState.currentThreadId || thread.threadId;
      if (!currentThreadId) {

        return;
      }

      // Kiểm tra SSE connection status
      if (chatSSE.isConnected) {

      } else {
        // Fallback: Thử kết nối với runId từ tool call interrupt
        const fallbackRunId = toolCallInterruptData?.runId;
        if (fallbackRunId) {
          await chatSSE.connect(currentThreadId, fallbackRunId);
        }
      }
    } catch (error) {

      // Don't throw error - SSE connection failure shouldn't break the flow
    }
  }, [threadState.currentThreadId, thread.threadId, chatSSE, toolCallInterruptData]);

  // Register integration
  useEffect(() => {
    integration.registerChatPanel({
      chatStream: {
        sendMessage,
        getCurrentThreadId: thread.getCurrentThreadId,
        switchToThread: thread.switchToThread,
        error,
        threadId: thread.threadId,
        isStreaming: !!currentStreamingMessageIdRef.current,
        isLoading, // ✅ FIX: Use comprehensive loading state
        messages: messages.messages,
        approveToolCall: toolCalls.approveToolCall,
        dismissToolCallInterrupt: toolCalls.dismissToolCallInterrupt
      },
      focusChatInput
    });
  }, [integration, sendMessage, thread, error, chatAPI.isLoading, messages.messages, toolCalls, focusChatInput]);

  return {
    // Message Management
    messages: messages.messages,
    unifiedMessages, // ✅ Phase 1: Add unified messages
    sendMessage,
    updateMessage,
    stopStreaming: async () => {
      // TODO: Implement stop streaming functionality

    },
    clearMessages: useCallback(() => {
      // Clear regular messages
      messages.clearMessages();
      // Clear streaming messages
      setStreamingMessages([]);
      setCurrentStreamingMessageId(null);
      currentStreamingMessageIdRef.current = null;
      // Clear tool call data
      setToolCallInterruptData(null);
    }, [messages]),
    isLoading, // ✅ FIX: Use comprehensive loading state
    isStreaming: !!currentStreamingMessageIdRef.current,

    // ✅ FIXED: Expose history data
    historyMessages: messages.historyMessages,
    isLoadingHistory: messages.isLoadingHistory,
    hasMoreHistory: messages.hasMoreHistory,
    totalHistoryItems: messages.totalHistoryItems,
    loadMoreHistory: messages.loadMoreHistory,
    historyError: messages.error,

    // Streaming State
    currentStreamingMessageId,

    threadId: threadState.currentThreadId || thread.threadId,

    // Thread Management
    clearCurrentThread: threadState.clearCurrentThread, // ✅ Clear current thread and enter new chat mode
    switchToThread: switchToThreadWrapper,
    getCurrentThreadId: thread.getCurrentThreadId,

    // Thread Loading States
    isLoadingFirstThread: threadState.isLoadingFirstThread,
    isSwitchingThread: threadState.isSwitchingThread,
    isClearingThread: threadState.isClearingThread,

    // Streaming & Markdown
    registerMarkdownHandler,
    getMarkdownProps,

    // Tool Calls
    toolCallInterrupt: toolCallInterruptData,
    approveToolCall: async (decision: 'yes' | 'no' | 'always') => {
      // ✅ Handle tool call decision via sendMessage API
      try {


        // Create tool call decision message
        const decisionRequest: MessageRequestDto = {
          contentBlocks: {
            type: MessageContentType.TOOL_CALL_DECISION,
            toolCallDecision: decision === 'yes' ? ToolCallDecision.YES : ToolCallDecision.NO
          }
        };



        // ✅ BƯỚC 1: Gửi decision qua sendMessage API
        // Note: sendMessage đã có logic kết nối SSE với runId từ response hoặc toolCallInterruptData
        await sendMessage(decisionRequest);

        // ✅ BƯỚC 2: Xóa tool call interrupt message từ UI
        await removeToolCallInterruptMessage();

        // ✅ BƯỚC 3: Verify SSE connection status
        await ensureSSEConnection();
      } catch (error) {

        throw error; // Re-throw để component có thể handle error
      }
    },
    dismissToolCallInterrupt: () => {
      // ✅ Remove tool call confirmation message from UI
      if (toolCallInterruptData?.messageId && messagesRef.current?.removeMessage) {
        messagesRef.current.removeMessage(toolCallInterruptData.messageId);
      }

      toolCalls.dismissToolCallInterrupt();
      setToolCallInterruptData(null); // Also clear UI data
    },

    // State & Config
    error,
    config: chatConfig,

    // Network State
    isOnline: chatAPI.isOnline,
    isRetrying: chatAPI.isRetrying,
    retryAttempts: chatAPI.retryAttempts,

    // Integration
    focusChatInput,

    // Utils
    clearError,
    retryLastMessage,
    retryFailedRequests: chatAPI.retryFailedRequests,
    resetRetries: chatAPI.resetRetries
  };
};
