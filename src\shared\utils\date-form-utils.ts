/**
 * Utility functions để xử lý chuyển đổi date trong forms
 */

/**
 * Chuyển đổi Date object thành string format YYYY-MM-DD
 * @param date Date object hoặc null
 * @returns String format YYYY-MM-DD hoặc empty string
 */
export const dateToString = (date: Date | null): string => {
  if (!date) return '';
  try {
    return date.toISOString().split('T')[0] || '';
  } catch {
    return '';
  }
};

/**
 * Chuyển đổi string thành Date object
 * @param dateString String format YYYY-MM-DD hoặc ISO string
 * @returns Date object hoặc null
 */
export const stringToDate = (dateString: string): Date | null => {
  if (!dateString) return null;
  try {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date;
  } catch {
    return null;
  }
};

/**
 * Chuyển đổi date string (YYYY-MM-DD) thành bigint timestamp (milliseconds)
 * @param dateString String format YYYY-MM-DD
 * @returns Bigint timestamp hoặc null nếu invalid
 */
export const dateStringToBigInt = (dateString: string): bigint | null => {
  if (!dateString) return null;
  try {
    const date = new Date(dateString + 'T00:00:00.000Z'); // Thêm time để đảm bảo UTC
    const timestamp = date.getTime();
    return isNaN(timestamp) ? null : BigInt(timestamp);
  } catch {
    return null;
  }
};

/**
 * Chuyển đổi Date object thành bigint timestamp (milliseconds)
 * @param date Date object
 * @returns Bigint timestamp hoặc null nếu invalid
 */
export const dateToBigInt = (date: Date | null): bigint | null => {
  if (!date) return null;
  try {
    const timestamp = date.getTime();
    return isNaN(timestamp) ? null : BigInt(timestamp);
  } catch {
    return null;
  }
};

/**
 * Chuyển đổi bigint timestamp thành Date object
 * @param timestamp Bigint timestamp (milliseconds)
 * @returns Date object hoặc null nếu invalid
 */
export const bigIntToDate = (timestamp: bigint | null): Date | null => {
  if (!timestamp) return null;
  try {
    const date = new Date(Number(timestamp));
    return isNaN(date.getTime()) ? null : date;
  } catch {
    return null;
  }
};

/**
 * Normalize date value - chuyển đổi string hoặc Date thành Date object
 * @param value String hoặc Date
 * @returns Date object hoặc null
 */
export const normalizeDate = (value: string | Date | null | undefined): Date | null => {
  if (!value) return null;
  if (value instanceof Date) return value;
  if (typeof value === 'string') return stringToDate(value);
  return null;
};

/**
 * Validate date range - kiểm tra ngày có trong khoảng cho phép
 * @param date Date để kiểm tra
 * @param minDate Ngày tối thiểu
 * @param maxDate Ngày tối đa
 * @returns true nếu hợp lệ
 */
export const isDateInRange = (
  date: Date | string | null,
  minDate?: Date | string | null,
  maxDate?: Date | string | null
): boolean => {
  const normalizedDate = normalizeDate(date);
  if (!normalizedDate) return false;

  const normalizedMin = normalizeDate(minDate);
  const normalizedMax = normalizeDate(maxDate);

  if (normalizedMin && normalizedDate < normalizedMin) return false;
  if (normalizedMax && normalizedDate > normalizedMax) return false;

  return true;
};

/**
 * Calculate age từ date of birth
 * @param birthDate Ngày sinh
 * @returns Tuổi (số nguyên)
 */
export const calculateAge = (birthDate: Date | string | null): number => {
  const normalizedBirthDate = normalizeDate(birthDate);
  if (!normalizedBirthDate) return 0;

  const today = new Date();
  let age = today.getFullYear() - normalizedBirthDate.getFullYear();
  const monthDiff = today.getMonth() - normalizedBirthDate.getMonth();

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < normalizedBirthDate.getDate())) {
    age--;
  }

  return age;
};

/**
 * Format date cho hiển thị
 * @param date Date object hoặc string
 * @param format Format string (default: 'dd/MM/yyyy')
 * @returns Formatted string
 */
export const formatDateForDisplay = (
  date: Date | string | null,
  format: string = 'dd/MM/yyyy'
): string => {
  const normalizedDate = normalizeDate(date);
  if (!normalizedDate) return '';

  try {
    // Simple format implementation
    const day = normalizedDate.getDate().toString().padStart(2, '0');
    const month = (normalizedDate.getMonth() + 1).toString().padStart(2, '0');
    const year = normalizedDate.getFullYear().toString();

    switch (format) {
      case 'dd/MM/yyyy':
        return `${day}/${month}/${year}`;
      case 'MM/dd/yyyy':
        return `${month}/${day}/${year}`;
      case 'yyyy-MM-dd':
        return `${year}-${month}-${day}`;
      default:
        return `${day}/${month}/${year}`;
    }
  } catch {
    return '';
  }
};

/**
 * Kiểm tra date có hợp lệ không
 * @param date Date để kiểm tra
 * @returns true nếu hợp lệ
 */
export const isValidDate = (date: Date | string | null): boolean => {
  const normalizedDate = normalizeDate(date);
  return normalizedDate !== null && !isNaN(normalizedDate.getTime());
};

/**
 * Tạo date từ các component riêng lẻ
 * @param year Năm
 * @param month Tháng (1-12)
 * @param day Ngày
 * @returns Date object hoặc null
 */
export const createDate = (year: number, month: number, day: number): Date | null => {
  try {
    const date = new Date(year, month - 1, day); // month is 0-indexed
    return isValidDate(date) ? date : null;
  } catch {
    return null;
  }
};

/**
 * So sánh hai date
 * @param date1 Date thứ nhất
 * @param date2 Date thứ hai
 * @returns -1 nếu date1 < date2, 0 nếu bằng nhau, 1 nếu date1 > date2
 */
export const compareDates = (
  date1: Date | string | null,
  date2: Date | string | null
): number => {
  const normalizedDate1 = normalizeDate(date1);
  const normalizedDate2 = normalizeDate(date2);

  if (!normalizedDate1 && !normalizedDate2) return 0;
  if (!normalizedDate1) return -1;
  if (!normalizedDate2) return 1;

  if (normalizedDate1 < normalizedDate2) return -1;
  if (normalizedDate1 > normalizedDate2) return 1;
  return 0;
};

/**
 * Kiểm tra date có phải là hôm nay không
 * @param date Date để kiểm tra
 * @returns true nếu là hôm nay
 */
export const isToday = (date: Date | string | null): boolean => {
  const normalizedDate = normalizeDate(date);
  if (!normalizedDate) return false;

  const today = new Date();
  return (
    normalizedDate.getDate() === today.getDate() &&
    normalizedDate.getMonth() === today.getMonth() &&
    normalizedDate.getFullYear() === today.getFullYear()
  );
};

/**
 * Kiểm tra date có phải là trong quá khứ không
 * @param date Date để kiểm tra
 * @returns true nếu là quá khứ
 */
export const isPastDate = (date: Date | string | null): boolean => {
  const normalizedDate = normalizeDate(date);
  if (!normalizedDate) return false;

  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset time to start of day
  
  const checkDate = new Date(normalizedDate);
  checkDate.setHours(0, 0, 0, 0);

  return checkDate < today;
};

/**
 * Kiểm tra date có phải là trong tương lai không
 * @param date Date để kiểm tra
 * @returns true nếu là tương lai
 */
export const isFutureDate = (date: Date | string | null): boolean => {
  const normalizedDate = normalizeDate(date);
  if (!normalizedDate) return false;

  const today = new Date();
  today.setHours(23, 59, 59, 999); // Reset time to end of day
  
  return normalizedDate > today;
};
