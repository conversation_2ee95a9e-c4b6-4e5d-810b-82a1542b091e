import React, { useCallback, useMemo, useEffect, useState } from 'react';
import { DashboardCollapsibleSidebar, DashboardWorkspace, AddWidgetModal } from '../components';
import { DashboardWidget } from '../types';
import { useDashboardTabs } from '../hooks/useDashboardTabs';
import { initializeWidgets } from '../registry/autoRegister';
import { debugWidgets } from '../utils/debugWidgets';
import { cleanupDashboardTabs } from '../utils/cleanupWidgets';
// import '../scripts/cleanupDashboard'; // Load cleanup scripts for debugging
import { usePDFExport } from '../hooks/usePDFExport';
import PDFExportModal from '../components/PDFExportModal';
import '../styles/dashboard.css';

const DashboardPage: React.FC = () => {
  const [isAddWidgetModalOpen, setIsAddWidgetModalOpen] = useState(false);
  const [displayMode, setDisplayMode] = useState<'normal' | 'minimal' | 'ultra-minimal'>('normal');
  const [smartLayoutMode, setSmartLayoutMode] = useState(true);
  const [autoHeightMode, setAutoHeightMode] = useState(true);
  const {
    isModalOpen: isPDFModalOpen,
    closeExportModal,
    openExportModal,
    isExporting,
  } = usePDFExport();

  // Initialize widgets on component mount
  useEffect(() => {
    const initWidgets = async () => {
      try {
        await initializeWidgets();

        // Clean up invalid widgets from localStorage
        const hasCleanedUp = cleanupDashboardTabs();
        if (hasCleanedUp) {
          console.log('🧹 Dashboard cleanup completed - page will reload');
          // Reload page để áp dụng cleanup
          window.location.reload();
          return;
        }

        // Debug widget registry
        setTimeout(() => {
          debugWidgets.checkRegistry();
        }, 1000);
      } catch (error) {
        console.error('Failed to initialize dashboard widgets:', error);
      }
    };

    initWidgets();
  }, []);

  // Use tab system with backend integration
  const {
    tabsState,
    currentTab,
    isInitialized,
    currentDashboardId,
    isLoading,
    switchToTab,
    createTab,
    renameTab,
    deleteTab,
    changeTabMode,
    addWidgetToCurrentTab,
    removeWidgetFromCurrentTab,
    updateWidgetTitleInCurrentTab,
    updateWidgetPropsInCurrentTab,
    updateTabWidgets,
    saveToStorage,
    saveToServer,
  } = useDashboardTabs();

  // Get current tab widgets
  const widgets = useMemo(() => currentTab?.widgets || [], [currentTab?.widgets]);

  // Handle layout change
  const handleLayoutChange = useCallback(
    (layout: any[]) => {
      if (!currentTab) return;

      // Update widgets with new layout positions
      const updatedWidgets = widgets.map(widget => {
        const layoutItem = layout.find(item => item.i === widget.id);
        if (layoutItem) {
          return {
            ...widget,
            x: layoutItem.x,
            y: layoutItem.y,
            w: layoutItem.w,
            h: layoutItem.h,
          };
        }
        return widget;
      });

      // Update current tab widgets using the hook
      updateTabWidgets(updatedWidgets);
    },
    [currentTab, widgets, updateTabWidgets]
  );

  // Handle adding widget from modal
  const handleAddWidget = useCallback(
    (widget: DashboardWidget) => {
      addWidgetToCurrentTab(widget);
    },
    [addWidgetToCurrentTab]
  );

  const handleRemoveWidget = useCallback(
    (widgetId: string) => {
      // Only allow removing widgets in edit mode
      if (currentTab?.mode !== 'edit') {
        return;
      }
      removeWidgetFromCurrentTab(widgetId);
    },
    [currentTab?.mode, removeWidgetFromCurrentTab]
  );

  const handleSave = useCallback(async () => {
    try {
      // Save to both localStorage and server
      saveToStorage();

      // If we have a dashboard ID, also save to server
      if (currentDashboardId) {
        await saveToServer();
      } else {
        // Create new dashboard on server
        await saveToServer('My Dashboard');
      }
    } catch (error) {
      // Still save to localStorage as fallback
      saveToStorage();
    }
  }, [saveToStorage, saveToServer, currentDashboardId]);

  const handleToggleDisplayMode = useCallback(() => {
    setDisplayMode(prev => {
      if (prev === 'normal') return 'minimal';
      if (prev === 'minimal') return 'ultra-minimal';
      return 'normal';
    });
  }, []);

  const handleToggleSmartLayout = useCallback(() => {
    setSmartLayoutMode(prev => !prev);
  }, []);

  const handleToggleAutoHeight = useCallback(() => {
    setAutoHeightMode(prev => !prev);
  }, []);

  // Show loading state while initializing
  if (isLoading && !isInitialized) {
    return (
      <div className="w-full bg-background text-foreground flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Đang tải dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-background text-foreground flex flex-col min-h-screen dashboard-page">
      {/* Horizontal Sidebar */}
      <DashboardCollapsibleSidebar
        onAddWidget={() => setIsAddWidgetModalOpen(true)}
        onCreateTab={createTab}
        canEdit={currentTab?.mode === 'edit'}
        tabs={tabsState.tabs}
        currentTabId={tabsState.currentTabId}
        currentTabMode={currentTab?.mode}
        onSwitchTab={switchToTab}
        onDeleteTab={deleteTab}
        onRenameTab={renameTab}
        onChangeMode={changeTabMode}
        onSave={handleSave}
        displayMode={displayMode}
        onToggleDisplayMode={handleToggleDisplayMode}
        onExportPDF={openExportModal}
        isExportingPDF={isExporting}
        smartLayoutMode={smartLayoutMode}
        onToggleSmartLayout={handleToggleSmartLayout}
        autoHeightMode={autoHeightMode}
        onToggleAutoHeight={handleToggleAutoHeight}
      />

      {/* Main Workspace */}
      <div className="flex-1" data-dashboard-container>
        <DashboardWorkspace
          widgets={widgets}
          onLayoutChange={handleLayoutChange}
          onRemoveWidget={handleRemoveWidget}
          onWidgetTitleChange={updateWidgetTitleInCurrentTab}
          onWidgetPropsChange={updateWidgetPropsInCurrentTab}
          isDraggable={currentTab?.mode === 'edit'}
          isResizable={currentTab?.mode === 'edit'}
          mode={currentTab?.mode || 'edit'}
          displayMode={displayMode}
          smartLayoutMode={smartLayoutMode}
          autoHeightMode={autoHeightMode}
        />
      </div>

      {/* Add Widget Modal */}
      <AddWidgetModal
        isOpen={isAddWidgetModalOpen}
        onClose={() => setIsAddWidgetModalOpen(false)}
        onAddWidget={handleAddWidget}
        existingWidgets={widgets}
      />

      {/* PDF Export Modal */}
      <PDFExportModal isOpen={isPDFModalOpen} onClose={closeExportModal} />
    </div>
  );
};

export default DashboardPage;
