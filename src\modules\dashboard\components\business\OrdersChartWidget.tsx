import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Loading, Typography, Button, DoubleDatePicker } from '@/shared/components/common';
import { BarChart } from '@/shared/components/charts';
import { useOrdersChart } from '@/modules/business/hooks/useReportQuery';
import { OrdersChartQueryDto } from '@/modules/business/types/report.types';
import { BarChart3, Calendar, RefreshCw } from 'lucide-react';
import { format } from 'date-fns';
import { type BaseWidgetProps } from '../../types';

/**
 * Widget hiển thị biểu đồ đơn hàng theo trạng thái
 */
const OrdersChartWidget: React.FC<BaseWidgetProps> = ({
  className,
  isLoading: externalLoading = false,
}) => {
  const { t } = useTranslation(['business', 'common']);
  
  // State cho filter
  const [queryParams, setQueryParams] = useState<OrdersChartQueryDto>({});

  // State cho date range picker
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

  // Gọi API với params
  const { data: ordersData, isLoading: internalLoading, error, refetch } = useOrdersChart(queryParams);

  const isLoading = externalLoading || internalLoading;

  // Xử lý dữ liệu cho biểu đồ
  const chartData = useMemo(() => {
    if (!ordersData?.data) return [];

    return ordersData.data.map(item => ({
      period: item.period,
      date: item.date,
      [t('business:order.status.pending', 'Chờ xử lý')]: item.pendingOrders,
      [t('business:order.status.preparing', 'Đang chuẩn bị')]: item.preparingOrders,
      [t('business:order.status.shipped', 'Đã gửi')]: item.shippedOrders,
      [t('business:order.status.inTransit', 'Đang vận chuyển')]: item.inTransitOrders,
      [t('business:order.status.delivered', 'Đã giao')]: item.deliveredOrders,
      [t('business:order.status.cancelled', 'Đã hủy')]: item.cancelledOrders,
      totalOrders: item.totalOrders,
    }));
  }, [ordersData, t]);

  // Cấu hình bars cho biểu đồ
  const barConfigs = useMemo(() => [
    {
      dataKey: t('business:order.status.pending', 'Chờ xử lý'),
      name: t('business:order.status.pending', 'Chờ xử lý'),
      color: '#F59E0B', // amber
    },
    {
      dataKey: t('business:order.status.preparing', 'Đang chuẩn bị'),
      name: t('business:order.status.preparing', 'Đang chuẩn bị'),
      color: '#3B82F6', // blue
    },
    {
      dataKey: t('business:order.status.shipped', 'Đã gửi'),
      name: t('business:order.status.shipped', 'Đã gửi'),
      color: '#8B5CF6', // violet
    },
    {
      dataKey: t('business:order.status.inTransit', 'Đang vận chuyển'),
      name: t('business:order.status.inTransit', 'Đang vận chuyển'),
      color: '#06B6D4', // cyan
    },
    {
      dataKey: t('business:order.status.delivered', 'Đã giao'),
      name: t('business:order.status.delivered', 'Đã giao'),
      color: '#10B981', // emerald
    },
    {
      dataKey: t('business:order.status.cancelled', 'Đã hủy'),
      name: t('business:order.status.cancelled', 'Đã hủy'),
      color: '#EF4444', // red
    },
  ], [t]);

  // Xử lý thay đổi date range
  const handleDateRangeChange = (dates: [Date | null, Date | null]) => {
    setDateRange(dates);

    // Cập nhật query params với startDate và endDate
    const [startDate, endDate] = dates;
    setQueryParams(prev => ({
      ...prev,
      startDate: startDate ? format(startDate, 'yyyy-MM-dd') : undefined,
      endDate: endDate ? format(endDate, 'yyyy-MM-dd') : undefined,
    }));
  };

  // Xử lý refresh
  const handleRefresh = () => {
    refetch();
  };

  if (error) {
    return (
      <div className={`flex flex-col items-center justify-center h-64 text-center ${className || ''}`}>
        <BarChart3 className="w-8 h-8 text-muted-foreground mb-2" />
        <Typography variant="body2" className="text-muted-foreground mb-2">
          {t('common:error.loadFailed')}
        </Typography>
        <Typography variant="caption" className="text-muted-foreground mb-4">
          {error.message}
        </Typography>
        <Button variant="outline" size="sm" onClick={handleRefresh}>
          {t('common:retry')}
        </Button>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className || ''}`}>
        <Loading size="sm" />
      </div>
    );
  }

  if (!chartData || chartData.length === 0) {
    return (
      <div className={`flex flex-col items-center justify-center h-64 text-center ${className || ''}`}>
        <BarChart3 className="w-8 h-8 text-muted-foreground mb-2" />
        <Typography variant="body2" className="text-muted-foreground mb-2">
          {t('business:order.noData', 'Chưa có dữ liệu đơn hàng')}
        </Typography>
        <Typography variant="caption" className="text-muted-foreground">
          {t('business:order.noDataDescription', 'Dữ liệu sẽ hiển thị khi có đơn hàng')}
        </Typography>
      </div>
    );
  }

  return (
    <div className={`w-full ${className || ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <BarChart3 className="w-5 h-5 text-primary" />
          <Typography variant="h6" className="font-semibold">
            {t('business:order.chart.title', 'Biểu đồ đơn hàng')}
          </Typography>
        </div>
        
        {/* Controls */}
        <div className="flex items-center gap-3">
          {/* Date Range Picker */}
          <DoubleDatePicker
            value={dateRange}
            onChange={handleDateRangeChange}
            triggerIcon={<Calendar className="w-4 h-4" />}
            size="sm"
          />

          {/* Refresh Button */}
          <Button variant="ghost" size="sm" onClick={handleRefresh}>
            <RefreshCw className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Status Breakdown Summary */}
      {ordersData?.statusBreakdown && (
        <div className="grid grid-cols-3 md:grid-cols-6 gap-2 mb-4">
          <div className="text-center p-2 bg-amber-50 rounded">
            <Typography variant="caption" className="text-amber-600">
              {t('business:order.status.pending', 'Chờ xử lý')}
            </Typography>
            <Typography variant="body2" className="font-semibold text-amber-800">
              {ordersData.statusBreakdown.pending}
            </Typography>
          </div>
          <div className="text-center p-2 bg-blue-50 rounded">
            <Typography variant="caption" className="text-blue-600">
              {t('business:order.status.preparing', 'Đang chuẩn bị')}
            </Typography>
            <Typography variant="body2" className="font-semibold text-blue-800">
              {ordersData.statusBreakdown.preparing}
            </Typography>
          </div>
          <div className="text-center p-2 bg-violet-50 rounded">
            <Typography variant="caption" className="text-violet-600">
              {t('business:order.status.shipped', 'Đã gửi')}
            </Typography>
            <Typography variant="body2" className="font-semibold text-violet-800">
              {ordersData.statusBreakdown.shipped}
            </Typography>
          </div>
          <div className="text-center p-2 bg-cyan-50 rounded">
            <Typography variant="caption" className="text-cyan-600">
              {t('business:order.status.inTransit', 'Đang vận chuyển')}
            </Typography>
            <Typography variant="body2" className="font-semibold text-cyan-800">
              {ordersData.statusBreakdown.inTransit}
            </Typography>
          </div>
          <div className="text-center p-2 bg-emerald-50 rounded">
            <Typography variant="caption" className="text-emerald-600">
              {t('business:order.status.delivered', 'Đã giao')}
            </Typography>
            <Typography variant="body2" className="font-semibold text-emerald-800">
              {ordersData.statusBreakdown.delivered}
            </Typography>
          </div>
          <div className="text-center p-2 bg-red-50 rounded">
            <Typography variant="caption" className="text-red-600">
              {t('business:order.status.cancelled', 'Đã hủy')}
            </Typography>
            <Typography variant="body2" className="font-semibold text-red-800">
              {ordersData.statusBreakdown.cancelled}
            </Typography>
          </div>
        </div>
      )}

      {/* Chart */}
      <Card className="overflow-hidden">
        <div className="p-4">
          <BarChart
            data={chartData}
            xAxisKey="period"
            bars={barConfigs}
            height={300}
            showGrid
            showTooltip
            showLegend
            stacked
            xAxisLabel={t('common:period.label', 'Thời gian')}
            yAxisLabel={t('business:order.count', 'Số đơn hàng')}
            legendPosition="bottom"
          />
        </div>
      </Card>
    </div>
  );
};

export default OrdersChartWidget;
