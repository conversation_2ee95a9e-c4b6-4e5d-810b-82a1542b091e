/**
 * Zalo Chat Service
 * Business logic cho module chat Zalo
 */

import {
  zaloAccountsApi,
  contacts<PERSON>pi,
  messagesApi,
  uploadApi,
  settingsApi,
} from '../api/zalo-chat.api';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import type {
  ZaloAccount,
  Contact,
  Message,
  ContactSearchParams,
  MessageSearchParams,
  SendMessageRequest,
  ContactDto,
  ZaloAccountDto,
  ChatSettings,
} from '../types/zalo-chat.types';
import {
  PAGINATION_DEFAULTS,
  FILE_UPLOAD_LIMITS,
  ERROR_MESSAGES,
  DEFAULT_CHAT_SETTINGS,
} from '../constants/zalo-chat.constants';

/**
 * Zalo Accounts Service
 */
export const zaloAccountsService = {
  /**
   * <PERSON><PERSON>y danh sách tài khoản Zalo với business logic
   */
  getAccountsWithBusinessLogic: async (): Promise<ZaloAccount[]> => {
    try {
      const response = await zaloAccountsApi.getAccounts();
      if (response.code < 200 || response.code >= 300) {
        throw new Error(response.message || ERROR_MESSAGES.NETWORK_ERROR);
      }

      // Sắp xếp theo trạng thái active và tên
      return response.result.sort((a, b) => {
        if (a.isActive !== b.isActive) {
          return a.isActive ? -1 : 1;
        }
        return a.name.localeCompare(b.name);
      });
    } catch (error) {
      console.error('Error fetching Zalo accounts:', error);
      throw error;
    }
  },

  /**
   * Lấy tài khoản active đầu tiên
   */
  getActiveAccount: async (): Promise<ZaloAccount | null> => {
    const accounts = await zaloAccountsService.getAccountsWithBusinessLogic();
    return accounts.find(account => account.isActive) || null;
  },

  /**
   * Tạo tài khoản Zalo với validation
   */
  createAccountWithValidation: async (data: ZaloAccountDto): Promise<ZaloAccount> => {
    // Validation
    if (!data.name.trim()) {
      throw new Error('Tên tài khoản không được để trống');
    }

    if (!['oa', 'personal'].includes(data.type)) {
      throw new Error('Loại tài khoản không hợp lệ');
    }

    try {
      const response = await zaloAccountsApi.createAccount(data);
      if (response.code < 200 || response.code >= 300) {
        throw new Error(response.message || 'Tạo tài khoản thất bại');
      }
      return response.result;
    } catch (error) {
      console.error('Error creating Zalo account:', error);
      throw error;
    }
  },

  /**
   * Cập nhật tài khoản với validation
   */
  updateAccountWithValidation: async (id: string, data: Partial<ZaloAccountDto>): Promise<ZaloAccount> => {
    if (data.name && !data.name.trim()) {
      throw new Error('Tên tài khoản không được để trống');
    }

    try {
      const response = await zaloAccountsApi.updateAccount(id, data);
      if (response.code < 200 || response.code >= 300) {
        throw new Error(response.message || 'Cập nhật tài khoản thất bại');
      }
      return response.result;
    } catch (error) {
      console.error('Error updating Zalo account:', error);
      throw error;
    }
  },
};

/**
 * Contacts Service
 */
export const contactsService = {
  /**
   * Lấy danh sách liên hệ với business logic
   */
  getContactsWithBusinessLogic: async (params?: ContactSearchParams): Promise<PaginatedResult<Contact>> => {
    const defaultParams: ContactSearchParams = {
      page: PAGINATION_DEFAULTS.PAGE,
      limit: PAGINATION_DEFAULTS.CONTACTS_LIMIT,
      sortBy: 'lastMessageTime',
      sortOrder: 'desc',
      ...params,
    };

    // Validation
    if (defaultParams.limit && defaultParams.limit > 100) {
      throw new Error('Limit không được vượt quá 100');
    }

    try {
      const response = await contactsApi.getContacts(defaultParams);

      // ApiResponseDto có cấu trúc: { code, message, result }
      if (response.code < 200 || response.code >= 300) {
        throw new Error(response.message || ERROR_MESSAGES.NETWORK_ERROR);
      }

      // Xử lý dữ liệu trả về
      const processedContacts = response.result.items.map(contact => ({
        ...contact,
        // Đảm bảo unreadCount không âm
        unreadCount: Math.max(0, contact.unreadCount),
        // Format tên hiển thị
        name: contact.name.trim() || contact.phone || contact.zaloId,
      }));

      return {
        ...response.result,
        items: processedContacts,
      };
    } catch (error) {
      console.error('Error fetching contacts:', error);
      throw error;
    }
  },

  /**
   * Tìm kiếm liên hệ với debounce logic
   */
  searchContactsWithDebounce: async (query: string, accountId?: string): Promise<Contact[]> => {
    if (!query.trim() || query.length < 2) {
      return [];
    }

    try {
      const response = await contactsApi.searchContacts(query.trim(), accountId);
      if (response.code < 200 || response.code >= 300) {
        throw new Error(response.message || 'Tìm kiếm thất bại');
      }

      return response.result.map((contact: Contact) => ({
        ...contact,
        name: contact.name.trim() || contact.phone || contact.zaloId,
      }));
    } catch (error) {
      console.error('Error searching contacts:', error);
      throw error;
    }
  },

  /**
   * Tạo liên hệ với validation
   */
  createContactWithValidation: async (data: ContactDto): Promise<Contact> => {
    // Validation
    if (!data.name.trim()) {
      throw new Error('Tên liên hệ không được để trống');
    }

    if (!data.zaloId.trim()) {
      throw new Error('Zalo ID không được để trống');
    }

    if (data.phone && !/^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/.test(data.phone)) {
      throw new Error('Số điện thoại không hợp lệ');
    }

    try {
      const response = await contactsApi.createContact({
        ...data,
        name: data.name.trim(),
        zaloId: data.zaloId.trim(),
        phone: data.phone?.trim(),
        notes: data.notes?.trim(),
      });

      if (response.code < 200 || response.code >= 300) {
        throw new Error(response.message || 'Tạo liên hệ thất bại');
      }

      return response.result;
    } catch (error) {
      console.error('Error creating contact:', error);
      throw error;
    }
  },
};

/**
 * Messages Service
 */
export const messagesService = {
  /**
   * Lấy danh sách tin nhắn với business logic
   */
  getMessagesWithBusinessLogic: async (params: MessageSearchParams): Promise<PaginatedResult<Message>> => {
    const defaultParams: MessageSearchParams = {
      page: PAGINATION_DEFAULTS.PAGE,
      limit: PAGINATION_DEFAULTS.MESSAGES_LIMIT,
      ...params,
    };

    // Validation
    if (!defaultParams.conversationId) {
      throw new Error('Conversation ID là bắt buộc');
    }

    if (defaultParams.limit && defaultParams.limit > 100) {
      throw new Error('Limit không được vượt quá 100');
    }

    try {
      const response = await messagesApi.getMessages(defaultParams);
      if (response.code < 200 || response.code >= 300) {
        throw new Error(response.message || ERROR_MESSAGES.NETWORK_ERROR);
      }

      // Sắp xếp tin nhắn theo thời gian (cũ nhất trước)
      const sortedMessages = response.result.items.sort((a, b) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );

      return {
        ...response.result,
        items: sortedMessages,
      };
    } catch (error) {
      console.error('Error fetching messages:', error);
      throw error;
    }
  },

  /**
   * Gửi tin nhắn với validation
   */
  sendMessageWithValidation: async (data: SendMessageRequest): Promise<Message> => {
    // Validation
    if (!data.conversationId) {
      throw new Error('Conversation ID là bắt buộc');
    }

    if (!data.content.trim() && (!data.attachments || data.attachments.length === 0)) {
      throw new Error('Nội dung tin nhắn hoặc file đính kèm là bắt buộc');
    }

    if (data.content.length > 2000) {
      throw new Error('Nội dung tin nhắn không được vượt quá 2000 ký tự');
    }

    // Validate attachments
    if (data.attachments && data.attachments.length > 0) {
      if (data.attachments.length > FILE_UPLOAD_LIMITS.MAX_FILES) {
        throw new Error(`Chỉ được đính kèm tối đa ${FILE_UPLOAD_LIMITS.MAX_FILES} file`);
      }

      for (const file of data.attachments) {
        if (file.size > FILE_UPLOAD_LIMITS.MAX_SIZE) {
          throw new Error(`File "${file.name}" quá lớn. Kích thước tối đa là ${FILE_UPLOAD_LIMITS.MAX_SIZE / 1024 / 1024}MB`);
        }

        const isImageType = (FILE_UPLOAD_LIMITS.ALLOWED_IMAGE_TYPES as readonly string[]).includes(file.type);
        const isFileType = (FILE_UPLOAD_LIMITS.ALLOWED_FILE_TYPES as readonly string[]).includes(file.type);

        if (!isImageType && !isFileType) {
          throw new Error(`Loại file "${file.type}" không được hỗ trợ`);
        }
      }
    }

    try {
      const response = await messagesApi.sendMessage({
        ...data,
        content: data.content.trim(),
      });

      if (response.code < 200 || response.code >= 300) {
        throw new Error(response.message || ERROR_MESSAGES.MESSAGE_SEND_FAILED);
      }

      return response.result;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  },

  /**
   * Đánh dấu tin nhắn đã đọc
   */
  markMessagesAsRead: async (conversationId: string, messageIds: string[]): Promise<void> => {
    if (!conversationId || messageIds.length === 0) {
      return;
    }

    try {
      const response = await messagesApi.markAsRead(conversationId, messageIds);
      if (response.code < 200 || response.code >= 300) {
        throw new Error(response.message || 'Đánh dấu đã đọc thất bại');
      }
    } catch (error) {
      console.error('Error marking messages as read:', error);
      // Không throw error để không ảnh hưởng đến UX
    }
  },
};

/**
 * File Upload Service
 */
export const uploadService = {
  /**
   * Upload file với validation và progress tracking
   */
  uploadFileWithValidation: async (
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<{ url: string; name: string; size: number }> => {
    // Validation
    if (file.size > FILE_UPLOAD_LIMITS.MAX_SIZE) {
      throw new Error(ERROR_MESSAGES.FILE_TOO_LARGE);
    }

    const isImageType = (FILE_UPLOAD_LIMITS.ALLOWED_IMAGE_TYPES as readonly string[]).includes(file.type);
    const isFileType = (FILE_UPLOAD_LIMITS.ALLOWED_FILE_TYPES as readonly string[]).includes(file.type);

    if (!isImageType && !isFileType) {
      throw new Error(ERROR_MESSAGES.FILE_TYPE_NOT_SUPPORTED);
    }

    try {
      const response = await uploadApi.uploadAttachment(file, onProgress);
      if (response.code < 200 || response.code >= 300) {
        throw new Error(response.message || 'Upload file thất bại');
      }

      return response.result;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  },
};

/**
 * Settings Service
 */
export const settingsService = {
  /**
   * Lấy cài đặt chat với default values
   */
  getChatSettingsWithDefaults: async (): Promise<ChatSettings> => {
    try {
      const response = await settingsApi.getChatSettings();
      if (response.code < 200 || response.code >= 300) {
        // Trả về default settings nếu không lấy được từ server
        return DEFAULT_CHAT_SETTINGS;
      }

      // Merge với default settings để đảm bảo có đầy đủ properties
      return {
        ...DEFAULT_CHAT_SETTINGS,
        ...response.result,
      };
    } catch (error) {
      console.error('Error fetching chat settings:', error);
      return DEFAULT_CHAT_SETTINGS;
    }
  },

  /**
   * Cập nhật cài đặt chat với validation
   */
  updateChatSettingsWithValidation: async (settings: Partial<ChatSettings>): Promise<ChatSettings> => {
    // Validation
    if (settings.autoReplyMessage && settings.autoReplyMessage.length > 500) {
      throw new Error('Tin nhắn tự động trả lời không được vượt quá 500 ký tự');
    }

    try {
      const response = await settingsApi.updateChatSettings(settings);
      if (response.code < 200 || response.code >= 300) {
        throw new Error(response.message || 'Cập nhật cài đặt thất bại');
      }

      return response.result;
    } catch (error) {
      console.error('Error updating chat settings:', error);
      throw error;
    }
  },
};
