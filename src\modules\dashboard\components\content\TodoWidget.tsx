import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Input, Select, Icon } from '@/shared/components/common';
import { BaseWidgetProps } from '../../types';

interface TodoItem {
  id: string;
  title: string;
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  category?: string;
  createdAt: string;
  completedAt?: string;
}

interface TodoWidgetProps extends BaseWidgetProps {
  initialTodos?: TodoItem[];
  editable?: boolean;
  showCompleted?: boolean;
  showPriority?: boolean;
  showCategories?: boolean;
  maxItems?: number;
}

/**
 * Widget todo list với add/remove/check tasks
 */
const TodoWidget: React.FC<TodoWidgetProps> = ({
  className,
  initialTodos = [
    { id: '1', title: 'Complete project proposal', completed: false, priority: 'high', category: 'Work', createdAt: new Date().toISOString() },
    { id: '2', title: 'Review code changes', completed: false, priority: 'medium', category: 'Work', createdAt: new Date().toISOString() },
    { id: '3', title: 'Buy groceries', completed: true, priority: 'low', category: 'Personal', createdAt: new Date().toISOString(), completedAt: new Date().toISOString() },
  ],
  editable = true,
  showCompleted = true,
  showPriority = true,
  showCategories = true,
  maxItems = 10,
  onPropsChange,
  ...props
}) => {
  const { t } = useTranslation(['dashboard', 'common']);
  
  // Use values from props if available
  const currentTodos = (props.todos as TodoItem[]) || initialTodos;
  const currentSettings = {
    showCompleted: (props.showCompleted as boolean) ?? showCompleted,
    showPriority: (props.showPriority as boolean) ?? showPriority,
    showCategories: (props.showCategories as boolean) ?? showCategories,
    maxItems: (props.maxItems as number) ?? maxItems,
  };

  const [todos, setTodos] = useState<TodoItem[]>(currentTodos);
  const [isEditing, setIsEditing] = useState(false);
  const [tempTodos, setTempTodos] = useState<TodoItem[]>(currentTodos);
  const [tempSettings, setTempSettings] = useState(currentSettings);
  const [newTodoTitle, setNewTodoTitle] = useState('');
  const [newTodoPriority, setNewTodoPriority] = useState<TodoItem['priority']>('medium');
  const [newTodoCategory, setNewTodoCategory] = useState('');

  // Sync with props changes
  useEffect(() => {
    const newTodos = (props.todos as TodoItem[]) || initialTodos;
    if (JSON.stringify(newTodos) !== JSON.stringify(todos)) {
      setTodos(newTodos);
      setTempTodos(newTodos);
    }
  }, [props.todos, initialTodos, todos]);

  const handleEdit = useCallback(() => {
    setTempTodos([...todos]);
    setTempSettings(currentSettings);
    setIsEditing(true);
  }, [todos, currentSettings]);

  const handleSave = useCallback(() => {
    setTodos(tempTodos);
    setIsEditing(false);
    
    // Save to widget props
    if (onPropsChange) {
      onPropsChange({
        todos: tempTodos,
        ...tempSettings,
      });
    }
  }, [tempTodos, tempSettings, onPropsChange]);

  const handleCancel = useCallback(() => {
    setTempTodos([...todos]);
    setTempSettings(currentSettings);
    setNewTodoTitle('');
    setNewTodoPriority('medium');
    setNewTodoCategory('');
    setIsEditing(false);
  }, [todos, currentSettings]);

  const addTodo = useCallback(() => {
    if (!newTodoTitle.trim()) return;
    
    const newTodo: TodoItem = {
      id: Date.now().toString(),
      title: newTodoTitle.trim(),
      completed: false,
      priority: newTodoPriority,
      category: newTodoCategory.trim() || undefined,
      createdAt: new Date().toISOString(),
    };
    
    setTempTodos(prev => [newTodo, ...prev].slice(0, tempSettings.maxItems));
    setNewTodoTitle('');
    setNewTodoCategory('');
  }, [newTodoTitle, newTodoPriority, newTodoCategory, tempSettings.maxItems]);

  const toggleTodo = useCallback((id: string) => {
    const targetTodos = isEditing ? tempTodos : todos;
    const updatedTodos = targetTodos.map(todo => {
      if (todo.id === id) {
        const completed = !todo.completed;
        return {
          ...todo,
          completed,
          completedAt: completed ? new Date().toISOString() : undefined,
        };
      }
      return todo;
    });
    
    if (isEditing) {
      setTempTodos(updatedTodos);
    } else {
      setTodos(updatedTodos);
      // Auto-save when toggling in view mode
      if (onPropsChange) {
        onPropsChange({
          todos: updatedTodos,
          ...currentSettings,
        });
      }
    }
  }, [isEditing, tempTodos, todos, onPropsChange, currentSettings]);

  const removeTodo = useCallback((id: string) => {
    setTempTodos(prev => prev.filter(todo => todo.id !== id));
  }, []);



  const getFilteredTodos = useCallback((todoList: TodoItem[]) => {
    if (currentSettings.showCompleted) {
      return todoList;
    }
    return todoList.filter(todo => !todo.completed);
  }, [currentSettings.showCompleted]);

  const getCompletedCount = useCallback((todoList: TodoItem[]) => {
    return todoList.filter(todo => todo.completed).length;
  }, []);

  const getPriorityColor = useCallback((priority: TodoItem['priority']) => {
    switch (priority) {
      case 'high': return 'text-red-500';
      case 'medium': return 'text-yellow-500';
      case 'low': return 'text-green-500';
      default: return 'text-muted-foreground';
    }
  }, []);

  const getPriorityIcon = useCallback((priority: TodoItem['priority']) => {
    switch (priority) {
      case 'high': return 'alert-circle';
      case 'medium': return 'minus-circle';
      case 'low': return 'check-circle';
      default: return 'circle';
    }
  }, []);

  const priorityOptions = [
    { value: 'low', label: t('dashboard:widgets.todo.priority.low', 'Thấp') },
    { value: 'medium', label: t('dashboard:widgets.todo.priority.medium', 'Trung bình') },
    { value: 'high', label: t('dashboard:widgets.todo.priority.high', 'Cao') },
  ];

  const categories = Array.from(new Set(todos.map(todo => todo.category).filter(Boolean))) as string[];

  if (isEditing) {
    return (
      <div className={`w-full h-full p-4 ${className || ''}`}>
        <div className="h-full flex flex-col">
          <div className="space-y-4 flex-1 overflow-y-auto">
            {/* Settings */}
            <div className="space-y-2">
              <Typography variant="body2">
                {t('dashboard:widgets.todo.settings', 'Cài đặt')}
              </Typography>
              
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.showCompleted}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, showCompleted: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.todo.showCompleted', 'Hiển thị task đã hoàn thành')}
                </Typography>
              </label>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.showPriority}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, showPriority: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.todo.showPriority', 'Hiển thị độ ưu tiên')}
                </Typography>
              </label>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.showCategories}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, showCategories: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.todo.showCategories', 'Hiển thị danh mục')}
                </Typography>
              </label>

              <div>
                <Typography variant="body2" className="mb-1">
                  {t('dashboard:widgets.todo.maxItems', 'Số lượng tối đa')}
                </Typography>
                <Input
                  type="number"
                  value={tempSettings.maxItems}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, maxItems: parseInt(e.target.value) || 10 }))}
                  min="1"
                  max="50"
                  className="w-full"
                />
              </div>
            </div>

            {/* Add Todo */}
            <div className="border border-border rounded-lg p-3">
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.todo.addTodo', 'Thêm task mới')}
              </Typography>
              
              <div className="space-y-2">
                <Input
                  value={newTodoTitle}
                  onChange={(e) => setNewTodoTitle(e.target.value)}
                  placeholder={t('dashboard:widgets.todo.todoTitle', 'Tiêu đề task')}
                  className="w-full"
                  onKeyPress={(e) => e.key === 'Enter' && addTodo()}
                />
                
                <div className="grid grid-cols-2 gap-2">
                  <Select
                    value={newTodoPriority}
                    onChange={(value) => setNewTodoPriority(value as TodoItem['priority'])}
                    options={priorityOptions}
                    className="w-full"
                  />
                  
                  <Input
                    value={newTodoCategory}
                    onChange={(e) => setNewTodoCategory(e.target.value)}
                    placeholder={t('dashboard:widgets.todo.category', 'Danh mục')}
                    className="w-full"
                  />
                </div>
                
                <Button
                  variant="primary"
                  size="sm"
                  onClick={addTodo}
                  disabled={!newTodoTitle.trim()}
                  className="w-full"
                >
                  {t('dashboard:widgets.todo.add', 'Thêm task')}
                </Button>
              </div>
            </div>

            {/* Todos List */}
            <div>
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.todo.todos', 'Danh sách task')} ({tempTodos.length})
              </Typography>
              
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {getFilteredTodos(tempTodos).map((todo) => (
                  <div key={todo.id} className="flex items-start gap-2 p-2 border border-border rounded">
                    <input
                      type="checkbox"
                      checked={todo.completed}
                      onChange={() => toggleTodo(todo.id)}
                      className="mt-1 rounded"
                    />
                    
                    <div className="flex-1 min-w-0">
                      <div className={`font-medium ${todo.completed ? 'line-through text-muted-foreground' : ''}`}>
                        {todo.title}
                      </div>
                      
                      <div className="flex items-center gap-2 mt-1">
                        {tempSettings.showPriority && (
                          <div className={`flex items-center gap-1 ${getPriorityColor(todo.priority)}`}>
                            <Icon name={getPriorityIcon(todo.priority)} size="xs" />
                            <span className="text-xs capitalize">{todo.priority}</span>
                          </div>
                        )}
                        
                        {tempSettings.showCategories && todo.category && (
                          <span className="text-xs bg-muted px-2 py-0.5 rounded">
                            {todo.category}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeTodo(todo.id)}
                      className="text-destructive hover:text-destructive flex-shrink-0"
                    >
                      ×
                    </Button>
                  </div>
                ))}
                
                {getFilteredTodos(tempTodos).length === 0 && (
                  <div className="text-center py-4 text-muted-foreground">
                    {t('dashboard:widgets.todo.noTodos', 'Chưa có task nào')}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              {t('common:cancel')}
            </Button>
            <Button variant="primary" size="sm" onClick={handleSave}>
              {t('common:save')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const filteredTodos = getFilteredTodos(todos);
  const completedCount = getCompletedCount(todos);
  const totalCount = todos.length;

  return (
    <div 
      className={`w-full h-full p-4 relative group ${className || ''}`}
    >
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div>
            <Typography variant="h3" className="font-semibold">
              {t('dashboard:widgets.todo.title', 'Todo List')}
            </Typography>
            <Typography variant="caption" className="text-muted-foreground">
              {completedCount}/{totalCount} {t('dashboard:widgets.todo.completed', 'hoàn thành')}
            </Typography>
          </div>
          
          {totalCount > 0 && (
            <div className="text-right">
              <div className="w-12 h-12 rounded-full border-4 border-muted relative">
                <div
                  className="absolute inset-0 rounded-full border-4 border-primary border-r-transparent transition-all duration-300"
                  style={{
                    transform: `rotate(${(completedCount / totalCount) * 360}deg)`,
                  }}
                />
                <div className="absolute inset-0 flex items-center justify-center text-xs font-bold">
                  {Math.round((completedCount / totalCount) * 100)}%
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Todo List */}
        <div className="flex-1 overflow-y-auto space-y-2">
          {filteredTodos.map((todo) => (
            <div
              key={todo.id}
              className="flex items-start gap-3 p-2 rounded-lg hover:bg-muted/50 transition-colors"
            >
              <input
                type="checkbox"
                checked={todo.completed}
                onChange={() => toggleTodo(todo.id)}
                className="mt-1 rounded"
              />
              
              <div className="flex-1 min-w-0">
                <div className={`font-medium ${todo.completed ? 'line-through text-muted-foreground' : ''}`}>
                  {todo.title}
                </div>
                
                {(currentSettings.showPriority || (currentSettings.showCategories && todo.category)) && (
                  <div className="flex items-center gap-2 mt-1">
                    {currentSettings.showPriority && (
                      <div className={`flex items-center gap-1 ${getPriorityColor(todo.priority)}`}>
                        <Icon name={getPriorityIcon(todo.priority)} size="xs" />
                        <span className="text-xs capitalize">{todo.priority}</span>
                      </div>
                    )}
                    
                    {currentSettings.showCategories && todo.category && (
                      <span className="text-xs bg-muted px-2 py-0.5 rounded">
                        {todo.category}
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
          
          {filteredTodos.length === 0 && (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <Icon name="check-circle" size="lg" className="text-muted-foreground mb-2 mx-auto" />
                <Typography variant="body2" className="text-muted-foreground">
                  {todos.length === 0 
                    ? t('dashboard:widgets.todo.empty', 'Chưa có task nào')
                    : t('dashboard:widgets.todo.allCompleted', 'Tất cả task đã hoàn thành!')
                  }
                </Typography>
              </div>
            </div>
          )}
        </div>

        {/* Categories */}
        {currentSettings.showCategories && categories.length > 0 && (
          <div className="mt-4 pt-2 border-t border-border">
            <Typography variant="caption" className="text-muted-foreground mb-2">
              {t('dashboard:widgets.todo.categories', 'Danh mục')}
            </Typography>
            <div className="flex flex-wrap gap-1">
              {categories.map((category) => {
                const categoryCount = todos.filter(todo => todo.category === category && !todo.completed).length;
                return (
                  <span key={category} className="text-xs bg-muted px-2 py-1 rounded">
                    {category} ({categoryCount})
                  </span>
                );
              })}
            </div>
          </div>
        )}

        {/* Edit Button */}
        {editable && (
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button variant="secondary" size="sm" onClick={handleEdit}>
              {t('common:edit')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default TodoWidget;
