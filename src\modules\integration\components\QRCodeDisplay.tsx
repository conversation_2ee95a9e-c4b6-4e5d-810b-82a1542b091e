import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button, Icon } from '@/shared/components/common';

interface QRCodeDisplayProps {
  /**
   * URL QR Code
   */
  qrCodeUrl: string;

  /**
   * Tiêu đề hiển thị
   */
  title?: string;

  /**
   * <PERSON>ô tả hiển thị
   */
  description?: string;

  /**
   * Thông tin bổ sung
   */
  additionalInfo?: string;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Callback khi nhấn nút làm mới
   */
  onRefresh?: () => void;

  /**
   * Trạng thái loading
   */
  isLoading?: boolean;
}

/**
 * Component hiển thị QR Code cho tích hợp
 */
const QRCodeDisplay: React.FC<QRCodeDisplayProps> = ({
  qrCodeUrl,
  title,
  description,
  additionalInfo,
  className = '',
  onRefresh,
  isLoading = false,
}) => {
  const { t } = useTranslation(['integration']);

  return (
    <Card
      className={`${className}`}
      variant="bordered"
    >
      <div className="flex flex-col items-center space-y-4">
        {/* Tiêu đề */}
        {title && (
          <Typography variant="h6" className="text-center font-medium">
            {title}
          </Typography>
        )}

        {/* Mô tả */}
        {description && (
          <Typography variant="body2" className="text-center text-muted-foreground">
            {description}
          </Typography>
        )}

        {/* QR Code */}
        <div className="flex flex-col items-center">
          <div className="border border-border p-4 rounded-lg bg-white shadow-sm">
            {isLoading ? (
              <div className="w-48 h-48 flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : qrCodeUrl ? (
              <img
                src={qrCodeUrl}
                alt="QR Code"
                className="w-48 h-48 object-contain"
                onError={(e) => {
                  console.error('QR Code image failed to load:', e);
                }}
              />
            ) : (
              <div className="w-48 h-48 flex items-center justify-center bg-gray-50 dark:bg-gray-800">
                <Typography variant="body2" className="text-center text-gray-600 dark:text-gray-300">
                  Chưa có QR Code
                </Typography>
              </div>
            )}
          </div>
        </div>

        {/* Thông tin bổ sung */}
        {additionalInfo && (
          <Typography variant="body2" className="text-center text-muted-foreground">
            {additionalInfo}
          </Typography>
        )}

        {/* Nút làm mới */}
        {onRefresh && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Icon name="refresh-cw" size="sm" />
            {t('integration:common.refresh', 'Làm mới')}
          </Button>
        )}
      </div>
    </Card>
  );
};

export default QRCodeDisplay;
