import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Input, Icon } from '@/shared/components/common';
import { BaseWidgetProps } from '../../types';

interface IframeWidgetProps extends BaseWidgetProps {
  initialUrl?: string;
  editable?: boolean;
  allowFullscreen?: boolean;
  sandbox?: string;
  title?: string;
}

/**
 * Widget hiển thị iframe
 */
const IframeWidget: React.FC<IframeWidgetProps> = ({
  className,
  initialUrl = '',
  editable = true,
  allowFullscreen = true,
  sandbox = 'allow-scripts allow-same-origin allow-forms allow-popups',
  title = 'Iframe Widget',
}) => {
  const { t } = useTranslation(['dashboard', 'common']);
  const [url, setUrl] = useState(initialUrl);
  const [isEditing, setIsEditing] = useState(false);
  const [tempUrl, setTempUrl] = useState(url);
  const [tempTitle, setTempTitle] = useState(title);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleEdit = useCallback(() => {
    setTempUrl(url);
    setTempTitle(title);
    setIsEditing(true);
    setError(null);
  }, [url, title]);

  const handleSave = useCallback(() => {
    try {
      if (tempUrl.trim() === '') {
        setError(t('dashboard:widgets.iframe.emptyError', 'URL không được để trống'));
        return;
      }

      // Basic URL validation
      try {
        new URL(tempUrl);
      } catch {
        setError(t('dashboard:widgets.iframe.invalidUrl', 'URL không hợp lệ'));
        return;
      }

      setUrl(tempUrl);
      setError(null);
      setIsEditing(false);
      setIsLoading(true);
    } catch (err) {
      setError(t('dashboard:widgets.iframe.saveError', 'Có lỗi xảy ra khi lưu'));
    }
  }, [tempUrl, t]);

  const handleCancel = useCallback(() => {
    setTempUrl(url);
    setTempTitle(title);
    setError(null);
    setIsEditing(false);
  }, [url, title]);

  const handleIframeLoad = useCallback(() => {
    setIsLoading(false);
    setError(null);
  }, []);

  const handleIframeError = useCallback(() => {
    setIsLoading(false);
    setError(t('dashboard:widgets.iframe.loadError', 'Không thể tải nội dung'));
  }, [t]);

  const commonUrls = [
    { label: 'Google Maps', url: 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.4609!2d106.6956!3d10.7769!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMTDCsDQ2JzM3LjAiTiAxMDbCsDQxJzQ0LjIiRQ!5e0!3m2!1sen!2s!4v1234567890' },
    { label: 'YouTube Video', url: 'https://www.youtube.com/embed/dQw4w9WgXcQ' },
    { label: 'Google Calendar', url: 'https://calendar.google.com/calendar/embed?src=primary' },
    { label: 'Google Docs', url: 'https://docs.google.com/document/d/YOUR_DOC_ID/edit?usp=sharing' },
  ];

  if (isEditing) {
    return (
      <div className={`w-full h-full p-4 ${className || ''}`}>
        <div className="h-full flex flex-col">
          <div className="space-y-4 flex-1">
            <div>
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.iframe.urlLabel', 'URL')}
              </Typography>
              <Input
                value={tempUrl}
                onChange={(e) => setTempUrl(e.target.value)}
                placeholder={t('dashboard:widgets.iframe.urlPlaceholder', 'https://example.com')}
                className="w-full"
              />
              {error && (
                <Typography variant="caption" className="text-destructive mt-1">
                  {error}
                </Typography>
              )}
            </div>

            <div>
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.iframe.titleLabel', 'Tiêu đề (tùy chọn)')}
              </Typography>
              <Input
                value={tempTitle}
                onChange={(e) => setTempTitle(e.target.value)}
                placeholder={t('dashboard:widgets.iframe.titlePlaceholder', 'Mô tả nội dung iframe')}
                className="w-full"
              />
            </div>

            <div>
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.iframe.commonUrls', 'URL phổ biến')}
              </Typography>
              <div className="grid grid-cols-1 gap-2">
                {commonUrls.map((item, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="sm"
                    onClick={() => setTempUrl(item.url)}
                    className="justify-start text-left"
                  >
                    {item.label}
                  </Button>
                ))}
              </div>
            </div>

            {tempUrl && (
              <div className="flex-1 min-h-0">
                <Typography variant="body2" className="mb-2">
                  {t('dashboard:widgets.iframe.preview', 'Xem trước')}
                </Typography>
                <div className="w-full h-full border border-border rounded-md overflow-hidden">
                  <iframe
                    src={tempUrl}
                    title="Preview"
                    className="w-full h-full"
                    sandbox={sandbox}
                    allowFullScreen={allowFullscreen}
                  />
                </div>
              </div>
            )}
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              {t('common:cancel')}
            </Button>
            <Button variant="primary" size="sm" onClick={handleSave}>
              {t('common:save')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!url) {
    return (
      <div 
        className={`w-full h-full p-4 ${className || ''} ${editable ? 'cursor-pointer hover:bg-muted/50 transition-colors' : ''}`}
        onClick={editable ? handleEdit : undefined}
      >
        <div className="h-full flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/30 rounded-lg">
          <Icon name="external-link" size="lg" className="text-muted-foreground mb-2" />
          <Typography variant="body2" className="text-muted-foreground text-center">
            {editable 
              ? t('dashboard:widgets.iframe.empty', 'Click để thêm URL')
              : t('dashboard:widgets.iframe.noUrl', 'Không có URL')
            }
          </Typography>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div 
        className={`w-full h-full p-4 ${className || ''} ${editable ? 'cursor-pointer hover:bg-muted/50 transition-colors' : ''}`}
        onClick={editable ? handleEdit : undefined}
      >
        <div className="h-full flex flex-col items-center justify-center border border-destructive/30 rounded-lg bg-destructive/5">
          <Icon name="alert-circle" size="lg" className="text-destructive mb-2" />
          <Typography variant="body2" className="text-destructive text-center mb-2">
            {error}
          </Typography>
          {editable && (
            <Button variant="ghost" size="sm" onClick={handleEdit}>
              {t('common:edit')}
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`w-full h-full relative group ${className || ''}`}
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-2"></div>
            <Typography variant="body2" className="text-muted-foreground">
              {t('dashboard:widgets.iframe.loading', 'Đang tải...')}
            </Typography>
          </div>
        </div>
      )}
      
      <iframe
        src={url}
        title={tempTitle}
        className="w-full h-full border-0"
        sandbox={sandbox}
        allowFullScreen={allowFullscreen}
        onLoad={handleIframeLoad}
        onError={handleIframeError}
      />
      
      {editable && (
        <div className="absolute inset-0 bg-transparent opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="absolute top-2 right-2">
            <Button variant="secondary" size="sm" onClick={handleEdit}>
              {t('common:edit')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default IframeWidget;
