/**
 * Dashboard Integration Test
 * File test để validate dashboard API integration
 */

import { DashboardManagementService } from './services/dashboard-management.service';
import {
  CreateDashboardPageDto,
  QueryDashboardPageDto,
  DashboardPageType,
} from './types/dashboard-api.types';

// ==================== TEST FUNCTIONS ====================

/**
 * Test tạo dashboard page
 */
export const testCreateDashboard = async () => {
  try {
    console.log('🧪 Testing dashboard creation...');

    const createData: CreateDashboardPageDto = {
      name: 'Test Dashboard',
      slug: `test-dashboard-${Date.now()}`,
      description: 'Dashboard test từ frontend',
      tabsConfig: {
        currentTabId: 'tab-1',
        tabs: [
          {
            id: 'tab-1',
            name: 'Tab Test',
            widgets: [],
            mode: 'edit',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ],
      },
    };

    const result = await DashboardManagementService.createDashboardPageWithValidation(createData);
    console.log('✅ Dashboard created successfully:', result);
    return result;
  } catch (error) {
    console.error('❌ Dashboard creation failed:', error);
    throw error;
  }
};

/**
 * Test lấy danh sách dashboard pages
 */
export const testGetDashboards = async () => {
  try {
    console.log('🧪 Testing dashboard list retrieval...');

    const queryParams: QueryDashboardPageDto = {
      page: 1,
      limit: 10,
      isActive: true,
    };

    const result = await DashboardManagementService.getDashboardPagesWithBusinessLogic(queryParams);
    console.log('✅ Dashboard list retrieved successfully:', result);
    return result;
  } catch (error) {
    console.error('❌ Dashboard list retrieval failed:', error);
    throw error;
  }
};

/**
 * Test cập nhật dashboard
 */
export const testUpdateDashboard = async (dashboardId: string) => {
  try {
    console.log('🧪 Testing dashboard update...');

    const updateData = {
      name: 'Updated Test Dashboard',
      description: 'Dashboard đã được cập nhật',
      tabsConfig: {
        currentTabId: 'tab-1',
        tabs: [
          {
            id: 'tab-1',
            name: 'Updated Tab',
            widgets: [
              {
                id: 'widget-1',
                title: 'Test Widget',
                type: 'USER_STATISTICS' as any,
                x: 0,
                y: 0,
                w: 4,
                h: 3,
                props: {},
              },
            ],
            mode: 'view' as const,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ],
      },
    } as any;

    const result = await DashboardManagementService.updateDashboardPageWithValidation(
      dashboardId,
      updateData
    );
    console.log('✅ Dashboard updated successfully:', result);
    return result;
  } catch (error) {
    console.error('❌ Dashboard update failed:', error);
    throw error;
  }
};

/**
 * Test localStorage operations
 */
export const testLocalStorageOperations = () => {
  try {
    console.log('🧪 Testing localStorage operations...');

    const testData = {
      currentTabId: 'tab-test',
      tabs: [
        {
          id: 'tab-test',
          name: 'Test Tab',
          widgets: [],
          mode: 'edit' as const,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ],
    };

    // Test save
    DashboardManagementService.saveToLocalStorage(testData);
    console.log('✅ Data saved to localStorage');

    // Test load
    const loadedData = DashboardManagementService.loadFromLocalStorage();
    console.log('✅ Data loaded from localStorage:', loadedData);

    // Test backup
    DashboardManagementService.createBackup(testData);
    console.log('✅ Backup created');

    // Test get backups
    const backups = DashboardManagementService.getBackups();
    console.log('✅ Backups retrieved:', backups);

    return true;
  } catch (error) {
    console.error('❌ localStorage operations failed:', error);
    throw error;
  }
};

/**
 * Test data transformation
 */
export const testDataTransformation = () => {
  try {
    console.log('🧪 Testing data transformation...');

    const tabsState = {
      currentTabId: 'tab-1',
      tabs: [
        {
          id: 'tab-1',
          name: 'Test Tab',
          widgets: [],
          mode: 'edit' as const,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ],
    };

    // Test transform to backend
    const backendFormat = DashboardManagementService.transformTabsStateToBackend(tabsState);
    console.log('✅ Transformed to backend format:', backendFormat);

    // Test transform from backend
    const mockBackendData = {
      id: 'test-id',
      name: 'Test Dashboard',
      slug: 'test-dashboard',
      sortOrder: 0,
      isActive: true,
      isDefault: false,
      pageType: DashboardPageType.USER_CUSTOM,
      ownerType: 'USER' as const,
      accessLevel: 'PRIVATE' as const,
      tabsConfig: backendFormat,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    const frontendFormat = DashboardManagementService.transformBackendToTabsState(mockBackendData);
    console.log('✅ Transformed to frontend format:', frontendFormat);

    return true;
  } catch (error) {
    console.error('❌ Data transformation failed:', error);
    throw error;
  }
};

/**
 * Chạy tất cả tests
 */
export const runAllTests = async () => {
  console.log('🚀 Starting Dashboard Integration Tests...');

  try {
    // Test localStorage operations
    testLocalStorageOperations();

    // Test data transformation
    testDataTransformation();

    // Test API operations (chỉ chạy nếu có backend connection)
    try {
      await testGetDashboards();

      const createdDashboard = await testCreateDashboard();
      if (createdDashboard?.id) {
        await testUpdateDashboard(createdDashboard.id);
      }
    } catch (apiError) {
      console.warn('⚠️ API tests skipped (backend not available):', apiError);
    }

    console.log('🎉 All tests completed successfully!');
  } catch (error) {
    console.error('💥 Test suite failed:', error);
    throw error;
  }
};

// ==================== VALIDATION HELPERS ====================

/**
 * Validate dashboard data structure
 */
export const validateDashboardData = (data: any): boolean => {
  if (!data || typeof data !== 'object') {
    console.error('❌ Invalid data: not an object');
    return false;
  }

  if (!data.currentTabId || typeof data.currentTabId !== 'string') {
    console.error('❌ Invalid data: missing or invalid currentTabId');
    return false;
  }

  if (!Array.isArray(data.tabs)) {
    console.error('❌ Invalid data: tabs is not an array');
    return false;
  }

  for (const tab of data.tabs) {
    if (!tab.id || !tab.name || !Array.isArray(tab.widgets)) {
      console.error('❌ Invalid tab data:', tab);
      return false;
    }
  }

  console.log('✅ Dashboard data structure is valid');
  return true;
};

/**
 * Export test functions để có thể gọi từ console
 */
if (typeof window !== 'undefined') {
  (window as any).dashboardTests = {
    runAllTests,
    testCreateDashboard,
    testGetDashboards,
    testUpdateDashboard,
    testLocalStorageOperations,
    testDataTransformation,
    validateDashboardData,
  };
}
