/**
 * Marketing Module
 *
 * Module quản lý marketing, bao gồm:
 * - Quản lý Audience
 * - Quản lý Segment
 * - Quản lý Chiến dịch
 */

// Export components
export * from './components';

// Export pages
export * from './pages';

// Export hooks and types are exported specifically below

// Export schemas
export * from './schemas';

// Export services (explicitly to avoid conflicts)
export { AudienceService } from './services/audience.service';
export { SegmentService } from './services/segment.service';
export { CampaignService } from './services/campaign.service';
export { MarketingStatisticsService } from './services/statistics.service';
export { TagService } from './services/tag.service';
// Note: Không export CustomFieldService để tránh xung đột với types
// Các service khác đã được export riêng ở dưới

// Export routes
export { default as marketingRoutes } from './marketingRoutes';

// Export pages
export { MarketingDashboardPage } from './pages/MarketingDashboardPage';

export { ZaloAccountsPage } from './pages/zalo/ZaloAccountsPage';
export { default as ZaloPersonalAccountsPage } from './pages/zalo/ZaloPersonalAccountsPage';
export { ZaloFollowersPage } from './pages/zalo/ZaloFollowersPage';
export { ZaloZnsPage } from './pages/zalo/ZaloZnsPage';
export { EmailTemplatesPage } from './pages/email/EmailTemplatesPage';
export { EmailCampaignsPage } from './pages/email/EmailCampaignsPage';

// ZNS Campaign pages
export { default as ZNSCampaignListPage } from './pages/zalo/ZNSCampaignListPage';
export { default as CreateZNSCampaignPage } from './pages/zalo/CreateZNSCampaignPage';

// Export components
export { ConnectZaloAccountForm } from './components/zalo/ConnectZaloAccountForm';
export { CreateZnsTemplateForm } from './components/zalo/CreateZnsTemplateForm';
export { default as CreateZNSCampaignForm } from './components/zalo/CreateZNSCampaignForm';
export { default as CreateZNSCampaignSlideForm } from './components/zalo/CreateZNSCampaignSlideForm';
export { CreateEmailTemplateForm } from './components/email/CreateEmailTemplateForm';
export { CreateEmailCampaignForm } from './components/email/CreateEmailCampaignForm';
export { EmailCampaignStatsGrid } from './components/email/EmailCampaignStatsGrid';
export { EmailTemplateSelector } from './components/email/EmailTemplateSelector';
export { TemplateVariableManager } from './components/email/TemplateVariableManager';
export { ExcelVariableImporter } from './components/email/ExcelVariableImporter';
export { MarketingViewHeader } from './components/common/MarketingViewHeader';
export { default as MarketingCustomFieldForm } from './components/forms/MarketingCustomFieldForm';

// Export hooks
export { useZaloAccounts, useZaloAccount, useZaloAccountManagement, useZaloOfficialAccounts } from './hooks/zalo/useZaloAccounts';
export { useZaloFollowers, useZaloFollowerManagement } from './hooks/zalo/useZaloFollowers';
export * from './hooks/zalo/useZaloPersonalIntegrations';
export * from './hooks/useZNSCampaigns';
export * from './hooks/useZNSTemplates';
export {
  useZNSTemplates as useZNSTemplatesQuery,
  useZNSTemplate as useZNSTemplateQuery,
  useCreateZNSTemplate,
  useUpdateZNSTemplate,
  useDeleteZNSTemplate,
  useBulkDeleteZNSTemplates
} from './hooks/useZNSTemplatesQuery';
export * from './hooks/useZaloOAAccounts';
export { useEmailTemplates, useEmailTemplateManagement, useCreateEmailTemplate } from './hooks/email/useEmailTemplates';
export { useEmailCampaigns, useEmailCampaignManagement, useCreateEmailCampaign, useRecentCampaigns, useEmailCampaignOverview, useSyncCampaignStatus } from './hooks/email/useEmailCampaigns';

// Article hooks
export * from './hooks/useArticles';

// Video hooks
export * from './hooks/useVideos';

// Facebook Ads hooks
export * from './hooks/facebook-ads/useFacebookAdsAccounts';
export * from './hooks/facebook-ads/useFacebookAdsCampaigns';
export * from './hooks/facebook-ads/useFacebookAuth';
export {
  useTemplateEmails,
  useTemplateEmail,
  useCreateTemplateEmail,
  useUpdateTemplateEmail,
  useDeleteTemplateEmail,
  useTemplateEmailStatistics,
  useTemplateEmailManagement,
  TEMPLATE_EMAIL_QUERY_KEYS
} from './hooks/useTemplateEmailQuery';
export {
  useEmailTemplatesAdapter,
  useEmailTemplateAdapter,
  useCreateEmailTemplateAdapter,
  useUpdateEmailTemplateAdapter,
  useDeleteEmailTemplateAdapter,
  useEmailTemplateStatisticsAdapter,
  useEmailTemplateOverviewAdapter,
  useEmailTemplateManagementAdapter,
  EMAIL_TEMPLATE_ADAPTER_QUERY_KEYS
} from './hooks/email/useEmailTemplatesAdapter';
export {
  useMarketingOverview,
  useRecentTemplates,
  MARKETING_OVERVIEW_QUERY_KEYS
} from './hooks/useMarketingOverview';
export { ZALO_PERSONAL_QUERY_KEYS } from './constants/zaloPersonalQueryKeys';
export { GMAIL_QUERY_KEYS } from './constants/gmail.constants';
export {
  useMarketingCustomFields,
  useMarketingCustomField,
  useCreateMarketingCustomField,
  useUpdateMarketingCustomField,
  useDeleteMarketingCustomField,
  useDeleteMultipleMarketingCustomFields,
  MARKETING_CUSTOM_FIELD_QUERY_KEYS
} from './hooks/useMarketingCustomFieldQuery';

// Tag hooks
export {
  useTags,
  useTag,
  useCreateTag,
  useUpdateTag,
  useDeleteTag,
  useDeleteMultipleTags,
  useAssignTagsToAudiences,
  TAG_QUERY_KEYS
} from './hooks/useTag';

// Export services
export { ZaloService } from './services/zalo.service';
export * from './services/zns-campaign.service';
export { EmailService } from './services/email.service';
export { EmailCampaignOverviewBusinessService } from './services/email-campaign-overview-business.service';
export { GmailService } from './services/gmail.service';
export { TemplateEmailService } from './services/template-email.service';
export { TemplateEmailBusinessService } from './services/template-email-business.service';
export { EmailTemplateAdapterService } from './services/email-template-adapter.service';
export { MarketingOverviewService } from './services/marketing-overview.service';
export { MarketingOverviewBusinessService } from './services/marketing-overview-business.service';
export { MarketingCustomFieldService, MarketingCustomFieldBusinessService } from './services/marketing-custom-field.service';

// Facebook Ads services
export * from './services/facebook-ads.service';
export * from './services/facebook-auth.service';
export * from './services/facebook-sdk.service';

// Export types
export type * from './types/zalo.types';
export type * from './types/zaloPersonal';
export type * from './types/email.types';
export type * from './types/template-email.types';
export type * from './types/statistics.types';
export type * from './types/facebook-ads.types';
export type * from './types/custom-field.types';
export type * from './types/article.types';
export type * from './types/video.types';
export type * from './types/tag.types';
export type * from './types/gmail.types';

// Export schemas
export * from './schemas/zalo.schema';
export * from './schemas/email.schema';
export * from './schemas/zns-campaign.schema';

// Facebook Ads pages
export { default as FacebookAdsOverviewPage } from './pages/facebook-ads/FacebookAdsOverviewPage';
export { default as FacebookAccountsPage } from './pages/facebook-ads/FacebookAccountsPage';
export { default as FacebookCampaignsPage } from './pages/facebook-ads/FacebookCampaignsPage';
export { default as FacebookAuthCallbackPage } from './pages/facebook-ads/FacebookAuthCallbackPage';
export { default as CreateCampaignPage } from './pages/facebook-ads/CreateCampaignPage';
export { default as FacebookAnalyticsPage } from './pages/facebook-ads/FacebookAnalyticsPage';

// Facebook Ads components
export { default as FacebookAuthButton } from './components/facebook-ads/FacebookAuthButton';
export { default as FacebookAuthCallback } from './components/facebook-ads/FacebookAuthCallback';
export { default as FacebookAccountCard } from './components/facebook-ads/FacebookAccountCard';
export { default as FacebookAccountManager } from './components/facebook-ads/FacebookAccountManager';
export { default as FacebookCampaignCard } from './components/facebook-ads/FacebookCampaignCard';
export { default as FacebookCampaignManager } from './components/facebook-ads/FacebookCampaignManager';
export { default as FacebookOverviewCards } from './components/facebook-ads/FacebookOverviewCards';
export { default as FacebookQuickStats } from './components/facebook-ads/FacebookQuickStats';
export { default as FacebookPerformanceDashboard } from './components/facebook-ads/FacebookPerformanceDashboard';
export { default as FacebookCampaignMonitor } from './components/facebook-ads/FacebookCampaignMonitor';
export { default as FacebookBudgetManager } from './components/facebook-ads/FacebookBudgetManager';
export { default as FacebookAdvancedFilters } from './components/facebook-ads/FacebookAdvancedFilters';
export { default as FacebookNotificationCenter } from './components/facebook-ads/FacebookNotificationCenter';
export { default as CreateCampaignForm } from './components/facebook-ads/CreateCampaignForm';
export { default as FacebookAnalyticsDashboard } from './components/facebook-ads/FacebookAnalyticsDashboard';

// Gmail components
export { default as GmailPage } from './components/gmail/GmailPage';

// TikTok Ads
export * from './hooks/tiktok-ads';
export * from './services/tiktok-ads.service';
export { default as TikTokAdsOverviewPage } from './pages/tiktok-ads/TikTokAdsOverviewPage';
export { default as TikTokAdsAccountsPage } from './pages/tiktok-ads/TikTokAdsAccountsPage';
export { default as TikTokAdsCampaignsPage } from './pages/tiktok-ads/TikTokAdsCampaignsPage';
export { default as TikTokAdsCreativesPage } from './pages/tiktok-ads/TikTokAdsCreativesPage';
export { default as TikTokAdsAudiencesPage } from './pages/tiktok-ads/TikTokAdsAudiencesPage';
export { default as TikTokAdsReportsPage } from './pages/tiktok-ads/TikTokAdsReportsPage';
export { default as TikTokAdsSettingsPage } from './pages/tiktok-ads/TikTokAdsSettingsPage';

// SMS Marketing
export * from './sms';
