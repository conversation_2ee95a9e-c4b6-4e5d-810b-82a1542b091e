/**
 * Zalo Chat Types
 * Định nghĩa các types và interfaces cho module chat Zalo
 */

/**
 * Loại tin nhắn
 */
export type MessageType = 'text' | 'image' | 'file' | 'sticker' | 'location';

/**
 * Trạng thái tin nhắn
 */
export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read' | 'failed';

/**
 * Loại người gửi
 */
export type SenderType = 'user' | 'contact';

/**
 * Tài khoản Zalo
 */
export interface ZaloAccount {
  id: string;
  name: string;
  avatar?: string;
  type: 'oa' | 'personal';
  isActive: boolean;
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Liên hệ
 */
export interface Contact {
  id: string;
  name: string;
  avatar?: string;
  phone?: string;
  zaloId: string;
  lastMessage?: Message;
  lastMessageTime?: string;
  unreadCount: number;
  isOnline: boolean;
  tags: string[];
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Tin nhắn
 */
export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderType: SenderType;
  type: MessageType;
  content: string;
  attachments?: MessageAttachment[];
  replyTo?: string; // ID của tin nhắn được reply
  status: MessageStatus;
  timestamp: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * File đính kèm tin nhắn
 */
export interface MessageAttachment {
  id: string;
  type: 'image' | 'file' | 'video' | 'audio';
  url: string;
  name: string;
  size: number;
  mimeType: string;
  thumbnail?: string;
}

/**
 * Cuộc trò chuyện
 */
export interface Conversation {
  id: string;
  contactId: string;
  zaloAccountId: string;
  lastMessage?: Message;
  lastMessageTime?: string;
  unreadCount: number;
  isArchived: boolean;
  isPinned: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Trạng thái chat
 */
export interface ChatState {
  selectedZaloAccount?: ZaloAccount;
  selectedContact?: Contact;
  selectedConversation?: Conversation;
  contacts: Contact[];
  messages: Message[];
  isLoading: boolean;
  isLoadingMessages: boolean;
  isSending: boolean;
  searchQuery: string;
  filteredContacts: Contact[];
}

/**
 * Tham số tìm kiếm liên hệ
 */
export interface ContactSearchParams {
  zaloAccountId?: string;
  query?: string;
  tags?: string[];
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'lastMessageTime' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Tham số tìm kiếm tin nhắn
 */
export interface MessageSearchParams {
  conversationId: string;
  page?: number;
  limit?: number;
  before?: string; // timestamp
  after?: string; // timestamp
  type?: MessageType;
}

/**
 * Request gửi tin nhắn
 */
export interface SendMessageRequest {
  conversationId: string;
  type: MessageType;
  content: string;
  attachments?: File[];
  replyTo?: string;
}

/**
 * Response API
 */
export interface ZaloChatApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

/**
 * Response danh sách có phân trang
 */
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * DTO cho tạo/cập nhật liên hệ
 */
export interface ContactDto {
  name: string;
  phone?: string;
  zaloId: string;
  tags?: string[];
  notes?: string;
}

/**
 * DTO cho cập nhật tài khoản Zalo
 */
export interface ZaloAccountDto {
  name: string;
  type: 'oa' | 'personal';
  accessToken?: string;
  refreshToken?: string;
}

/**
 * Event types cho real-time updates
 */
export type ChatEventType = 
  | 'message_received'
  | 'message_sent'
  | 'message_read'
  | 'contact_online'
  | 'contact_offline'
  | 'typing_start'
  | 'typing_stop';

/**
 * Chat event data
 */
export interface ChatEvent {
  type: ChatEventType;
  data: any;
  timestamp: string;
}

/**
 * Typing indicator
 */
export interface TypingIndicator {
  conversationId: string;
  contactId: string;
  isTyping: boolean;
}

/**
 * Chat settings
 */
export interface ChatSettings {
  autoReply: boolean;
  autoReplyMessage?: string;
  notificationEnabled: boolean;
  soundEnabled: boolean;
  showOnlineStatus: boolean;
}
