{"business": {"title": "企业管理", "description": "管理企业活动", "common": {"moduleTitle": "商务", "createAt": "创建时间", "selected": "已选择", "continue": "继续", "status": {"active": "活跃", "inactive": "非活跃", "pending": "待处理", "processing": "处理中", "completed": "已完成", "cancelled": "已取消", "blocked": "已屏蔽", "draft": "草稿"}, "orderStatus": {"pending": "待处理", "processing": "处理中", "confirmed": "已确认", "shipped": "已发货", "delivered": "已送达", "cancelled": "已取消", "completed": "已完成"}, "paymentStatus": {"paid": "已付款", "pending": "待付款", "unpaid": "未付款", "failed": "付款失败", "partiallyPaid": "部分付款", "refunded": "已退款"}, "shippingStatus": {"title": "配送状态", "pending": "待处理", "preparing": "准备中", "shipped": "已发货", "inTransit": "运输中", "sorting": "分拣中", "delivered": "已送达", "deliveryFailed": "配送失败", "returning": "退货中", "cancelled": "已取消"}, "actions": {"save": "保存", "cancel": "取消", "delete": "删除", "edit": "编辑", "create": "创建", "back": "返回", "next": "下一步", "submit": "提交", "search": "搜索", "filter": "筛选", "sort": "排序", "add": "添加", "remove": "移除", "upload": "上传", "download": "下载", "view": "查看", "details": "详情", "actions": "操作"}, "form": {"name": "姓名", "email": "邮箱", "phone": "电话号码", "address": "地址", "description": "描述", "status": "状态", "tags": "标签", "notes": "备注"}, "messages": {"createSuccess": "创建成功", "createError": "创建时出错", "updateSuccess": "更新成功", "updateError": "更新时出错", "deleteSuccess": "删除成功", "deleteError": "删除时出错", "bulkDeleteSuccess": "成功删除{{count}}项", "bulkDeleteError": "删除时出错", "loadError": "加载数据时出错"}}, "customer": {"title": "客户", "description": "管理客户信息", "add": "添加客户", "edit": "编辑客户", "view": "查看客户详情", "addForm": "添加新客户", "editForm": "编辑客户信息", "detailForm": "客户详情", "totalCustomers": "客户总数", "manage": "管理客户", "platform": "平台", "timezone": "时区", "name": "客户姓名", "totalOrders": "总订单数", "totalSpent": "总消费", "potentialScore": "潜力评分", "form": {"name": "姓名", "avatar": "头像", "namePlaceholder": "请输入客户姓名", "email": "邮箱", "emailPlaceholder": "请输入邮箱地址", "phone": "电话号码", "phonePlaceholder": "请输入电话号码", "tags": "客户标签", "tagsPlaceholder": "输入标签并按回车", "address": "地址", "addressPlaceholder": "请输入地址", "agent": "支持代理"}, "status": {"active": "活跃", "inactive": "非活跃", "blocked": "已屏蔽"}, "detail": {"generalInfo": "基本信息", "social": "社交", "customFields": "自定义字段", "orders": "订单", "activities": "活动", "overview": "概览", "totalOrders": "总订单数", "totalSpent": "总消费", "averageOrderValue": "平均订单价值", "lastOrderDate": "最后订单", "customerSince": "客户起始时间", "interactionChannels": "互动渠道", "orderHistory": "订单历史", "activityLog": "活动日志", "socialProfiles": "社交档案", "customFieldValues": "自定义字段值", "noData": "暂无数据", "noOrders": "暂无订单", "noActivities": "暂无活动", "noInteractions": "暂无互动", "revenue": "收入", "interactions": "互动次数", "topChannels": "热门消息渠道", "topDevices": "热门客户设备", "interactedFlows": "已互动流程", "interactedCampaigns": "已互动活动", "orderList": "订单历史", "orderCode": "订单号", "orderDate": "订单日期", "paymentMethod": "支付方式", "deliveryStatus": "配送状态", "shippingStatus": "配送状态", "paymentStatus": "支付状态", "orderStatus": "订单状态", "source": "来源", "totalAmount": "总金额", "flowName": "流程名称", "lastInteraction": "最后互动", "campaignName": "活动名称", "interactionType": "互动类型", "sent": "已发送", "opened": "已打开", "clicked": "已点击", "noOrdersDesc": "该客户尚未下过任何订单", "noActivitiesDesc": "该客户尚无记录的活动", "allActivities": "所有活动"}, "activity": {"type": "活动类型", "date": "日期", "details": "详情", "moreDetails": "更多详情", "types": {"order": "订单", "login": "登录", "support": "支持", "review": "评价"}}, "overview": {"flowCount": "流程数", "campaignCount": "活动数", "sequenceCount": "序列数", "interactions": "互动次数", "revenue": "收入"}, "social": {"platform": "平台", "username": "用户名", "link": "链接", "editDescription": "输入 {{platform}} 上的用户名或链接", "save": "保存"}, "messages": {"createSuccess": "客户创建成功", "createError": "创建客户时出错", "updateSuccess": "客户更新成功", "updateError": "更新客户时出错", "deleteSuccess": "客户删除成功", "deleteError": "删除客户时出错", "bulkDeleteSuccess": "成功删除{{count}}个客户", "bulkDeleteError": "删除客户时出错"}, "errors": {"phoneExists": "电话号码已存在", "createFailed": "创建客户失败"}, "bulkDeleteConfirmation": "您确定要删除{{count}}个选中的客户吗？", "merge": {"title": "合并客户", "action": "合并", "success": "客户合并成功", "error": "合并客户时出错", "originalCustomer": "原始客户", "recommendedCustomer": "推荐客户", "similarity": "相似度", "high": "高", "medium": "中", "low": "低", "recommendations": {"title": "客户合并建议", "count": "找到{{count}}个客户合并建议", "noData": "没有客户合并建议"}, "search": {"placeholder": "搜索客户..."}, "manualSelection": "手动选择客户", "manualSubtitle": "手动选择并合并客户", "recommendationSubtitle": "从系统建议合并客户", "aiRecommendation": "AI推荐", "highSimilarity": "高相似度", "customer1": "第一个客户", "customer2": "第二个客户", "searchCustomer1": "搜索第一个客户...", "searchCustomer2": "搜索第二个客户...", "helpTitle": "指南", "helpText": "选择您要合并的两个客户。系统将显示比较表单，供您选择要保留的信息。", "step1": "选择客户", "step2": "合并数据", "readyToMerge": "准备合并客户", "mergeWarning": "此操作无法撤销", "confirmMerge": "确认客户合并", "validation": {"selectSourceCustomer": "请选择源客户", "selectTargetCustomer": "请选择目标客户", "nameMinLength": "客户姓名至少需要2个字符", "phoneRequired": "电话号码是必需的", "invalidEmail": "无效的电子邮件地址", "differentCustomers": "目标客户必须与源客户不同"}}, "import": {"title": "导入客户", "steps": {"upload": "上传文件", "productType": "选择产品类型", "mapping": "列映射", "preview": "预览数据", "importing": "导入中"}, "productType": {"title": "选择产品类型", "description": "选择合适的产品类型以配置最优导入设置"}, "upload": {"title": "上传Excel文件", "description": "选择Excel文件或输入URL来导入客户列表", "fromFile": "从文件", "fromUrl": "从URL", "dragDrop": "拖拽文件到此处或点击选择", "supportedFormats": "支持：.xlsx, .xls, .csv（最大10MB）", "selectFile": "选择文件", "hasHeader": "文件包含标题行", "urlTitle": "从URL导入", "urlPlaceholder": "输入Excel文件URL...", "excelUrl": "Excel文件URL", "sheetName": "工作表名称", "sheetNamePlaceholder": "输入工作表名称（留空使用第一个工作表）", "sheetNameHelp": "留空将使用文件中的第一个工作表", "loading": "加载中...", "loadFromUrl": "从URL加载", "dragDropTitle": "拖拽文件到此处", "dragDropDescription": "或点击从计算机选择文件"}, "mapping": {"title": "映射列数据", "description": "选择Excel列对应的客户字段", "columnMapping": "列映射", "selectField": "选择字段", "skipColumn": "跳过此列", "requiredField": "必填字段", "dataPreview": "数据预览", "previewData": "显示示例数据", "previewNote": "显示前5行", "validationErrors": "验证错误", "errors": {"requiredFieldMissing": "缺少必填字段：{{field}}", "duplicateMapping": "检测到重复字段映射"}}, "preview": {"title": "预览导入数据", "description": "导入到系统前检查数据", "totalRows": "总行数", "validRows": "有效行", "invalidRows": "无效行", "validationWarnings": "验证警告", "dataPreview": "数据预览", "showingFirst10": "显示前10行", "importOptions": "导入选项", "skipInvalidRows": "跳过无效行", "sendWelcomeEmail": "发送欢迎邮件", "startImport": "开始导入", "row": "行"}, "progress": {"importing": "正在导入数据", "pleaseWait": "请等待系统处理数据", "processing": "处理中", "imported": "已导入", "errors": "错误", "recentErrors": "最近错误"}, "complete": {"title": "导入完成", "description": "导入过程已成功完成", "totalProcessed": "总处理数", "successfullyImported": "成功导入", "failed": "失败", "errorDetails": "错误详情", "nextSteps": "下一步", "reviewCustomers": "查看客户列表", "setupSegments": "设置客户细分", "createCampaigns": "创建营销活动", "viewCustomers": "查看客户", "successMessage": "成功导入{{count}}个客户！"}, "validation": {"nameRequired": "客户姓名是必需的", "invalidEmail": "无效邮箱", "invalidPhone": "无效电话号码"}, "errors": {"fileRequired": "请选择文件", "urlRequired": "请输入文件URL", "invalidUrl": "无效的URL格式", "unsupportedFormat": "不支持的文件格式。请使用.xlsx、.xls或.csv", "loadFailed": "加载文件失败", "networkError": "网络错误", "parseError": "解析文件内容失败", "emptyFile": "文件为空或无数据", "invalidFile": "无效或损坏的文件", "readError": "读取文件错误", "invalidFileType": "不支持的文件格式", "fileTooLarge": "文件过大（最大10MB）", "urlFetchError": "无法从URL获取文件", "urlLoadError": "从URL加载文件错误", "invalidSheet": "无效的工作表名称", "sheetNotFound": "在文件中未找到工作表"}}}, "product": {"title": "产品", "description": "管理产品信息", "productContent": "产品内容", "createProduct": "创建产品", "editProduct": "编辑产品", "productList": "产品列表", "productInfo": "产品信息", "productAttributes": "产品属性", "productImages": "产品图片", "name": "产品名称", "tags": "标签", "image": "图片", "priceType": {"title": "价格类型", "hasPrice": "固定价格", "stringPrice": "描述性价格", "noPrice": "无价格"}, "productType": {"title": "产品类型", "physical": "实体", "digital": "数字", "service": "服务", "event": "活动", "combo": "套餐"}, "types": {"physical": {"title": "实体产品", "description": "可触摸的产品，需要运输", "examples": "例如：服装、电子产品、书籍、家居用品"}, "digital": {"title": "数字产品", "description": "文件、课程、电子书、软件", "examples": "例如：电子书、在线课程、模板、软件"}, "service": {"title": "服务", "description": "咨询、美容、维护、安装", "examples": "例如：咨询、设计、维护、技术支持"}, "event": {"title": "活动", "description": "研讨会、课程、表演", "examples": "例如：研讨会、课程、表演"}, "combo": {"title": "套餐", "description": "结合多种类型的产品包"}}, "typeSelector": {"title": "选择产品类型"}, "statusTitle": "状态", "statusPending": "待处理", "statusApproved": "已批准", "statusRejected": "已拒绝", "createSimpleTitle": "创建简单产品", "customFields": {"title": "自定义字段", "selectField": "选择自定义字段", "selectGroupForm": "选择自定义字段组", "searchPlaceholder": "搜索自定义字段...", "searchGroupPlaceholder": "搜索自定义字段组...", "selectedFields": "已选自定义字段", "selectedGroupForm": "已选自定义字段组", "addField": "添加自定义字段", "addGroupForm": "添加自定义字段组"}, "listPrice": "标价", "enterListPrice": "请输入标价", "enterSalePrice": "请输入销售价", "salePrice": "销售价", "currency": "货币", "priceDescription": "价格描述", "quantity": "数量", "quantityNotManaged": "不管理数量", "outOfStock": "缺货", "createSuccess": "产品创建成功", "createError": "创建产品时出错", "updateSuccess": "产品更新成功", "updateError": "更新产品时出错", "deleteSuccess": "产品删除成功", "deleteError": "删除产品时出错", "bulkDeleteSuccess": "成功删除 {{count}} 个产品", "bulkDeleteError": "批量删除产品时出错", "selectToDelete": "请至少选择一个产品进行删除", "confirmDeleteMessage": "您确定要删除此产品吗？", "confirmBulkDeleteMessage": "您确定要删除选定的 {{count}} 个产品吗？", "fields": {"name": "产品名称", "price": "价格", "priceType": "价格类型", "priceTypes": {"yes": "是", "no": "否", "other": "其他"}}, "form": {"title": "添加新产品", "name": "产品名称", "description": "描述", "price": "价格", "category": "类别", "sku": "SKU", "status": "状态", "submit": "保存产品", "cancel": "取消", "createTitle": "添加新产品", "createDigitalTitle": "创建数字产品", "createServiceTitle": "创建服务", "createEventTitle": "创建活动", "createComboTitle": "创建组合产品", "editTitle": "编辑产品", "updating": "正在更新...", "namePlaceholder": "输入产品名称", "serviceNamePlaceholder": "输入服务名称", "eventNamePlaceholder": "输入活动名称", "comboNamePlaceholder": "输入组合产品名称", "descriptionPlaceholder": "输入产品描述", "serviceDescriptionPlaceholder": "输入服务的详细描述", "eventDescriptionPlaceholder": "输入活动的详细描述", "comboDescriptionPlaceholder": "输入组合产品的详细描述", "pricePlaceholder": "输入产品价格", "categoryPlaceholder": "选择产品类别", "skuPlaceholder": "输入产品SKU", "statusPlaceholder": "选择产品状态", "inventoryPlaceholder": "输入产品库存", "tagsPlaceholder": "输入产品标签并按回车", "mediaPlaceholder": "拖放或点击上传产品图片", "media": "产品图片", "priceDescriptionPlaceholder": "输入价格描述", "listPricePlaceholder": "输入标价", "salePricePlaceholder": "输入售价", "currency": "货币", "listPrice": "标价", "salePrice": "销售价", "sections": {"generalInfo": "基本信息", "pricing": "产品定价", "images": "产品图片", "shipping": "运输", "inventory": "库存管理", "variants": "变体", "customFields": "自定义字段", "digitalProcessing": "数字订单处理", "digitalOutput": "数字产品输出", "media": "产品图片", "serviceInfo": "服务信息", "eventInfo": "活动组织信息", "ticketTypes": "活动门票类型", "comboProducts": "组合中的产品"}, "shipmentConfig": {"title": "运输配置", "widthCm": "宽度 (厘米)", "heightCm": "高度 (厘米)", "lengthCm": "长度 (厘米)", "weightGram": "重量 (克)"}, "inventory": {"warehouse": "仓库", "warehousePlaceholder": "选择仓库", "availableQuantity": "可用数量", "sku": "SKU", "barcode": "条形码"}, "customFields": {"title": "自定义字段", "selectField": "选择自定义字段", "selectGroupForm": "选择自定义字段组", "searchPlaceholder": "搜索自定义字段...", "searchGroupPlaceholder": "搜索自定义字段组...", "selectedFields": "已选自定义字段", "selectedGroupForm": "已选自定义字段组", "addField": "添加自定义字段", "addGroupForm": "添加自定义字段组", "valuePlaceholder": "输入值", "arrayPlaceholder": "输入值并按回车键添加", "selectBoolean": "选择是/否", "noOptions": "未配置选项", "validation": {"required": "此字段是必需的", "minLength": "最少{{min}}个字符", "maxLength": "最多{{max}}个字符", "pattern": "格式无效", "invalidNumber": "必须是有效数字", "min": "最小值{{min}}", "max": "最大值{{max}}", "invalidEmail": "邮箱无效", "invalidUrl": "URL无效", "invalidDate": "日期无效"}}, "variants": {"title": "产品分类", "addVariant": "添加变体", "variant": "分类", "noVariants": "暂无分类。点击\"添加分类\"开始。", "customFields": "变体属性", "searchCustomField": "搜索属性", "basicInfo": "基本信息", "name": "变体名称", "priceDescription": "价格描述", "currency": "货币单位", "listPrice": "标价", "salePrice": "销售价", "inventory": "库存管理 & 图片", "sku": "变体SKU", "images": "变体图片", "imagesHelper": "为此变体上传专用图片"}, "versions": {"title": "版本", "addVersion": "添加版本", "version": "版本", "noVersions": "暂无版本。点击\"添加版本\"开始。", "name": "版本名称", "namePlaceholder": "基础版, 专业版, 高级版...", "price": "价格", "currency": "货币单位", "description": "版本描述", "descriptionPlaceholder": "输入版本的详细描述...", "quantity": "可用数量", "sku": "版本SKU", "skuPlaceholder": "BASIC-001", "minQuantity": "每次购买的最低数量", "maxQuantity": "每次购买的最大数量", "status": "状态", "statusPending": "待处理", "statusActive": "积极的", "statusInactive": "不活跃的", "removeVersion": "删除版本"}, "digitalProduct": {"deliveryMethod": {"title": "交付方式", "email": "邮件", "dashboardDownload": "控制台下载", "sms": "短信", "directMessage": "直接消息", "zalo": "<PERSON><PERSON>", "courseActivation": "课程激活"}, "deliveryTiming": {"title": "交付时间", "immediate": "付款后立即", "delayed": "延迟交付"}, "deliveryDelayMinutes": "延迟时间（分钟）", "deliveryDelayPlaceholder": "输入延迟时间（分钟）", "accessStatus": {"title": "访问状态", "pending": "待处理", "delivered": "已交付", "notDelivered": "未交付", "deliveryError": "交付错误"}, "digitalProductType": {"title": "数字产品类型", "onlineCourse": "在线课程", "fileDownload": "文件下载", "licenseKey": "许可证密钥", "ebook": "电子书"}, "downloadLink": "访问链接", "downloadLinkPlaceholder": "输入数字产品访问链接", "accessLink": "访问链接", "accessLinkPlaceholder": "输入数字产品访问链接", "loginInfo": {"title": "课程登录信息", "username": "用户名", "usernamePlaceholder": "输入用户名", "password": "密码", "passwordPlaceholder": "输入密码"}, "usageInstructions": "使用说明", "usageInstructionsPlaceholder": "输入数字产品的详细使用说明"}, "serviceProduct": {"serviceTime": "服务时间", "serviceTimePlaceholder": "选择服务执行的日期和时间", "serviceDuration": "持续时间（分钟）", "serviceDurationPlaceholder": "输入服务持续时间", "serviceProvider": "服务提供者", "serviceProviderPlaceholder": "提供服务的人员/团队名称", "serviceType": {"title": "服务类型", "consultation": "咨询", "beauty": "美容", "maintenance": "维护", "installation": "安装"}, "serviceLocation": {"title": "服务地点", "atHome": "上门服务", "atCenter": "到店服务", "online": "在线服务"}, "defaultPackageName": "基础咨询套餐", "defaultPackageDescription": "基础咨询套餐包含3次在线咨询会话"}, "servicePackages": {"title": "服务套餐列表", "addPackage": "添加套餐", "newPackageName": "新服务套餐", "noPackages": "暂无服务套餐。添加您的第一个套餐！", "namePlaceholder": "输入服务套餐名称", "descriptionPlaceholder": "服务套餐的详细描述", "features": "功能特性", "featuresPlaceholder": "输入功能特性并按回车", "isActive": "状态", "isLimited": "数量限制", "quantity": "最大数量"}, "eventProduct": {"eventDateTime": "活动时间", "eventDateTimePlaceholder": "选择活动日期和时间", "eventEndDateTime": "活动结束时间", "eventEndDateTimePlaceholder": "选择活动结束日期和时间", "eventTimeZone": "时区", "eventTimeZonePlaceholder": "选择时区", "attendanceMode": {"title": "参与方式", "offline": "线下（现场）", "online": "线上（虚拟）"}, "eventLocation": "活动地点", "eventLocationPlaceholder": "输入活动举办地点", "zoomLink": "Zoom链接", "zoomLinkPlaceholder": "输入线上活动的Zoom链接", "ticketTypes": {"title": "门票类型列表", "addTicket": "添加门票类型", "removeTicket": "删除门票类型", "ticketName": "门票名称", "ticketNamePlaceholder": "VIP、普通、学生...", "price": "价格", "currency": "货币", "totalTickets": "总票数", "sku": "SKU代码", "skuPlaceholder": "VIP-001", "saleStartTime": "销售开始时间", "saleStartTimePlaceholder": "选择销售开始日期和时间", "saleEndTime": "销售结束时间", "saleEndTimePlaceholder": "选择销售结束日期和时间", "timeZone": "门票时区", "timeZonePlaceholder": "选择门票时区", "minQuantityPerOrder": "每单最少票数", "maxQuantityPerOrder": "每单最多票数", "ticketImage": "门票图片", "ticketImagePlaceholder": "无门票图片", "description": "门票描述", "descriptionPlaceholder": "此门票类型的详细描述...", "defaultTicketName": "门票类型", "defaultTicketDescription": "基础会议参与门票"}, "defaultEventLocation": "国家会议中心，北京"}, "comboProduct": {"searchProductLabel": "搜索产品添加到组合:", "defaultQuantityLabel": "默认数量", "defaultQuantity": "数量", "quantityPlaceholder": "输入数量...", "quantityNote": "此数量将应用于添加到组合中的新产品", "selectedProductsLabel": "已选产品", "noProductsMessage": "组合中还没有产品。请在上方选择产品。", "productAlreadyExists": "此产品已在组合中", "tableHeaders": {"productName": "产品名称", "quantity": "数量", "originalPrice": "原价", "actions": "操作"}, "totalLabel": "总计:", "listPriceNote": "根据组合中产品总价自动计算", "salePriceNote": "销售价格通常低于标价以创造折扣", "priceDescriptionPlaceholder": "例如：联系获取组合定价"}, "url": {"title": "产品URL", "selectedUrl": "已选URL", "selectedUrls": "已选URL", "placeholder": "输入URL或从列表中选择", "selectFromList": "从列表选择", "addMultiple": "添加URL", "preview": "预览", "selectUrl": "选择URL", "selectFromExisting": "从现有列表选择", "addNew": "添加新的", "addNewUrl": "添加新URL", "pleaseEnterAllInfo": "请输入所有信息", "invalidUrl": "无效的URL。请输入有效的URL格式", "createSuccess": "新URL创建成功", "createError": "创建新URL时发生错误"}, "validation": {"nameRequired": "产品名称是必需的", "descriptionRequired": "产品描述是必需的", "productTypeRequired": "请选择产品类型", "priceTypeRequired": "请选择价格类型", "listPriceRequired": "请输入标价", "salePriceRequired": "请输入售价", "currencyRequired": "请选择货币", "priceDescriptionRequired": "请输入价格描述", "loginInfoRequired": "请输入在线课程的登录信息", "versionNameRequired": "版本名称是必需的", "versionPriceMin": "版本价格必须 >= 0", "versionCurrencyRequired": "货币是必需的", "versionQuantityMin": "数量必须 >= 1", "versionMinQuantityMin": "最小数量必须 >= 1", "versionMaxQuantityMin": "最大数量必须 >= 1", "listPriceInvalid": "标价必须是数字 > 0", "salePriceInvalid": "售价必须是数字 > 0", "comboTotalPriceInvalid": "组合中产品的总价值必须大于0", "listPriceGreaterThanSale": "标价必须大于售价", "invalidPriceType": "无效的价格类型", "formValidationError": "请检查输入的信息", "requiredFieldsMissing": "请输入产品名称并选择价格类型", "serviceRequiredFieldsMissing": "请输入服务名称并选择价格类型", "priceValidationError": "价格验证错误", "serviceDurationMin": "服务持续时间必须大于0", "serviceNameRequired": "服务名称是必需的", "eventNameRequired": "活动名称是必需的", "eventRequiredFieldsMissing": "请输入活动名称", "eventLocationRequired": "请输入线下活动的举办地点", "zoomLinkInvalid": "无效的Zoom链接", "ticketTypesRequired": "必须至少有1种门票类型", "ticketNameRequired": "门票名称是必需的", "ticketPriceMin": "门票价格必须 >= 0", "ticketTotalMin": "总票数必须 >= 1", "ticketMinQuantityMin": "最少票数必须 >= 1", "ticketMaxQuantityMin": "最多票数必须 >= 1", "ticketMaxGreaterThanMin": "最多票数必须 >= 最少票数", "ticketUploadSuccess": "门票图片上传成功", "ticketUploadError": "上传门票图片时出错", "comboNameRequired": "组合名称是必需的", "comboRequiredFieldsMissing": "请输入组合名称并选择价格类型", "comboProductsRequired": "组合必须至少有1个产品", "comboProductIdInvalid": "无效的产品ID", "comboProductNameRequired": "产品名称是必需的", "comboProductQuantityMin": "数量必须大于0"}}, "productDetails": {"regularPrice": "常规价格", "salePrice": "促销价格", "priceNote": "价格备注", "brand": "品牌", "url": "网址", "description": "描述", "attributes": "属性", "attributeName": "属性名称", "attributeType": "数据类型", "attributeValue": "默认值"}, "validation": {"nameRequired": "请输入产品名称", "attributeNameRequired": "请输入属性名称", "attributeTypeRequired": "请选择数据类型"}, "attributeTypes": {"text": "文本", "number": "数字", "date": "日期", "boolean": "是/否", "list": "列表"}, "images": {"addImages": "添加产品图片", "image": "图片", "url": "网址", "video": "视频", "uploadImage": "上传图片", "enterImageUrl": "输入图片网址", "enterVideoUrl": "输入视频网址", "recommendedSize": "建议尺寸：800x600像素，最大2MB", "addToList": "添加到列表", "uploadedImages": "已上传图片", "urlImages": "网址图片", "videoList": "视频列表", "setCover": "设为封面", "coverImage": "封面图片", "uploadedFromComputer": "从电脑上传", "dragAndDrop": "拖放或点击上传产品图片"}, "actions": {"createProduct": "创建产品", "saveProduct": "保存产品", "deleteProduct": "删除产品", "cancelCreation": "取消"}, "messages": {"productCreated": "产品创建成功", "productUpdated": "产品更新成功", "productDeleted": "产品删除成功", "confirmDelete": "您确定要删除此产品吗？"}, "import": {"title": "导入产品", "steps": {"upload": "上传文件", "productType": "选择产品类型", "mapping": "列映射", "preview": "预览数据", "importing": "导入中"}, "productType": {"title": "选择产品类型", "description": "选择合适的产品类型以配置最优导入设置"}, "upload": {"title": "上传Excel文件", "description": "选择Excel文件或输入URL来导入产品列表", "fromFile": "从文件", "fromUrl": "从URL", "dragDrop": "拖放文件到此处或点击选择", "supportedFormats": "支持格式：.xlsx, .xls, .csv (最大10MB)", "selectFile": "选择文件", "urlTitle": "从URL导入", "urlPlaceholder": "输入Excel文件URL...", "excelUrl": "Excel文件URL", "sheetName": "Sheet名称", "sheetNamePlaceholder": "输入Sheet名称（留空以使用第一个Sheet）", "sheetNameHelp": "留空以使用文件中的第一个Sheet", "hasHeader": "文件有标题行", "loadFromUrl": "从URL加载", "dragDropTitle": "拖放文件到此处", "dragDropDescription": "或点击选择文件", "loading": "正在加载..."}, "mapping": {"title": "列映射", "description": "将Excel列映射到产品字段", "columnMapping": "列映射", "skipColumn": "跳过此列", "requiredField": "此字段是必需的", "dataPreview": "数据预览", "validationErrors": "验证错误", "errors": {"duplicateMapping": "此字段已映射到另一列", "requiredFieldMissing": "必需字段{{field}}未映射"}}, "errors": {"parseError": "解析文件错误", "urlRequired": "URL是必需的", "urlFetchError": "从URL获取文件错误", "urlLoadError": "从URL加载文件错误"}, "progress": {"importing": "正在导入产品", "pleaseWait": "请等待系统处理数据", "processing": "处理中", "imported": "已导入", "errors": "错误"}, "complete": {"title": "导入完成", "description": "产品导入已成功完成", "totalProcessed": "总处理数", "successfullyImported": "成功导入", "failed": "失败", "errorDetails": "错误详情", "nextSteps": "下一步", "reviewProducts": "查看产品列表", "updateInventory": "更新库存水平", "setupCategories": "设置产品类别", "viewProducts": "查看产品"}, "preview": {"title": "预览导入数据", "description": "导入前查看和验证数据", "totalRows": "总行数", "validRows": "有效行", "invalidRows": "无效行", "validationWarnings": "验证警告", "dataPreview": "数据预览", "showingFirst10": "显示前10行", "importOptions": "导入选项", "skipInvalidRows": "跳过无效行", "updateExisting": "更新现有产品", "sendNotification": "发送通知", "startImport": "开始导入", "row": "行"}, "validation": {"nameRequired": "产品名称是必需的", "skuRequired": "SKU是必需的", "priceRequired": "价格是必需的", "invalidPrice": "价格必须是正数", "invalidStock": "库存必须是非负数"}}}, "customField": {"configId": "字段标识符名称", "title": "自定义字段", "description": "管理自定义字段", "component": "组件类型", "components": {"input": "输入框", "textarea": "文本区域", "select": "下拉选择", "checkbox": "复选框", "radio": "单选按钮", "date": "日期", "number": "数字", "file": "文件", "multiSelect": "多选"}, "type": "数据类型", "type.string": "文档", "type.number": "数字", "type.boolean": "布尔值", "type.date": "日期", "type.object": "对象", "type.array": "数组", "types": {"text": "文本", "number": "数字", "boolean": "是/否", "date": "日期", "select": "选择框", "object": "对象", "array": "数组"}, "name": "字段名称", "label": "标签", "placeholder": "占位符", "defaultValue": "默认值", "options": "选项", "required": "必填", "validation": {"minLength": "最小长度", "maxLength": "最大长度", "pattern": "模式", "min": "最小值", "max": "最大值"}, "booleanValues": {"true": "是", "false": "否"}, "patterns": {"email": "邮箱", "phoneVN": "越南电话号码", "phoneIntl": "国际电话号码", "postalCodeVN": "越南邮政编码", "lettersOnly": "仅字母", "numbersOnly": "仅数字", "alphanumeric": "字母和数字", "noSpecialChars": "无特殊字符", "url": "网址", "ipv4": "IPv4地址", "strongPassword": "强密码", "vietnameseName": "越南姓名", "studentId": "学生证号", "nationalId": "身份证号", "taxCode": "税号", "dateFormat": "日期 (dd/mm/yyyy)", "timeFormat": "时间 (hh:mm)", "hexColor": "十六进制颜色", "base64": "Base64编码", "uuid": "UUID", "filename": "文件名", "urlSlug": "URL别名", "variableName": "变量名", "creditCard": "信用卡号", "qrCode": "二维码", "gpsCoordinate": "GPS坐标", "rgbColor": "RGB颜色", "domain": "域名", "decimal": "小数", "barcode": "条形码"}, "confirmDeleteMessage": "您确定要删除此自定义字段吗？", "createSuccess": "自定义字段创建成功", "createError": "创建自定义字段时出错", "updateSuccess": "自定义字段更新成功", "updateError": "更新自定义字段时出错", "deleteSuccess": "自定义字段删除成功", "deleteError": "删除自定义字段时出错", "loadError": "加载自定义字段时出错", "form": {"showAdvancedSettings": "显示高级设置", "componentRequired": "请选择组件类型", "labelRequired": "请输入标签", "typeRequired": "请选择数据类型", "idRequired": "请输入字段标识符名称", "labelPlaceholder": "输入显示标签", "descriptionPlaceholder": "输入描述", "description": "描述", "labelTagRequired": "请至少添加一个标签", "fieldIdLabel": "字段标识符名称", "fieldIdPlaceholder": "text-input-001", "displayNameLabel": "显示名称", "displayNamePlaceholder": "输入此字段的显示名称", "displayNameRequired": "请输入显示名称", "labelInputPlaceholder": "输入标签并按回车", "tagsCount": "个标签已添加", "patternSuggestions": "常用模式建议：", "defaultValue": "默认值", "minLength": "最小长度", "maxLength": "最大长度", "pattern": "模式", "options": "选项", "min": "最小值", "max": "最大值", "placeholder": "占位符", "required": "必填", "label": "标签", "optionsPlaceholder": "输入选项，逗号分隔或JSON格式", "selectOptionsPlaceholder": "按Name|Value格式输入值，每行一对。示例：\na|1\nb|2", "booleanDefaultPlaceholder": "选择默认值", "dateDefaultPlaceholder": "选择默认日期", "placeholderPlaceholder": "输入占位符文本", "defaultValuePlaceholder": "输入默认值", "arrayDefaultPlaceholder": "输入值并按回车键添加"}}, "customGroupForm": {"title": "自定义字段组", "description": "管理自定义字段组", "createSuccess": "自定义字段组创建成功", "createError": "创建自定义字段组时出错", "updateSuccess": "自定义字段组更新成功", "updateError": "更新自定义字段组时出错", "deleteSuccess": "自定义字段组删除成功", "deleteError": "删除自定义字段组时出错", "loadError": "加载自定义字段组时出错"}, "warehouse": {"title": "仓库", "description": "管理仓库", "name": "仓库名称", "code": "仓库代码", "desc": "描述", "type": "仓库类型", "types": {"PHYSICAL": "实体仓库", "VIRTUAL": "虚拟仓库"}, "status": "状态", "address": "地址", "contact": "联系信息", "add": "添加仓库", "edit": "编辑仓库", "addForm": "添加新仓库", "editForm": "编辑仓库信息", "createSuccess": "仓库创建成功", "updateSuccess": "仓库更新成功", "deleteSuccess": "仓库删除成功", "createError": "创建仓库时出错", "updateError": "更新仓库时出错", "deleteError": "删除仓库时出错", "confirmDeleteMessage": "您确定要删除此仓库吗？", "notFound": "未找到仓库", "form": {"namePlaceholder": "输入仓库名称", "descriptionPlaceholder": "输入仓库描述", "typePlaceholder": "选择仓库类型", "selectType": "选择仓库类型"}}, "physicalWarehouse": {"title": "实体仓库", "information": "管理实体仓库", "type": "仓库类型", "description": "管理实体仓库", "manage": "管理实体仓库", "totalWarehouses": "实体仓库总数", "name": "实体仓库名称", "warehouse": "仓库", "address": "地址", "capacity": "容量", "actions": "操作", "add": "添加实体仓库", "create": "创建实体仓库", "edit": "编辑", "delete": "编辑", "view": "查看详情", "search": "搜索实体仓库...", "noData": "无实体仓库数据", "createSuccess": "实体仓库创建成功", "updateSuccess": "实体仓库更新成功", "deleteSuccess": "实体仓库删除成功", "deleteMultipleSuccess": "成功删除多个实体仓库", "createError": "创建实体仓库失败", "updateError": "更新实体仓库失败", "deleteError": "删除实体仓库失败", "deleteMultipleError": "删除多个实体仓库失败", "errors": {"nameRequired": "仓库名称是必需的", "nameExists": "仓库名称已存在"}, "selectToDelete": "请至少选择一个实体仓库进行删除", "confirmDeleteMessage": "您确定要删除此实体仓库吗？", "confirmBulkDeleteMessage": "您确定要删除选定的 {{count}} 个实体仓库吗？", "form": {"createTitle": "创建新实体仓库", "editTitle": "编辑实体仓库", "create": "创建实体仓库", "update": "更新", "selectWarehouse": "选择仓库", "warehousePlaceholder": "选择仓库以创建实体仓库", "warehouseRequired": "仓库是必需的", "addressPlaceholder": "输入实体仓库地址", "addressRequired": "地址是必需的", "addressMaxLength": "地址不能超过255个字符", "capacityPlaceholder": "输入仓库容量", "capacityMin": "容量必须大于或等于0"}}, "virtualWarehouse": {"title": "虚拟仓库", "description": "管理虚拟仓库和数字存储系统", "manage": "管理虚拟仓库", "totalWarehouses": "虚拟仓库总数", "name": "虚拟仓库名称", "status": {"title": "状态", "active": "活跃", "inactive": "非活跃"}, "associatedSystem": "关联系统", "purpose": "用途", "actions": "操作", "create": "创建虚拟仓库", "edit": "编辑虚拟仓库", "delete": "删除虚拟仓库", "view": "查看详情", "search": "搜索虚拟仓库...", "noData": "无虚拟仓库数据", "createSuccess": "虚拟仓库创建成功", "updateSuccess": "虚拟仓库更新成功", "deleteSuccess": "虚拟仓库删除成功", "deleteMultipleSuccess": "成功删除多个虚拟仓库", "createError": "创建虚拟仓库失败", "updateError": "更新虚拟仓库失败", "deleteError": "删除虚拟仓库失败", "deleteMultipleError": "删除多个虚拟仓库失败", "confirmDeleteMessage": "您确定要删除此虚拟仓库吗？", "confirmBulkDeleteMessage": "您确定要删除选定的 {{count}} 个虚拟仓库吗？", "form": {"createTitle": "创建新虚拟仓库", "editTitle": "编辑虚拟仓库", "create": "创建虚拟仓库", "update": "更新", "warehousePlaceholder": "选择仓库以创建虚拟仓库", "warehouseRequired": "仓库是必需的", "descriptionPlaceholder": "输入虚拟仓库描述", "descriptionMaxLength": "描述不能超过500个字符", "associatedSystemPlaceholder": "输入关联系统", "associatedSystemMaxLength": "关联系统不能超过200个字符", "purposePlaceholder": "输入用途", "purposeMaxLength": "用途不能超过300个字符", "statusPlaceholder": "选择状态", "statusRequired": "状态是必需的"}}, "inventory": {"title": "库存管理", "description": "管理库存和出入库", "totalItems": "总商品数", "totalProducts": "总产品数", "manage": "管理库存", "currentQuantity": "当前数量", "availableQuantity": "可用数量", "reservedQuantity": "预留数量", "defectiveQuantity": "缺陷数量", "totalQuantity": "总数量", "updateQuantity": "更新数量", "addProduct": "添加产品", "noProducts": "仓库中没有产品", "noProductsDescription": "此仓库目前没有产品。请向仓库添加产品以开始管理。", "status": {"inStock": "有库存", "lowStock": "库存不足", "outOfStock": "无库存"}}, "conversion": {"title": "转化", "description": "跟踪和管理转化", "totalConversions": "总转化数", "manage": "管理转化", "id": "ID", "customerId": "客户ID", "userId": "用户ID", "type": "转化类型", "name": "名称", "source": "来源", "destination": "目的地", "value": "价值", "date": "日期", "status": {"completed": "已完成", "pending": "处理中", "failed": "失败"}, "sourceOptions": {"website": "网站", "social_media": "社交媒体", "event": "活动"}}, "order": {"notesPlaceholder": "输入订单备注", "title": "订单", "description": "管理订单", "createOrder": "创建新订单", "editOrder": "编辑订单", "viewOrder": "查看订单详情", "orderNumber": "订单号", "customerInfo": "客户信息", "customerName": "客户姓名", "customerEmail": "电子邮件", "customerPhone": "电话", "customerAddress": "地址", "items": "订单商品", "noItems": "此订单没有商品", "quantity": "数量", "totalAmount": "总金额", "status": {"title": "订单状态", "pending": "待处理", "confirmed": "已确认", "processing": "处理中", "completed": "已完成", "cancelled": "已取消"}, "shippingStatus": {"title": "配送状态", "pending": "待处理", "preparing": "准备中", "shipped": "已发货", "inTransit": "运输中", "sorting": "分拣中", "delivered": "已送达", "deliveryFailed": "配送失败", "returning": "退货中", "cancelled": "已取消"}, "paymentMethod": "支付方式", "paymentMethods": {"cash": "现金", "creditCard": "信用卡", "bankTransfer": "银行转账", "digitalWallet": "电子钱包"}, "paymentStatus": {"title": "支付状态", "paid": "已支付", "pending": "待支付", "unpaid": "未支付", "failed": "支付失败", "refunded": "已退款", "partiallyPaid": "部分支付"}, "payment": {"method": {"cash": "现金", "banking": "银行转账", "creditCard": "信用卡", "eWallet": "电子钱包"}}, "notes": "备注", "shippingMethod": "配送方式", "shippingFee": "配送费用", "shippingNote": "配送备注", "shippingNotePlaceholder": "输入配送备注...", "codAmount": "货到付款金额", "tags": "订单标签", "tagsPlaceholder": "输入标签并按回车", "addTag": "添加标签", "removeTag": "删除标签", "subtotal": "小计", "form": {"customerNamePlaceholder": "输入客户姓名", "customerEmailPlaceholder": "输入客户电子邮件", "customerPhonePlaceholder": "输入客户电话", "customerAddressPlaceholder": "输入客户地址", "notesPlaceholder": "输入订单备注"}, "createSuccess": "订单创建成功", "createError": "创建订单时出错", "updateSuccess": "订单更新成功", "updateError": "更新订单时出错", "deleteSuccess": "订单删除成功", "deleteError": "删除订单时出错", "confirmDeleteMessage": "您确定要删除此订单吗？", "cancel": "取消订单", "estimatedDelivery": "预计送达时间", "tax": "税费", "total": "总计", "printInProgress": "正在准备打印订单...", "tracking": "订单跟踪", "carrier": "承运商", "trackingNumber": "运单号", "currentStatus": "当前状态", "loadingTracking": "正在加载跟踪信息...", "trackingError": "加载跟踪信息时出错", "trackingErrorGeneric": "加载跟踪信息时发生错误", "trackingTimeline": "配送时间线", "detailedTracking": "详细跟踪信息", "noTrackingInfo": "暂无跟踪信息", "printSuccess": "打印订单成功", "printSuccessDescription": "订单已准备好打印", "printError": "打印订单错误", "printErrorDescription": "无法打印订单。请重试。", "batchPrintSuccess": "批量打印成功", "batchPrintSuccessDescription": "成功打印了{{count}}个订单", "batchPrintError": "批量打印错误", "batchPrintErrorDescription": "无法批量打印订单。请重试。", "fetchPrintableOrdersError": "无法加载可打印订单列表", "notFound": "未找到订单", "notFoundDescription": "订单不存在或您没有查看权限。", "backToList": "返回列表", "loading": "正在加载订单详情...", "createdAt": "创建时间", "productInfo": "产品信息", "paymentInfo": "支付信息", "shippingInfo": "配送信息", "recipient": "收件人", "phone": "电话", "print": "打印订单", "deliveryConfiguration": "配送配置", "products": "产品", "physicalShippingTitle": "实体产品配送", "digitalDeliveryTitle": "数字产品交付", "serviceDeliveryTitle": "服务信息", "eventDeliveryTitle": "活动信息", "selectProductsFirstDescription": "请选择产品以配置配送", "deliveryAddress": "配送地址", "enhancedOrderForm": {"title": "创建新订单", "editTitle": "编辑订单", "stepTitles": {"customer": "客户信息", "products": "选择产品", "delivery": "配送配置", "payment": "支付信息", "review": "订单确认"}, "navigation": {"previous": "上一步", "continue": "继续", "next": "下一步", "cancel": "取消", "submit": "创建订单", "update": "更新订单"}, "validation": {"customerRequired": "请选择客户", "productsRequired": "请至少选择一个产品", "paymentMethodRequired": "请选择支付方式"}, "summary": {"title": "订单摘要", "customer": "客户", "products": "产品", "subtotal": "小计", "shipping": "配送费", "total": "总计", "paymentMethod": "支付方式", "notes": "备注"}}}, "report": {"charts": {"multiLine": {"title": "业务图表"}}}, "shop": {"title": "店铺地址管理", "description": "管理店铺地址和配送信息", "form": {"shopId": "店铺ID", "shopName": "店铺名称", "shopNamePlaceholder": "请输入店铺名称", "shopPhone": "电话号码", "shopPhonePlaceholder": "请输入店铺电话号码", "shopAddress": "详细地址", "shopAddressPlaceholder": "街道、门牌号等", "shopProvince": "省/市", "shopProvincePlaceholder": "请输入省/市", "shopDistrict": "区/县", "shopDistrictPlaceholder": "请输入区/县", "shopWard": "街道/乡镇", "shopWardPlaceholder": "请输入街道/乡镇", "isDefault": "默认", "createTitle": "创建店铺地址", "editTitle": "更新店铺地址", "description": "输入店铺地址以管理订单和配送", "basicInfo": "基本信息", "addressShop": "店铺地址"}, "filter": {"recent": "最近"}, "empty": {"title": "暂无店铺", "description": "创建您的第一个店铺以开始管理订单和配送", "action": "创建店铺"}, "messages": {"createSuccess": "店铺创建成功", "createError": "创建店铺时出错", "updateSuccess": "店铺更新成功", "updateError": "更新店铺时出错", "loadError": "加载店铺信息时出错", "noShopInfo": "暂无店铺信息。请创建店铺信息以管理订单。", "bulkDeleteSuccess": "成功删除{{count}}个店铺", "bulkDeleteError": "删除多个店铺时出错", "confirmDelete": "您确定要删除此店铺地址吗？"}, "actions": {"setDefault": "设为默认"}, "selectToDelete": "请选择至少一个店铺进行删除", "confirmBulkDeleteMessage": "您确定要删除{{count}}个选中的店铺吗？", "charts": {"salesPlaceholder": "销售图表将在此处显示", "ordersPlaceholder": "订单图表将在此处显示", "customersPlaceholder": "客户图表将在此处显示", "productsPlaceholder": "产品图表将在此处显示", "labels": {"revenue": "收入", "newCustomers": "新客户", "totalCustomers": "总客户", "totalProducts": "总产品", "newProducts": "新产品", "soldProducts": "已售产品", "timeLabel": "时间"}}}, "productType": {"chart": {"title": "产品类型图表", "totalProducts": "总产品", "categories": "类别", "noData": "无数据", "mostPopular": "最受欢迎", "period": "时期", "allTime": "全部时间"}}, "bankAccount": {"title": "银行账户管理", "description": "管理和监控企业银行账户", "stats": {"totalAccounts": "总账户数", "activeAccounts": "活跃账户", "pendingAccounts": "待处理账户", "virtualAccounts": "虚拟账户", "totalBalance": "总余额", "connectedBanks": "已连接银行"}, "actions": {"add": "添加账户", "delete": "删除账户", "view": "查看详情", "edit": "编辑", "connect": "连接", "disconnect": "断开连接", "sync": "同步"}, "deleteModal": {"title": "确认删除账户", "message": "您确定要删除{{count}}个选中的银行账户吗？此操作无法撤销。"}, "messages": {"deleteSuccess": "银行账户删除成功", "deleteError": "删除银行账户时出错", "connectSuccess": "银行账户连接成功", "connectError": "连接银行账户时出错", "syncSuccess": "银行账户同步成功", "syncError": "同步银行账户时出错"}}, "company": {"information": {"title": "公司信息", "fullName": "全称", "fullNamePlaceholder": "输入公司全称", "shortName": "简称", "shortNamePlaceholder": "输入公司简称", "status": "状态", "statusPlaceholder": "选择状态"}, "configuration": {"title": "公司配置", "paymentCode": "支付代码", "paymentCodePlaceholder": "选择支付代码状态", "paymentCodeOn": "开启", "paymentCodeOff": "关闭", "paymentCodePrefix": "支付代码前缀", "paymentCodePrefixPlaceholder": "输入支付代码前缀", "paymentCodeSuffixFrom": "后缀起始", "paymentCodeSuffixFromPlaceholder": "输入后缀起始数字", "paymentCodeSuffixTo": "后缀结束", "paymentCodeSuffixToPlaceholder": "输入后缀结束数字", "paymentCodeSuffixCharacterType": "后缀字符类型", "paymentCodeSuffixCharacterTypePlaceholder": "选择后缀字符类型", "numberAndLetter": "允许字母和数字", "numberOnly": "仅允许数字"}, "status": {"pending": "待处理", "active": "活跃", "suspended": "暂停", "terminated": "终止", "cancelled": "已取消", "fraud": "欺诈"}, "updateSuccess": "公司信息更新成功", "updateError": "更新公司信息时出错", "loadError": "加载公司信息时出错"}}}