import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Input, Select } from '@/shared/components/common';
import { BaseWidgetProps } from '../../types';

interface ProgressItem {
  id: string;
  label: string;
  value: number;
  maxValue: number;
  color?: string;
}

interface ProgressWidgetProps extends BaseWidgetProps {
  initialItems?: ProgressItem[];
  editable?: boolean;
  type?: 'linear' | 'circular';
  showPercentage?: boolean;
  showValues?: boolean;
  animated?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Widget hiển thị progress bars với animation
 */
const ProgressWidget: React.FC<ProgressWidgetProps> = ({
  className,
  initialItems = [
    { id: '1', label: 'Task 1', value: 75, maxValue: 100, color: '#3b82f6' },
    { id: '2', label: 'Task 2', value: 45, maxValue: 100, color: '#10b981' },
    { id: '3', label: 'Task 3', value: 90, maxValue: 100, color: '#f59e0b' },
  ],
  editable = true,
  type = 'linear',
  showPercentage = true,
  showValues = false,
  animated = true,
  size = 'md',
  onPropsChange,
  ...props
}) => {
  const { t } = useTranslation(['dashboard', 'common']);
  
  // Use values from props if available
  const currentItems = (props.items as ProgressItem[]) || initialItems;
  const currentSettings = {
    type: (props.type as typeof type) || type,
    showPercentage: (props.showPercentage as boolean) ?? showPercentage,
    showValues: (props.showValues as boolean) ?? showValues,
    animated: (props.animated as boolean) ?? animated,
    size: (props.size as typeof size) || size,
  };

  const [items, setItems] = useState<ProgressItem[]>(currentItems);
  const [isEditing, setIsEditing] = useState(false);
  const [tempItems, setTempItems] = useState<ProgressItem[]>(currentItems);
  const [tempSettings, setTempSettings] = useState(currentSettings);

  // Sync with props changes
  useEffect(() => {
    const newItems = (props.items as ProgressItem[]) || initialItems;
    if (JSON.stringify(newItems) !== JSON.stringify(items)) {
      setItems(newItems);
      setTempItems(newItems);
    }
  }, [props.items, initialItems, items]);

  const handleEdit = useCallback(() => {
    setTempItems([...items]);
    setTempSettings(currentSettings);
    setIsEditing(true);
  }, [items, currentSettings]);

  const handleSave = useCallback(() => {
    setItems(tempItems);
    setIsEditing(false);
    
    // Save to widget props
    if (onPropsChange) {
      onPropsChange({
        items: tempItems,
        ...tempSettings,
      });
    }
  }, [tempItems, tempSettings, onPropsChange]);

  const handleCancel = useCallback(() => {
    setTempItems([...items]);
    setTempSettings(currentSettings);
    setIsEditing(false);
  }, [items, currentSettings]);

  const addItem = useCallback(() => {
    const newItem: ProgressItem = {
      id: Date.now().toString(),
      label: `Task ${tempItems.length + 1}`,
      value: 50,
      maxValue: 100,
      color: '#3b82f6',
    };
    setTempItems(prev => [...prev, newItem]);
  }, [tempItems.length]);

  const removeItem = useCallback((id: string) => {
    setTempItems(prev => prev.filter(item => item.id !== id));
  }, []);

  const updateItem = useCallback((id: string, updates: Partial<ProgressItem>) => {
    setTempItems(prev => prev.map(item => 
      item.id === id ? { ...item, ...updates } : item
    ));
  }, []);

  const getPercentage = useCallback((value: number, maxValue: number): number => {
    return Math.min(Math.max((value / maxValue) * 100, 0), 100);
  }, []);

  const sizeClasses = {
    sm: { height: 'h-2', text: 'text-sm', spacing: 'space-y-2' },
    md: { height: 'h-3', text: 'text-base', spacing: 'space-y-3' },
    lg: { height: 'h-4', text: 'text-lg', spacing: 'space-y-4' },
  };

  const typeOptions = [
    { value: 'linear', label: 'Linear Progress' },
    { value: 'circular', label: 'Circular Progress' },
  ];

  const sizeOptions = [
    { value: 'sm', label: 'Small' },
    { value: 'md', label: 'Medium' },
    { value: 'lg', label: 'Large' },
  ];

  const colorOptions = [
    { value: '#3b82f6', label: 'Blue' },
    { value: '#10b981', label: 'Green' },
    { value: '#f59e0b', label: 'Yellow' },
    { value: '#ef4444', label: 'Red' },
    { value: '#8b5cf6', label: 'Purple' },
    { value: '#06b6d4', label: 'Cyan' },
  ];

  if (isEditing) {
    return (
      <div className={`w-full h-full p-4 ${className || ''}`}>
        <div className="h-full flex flex-col">
          <div className="space-y-4 flex-1 overflow-y-auto">
            {/* Settings */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Typography variant="body2" className="mb-2">
                  {t('dashboard:widgets.progress.type', 'Kiểu hiển thị')}
                </Typography>
                <Select
                  value={tempSettings.type}
                  onChange={(value) => setTempSettings(prev => ({ ...prev, type: value as typeof type }))}
                  options={typeOptions}
                  className="w-full"
                />
              </div>
              
              <div>
                <Typography variant="body2" className="mb-2">
                  {t('dashboard:widgets.progress.size', 'Kích thước')}
                </Typography>
                <Select
                  value={tempSettings.size}
                  onChange={(value) => setTempSettings(prev => ({ ...prev, size: value as typeof size }))}
                  options={sizeOptions}
                  className="w-full"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.showPercentage}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, showPercentage: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.progress.showPercentage', 'Hiển thị phần trăm')}
                </Typography>
              </label>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.showValues}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, showValues: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.progress.showValues', 'Hiển thị giá trị')}
                </Typography>
              </label>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.animated}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, animated: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.progress.animated', 'Animation')}
                </Typography>
              </label>
            </div>

            {/* Progress Items */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <Typography variant="body2">
                  {t('dashboard:widgets.progress.items', 'Các mục tiến độ')}
                </Typography>
                <Button variant="ghost" size="sm" onClick={addItem}>
                  {t('dashboard:widgets.progress.addItem', 'Thêm mục')}
                </Button>
              </div>
              
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {tempItems.map((item) => (
                  <div key={item.id} className="border border-border rounded-lg p-3">
                    <div className="grid grid-cols-2 gap-2 mb-2">
                      <Input
                        value={item.label}
                        onChange={(e) => updateItem(item.id, { label: e.target.value })}
                        placeholder="Label"
                        className="text-sm"
                      />
                      <div className="flex gap-1">
                        <Input
                          type="number"
                          value={item.value}
                          onChange={(e) => updateItem(item.id, { value: parseInt(e.target.value) || 0 })}
                          placeholder="Value"
                          className="text-sm"
                          min="0"
                        />
                        <Input
                          type="number"
                          value={item.maxValue}
                          onChange={(e) => updateItem(item.id, { maxValue: parseInt(e.target.value) || 100 })}
                          placeholder="Max"
                          className="text-sm"
                          min="1"
                        />
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <Select
                        value={item.color || '#3b82f6'}
                        onChange={(value) => updateItem(item.id, { color: value as string })}
                        options={colorOptions}
                        className="flex-1 mr-2"
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeItem(item.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        ×
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              {t('common:cancel')}
            </Button>
            <Button variant="primary" size="sm" onClick={handleSave}>
              {t('common:save')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (items.length === 0) {
    return (
      <div 
        className={`w-full h-full p-4 ${className || ''} ${editable ? 'cursor-pointer hover:bg-muted/50 transition-colors' : ''}`}
        onClick={editable ? handleEdit : undefined}
      >
        <div className="h-full flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/30 rounded-lg">
          <Typography variant="body2" className="text-muted-foreground text-center">
            {editable 
              ? t('dashboard:widgets.progress.empty', 'Click để thêm progress items')
              : t('dashboard:widgets.progress.noItems', 'Không có progress items')
            }
          </Typography>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`w-full h-full p-4 relative group ${className || ''}`}
    >
      <div className={`h-full flex flex-col ${sizeClasses[currentSettings.size].spacing}`}>
        {currentSettings.type === 'linear' ? (
          // Linear Progress Bars
          items.map((item) => {
            const percentage = getPercentage(item.value, item.maxValue);
            
            return (
              <div key={item.id} className="space-y-1">
                <div className="flex justify-between items-center">
                  <Typography variant="body2" className={`font-medium ${sizeClasses[currentSettings.size].text}`}>
                    {item.label}
                  </Typography>
                  <div className="flex gap-2 text-sm text-muted-foreground">
                    {currentSettings.showPercentage && (
                      <span>{Math.round(percentage)}%</span>
                    )}
                    {currentSettings.showValues && (
                      <span>{item.value}/{item.maxValue}</span>
                    )}
                  </div>
                </div>
                
                <div className={`w-full bg-muted rounded-full ${sizeClasses[currentSettings.size].height}`}>
                  <div
                    className={`${sizeClasses[currentSettings.size].height} rounded-full ${currentSettings.animated ? 'transition-all duration-1000 ease-out' : ''}`}
                    style={{
                      width: `${percentage}%`,
                      backgroundColor: item.color || '#3b82f6',
                    }}
                  />
                </div>
              </div>
            );
          })
        ) : (
          // Circular Progress
          <div className="grid grid-cols-2 gap-4 h-full">
            {items.slice(0, 4).map((item) => {
              const percentage = getPercentage(item.value, item.maxValue);
              const circumference = 2 * Math.PI * 45;
              const strokeDasharray = circumference;
              const strokeDashoffset = circumference - (percentage / 100) * circumference;
              
              return (
                <div key={item.id} className="flex flex-col items-center justify-center">
                  <div className="relative">
                    <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                      {/* Background circle */}
                      <circle
                        cx="50"
                        cy="50"
                        r="45"
                        stroke="currentColor"
                        strokeWidth="8"
                        fill="transparent"
                        className="text-muted"
                      />
                      {/* Progress circle */}
                      <circle
                        cx="50"
                        cy="50"
                        r="45"
                        stroke={item.color || '#3b82f6'}
                        strokeWidth="8"
                        fill="transparent"
                        strokeDasharray={strokeDasharray}
                        strokeDashoffset={strokeDashoffset}
                        strokeLinecap="round"
                        className={currentSettings.animated ? 'transition-all duration-1000 ease-out' : ''}
                      />
                    </svg>
                    
                    {/* Center text */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center">
                        {currentSettings.showPercentage && (
                          <div className="text-lg font-bold">{Math.round(percentage)}%</div>
                        )}
                        {currentSettings.showValues && (
                          <div className="text-xs text-muted-foreground">
                            {item.value}/{item.maxValue}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <Typography variant="body2" className="mt-2 text-center font-medium">
                    {item.label}
                  </Typography>
                </div>
              );
            })}
          </div>
        )}

        {/* Edit Button */}
        {editable && (
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button variant="secondary" size="sm" onClick={handleEdit}>
              {t('common:edit')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProgressWidget;
