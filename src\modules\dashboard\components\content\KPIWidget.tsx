import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Input, Select, Icon } from '@/shared/components/common';
import { BaseWidgetProps } from '../../types';

interface KPIMetric {
  id: string;
  label: string;
  value: number;
  target?: number;
  previousValue?: number;
  unit?: string;
  prefix?: string;
  suffix?: string;
  color?: string;
  format?: 'number' | 'currency' | 'percentage';
}

interface KPIWidgetProps extends BaseWidgetProps {
  initialMetrics?: KPIMetric[];
  editable?: boolean;
  showTrends?: boolean;
  showTargets?: boolean;
  layout?: 'grid' | 'list';
  compactMode?: boolean;
}

/**
 * Widget hiển thị KPI metrics với trend indicators
 */
const KPIWidget: React.FC<KPIWidgetProps> = ({
  className,
  initialMetrics = [
    { id: '1', label: 'Revenue', value: 125000, target: 150000, previousValue: 110000, format: 'currency', color: '#10b981' },
    { id: '2', label: 'Conversion Rate', value: 3.2, target: 4.0, previousValue: 2.8, format: 'percentage', color: '#3b82f6' },
    { id: '3', label: 'Active Users', value: 8420, target: 10000, previousValue: 7890, format: 'number', color: '#f59e0b' },
  ],
  editable = true,
  showTrends = true,
  showTargets = true,
  layout = 'grid',
  compactMode = false,
  onPropsChange,
  ...props
}) => {
  const { t } = useTranslation(['dashboard', 'common']);
  
  // Use values from props if available
  const currentMetrics = (props.metrics as KPIMetric[]) || initialMetrics;
  const currentSettings = {
    showTrends: (props.showTrends as boolean) ?? showTrends,
    showTargets: (props.showTargets as boolean) ?? showTargets,
    layout: (props.layout as typeof layout) || layout,
    compactMode: (props.compactMode as boolean) ?? compactMode,
  };

  const [metrics, setMetrics] = useState<KPIMetric[]>(currentMetrics);
  const [isEditing, setIsEditing] = useState(false);
  const [tempMetrics, setTempMetrics] = useState<KPIMetric[]>(currentMetrics);
  const [tempSettings, setTempSettings] = useState(currentSettings);

  // Sync with props changes
  useEffect(() => {
    const newMetrics = (props.metrics as KPIMetric[]) || initialMetrics;
    if (JSON.stringify(newMetrics) !== JSON.stringify(metrics)) {
      setMetrics(newMetrics);
      setTempMetrics(newMetrics);
    }
  }, [props.metrics, initialMetrics, metrics]);

  const handleEdit = useCallback(() => {
    setTempMetrics([...metrics]);
    setTempSettings(currentSettings);
    setIsEditing(true);
  }, [metrics, currentSettings]);

  const handleSave = useCallback(() => {
    setMetrics(tempMetrics);
    setIsEditing(false);
    
    // Save to widget props
    if (onPropsChange) {
      onPropsChange({
        metrics: tempMetrics,
        ...tempSettings,
      });
    }
  }, [tempMetrics, tempSettings, onPropsChange]);

  const handleCancel = useCallback(() => {
    setTempMetrics([...metrics]);
    setTempSettings(currentSettings);
    setIsEditing(false);
  }, [metrics, currentSettings]);

  const addMetric = useCallback(() => {
    const newMetric: KPIMetric = {
      id: Date.now().toString(),
      label: 'New Metric',
      value: 0,
      target: 100,
      previousValue: 0,
      format: 'number',
      color: '#3b82f6',
    };
    setTempMetrics(prev => [...prev, newMetric]);
  }, []);

  const removeMetric = useCallback((id: string) => {
    setTempMetrics(prev => prev.filter(metric => metric.id !== id));
  }, []);

  const updateMetric = useCallback((id: string, updates: Partial<KPIMetric>) => {
    setTempMetrics(prev => prev.map(metric => 
      metric.id === id ? { ...metric, ...updates } : metric
    ));
  }, []);

  const formatValue = useCallback((value: number, format: KPIMetric['format'], prefix?: string, suffix?: string) => {
    let formatted = '';
    
    switch (format) {
      case 'currency':
        formatted = new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(value);
        break;
      case 'percentage':
        formatted = `${value.toFixed(1)}%`;
        break;
      default:
        formatted = value.toLocaleString();
    }
    
    return `${prefix || ''}${formatted}${suffix || ''}`;
  }, []);

  const getTrendDirection = useCallback((current: number, previous?: number) => {
    if (!previous) return 'neutral';
    if (current > previous) return 'up';
    if (current < previous) return 'down';
    return 'neutral';
  }, []);

  const getTrendPercentage = useCallback((current: number, previous?: number) => {
    if (!previous || previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  }, []);

  const getTrendColor = useCallback((direction: string) => {
    switch (direction) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      default: return 'text-muted-foreground';
    }
  }, []);

  const getTrendIcon = useCallback((direction: string) => {
    switch (direction) {
      case 'up': return 'trending-up';
      case 'down': return 'trending-down';
      default: return 'minus';
    }
  }, []);

  const getTargetProgress = useCallback((value: number, target?: number) => {
    if (!target || target === 0) return 0;
    return Math.min((value / target) * 100, 100);
  }, []);

  const formatOptions = [
    { value: 'number', label: 'Number' },
    { value: 'currency', label: 'Currency' },
    { value: 'percentage', label: 'Percentage' },
  ];

  const layoutOptions = [
    { value: 'grid', label: 'Grid' },
    { value: 'list', label: 'List' },
  ];

  const colorOptions = [
    { value: '#3b82f6', label: 'Blue' },
    { value: '#10b981', label: 'Green' },
    { value: '#f59e0b', label: 'Yellow' },
    { value: '#ef4444', label: 'Red' },
    { value: '#8b5cf6', label: 'Purple' },
    { value: '#06b6d4', label: 'Cyan' },
  ];

  if (isEditing) {
    return (
      <div className={`w-full h-full p-4 ${className || ''}`}>
        <div className="h-full flex flex-col">
          <div className="space-y-4 flex-1 overflow-y-auto">
            {/* Settings */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Typography variant="body2" className="mb-2">
                  {t('dashboard:widgets.kpi.layout', 'Layout')}
                </Typography>
                <Select
                  value={tempSettings.layout}
                  onChange={(value) => setTempSettings(prev => ({ ...prev, layout: value as typeof layout }))}
                  options={layoutOptions}
                  className="w-full"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.showTrends}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, showTrends: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.kpi.showTrends', 'Hiển thị xu hướng')}
                </Typography>
              </label>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.showTargets}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, showTargets: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.kpi.showTargets', 'Hiển thị mục tiêu')}
                </Typography>
              </label>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.compactMode}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, compactMode: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.kpi.compactMode', 'Chế độ compact')}
                </Typography>
              </label>
            </div>

            {/* Metrics */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <Typography variant="body2">
                  {t('dashboard:widgets.kpi.metrics', 'KPI Metrics')}
                </Typography>
                <Button variant="ghost" size="sm" onClick={addMetric}>
                  {t('dashboard:widgets.kpi.addMetric', 'Thêm KPI')}
                </Button>
              </div>
              
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {tempMetrics.map((metric) => (
                  <div key={metric.id} className="border border-border rounded-lg p-3">
                    <div className="grid grid-cols-2 gap-2 mb-2">
                      <Input
                        value={metric.label}
                        onChange={(e) => updateMetric(metric.id, { label: e.target.value })}
                        placeholder="Label"
                        className="text-sm"
                      />
                      <Select
                        value={metric.format || 'number'}
                        onChange={(value) => updateMetric(metric.id, { format: value as KPIMetric['format'] })}
                        options={formatOptions}
                        className="text-sm"
                      />
                    </div>
                    
                    <div className="grid grid-cols-3 gap-2 mb-2">
                      <Input
                        type="number"
                        value={metric.value}
                        onChange={(e) => updateMetric(metric.id, { value: parseFloat(e.target.value) || 0 })}
                        placeholder="Value"
                        className="text-sm"
                      />
                      <Input
                        type="number"
                        value={metric.target || ''}
                        onChange={(e) => updateMetric(metric.id, { target: parseFloat(e.target.value) || undefined })}
                        placeholder="Target"
                        className="text-sm"
                      />
                      <Input
                        type="number"
                        value={metric.previousValue || ''}
                        onChange={(e) => updateMetric(metric.id, { previousValue: parseFloat(e.target.value) || undefined })}
                        placeholder="Previous"
                        className="text-sm"
                      />
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <Select
                        value={metric.color || '#3b82f6'}
                        onChange={(value) => updateMetric(metric.id, { color: value as string })}
                        options={colorOptions}
                        className="flex-1 mr-2"
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeMetric(metric.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        ×
                      </Button>
                    </div>
                  </div>
                ))}
                
                {tempMetrics.length === 0 && (
                  <div className="text-center py-4 text-muted-foreground">
                    {t('dashboard:widgets.kpi.noMetrics', 'Chưa có KPI nào')}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              {t('common:cancel')}
            </Button>
            <Button variant="primary" size="sm" onClick={handleSave}>
              {t('common:save')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (metrics.length === 0) {
    return (
      <div 
        className={`w-full h-full p-4 ${className || ''} ${editable ? 'cursor-pointer hover:bg-muted/50 transition-colors' : ''}`}
        onClick={editable ? handleEdit : undefined}
      >
        <div className="h-full flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/30 rounded-lg">
          <Icon name="bar-chart-3" size="lg" className="text-muted-foreground mb-2" />
          <Typography variant="body2" className="text-muted-foreground text-center">
            {editable 
              ? t('dashboard:widgets.kpi.empty', 'Click để thêm KPI metrics')
              : t('dashboard:widgets.kpi.noData', 'Không có KPI data')
            }
          </Typography>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`w-full h-full p-4 relative group ${className || ''}`}
    >
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <Typography variant="h3" className="font-semibold">
            {t('dashboard:widgets.kpi.title', 'KPI Dashboard')}
          </Typography>
        </div>

        {/* Metrics */}
        <div className={`flex-1 ${currentSettings.layout === 'grid' ? 'grid grid-cols-1 gap-4' : 'space-y-3'} overflow-y-auto`}>
          {metrics.map((metric) => {
            const trendDirection = getTrendDirection(metric.value, metric.previousValue);
            const trendPercentage = getTrendPercentage(metric.value, metric.previousValue);
            const targetProgress = getTargetProgress(metric.value, metric.target);
            
            return (
              <div
                key={metric.id}
                className={`
                  border border-border rounded-lg p-4 
                  ${currentSettings.compactMode ? 'p-3' : 'p-4'}
                  ${currentSettings.layout === 'list' ? 'flex items-center justify-between' : ''}
                `}
              >
                <div className={currentSettings.layout === 'list' ? 'flex-1' : ''}>
                  <div className="flex items-center justify-between mb-2">
                    <Typography variant="body2" className="text-muted-foreground">
                      {metric.label}
                    </Typography>
                    
                    {currentSettings.showTrends && metric.previousValue && (
                      <div className={`flex items-center gap-1 ${getTrendColor(trendDirection)}`}>
                        <Icon name={getTrendIcon(trendDirection)} size="xs" />
                        <span className="text-xs">
                          {trendPercentage > 0 ? '+' : ''}{trendPercentage.toFixed(1)}%
                        </span>
                      </div>
                    )}
                  </div>
                  
                  <div className="mb-2">
                    <Typography
                      variant={currentSettings.compactMode ? "h3" : "h2"}
                      className="font-bold"
                      style={{ color: metric.color }}
                    >
                      {formatValue(metric.value, metric.format, metric.prefix, metric.suffix)}
                    </Typography>
                  </div>
                  
                  {currentSettings.showTargets && metric.target && (
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Target: {formatValue(metric.target, metric.format, metric.prefix, metric.suffix)}</span>
                        <span>{targetProgress.toFixed(0)}%</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-1.5">
                        <div
                          className="h-1.5 rounded-full transition-all duration-300"
                          style={{
                            width: `${targetProgress}%`,
                            backgroundColor: metric.color,
                          }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Edit Button */}
        {editable && (
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button variant="secondary" size="sm" onClick={handleEdit}>
              {t('common:edit')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default KPIWidget;
