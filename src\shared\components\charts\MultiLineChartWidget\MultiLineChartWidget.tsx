import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RefreshCw, TrendingUp } from 'lucide-react';
import { format } from 'date-fns';
import { Card, Typography, Button, DoubleDatePicker, Select, IconCard, Loading } from '@/shared/components/common';
import { LineChart } from '@/shared/components/charts';
import { BaseWidgetProps } from '@/modules/dashboard/types';
import { defaultPeriodFormatter } from '@/shared/utils/periodFormatter';
import type { LucideIcon } from 'lucide-react';

export interface ChartTypeOption {
  value: string;
  label: string;
  icon: LucideIcon;
  color: string;
  dataKey: string;
}

export interface MultiLineChartWidgetProps extends BaseWidgetProps {
  /**
   * Widget title
   */
  title: string;

  /**
   * Chart type options
   */
  chartTypeOptions: ChartTypeOption[];

  /**
   * Hook để fetch data
   */
  useDataHook: (params: any) => {
    data: any;
    isLoading: boolean;
    error: any;
    refetch: () => void;
  };

  /**
   * Translation namespace
   */
  translationNamespace: string;

  /**
   * Default chart type
   */
  defaultChartType: string;

  /**
   * Custom period formatter function
   */
  periodFormatter?: (label: string, t: any) => string;

  /**
   * Widget icon
   */
  icon?: LucideIcon;
}

/**
 * Generic Multi Line Chart Widget - Có thể dùng cho nhiều module
 */
const MultiLineChartWidget: React.FC<MultiLineChartWidgetProps> = ({
  className,
  isLoading: externalLoading = false,
  title,
  chartTypeOptions,
  useDataHook,
  translationNamespace,
  defaultChartType,
  periodFormatter = defaultPeriodFormatter,
  icon: WidgetIcon = TrendingUp,
}) => {
  const { t } = useTranslation(['dashboard', translationNamespace]);

  // State cho selected chart type
  const [selectedType, setSelectedType] = useState<string>(defaultChartType);

  // State cho date range picker
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

  // Tạo query params từ date range
  const baseQueryParams = useMemo(() => {
    const [startDate, endDate] = dateRange;
    return {
      begin: startDate ? format(startDate, 'yyyy-MM-dd') : undefined,
      end: endDate ? format(endDate, 'yyyy-MM-dd') : undefined,
    };
  }, [dateRange]);

  // Lấy dữ liệu cho loại được chọn
  const {
    data: chartData,
    isLoading: internalLoading,
    error,
    refetch,
  } = useDataHook({
    type: selectedType,
    ...baseQueryParams,
  });

  const isLoading = externalLoading || internalLoading;
  const hasError = !!error;

  // Cấu hình đường biểu đồ hiện tại
  const currentConfig = chartTypeOptions.find(opt => opt.value === selectedType);

  // Chuyển đổi dữ liệu từ API thành format cho LineChart
  const processedChartData = useMemo(() => {
    if (!chartData?.data) return [];

    return Object.entries(chartData.data).map(([date, value]) => ({
      date,
      period: periodFormatter(date, t), // Format period để hiển thị đẹp trên trục X
      [currentConfig?.dataKey || 'value']: value, // Sử dụng dataKey từ config
    }));
  }, [chartData?.data, currentConfig?.dataKey, t, periodFormatter]);

  const lines = useMemo(() => {
    if (!currentConfig) return [];

    return [
      {
        dataKey: currentConfig.dataKey,
        name: currentConfig.label,
        color: currentConfig.color,
        strokeWidth: 2,
        showDot: true,
        dotSize: 4,
      },
    ];
  }, [currentConfig]);

  // Xử lý thay đổi selected type
  const handleTypeChange = (value: string | number | string[] | number[]) => {
    if (typeof value === 'string') {
      setSelectedType(value);
    }
  };

  // Xử lý thay đổi date range
  const handleDateRangeChange = (dates: [Date | null, Date | null]) => {
    setDateRange(dates);
  };

  // Xử lý refresh
  const handleRefresh = () => {
    refetch();
  };

  if (hasError) {
    return (
      <Card className={`p-4 h-full flex flex-col ${className || ''}`}>
        <Typography variant="h3" className="mb-4">
          {title}
        </Typography>
        <div className="flex items-center justify-center h-64">
          <Typography variant="body2" className="text-red-500">
            {t('dashboard:widgets.error.loadFailed', 'Không thể tải dữ liệu')}
          </Typography>
        </div>
      </Card>
    );
  }

  return (
    <Card
      className={`p-4 h-full flex flex-col ${className || ''}`}
      allowOverflow={true} // Cho phép tooltip hiển thị ra ngoài
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <WidgetIcon className="w-5 h-5 text-primary" />
          <Typography variant="h3" className="font-semibold">
            {title}
          </Typography>
        </div>

        {/* Controls */}
        <div className="flex items-center gap-3">
          {/* Refresh Button */}
          <Button variant="ghost" size="sm" onClick={handleRefresh}>
            <RefreshCw className="w-4 h-4" />
          </Button>

          {/* Period Info */}
          {chartData?.period && (
            <Typography variant="caption" className="text-muted-foreground">
              {t('dashboard:widgets.period', 'Chu kỳ')}: {periodFormatter(chartData.period, t)}
            </Typography>
          )}
        </div>
      </div>

      {/* Controls Row - Compact */}
      <div className="flex items-end gap-3 mb-4">
        {/* Chart Type Selection - Compact */}
        <div className="flex-1 min-w-0">
          <Select
            value={selectedType}
            onChange={handleTypeChange}
            options={chartTypeOptions.map(option => ({
              value: option.value,
              label: option.label,
            }))}
            placeholder={t('dashboard:widgets.selectType', 'Chọn loại...')}
            size="sm"
            className="w-full"
          />
        </div>

        {/* Date Range Picker - Compact & Centered */}
        <div className="flex items-center justify-center">
          <DoubleDatePicker
            value={dateRange}
            onChange={handleDateRangeChange}
            triggerIcon={<IconCard icon="calendar" size="md" />}
            size="sm"
          />
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <Loading size="lg" />
        </div>
      ) : error ? (
        <div className="flex items-center justify-center h-64 border border-dashed border-red-300 rounded-lg">
          <div className="text-center">
            <Typography variant="body2" className="text-red-500 mb-2">
              ❌ {t('dashboard:widgets.error.loadData', 'Lỗi khi tải dữ liệu')}
            </Typography>
            <Typography variant="caption" className="text-muted-foreground">
              {String(error) || t('dashboard:widgets.error.unknown', 'Lỗi không xác định')}
            </Typography>
          </div>
        </div>
      ) : processedChartData.length === 0 ? (
        <div className="flex items-center justify-center h-64 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
          <div className="text-center">
            <Typography variant="body2" className="text-muted-foreground mb-2">
              📊 {t('dashboard:widgets.chart', 'Biểu đồ')} {currentConfig?.label}
            </Typography>
            <Typography variant="caption" className="text-muted-foreground">
              {t('dashboard:widgets.noData', 'Không có dữ liệu để hiển thị')}
            </Typography>
          </div>
        </div>
      ) : (
        <div
          className="flex-1 min-h-0"
          style={{
            height: 'calc(100% - 160px)',
            overflow: 'visible', // Cho phép tooltip hiển thị ra ngoài
            position: 'relative',
            zIndex: 1
          }}
        >
          <LineChart
            data={processedChartData}
            xAxisKey="period"
            lines={lines}
            height={300}
            showGrid
            showTooltip
            showLegend={false}
          />
        </div>
      )}
    </Card>
  );
};

export default MultiLineChartWidget;
