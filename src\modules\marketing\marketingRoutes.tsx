import React, { Suspense, lazy } from 'react';
import { RouteObject, Navigate } from 'react-router-dom';
import { Loading } from '@/shared/components/common';
import i18n from '@/lib/i18n';
import MarketingSubSidebarLayout from './components/layout/MarketingSubSidebarLayout';
import ZaloZNSTemplatesPage from './pages/resources/ZaloZNSTemplatesPage';
import MediaResourcesPage from './pages/zalo/MediaResourcesPage';

// Helper function để tạo marketing route với MarketingSubSidebarLayout tối ưu hóa
const createMarketingRoute = (title: string, component: React.ComponentType) => (
  <MarketingSubSidebarLayout title={title}>
    <Suspense fallback={<Loading />}>{React.createElement(component)}</Suspense>
  </MarketingSubSidebarLayout>
);

// Lazy load pages
const MarketingPage = lazy(() => import('./pages/MarketingPage'));
const AudiencePage = lazy(() => import('./pages/AudiencePage'));
const SegmentPage = lazy(() => import('./pages/SegmentPage'));
const CustomFieldsPage = lazy(() => import('./pages/CustomFieldsPage'));
const TagManagementPage = lazy(() => import('./pages/TagManagementPage'));
const ReportsPage = lazy(() => import('./pages/ReportsPage'));
const TemplateEmailPage = lazy(() => import('./pages/TemplateEmailPage'));
const SmsMarketingPage = lazy(() => import('./pages/SmsMarketingPage'));

const GoogleAdsPage = lazy(() => import('./pages/GoogleAdsPage'));
const GoogleAdsAccountsPage = lazy(() => import('./pages/google-ads/GoogleAdsAccountsPage'));
const GoogleAdsCampaignsPage = lazy(() => import('./pages/google-ads/GoogleAdsCampaignsPage'));
const GoogleAdsKeywordsPage = lazy(() => import('./pages/google-ads/GoogleAdsKeywordsPage'));
const GoogleAdsAdsPage = lazy(() => import('./pages/google-ads/GoogleAdsAdsPage'));
const GoogleAdsReportsPage = lazy(() => import('./pages/google-ads/GoogleAdsReportsPage'));
const GoogleAdsSettingsPage = lazy(() => import('./pages/google-ads/GoogleAdsSettingsPage'));
const FacebookAdsPage = lazy(() => import('./pages/FacebookAdsPage'));
const FacebookAccountsPage = lazy(() => import('./pages/facebook-ads/FacebookAccountsPage'));
const FacebookCampaignsPage = lazy(() => import('./pages/facebook-ads/FacebookCampaignsPage'));
const CreateCampaignPage = lazy(() => import('./pages/facebook-ads/CreateCampaignPage'));
const FacebookAnalyticsPage = lazy(() => import('./pages/facebook-ads/FacebookAnalyticsPage'));
const FacebookAuthCallbackPage = lazy(
  () => import('./pages/facebook-ads/FacebookAuthCallbackPage')
);

// Facebook pages
const FacebookPage = lazy(() => import('./pages/FacebookPage'));
const FacebookPagesPage = lazy(() => import('./pages/FacebookPagesPage'));
const InstagramBusinessPage = lazy(() => import('./pages/InstagramBusinessPage'));
const FacebookPixelPage = lazy(() => import('./pages/FacebookPixelPage'));
const FacebookMessengerPage = lazy(() => import('./pages/FacebookMessengerPage'));
const FacebookAnalyticsMainPage = lazy(() => import('./pages/FacebookAnalyticsPage'));

// Zalo pages

const ZaloAccountsPage = lazy(() => import('./pages/zalo/ZaloAccountsPage'));
const ZaloPersonalAccountsPage = lazy(() => import('./pages/zalo/ZaloPersonalAccountsPage'));
const ZaloGroupsPage = lazy(() => import('./pages/zalo/ZaloGroupsPage'));
const ZaloArticlesPage = lazy(() => import('./pages/zalo/ZaloArticlesPage'));
const ZaloFollowersPage = lazy(() => import('./pages/zalo/ZaloFollowersPage'));
const ZaloZnsPage = lazy(() => import('./pages/zalo/ZaloZnsPage'));
const ZaloOATemplatesPage = lazy(() => import('./pages/zalo/ZaloOATemplatesPage'));
const ZaloZNSCampaignsPage = lazy(() => import('./pages/zalo/ZaloZNSCampaignsPage'));
const ZaloOAMessagesPage = lazy(() => import('./pages/zalo/ZaloOAMessagesPage'));
const ZaloContentManagementPage = lazy(() => import('./pages/zalo/ZaloContentManagementPage'));
const ZaloOAManagementPage = lazy(() => import('./pages/zalo/ZaloOAManagementPage'));
const ZNSTemplatesPage = lazy(() => import('./pages/zalo/ZNSTemplatesPage'));
const ZNSTemplatePage = lazy(() => import('./pages/zns/ZNSTemplatePage'));
const ZaloPersonalCampaignsPage = lazy(() => import('./pages/zalo/PersonalCampaignsPage'));
const OACampaignsPage = lazy(() => import('./pages/zalo/OACampaignsPage'));
const ZaloChatPage = lazy(() => import('./pages/zalo/ZaloChatPage'));

// ZNS Campaign pages
const ZNSCampaignListPage = lazy(() => import('./pages/zalo/ZNSCampaignListPage'));
const CreateZNSCampaignPage = lazy(() => import('./pages/zalo/CreateZNSCampaignPage'));

// Email pages
const EmailTemplatesPage = lazy(() => import('./pages/email/EmailTemplatesPage'));
const EmailCampaignsPage = lazy(() => import('./pages/email/EmailCampaignsPage'));
const EmailAnalyticsPage = lazy(() => import('./pages/email/EmailAnalyticsPage'));
const EmailAutomationPage = lazy(() => import('./pages/email/automation/EmailAutomationPage'));

// Gmail pages
const GmailPage = lazy(() => import('./components/gmail/GmailPage'));
const GmailConversationDetailPage = lazy(() => import('./components/gmail/GmailConversationDetailPage'));

// Zalo Ads pages
const ZaloAdsOverviewPage = lazy(() => import('./pages/zalo-ads/ZaloAdsOverviewPage'));
const ZaloAdsAccountsPage = lazy(() => import('./pages/zalo-ads/ZaloAdsAccountsPage'));
const ZaloAdsCampaignsPage = lazy(() => import('./pages/zalo-ads/ZaloAdsCampaignsPage'));
const ZaloAdsReportsPage = lazy(() => import('./pages/zalo-ads/ZaloAdsReportsPage'));

// TikTok Ads pages
const TikTokAdsOverviewPage = lazy(() => import('./pages/tiktok-ads/TikTokAdsOverviewPage'));
const TikTokAdsAccountsPage = lazy(() => import('./pages/tiktok-ads/TikTokAdsAccountsPage'));
const TikTokAdsCampaignsPage = lazy(() => import('./pages/tiktok-ads/TikTokAdsCampaignsPage'));
const TikTokAdsCreativesPage = lazy(() => import('./pages/tiktok-ads/TikTokAdsCreativesPage'));
const TikTokAdsAudiencesPage = lazy(() => import('./pages/tiktok-ads/TikTokAdsAudiencesPage'));
const TikTokAdsReportsPage = lazy(() => import('./pages/tiktok-ads/TikTokAdsReportsPage'));
const TikTokAdsSettingsPage = lazy(() => import('./pages/tiktok-ads/TikTokAdsSettingsPage'));

// Zalo Ecosystem
const ZaloEcosystemPage = lazy(() => import('./pages/zalo/ZaloEcosystemPage'));

// Dashboard
const MarketingDashboardPage = lazy(() => import('./pages/MarketingDashboardPage'));

// SMS pages
const SmsSendPage = lazy(() => import('./sms/pages/SmsSendPage'));
const SmsTemplateListPage = lazy(() => import('./sms/pages/SmsTemplateListPage'));
const SmsTemplateCreatePage = lazy(() => import('./sms/pages/SmsTemplateCreatePage'));
const SmsCampaignListPage = lazy(() => import('./sms/pages/SmsCampaignListPage'));

// Resources pages
// const ResourcesPage = lazy(() => import('./pages/ResourcesPage')); // Not used in routes
const EmailTemplatesResourcePage = lazy(() => import('./pages/resources/EmailTemplatesPage'));
const EmailTemplatePreviewPage = lazy(() => import('./pages/resources/EmailTemplatePreviewPage'));
const ImageEditorPage = lazy(() => import('./pages/resources/ImageEditorPage'));
const CampaignAnalyticsPage = lazy(() => import('./pages/resources/CampaignAnalyticsPage'));
const ROICalculatorPage = lazy(() => import('./pages/resources/ROICalculatorPage'));

/**
 * Marketing module routes
 */
const marketingRoutes: RouteObject[] = [
  // Redirect từ /marketing đến module đầu tiên (audience)
  {
    path: '/marketing',
    element: <Navigate to="/marketing/audience" replace />,
  },

  // Trang tổng quan (optional, có thể truy cập qua /marketing/overview)
  {
    path: '/marketing/overview',
    element: createMarketingRoute(i18n.t('marketing:title', 'Marketing'), MarketingPage),
  },

  // Marketing Dashboard
  {
    path: '/marketing/dashboard',
    element: createMarketingRoute(
      i18n.t('marketing:dashboard.title', 'Marketing Dashboard'),
      MarketingDashboardPage
    ),
  },

  // Audience routes
  {
    path: '/marketing/audience',
    element: createMarketingRoute(
      i18n.t('marketing:audience.title', 'Quản lý đối tượng'),
      AudiencePage
    ),
  },

  // Segment routes
  {
    path: '/marketing/segment',
    element: createMarketingRoute(
      i18n.t('marketing:segment.title', 'Quản lý phân đoạn'),
      SegmentPage
    ),
  },

  // Custom Fields routes
  {
    path: '/marketing/custom-fields',
    element: createMarketingRoute(
      i18n.t('marketing:customFields.title', 'Quản lý trường tùy chỉnh'),
      CustomFieldsPage
    ),
  },

  // Tag Management routes
  {
    path: '/marketing/tags',
    element: createMarketingRoute(i18n.t('marketing:tags.title', 'Quản lý thẻ'), TagManagementPage),
  },

  // Reports routes
  {
    path: '/marketing/reports',
    element: createMarketingRoute(i18n.t('marketing:reports.title', 'Báo cáo'), ReportsPage),
  },

  // Template Email routes
  {
    path: '/marketing/template-emails',
    element: createMarketingRoute(
      i18n.t('marketing:templateEmail.title', 'Quản lý mẫu email'),
      TemplateEmailPage
    ),
  },
  {
    path: '/marketing/resources/email-templates',
    element: createMarketingRoute(
      i18n.t('marketing:resources.emailTemplates.title', 'Mẫu Email Marketing'),
      EmailTemplatesResourcePage
    ),
  },
  {
    path: '/marketing/resources/email-templates/preview/:templateId',
    element: createMarketingRoute(
      i18n.t('marketing:resources.emailTemplates.preview.title', 'Xem trước Email Template'),
      EmailTemplatePreviewPage
    ),
  },

  {
    path: '/marketing/resources/image-editor',
    element: createMarketingRoute(
      i18n.t('marketing:resources.imageEditor.title', 'Chỉnh sửa ảnh'),
      ImageEditorPage
    ),
  },
  {
    path: '/marketing/resources/campaign-analytics',
    element: createMarketingRoute(
      i18n.t('marketing:resources.analytics.campaign.title', 'Phân tích chiến dịch'),
      CampaignAnalyticsPage
    ),
  },

  {
    path: '/marketing/resources/roi-calculator',
    element: createMarketingRoute(
      i18n.t('marketing:resources.analytics.roi.title', 'Tính ROI'),
      ROICalculatorPage
    ),
  },

  // SMS Templates route (moved from SMS section to Resources)
  {
    path: '/marketing/resources/sms-templates',
    element: createMarketingRoute(
      i18n.t('marketing:resources.smsTemplates.title', 'Mẫu tin nhắn SMS'),
      SmsTemplateListPage
    ),
  },

  // Zalo ZNS Templates route (moved from Zalo section to Resources)
  {
    path: '/marketing/resources/zalo-zns-templates',
    element: createMarketingRoute(
      i18n.t('marketing:resources.zaloZnsTemplates.title', 'Mẫu tin nhắn ZNS'),
      ZaloZNSTemplatesPage
    ),
  },

  // Zalo OA Templates route (moved from Zalo section to Resources)
  {
    path: '/marketing/resources/zalo-oa-templates',
    element: createMarketingRoute(
      i18n.t('marketing:resources.zaloOaTemplates.title', 'Mẫu tin nhắn OA'),
      ZaloOATemplatesPage
    ),
  },

  // Zalo Content Management route (moved from Zalo section to Resources)
  {
    path: '/marketing/resources/zalo-content',
    element: createMarketingRoute(
      i18n.t('marketing:resources.zaloContent.title', 'Nội dung Zalo của tôi'),
      ZaloContentManagementPage
    ),
  },

  // SMS Marketing routes
  {
    path: '/marketing/sms',
    element: createMarketingRoute(i18n.t('marketing:sms.title', 'SMS Marketing'), SmsMarketingPage),
  },

  // SMS Send routes
  {
    path: '/marketing/sms/send',
    element: createMarketingRoute(
      i18n.t('marketing:sms.send.title', 'Gửi tin nhắn SMS'),
      SmsSendPage
    ),
  },

  // SMS Campaigns routes
  {
    path: '/marketing/sms/campaigns',
    element: createMarketingRoute(
      i18n.t('marketing:sms.campaigns.title', 'Quản lý chiến dịch SMS'),
      SmsCampaignListPage
    ),
  },

  // SMS Templates routes
  {
    path: '/marketing/sms/my-sms-templates',
    element: createMarketingRoute(
      i18n.t('sms:smsTemplate.title', 'Mẫu tin nhắn SMS của tôi'),
      SmsTemplateListPage
    ),
  },

  // SMS Template Create route
  {
    path: '/marketing/sms/my-sms-templates/create',
    element: createMarketingRoute(
      i18n.t('sms:smsTemplate.form.createTitle', 'Tạo mẫu tin nhắn SMS'),
      SmsTemplateCreatePage
    ),
  },

  // SMS Template Edit route
  {
    path: '/marketing/sms/my-sms-templates/edit/:id',
    element: createMarketingRoute(
      i18n.t('sms:smsTemplate.form.editTitle', 'Chỉnh sửa mẫu tin nhắn SMS'),
      SmsTemplateCreatePage // Sử dụng cùng component, sẽ detect edit mode qua URL params
    ),
  },

  // Articles routes (redirect to Zalo content)
  {
    path: '/marketing/articles',
    element: createMarketingRoute(
      i18n.t('marketing:articles.title', 'Quản lý bài viết'),
      ZaloContentManagementPage
    ),
  },

  // Videos routes (redirect to Zalo content)
  {
    path: '/marketing/videos',
    element: createMarketingRoute(
      i18n.t('marketing:videos.title', 'Quản lý video'),
      ZaloContentManagementPage
    ),
  },

  // Google Ads routes
  {
    path: '/marketing/google-ads',
    element: createMarketingRoute(
      i18n.t('marketing:googleAds.title', 'Google Ads'),
      GoogleAdsAccountsPage
    ),
  },

  // Google Ads Detail routes
  {
    path: '/marketing/google-ads/detail',
    element: createMarketingRoute(
      i18n.t('marketing:googleAds.detail.title', 'Chi tiết Google Ads'),
      GoogleAdsPage
    ),
  },

  // Google Ads Accounts routes
  {
    path: '/marketing/google-ads/accounts',
    element: createMarketingRoute(
      i18n.t('marketing:googleAds.accounts.title', 'Tài khoản Google Ads'),
      GoogleAdsAccountsPage
    ),
  },

  // Google Ads Campaigns routes
  {
    path: '/marketing/google-ads/campaigns',
    element: createMarketingRoute(
      i18n.t('marketing:googleAds.campaigns.title', 'Chiến dịch Google Ads'),
      GoogleAdsCampaignsPage
    ),
  },

  // Google Ads Keywords routes
  {
    path: '/marketing/google-ads/keywords',
    element: createMarketingRoute(
      i18n.t('marketing:googleAds.keywords.title', 'Từ khóa Google Ads'),
      GoogleAdsKeywordsPage
    ),
  },

  // Google Ads Ads routes
  {
    path: '/marketing/google-ads/ads',
    element: createMarketingRoute(
      i18n.t('marketing:googleAds.ads.title', 'Quảng cáo Google Ads'),
      GoogleAdsAdsPage
    ),
  },

  // Google Ads Reports routes
  {
    path: '/marketing/google-ads/reports',
    element: createMarketingRoute(
      i18n.t('marketing:googleAds.reports.title', 'Báo cáo Google Ads'),
      GoogleAdsReportsPage
    ),
  },

  // Google Ads Settings routes
  {
    path: '/marketing/google-ads/settings',
    element: createMarketingRoute(
      i18n.t('marketing:googleAds.settings.title', 'Cài đặt Google Ads'),
      GoogleAdsSettingsPage
    ),
  },

  // Facebook Ads routes
  {
    path: '/marketing/facebook-ads',
    element: createMarketingRoute(
      i18n.t('marketing:facebookAds.title', 'Facebook Ads'),
      FacebookAdsPage
    ),
  },

  // Facebook Ads Detail routes
  {
    path: '/marketing/facebook-ads/detail',
    element: createMarketingRoute(
      i18n.t('marketing:facebookAds.detail.title', 'Chi tiết Facebook Ads'),
      FacebookAdsPage
    ),
  },

  // Facebook Ads Accounts routes
  {
    path: '/marketing/facebook-ads/accounts',
    element: createMarketingRoute(
      i18n.t('marketing:facebookAds.accounts.title', 'Tài khoản Facebook Ads'),
      FacebookAccountsPage
    ),
  },

  // Facebook Ads Campaigns routes
  {
    path: '/marketing/facebook-ads/campaigns',
    element: createMarketingRoute(
      i18n.t('marketing:facebookAds.campaigns.title', 'Chiến dịch Facebook Ads'),
      FacebookCampaignsPage
    ),
  },

  // Create Facebook Campaign routes
  {
    path: '/marketing/facebook-ads/campaigns/create',
    element: createMarketingRoute(
      i18n.t('marketing:facebookAds.campaigns.create', 'Tạo chiến dịch Facebook Ads'),
      CreateCampaignPage
    ),
  },

  // Facebook Ads Analytics routes
  {
    path: '/marketing/facebook-ads/analytics',
    element: createMarketingRoute(
      i18n.t('marketing:facebookAds.analytics.title', 'Phân tích Facebook Ads'),
      FacebookAnalyticsPage
    ),
  },

  // Facebook Ads Reports routes
  {
    path: '/marketing/facebook-ads/reports',
    element: createMarketingRoute(
      i18n.t('marketing:facebookAds.reports.title', 'Báo cáo Facebook Ads'),
      FacebookAnalyticsPage // Tạm thời dùng chung component, có thể tạo riêng sau
    ),
  },

  // Facebook Auth Callback routes
  {
    path: '/marketing/facebook-ads/auth/callback',
    element: createMarketingRoute(
      i18n.t('marketing:facebookAds.authCallback.title', 'Xác thực Facebook Ads'),
      FacebookAuthCallbackPage
    ),
  },

  // Facebook main page
  {
    path: '/marketing/facebook',
    element: createMarketingRoute(i18n.t('marketing:facebook.title', 'Facebook'), FacebookPage),
  },

  // Facebook Pages routes
  {
    path: '/marketing/facebook/pages',
    element: createMarketingRoute(
      i18n.t('marketing:facebook.pages.title', 'Facebook Pages'),
      FacebookPagesPage
    ),
  },

  // Instagram Business routes
  {
    path: '/marketing/facebook/instagram',
    element: createMarketingRoute(
      i18n.t('marketing:facebook.instagram.title', 'Instagram Business'),
      InstagramBusinessPage
    ),
  },

  // Facebook Pixel routes
  {
    path: '/marketing/facebook/pixel',
    element: createMarketingRoute(
      i18n.t('marketing:facebook.pixel.title', 'Facebook Pixel'),
      FacebookPixelPage
    ),
  },

  // Facebook Messenger routes
  {
    path: '/marketing/facebook/messenger',
    element: createMarketingRoute(
      i18n.t('marketing:facebook.messenger.title', 'Facebook Messenger'),
      FacebookMessengerPage
    ),
  },

  // Facebook Analytics routes
  {
    path: '/marketing/facebook/analytics',
    element: createMarketingRoute(
      i18n.t('marketing:facebook.analytics.title', 'Facebook Analytics'),
      FacebookAnalyticsMainPage
    ),
  },



  // Zalo Ecosystem (alternative path)
  {
    path: '/marketing/zalo/ecosystem',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.ecosystem.title', 'Zalo Ecosystem'),
      ZaloEcosystemPage
    ),
  },



  // Zalo Accounts
  {
    path: '/marketing/zalo/zalo-oa',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.accounts.title', 'Quản lý Zalo OA'),
      ZaloAccountsPage
    ),
  },

  // Zalo Personal Accounts (alternative route)
  {
    path: '/marketing/zalo/zalo-personal',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.modules.personalAccount.title', 'Tài khoản cá nhân'),
      ZaloPersonalAccountsPage
    ),
  },



  // Zalo Personal Campaigns
  {
    path: '/marketing/zalo/personal-campaigns',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.personalCampaigns.title', 'Chiến dịch Zalo cá nhân'),
      ZaloPersonalCampaignsPage
    ),
  },

  // Zalo OA Campaigns
  {
    path: '/marketing/zalo/oa-campaigns',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.oaCampaigns.title', 'Chiến dịch OA'),
      OACampaignsPage
    ),
  },

  // Zalo Groups
  {
    path: '/marketing/zalo/groups',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.groups.title', 'Quản lý nhóm'),
      ZaloGroupsPage
    ),
  },

  // Zalo Followers
  {
    path: '/marketing/zalo/accounts/:oaId/followers',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.followers.title', 'Quản lý Followers'),
      ZaloFollowersPage
    ),
  },

  // ZNS Campaigns List
  {
    path: '/marketing/zalo/zns/campaigns',
    element: createMarketingRoute(
      i18n.t('marketing:zns.campaign.list.title', 'Danh sách chiến dịch ZNS'),
      ZNSCampaignListPage
    ),
  },

  // Create ZNS Campaign
  {
    path: '/marketing/zalo/zns/campaigns/create',
    element: createMarketingRoute(
      i18n.t('marketing:zns.campaign.create.title', 'Tạo chiến dịch ZNS'),
      CreateZNSCampaignPage
    ),
  },

  // Zalo ZNS Campaigns
  {
    path: '/marketing/zalo/zns-campaigns',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.znsCampaigns.title', 'Chiến dịch ZNS'),
      ZaloZNSCampaignsPage
    ),
  },

  // Zalo OA Messages
  {
    path: '/marketing/zalo/oa-messages',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.oaMessages.title', 'Tin nhắn OA'),
      ZaloOAMessagesPage
    ),
  },

  // Zalo Followers (general route)
  {
    path: '/marketing/zalo/followers',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.followers.title', 'Quản lý Followers'),
      ZaloFollowersPage
    ),
  },

  // Zalo Analytics
  {
    path: '/marketing/zalo/analytics',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.analytics.title', 'Phân tích Zalo'),
      ZaloAccountsPage
    ),
  },

  // Zalo Articles
  {
    path: '/marketing/zalo/articles',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.articles.title', 'Quản lý bài viết Zalo'),
      ZaloArticlesPage
    ),
  },

  // ZNS Templates
  {
    path: '/marketing/zalo/zns-templates',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.znsTemplates.title', 'ZNS Templates'),
      ZNSTemplatesPage
    ),
  },

  // ZNS Template (fallback for import errors)
  {
    path: '/marketing/zalo/zns-template',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.znsTemplates.title', 'ZNS Templates'),
      ZNSTemplatePage
    ),
  },

  // Media Resources
  {
    path: '/marketing/zalo/media-resources',
    element: createMarketingRoute(
      i18n.t('marketing:mediaResources.title', 'Tài nguyên Media'),
      MediaResourcesPage
    ),
  },

  // Zalo Chat
  {
    path: '/marketing/zalo/chat',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.chat.title', 'Chat'),
      ZaloChatPage
    ),
  },

  // Zalo Videos (redirect to content)
  {
    path: '/marketing/zalo/videos',
    element: createMarketingRoute(
      i18n.t('marketing:videos.title', 'Quản lý video'),
      ZaloContentManagementPage
    ),
  },

  // Zalo OA Management - Quản lý từng OA cụ thể
  {
    path: '/marketing/zalo/oa/:oaId',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.oa.management.title', 'Quản lý Zalo OA'),
      ZaloOAManagementPage
    ),
  },

  // Email Campaigns - Sử dụng trực tiếp cho /marketing/email
  {
    path: '/marketing/email/email-campaigns',
    element: createMarketingRoute(
      i18n.t('marketing:email.campaigns.title', 'Chiến dịch Email'),
      EmailCampaignsPage
    ),
  },
  // Email Templates Resource
  {
    path: '/marketing/resources/my-email-templates',
    element: createMarketingRoute(
      i18n.t('marketing:email.templates.title', 'Email Templates của tôi'),
      EmailTemplatesPage
    ),
  },

  // Gmail routes
  {
    path: '/marketing/gmail',
    element: createMarketingRoute(
      i18n.t('marketing:gmail.title', 'Gmail'),
      GmailPage
    ),
  },

  // Gmail Conversation Detail routes
  {
    path: '/marketing/gmail/conversation/:integrationId/:conversationId',
    element: createMarketingRoute(
      i18n.t('gmailMarketing:conversation.detail', 'Chi tiết cuộc trò chuyện'),
      GmailConversationDetailPage
    ),
  },

  // SMS Templates Resource
  {
    path: '/marketing/resources/my-sms-templates',
    element: createMarketingRoute(
      i18n.t('marketing:sms.templates.title', 'SMS Templates của tôi'),
      SmsTemplateListPage
    ),
  },

  // Zalo ZNS Templates Resource
  {
    path: '/marketing/zalo/my-zalo-zns-templates',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.modules.znsTemplates.title', 'Zalo ZNS Templates của tôi'),
      ZaloZnsPage
    ),
  },

  // Zalo OA Templates Resource
  {
    path: '/marketing/resources/my-zalo-oa-templates',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.modules.oaTemplates.title', 'Zalo OA Templates của tôi'),
      ZaloOATemplatesPage
    ),
  },

  // Zalo Content Resource
  {
    path: '/marketing/resources/my-zalo-content',
    element: createMarketingRoute(
      i18n.t('marketing:zalo.modules.content.title', 'Nội dung Zalo của tôi'),
      ZaloContentManagementPage
    ),
  },

  // Email Analytics
  {
    path: '/marketing/email/analytics',
    element: createMarketingRoute(
      i18n.t('marketing:email.analytics.title', 'Phân tích Email'),
      EmailAnalyticsPage
    ),
  },

  // Email Automation
  {
    path: '/marketing/email/automation',
    element: createMarketingRoute(
      i18n.t('marketing:email.automation.title', 'Tự động hóa Email'),
      EmailAutomationPage
    ),
  },

  // Zalo Ads Overview
  {
    path: '/marketing/zalo-ads',
    element: createMarketingRoute(
      i18n.t('marketing:zaloAds.title', 'Zalo Ads'),
      ZaloAdsOverviewPage
    ),
  },

  // Zalo Ads Overview (alternative path)
  {
    path: '/marketing/zalo-ads/overview',
    element: createMarketingRoute(
      i18n.t('marketing:zaloAds.overview.title', 'Zalo Ads Overview'),
      ZaloAdsOverviewPage
    ),
  },

  // Zalo Ads Accounts
  {
    path: '/marketing/zalo-ads/accounts',
    element: createMarketingRoute(
      i18n.t('marketing:zaloAds.accounts.title', 'Quản lý tài khoản Zalo Ads'),
      ZaloAdsAccountsPage
    ),
  },

  // Zalo Ads Campaigns
  {
    path: '/marketing/zalo-ads/campaigns',
    element: createMarketingRoute(
      i18n.t('marketing:zaloAds.campaigns.title', 'Quản lý chiến dịch Zalo Ads'),
      ZaloAdsCampaignsPage
    ),
  },

  // Zalo Ads Reports
  {
    path: '/marketing/zalo-ads/reports',
    element: createMarketingRoute(
      i18n.t('marketing:zaloAds.reports.title', 'Báo cáo Zalo Ads'),
      ZaloAdsReportsPage
    ),
  },

  // TikTok Ads Overview
  {
    path: '/marketing/tiktok-ads',
    element: createMarketingRoute(
      i18n.t('marketing:tiktokAds.title', 'TikTok Ads'),
      TikTokAdsOverviewPage
    ),
  },

  // TikTok Ads Overview (alternative path)
  {
    path: '/marketing/tiktok-ads/overview',
    element: createMarketingRoute(
      i18n.t('marketing:tiktokAds.overview.title', 'TikTok Ads Overview'),
      TikTokAdsOverviewPage
    ),
  },

  // TikTok Ads Accounts
  {
    path: '/marketing/tiktok-ads/accounts',
    element: createMarketingRoute(
      i18n.t('marketing:tiktokAds.accounts.title', 'TikTok Ads Accounts'),
      TikTokAdsAccountsPage
    ),
  },

  // TikTok Ads Campaigns
  {
    path: '/marketing/tiktok-ads/campaigns',
    element: createMarketingRoute(
      i18n.t('marketing:tiktokAds.campaigns.title', 'TikTok Ads Campaigns'),
      TikTokAdsCampaignsPage
    ),
  },

  // TikTok Ads Creatives
  {
    path: '/marketing/tiktok-ads/creatives',
    element: createMarketingRoute(
      i18n.t('marketing:tiktokAds.creatives.title', 'TikTok Ads Creatives'),
      TikTokAdsCreativesPage
    ),
  },

  // TikTok Ads Audiences
  {
    path: '/marketing/tiktok-ads/audiences',
    element: createMarketingRoute(
      i18n.t('marketing:tiktokAds.audiences.title', 'TikTok Ads Audiences'),
      TikTokAdsAudiencesPage
    ),
  },

  // TikTok Ads Reports
  {
    path: '/marketing/tiktok-ads/reports',
    element: createMarketingRoute(
      i18n.t('marketing:tiktokAds.reports.title', 'TikTok Ads Reports'),
      TikTokAdsReportsPage
    ),
  },

  // TikTok Ads Settings
  {
    path: '/marketing/tiktok-ads/settings',
    element: createMarketingRoute(
      i18n.t('marketing:tiktokAds.settings.title', 'TikTok Ads Settings'),
      TikTokAdsSettingsPage
    ),
  },
];

export default marketingRoutes;
