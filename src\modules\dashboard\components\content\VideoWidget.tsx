import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Input, Select, Icon } from '@/shared/components/common';
import { BaseWidgetProps } from '../../types';

interface VideoWidgetProps extends BaseWidgetProps {
  initialVideoUrl?: string;
  editable?: boolean;
  autoplay?: boolean;
  controls?: boolean;
  muted?: boolean;
  loop?: boolean;
  aspectRatio?: '16:9' | '4:3' | '1:1' | '21:9';
}

/**
 * Widget hiển thị video từ YouTube, Vimeo hoặc URL trực tiếp
 */
const VideoWidget: React.FC<VideoWidgetProps> = ({
  className,
  initialVideoUrl = '',
  editable = true,
  autoplay = false,
  controls = true,
  muted = false,
  loop = false,
  aspectRatio = '16:9',
  onPropsChange,
  ...props
}) => {
  const { t } = useTranslation(['dashboard', 'common']);
  
  // Use videoUrl from props if available, otherwise use initialVideoUrl
  const currentVideoUrl = (props.videoUrl as string) || initialVideoUrl;
  const currentSettings = {
    autoplay: (props.autoplay as boolean) ?? autoplay,
    controls: (props.controls as boolean) ?? controls,
    muted: (props.muted as boolean) ?? muted,
    loop: (props.loop as boolean) ?? loop,
    aspectRatio: (props.aspectRatio as typeof aspectRatio) || aspectRatio,
  };

  const [videoUrl, setVideoUrl] = useState(currentVideoUrl);
  const [isEditing, setIsEditing] = useState(false);
  const [tempUrl, setTempUrl] = useState(videoUrl);
  const [tempSettings, setTempSettings] = useState(currentSettings);
  const [error, setError] = useState<string | null>(null);

  // Sync with props changes
  useEffect(() => {
    const newVideoUrl = (props.videoUrl as string) || initialVideoUrl;
    if (newVideoUrl !== videoUrl) {
      setVideoUrl(newVideoUrl);
      setTempUrl(newVideoUrl);
    }
  }, [props.videoUrl, initialVideoUrl, videoUrl]);

  const handleEdit = useCallback(() => {
    setTempUrl(videoUrl);
    setTempSettings(currentSettings);
    setIsEditing(true);
    setError(null);
  }, [videoUrl, currentSettings]);

  const handleSave = useCallback(() => {
    if (tempUrl.trim() === '') {
      setError(t('dashboard:widgets.video.emptyError', 'URL video không được để trống'));
      return;
    }

    // Basic URL validation
    if (!isValidVideoUrl(tempUrl)) {
      setError(t('dashboard:widgets.video.invalidUrl', 'URL video không hợp lệ'));
      return;
    }

    setVideoUrl(tempUrl);
    setError(null);
    setIsEditing(false);
    
    // Save to widget props
    if (onPropsChange) {
      onPropsChange({
        videoUrl: tempUrl,
        ...tempSettings,
      });
    }
  }, [tempUrl, tempSettings, onPropsChange, t]);

  const handleCancel = useCallback(() => {
    setTempUrl(videoUrl);
    setTempSettings(currentSettings);
    setError(null);
    setIsEditing(false);
  }, [videoUrl, currentSettings]);

  const isValidVideoUrl = (url: string): boolean => {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();
      
      // Support YouTube, Vimeo, and direct video files
      return (
        hostname.includes('youtube.com') ||
        hostname.includes('youtu.be') ||
        hostname.includes('vimeo.com') ||
        url.match(/\.(mp4|webm|ogg|mov|avi)(\?.*)?$/i) !== null
      );
    } catch {
      return false;
    }
  };

  const getEmbedUrl = (url: string): string => {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();

      // YouTube
      if (hostname.includes('youtube.com')) {
        const videoId = urlObj.searchParams.get('v');
        if (videoId) {
          const params = new URLSearchParams();
          if (tempSettings.autoplay) params.set('autoplay', '1');
          if (tempSettings.muted) params.set('mute', '1');
          if (tempSettings.loop) params.set('loop', '1');
          if (!tempSettings.controls) params.set('controls', '0');
          
          return `https://www.youtube.com/embed/${videoId}?${params.toString()}`;
        }
      }

      // YouTube short URL
      if (hostname.includes('youtu.be')) {
        const videoId = urlObj.pathname.slice(1);
        if (videoId) {
          const params = new URLSearchParams();
          if (tempSettings.autoplay) params.set('autoplay', '1');
          if (tempSettings.muted) params.set('mute', '1');
          if (tempSettings.loop) params.set('loop', '1');
          if (!tempSettings.controls) params.set('controls', '0');
          
          return `https://www.youtube.com/embed/${videoId}?${params.toString()}`;
        }
      }

      // Vimeo
      if (hostname.includes('vimeo.com')) {
        const videoId = urlObj.pathname.split('/').pop();
        if (videoId) {
          const params = new URLSearchParams();
          if (tempSettings.autoplay) params.set('autoplay', '1');
          if (tempSettings.muted) params.set('muted', '1');
          if (tempSettings.loop) params.set('loop', '1');
          if (!tempSettings.controls) params.set('controls', '0');
          
          return `https://player.vimeo.com/video/${videoId}?${params.toString()}`;
        }
      }

      // Direct video URL
      return url;
    } catch {
      return url;
    }
  };

  const aspectRatioClasses = {
    '16:9': 'aspect-video',
    '4:3': 'aspect-[4/3]',
    '1:1': 'aspect-square',
    '21:9': 'aspect-[21/9]',
  };

  const aspectRatioOptions = [
    { value: '16:9', label: '16:9 (Widescreen)' },
    { value: '4:3', label: '4:3 (Standard)' },
    { value: '1:1', label: '1:1 (Square)' },
    { value: '21:9', label: '21:9 (Ultrawide)' },
  ];

  const commonVideoUrls = [
    { label: 'YouTube Demo', url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ' },
    { label: 'Vimeo Demo', url: 'https://vimeo.com/148751763' },
  ];

  if (isEditing) {
    return (
      <div className={`w-full h-full p-4 ${className || ''}`}>
        <div className="h-full flex flex-col">
          <div className="space-y-4 flex-1">
            <div>
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.video.urlLabel', 'URL Video')}
              </Typography>
              <Input
                value={tempUrl}
                onChange={(e) => setTempUrl(e.target.value)}
                placeholder={t('dashboard:widgets.video.urlPlaceholder', 'https://youtube.com/watch?v=...')}
                className="w-full"
              />
              {error && (
                <Typography variant="caption" className="text-destructive mt-1">
                  {error}
                </Typography>
              )}
            </div>

            <div>
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.video.aspectRatio', 'Tỷ lệ khung hình')}
              </Typography>
              <Select
                value={tempSettings.aspectRatio}
                onChange={(value) => setTempSettings(prev => ({ ...prev, aspectRatio: value as typeof aspectRatio }))}
                options={aspectRatioOptions}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Typography variant="body2">
                {t('dashboard:widgets.video.settings', 'Cài đặt')}
              </Typography>
              
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.autoplay}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, autoplay: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.video.autoplay', 'Tự động phát')}
                </Typography>
              </label>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.controls}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, controls: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.video.controls', 'Hiển thị controls')}
                </Typography>
              </label>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.muted}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, muted: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.video.muted', 'Tắt tiếng')}
                </Typography>
              </label>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={tempSettings.loop}
                  onChange={(e) => setTempSettings(prev => ({ ...prev, loop: e.target.checked }))}
                  className="rounded"
                />
                <Typography variant="body2">
                  {t('dashboard:widgets.video.loop', 'Lặp lại')}
                </Typography>
              </label>
            </div>

            <div>
              <Typography variant="body2" className="mb-2">
                {t('dashboard:widgets.video.commonUrls', 'URL phổ biến')}
              </Typography>
              <div className="grid grid-cols-1 gap-2">
                {commonVideoUrls.map((item, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="sm"
                    onClick={() => setTempUrl(item.url)}
                    className="justify-start text-left"
                  >
                    {item.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              {t('common:cancel')}
            </Button>
            <Button variant="primary" size="sm" onClick={handleSave}>
              {t('common:save')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!videoUrl) {
    return (
      <div 
        className={`w-full h-full p-4 ${className || ''} ${editable ? 'cursor-pointer hover:bg-muted/50 transition-colors' : ''}`}
        onClick={editable ? handleEdit : undefined}
      >
        <div className="h-full flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/30 rounded-lg">
          <Icon name="play-circle" size="lg" className="text-muted-foreground mb-2" />
          <Typography variant="body2" className="text-muted-foreground text-center">
            {editable 
              ? t('dashboard:widgets.video.empty', 'Click để thêm video')
              : t('dashboard:widgets.video.noVideo', 'Không có video')
            }
          </Typography>
        </div>
      </div>
    );
  }

  const embedUrl = getEmbedUrl(videoUrl);
  const isDirectVideo = !embedUrl.includes('youtube.com') && !embedUrl.includes('vimeo.com');

  return (
    <div 
      className={`w-full h-full relative group ${className || ''}`}
    >
      <div className={`w-full h-full ${aspectRatioClasses[currentSettings.aspectRatio]} overflow-hidden rounded-lg`}>
        {isDirectVideo ? (
          <video
            src={embedUrl}
            controls={currentSettings.controls}
            autoPlay={currentSettings.autoplay}
            muted={currentSettings.muted}
            loop={currentSettings.loop}
            className="w-full h-full object-cover"
          />
        ) : (
          <iframe
            src={embedUrl}
            title="Video Widget"
            className="w-full h-full border-0"
            allowFullScreen
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          />
        )}
      </div>
      
      {editable && (
        <div className="absolute inset-0 bg-transparent opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="absolute top-2 right-2">
            <Button variant="secondary" size="sm" onClick={handleEdit}>
              {t('common:edit')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoWidget;
