/**
 * useThreadState Hook
 * Centralized thread state management using React Query
 * Replaces manual thread loading logic to prevent duplicate API calls
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAppDispatch, useAppSelector } from '@/shared/store';
import { selectCurrentThreadId, setCurrentThread, clearCurrentThread } from '@/shared/store/slices/threadSlice';
import { ThreadsService } from '@/modules/threads/services/threads.service';
import { THREADS_QUERY_KEYS } from '@/modules/threads/constants/threads-query-keys';
import type { ThreadData } from '@/shared/types';


export interface UseThreadStateConfig {
  /**
   * Whether to auto-load first thread if no current thread exists
   */
  autoLoadFirstThread?: boolean;
  
  /**
   * Whether to enable the hook (useful for conditional loading)
   */
  enabled?: boolean;
}

export interface UseThreadStateReturn {
  /**
   * Current thread from Redux state
   */
  currentThread: ThreadData | null;
  
  /**
   * Current thread ID from Redux state
   */
  currentThreadId: string | null;
  
  /**
   * Whether thread data is loading
   */
  isLoading: boolean;
  
  /**
   * Error if thread loading failed
   */
  error: Error | null;

  /**
   * Whether in new chat mode (prevents auto-loading first thread)
   */
  isNewChatMode: boolean;

  // Loading states for better UX
  /**
   * Whether loading first thread
   */
  isLoadingFirstThread: boolean;

  /**
   * Whether switching thread
   */
  isSwitchingThread: boolean;

  /**
   * Whether clearing thread
   */
  isClearingThread: boolean;

  /**
   * Switch to a specific thread
   */
  switchToThread: (threadId: string, threadName?: string) => Promise<void>;
  
  /**
   * Clear current thread
   */
  clearCurrentThread: () => Promise<void>;
  
  /**
   * Refresh thread data
   */
  refreshThread: () => Promise<void>;
  
  /**
   * Load first available thread
   */
  loadFirstThread: () => Promise<void>;
}

/**
 * Centralized thread state management hook
 * Uses React Query for caching and deduplication
 */
export const useThreadState = (config: UseThreadStateConfig = {}): UseThreadStateReturn => {
  const {
    autoLoadFirstThread = true,
    enabled = true
  } = config;

  const dispatch = useAppDispatch();
  const currentThreadId = useAppSelector(selectCurrentThreadId);
  const queryClient = useQueryClient();

  // ✅ Track if user manually cleared thread (new chat mode)
  const isNewChatModeRef = useRef(false);

  // Loading states for better UX
  const [isSwitchingThread, setIsSwitchingThread] = useState(false);
  const [isClearingThread, setIsClearingThread] = useState(false);

  // ✅ Cleanup on unmount
  useEffect(() => {
    return () => {
      // Reset loading states on unmount
      setIsSwitchingThread(false);
      setIsClearingThread(false);
    };
  }, []);

  // ✅ Track query enabled condition
  const queryEnabled = enabled && autoLoadFirstThread && !currentThreadId && !isNewChatModeRef.current;

  // ✅ Use React Query to get first available thread
  const firstThreadQuery = useQuery({
    queryKey: ['threads', 'first-available'],
    queryFn: async () => {
      const response = await ThreadsService.getThreads({
        page: 1,
        limit: 1,
        sortBy: 'updatedAt',
        sortDirection: 'DESC'
      });

      return response.items?.[0] || null;
    },
    enabled: queryEnabled,
    // ✅ OPTIMIZATION: Caching settings to prevent duplicate API calls
    staleTime: 5 * 60 * 1000, // 5 minutes - thread list stays fresh
    gcTime: 10 * 60 * 1000, // 10 minutes - cache persists
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: true,
    retry: (failureCount, error) => {
      // Retry up to 3 times for network errors, but not for 4xx errors
      if (failureCount >= 3) return false;

      // Don't retry for client errors (4xx)
      if (error && typeof error === 'object' && 'status' in error) {
        const status = (error as any).status;
        if (status >= 400 && status < 500) return false;
      }

      return true;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000) // Exponential backoff
  });

  // ✅ Auto-set current thread when first thread is loaded (only if not in new chat mode)
  useEffect(() => {
    const firstThread = firstThreadQuery.data;
    const shouldAutoLoad = firstThread && !currentThreadId && firstThreadQuery.isSuccess && !isNewChatModeRef.current;

    if (shouldAutoLoad) {
      dispatch(setCurrentThread({
        threadId: firstThread.id,
        threadName: firstThread.title
      }));
    }
  }, [firstThreadQuery.data, firstThreadQuery.isSuccess, currentThreadId, dispatch]);

  // ✅ Get current thread details if we have a thread ID
  const currentThreadQuery = useQuery({
    queryKey: THREADS_QUERY_KEYS.DETAIL(currentThreadId!),
    queryFn: () => ThreadsService.getThreadDetail(currentThreadId!),
    enabled: !!currentThreadId,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    retry: 1
  });

  // Switch to thread
  const switchToThread = useCallback(async (threadId: string, threadName?: string) => {
    // Validate input
    if (!threadId || typeof threadId !== 'string' || threadId.trim() === '') {
      throw new Error('Thread ID is required and must be a non-empty string');
    }

    setIsSwitchingThread(true);

    try {
      // ✅ Reset new chat mode when switching to existing thread
      isNewChatModeRef.current = false;
      dispatch(setCurrentThread({
        threadId: threadId.trim(),
        threadName: threadName || `Thread ${threadId}`
      }));
    } catch (error) {
      throw error; // Re-throw to let caller handle
    } finally {
      setIsSwitchingThread(false);
    }
  }, [dispatch]);

  // Clear current thread
  const clearCurrentThreadAction = useCallback(async () => {
    setIsClearingThread(true);

    try {
      // ✅ CRITICAL: Mark as new chat mode BEFORE dispatching to prevent race condition
      isNewChatModeRef.current = true;

      // ✅ Remove the query from cache to prevent API call
      try {
        queryClient.removeQueries({ queryKey: ['threads', 'first-available'] });
      } catch (cacheError) {
        // Cache cleanup failure shouldn't block the operation
      }

      dispatch(clearCurrentThread());
    } catch (error) {
      // Reset new chat mode if clearing failed
      isNewChatModeRef.current = false;
      throw error; // Re-throw to let caller handle
    } finally {
      setIsClearingThread(false);
    }
  }, [dispatch, queryClient, currentThreadId]);

  // Refresh thread data
  const refreshThread = useCallback(async () => {
    if (currentThreadId) {
      await currentThreadQuery.refetch();
    }
    await firstThreadQuery.refetch();
  }, [currentThreadId, currentThreadQuery, firstThreadQuery]);

  // Load first thread manually
  const loadFirstThread = useCallback(async () => {
    const result = await firstThreadQuery.refetch();
    const firstThread = result.data;
    
    if (firstThread) {
      dispatch(setCurrentThread({
        threadId: firstThread.id,
        threadName: firstThread.title
      }));
    }
  }, [firstThreadQuery, dispatch]);

  return {
    // State
    currentThread: currentThreadQuery.data || null,
    currentThreadId,
    isLoading: firstThreadQuery.isLoading || currentThreadQuery.isLoading,
    error: (firstThreadQuery.error || currentThreadQuery.error) as Error | null,
    isNewChatMode: isNewChatModeRef.current, // ✅ Expose new chat mode flag

    // Loading states for better UX
    isLoadingFirstThread: firstThreadQuery.isLoading,
    isSwitchingThread,
    isClearingThread,

    // Actions
    switchToThread,
    clearCurrentThread: clearCurrentThreadAction,
    refreshThread,
    loadFirstThread
  };
};
