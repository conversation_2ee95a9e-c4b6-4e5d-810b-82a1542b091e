import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Calendar, RefreshCw, TrendingUp, ShoppingCart, BarChart3 } from 'lucide-react';
import { format } from 'date-fns';
import { Card, Typography, Button, DoubleDatePicker, Select } from '@/shared/components/common';
import { LineChart } from '@/shared/components/charts';
import { useLineChart } from '@/modules/business/hooks/useReportQuery';
import { BusinessReportTypeEnum } from '@/modules/business/types/report.types';
import { BaseWidgetProps } from '../../types';

/**
 * Chart type options cho Universal Line Chart Widget
 */
const CHART_TYPE_OPTIONS = [
  {
    value: BusinessReportTypeEnum.REVENUE,
    label: 'Doanh thu',
    icon: TrendingUp,
    color: '#2563eb',
    dataKey: 'revenue',
  },
  {
    value: BusinessReportTypeEnum.ORDER,
    label: 'Đơn hàng',
    icon: ShoppingCart,
    color: '#16a34a',
    dataKey: 'orders',
  },
  {
    value: BusinessReportTypeEnum.AVERAGE_ORDER_VALUE,
    label: 'Giá trị TB đơn hàng',
    icon: BarChart3,
    color: '#dc2626',
    dataKey: 'avgOrderValue',
  },
  {
    value: BusinessReportTypeEnum.CUSTOMER,
    label: 'Khách hàng',
    icon: BarChart3,
    color: '#7c3aed',
    dataKey: 'customers',
  },
  {
    value: BusinessReportTypeEnum.NEW_CUSTOMERS,
    label: 'Khách hàng mới',
    icon: BarChart3,
    color: '#059669',
    dataKey: 'newCustomers',
  },
] as const;

/**
 * Universal Line Chart Widget - Thay thế cho SalesLineChartWidget, OrdersLineChartWidget
 */
const UniversalLineChartWidget: React.FC<BaseWidgetProps> = ({
  className,
  isLoading: externalLoading = false
}) => {
  const { t } = useTranslation(['dashboard', 'business']);

  // State cho chart type selection
  const [selectedType, setSelectedType] = useState<BusinessReportTypeEnum>(
    BusinessReportTypeEnum.REVENUE
  );

  // State cho date range picker
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

  // Tạo query params từ date range và selected type
  const queryParams = useMemo(() => {
    const [startDate, endDate] = dateRange;
    return {
      type: selectedType,
      begin: startDate ? format(startDate, 'yyyy-MM-dd') : undefined,
      end: endDate ? format(endDate, 'yyyy-MM-dd') : undefined,
    };
  }, [selectedType, dateRange]);

  // Lấy dữ liệu từ API line-chart chính
  const { data: chartApiData, isLoading: internalLoading, error, refetch } = useLineChart(queryParams);

  const isLoading = externalLoading || internalLoading;

  // Get current chart config
  const currentChartConfig = useMemo(() => {
    return CHART_TYPE_OPTIONS.find(option => option.value === selectedType) || CHART_TYPE_OPTIONS[0];
  }, [selectedType]);

  // Chuyển đổi dữ liệu từ API thành format cho LineChart component
  const chartData = useMemo(() => {
    if (!chartApiData?.data) return [];

    return Object.entries(chartApiData.data).map(([date, value]) => ({
      date,
      period: date, // Sử dụng date làm period để hiển thị trên trục X
      [currentChartConfig.dataKey]: value,
    }));
  }, [chartApiData?.data, currentChartConfig.dataKey]);

  // Cấu hình đường biểu đồ
  const lines = useMemo(() => [
    {
      dataKey: currentChartConfig.dataKey,
      name: currentChartConfig.label,
      color: currentChartConfig.color,
      strokeWidth: 2,
      showDot: true,
      dotSize: 4,
    }
  ], [currentChartConfig]);

  // Xử lý thay đổi chart type
  const handleTypeChange = (value: string | number | string[] | number[]) => {
    if (typeof value === 'string') {
      setSelectedType(value as BusinessReportTypeEnum);
    }
  };

  // Xử lý thay đổi date range
  const handleDateRangeChange = (dates: [Date | null, Date | null]) => {
    setDateRange(dates);
  };

  // Xử lý refresh
  const handleRefresh = () => {
    refetch();
  };

  // Select options cho dropdown
  const selectOptions = CHART_TYPE_OPTIONS.map(option => ({
    value: option.value,
    label: option.label,
  }));

  if (error) {
    return (
      <Card className={`p-4 h-full flex flex-col ${className || ''}`}>
        <Typography variant="h3" className="mb-4">
          Biểu đồ kinh doanh
        </Typography>
        <div className="flex items-center justify-center h-64">
          <Typography variant="body2" className="text-red-500">
            {t('dashboard:widgets.error.loadFailed', 'Không thể tải dữ liệu')}
          </Typography>
        </div>
      </Card>
    );
  }

  const IconComponent = currentChartConfig.icon;

  return (
    <Card className={`p-4 h-full flex flex-col ${className || ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <IconComponent className="w-5 h-5 text-primary" />
          <Typography variant="h3" className="font-semibold">
            {t('business:report.charts.universal.title', 'Biểu đồ kinh doanh')}
          </Typography>
        </div>
        
        {/* Controls */}
        <div className="flex items-center gap-3">
          {/* Chart Type Selector */}
          <Select
            value={selectedType}
            onChange={handleTypeChange}
            options={selectOptions}
            placeholder="Chọn loại biểu đồ"
            size="sm"
            className="min-w-[160px]"
          />

          {/* Date Range Picker */}
          <DoubleDatePicker
            value={dateRange}
            onChange={handleDateRangeChange}
            triggerIcon={<Calendar className="w-4 h-4" />}
            size="sm"
          />

          {/* Refresh Button */}
          <Button variant="ghost" size="sm" onClick={handleRefresh}>
            <RefreshCw className="w-4 h-4" />
          </Button>
          
          {/* Period Info */}
          {chartApiData?.period && (
            <Typography variant="caption" className="text-muted-foreground">
              {t('dashboard:widgets.period', 'Chu kỳ')}: {chartApiData.period}
            </Typography>
          )}
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <Typography variant="body2" className="text-muted-foreground">
            {t('dashboard:widgets.loading', 'Đang tải...')}
          </Typography>
        </div>
      ) : chartData.length === 0 ? (
        <div className="flex items-center justify-center h-64">
          <Typography variant="body2" className="text-muted-foreground">
            {t('dashboard:widgets.noData', 'Không có dữ liệu')}
          </Typography>
        </div>
      ) : (
        <div className="flex-1 min-h-0" style={{ height: 'calc(100% - 80px)' }}>
          <LineChart
            data={chartData}
            xAxisKey="period"
            lines={lines}
            height={300}
            showGrid
            showTooltip
            showLegend={false}
          />
        </div>
      )}
    </Card>
  );
};

export default UniversalLineChartWidget;
