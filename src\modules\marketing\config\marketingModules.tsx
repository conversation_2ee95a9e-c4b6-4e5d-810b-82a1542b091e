import { useTranslation } from 'react-i18next';
import { ModuleItem } from '@/shared/components/common';

/**
 * Hook để lấy danh sách marketing modules với i18n, routing paths và subModules
 */
export const useMarketingModules = (): ModuleItem[] => {
  const { t } = useTranslation('marketing');

  return [
    {
      id: 'audience',
      title: t('marketing:audience.title'),
      description: t('marketing:audience.description'),
      icon: 'users',
      linkTo: '/marketing/audience',
    },
    {
      id: 'segment',
      title: t('marketing:segment.title'),
      description: t('marketing:segment.description'),
      icon: 'filter',
      linkTo: '/marketing/segment',
    },
    {
      id: 'customFields',
      title: t('marketing:customFields.title', 'Custom Fields'),
      description: t('marketing:customFields.description', 'Manage custom fields for...'),
      icon: 'database',
      linkTo: '/marketing/custom-fields',
    },
    {
      id: 'tags',
      title: t('marketing:tags.title', 'Tag Management'),
      description: t('marketing:tags.description', 'Create and manage tags for...'),
      icon: 'tag',
      linkTo: '/marketing/tags',
    },
    {
      id: 'email',
      title: t('marketing:email.title', 'Email'),
      description: t('marketing:email.description', 'Email'),
      icon: 'mail',
      linkTo: '/marketing/email/email-campaigns',
      hasSubModules: true,
      subModules: [
        {
          id: 'email-campaigns',
          title: t('marketing:email.campaigns.title', 'Email'),
          description: t(
            'marketing:email.campaigns.description',
            'Email'
          ),
          icon: 'mail',
          linkTo: '/marketing/email/email-campaigns',
        },
        {
          id: 'my-email-templates',
          title: t('marketing:email.templates.title', 'Mẫu Template Email'),
          description: t(
            'marketing:email.templates.description',
            'Email'
          ),
          icon: 'file-text',
          linkTo: '/marketing/resources/my-email-templates',
        },
        {
          id: 'gmail',
          title: t('marketing:gmail.title', 'Gmail'),
          description: t('marketing:gmail.description', 'Quản lý cuộc trò chuyện Gmail'),
          icon: 'google-gmail',
          linkTo: '/marketing/gmail',
        },
      ],
    },
    {
      id: 'sms',
      title: t('marketing:sms.title', 'SMS'),
      description: t('marketing:sms.description', 'SMS'),
      icon: 'message-square',
      linkTo: '/marketing/sms',
      hasSubModules: true,
      subModules: [
        {
          id: 'sms-campaigns',
          title: t('marketing:sms.campaigns.title', 'SMS'),
          description: t(
            'marketing:sms.campaigns.description',
            'SMS'
          ),
          icon: 'message-square',
          linkTo: '/marketing/sms/campaigns',
        },
        {
          id: 'my-sms-templates',
          title: t('marketing:sms.templates.title', 'SMS Templates của tôi'),
          description: t(
            'marketing:sms.templates.description',
            'SMS'
          ),
          icon: 'file-text',
          linkTo: '/marketing/sms/my-sms-templates',
        },
      ],
    },
    {
      id: 'zalo',
      title: t('marketing:zalo.title'),
      description: t('marketing:zalo.description'),
      icon: 'zaloIcon',
      linkTo: '/marketing/zalo/accounts',
      hasSubModules: true,
      subModules: [
        {
          id: 'zalo-oa',
          title: t('marketing:zalo.accounts.title', 'Zalo OA'),
          description: t('marketing:zalo.modules.officialAccount.description'),
          icon: 'users',
          linkTo: '/marketing/zalo/zalo-oa',
        },
        {
          id: 'zalo-personal',
          title: t('marketing:zalo.modules.personalAccount.title'),
          description: t('marketing:zalo.modules.personalAccount.description'),
          icon: 'user',
          linkTo: '/marketing/zalo/zalo-personal',
        },
        {
          id: 'zalo-personal-campaigns',
          title: t('marketing:zalo.personalCampaigns.title', 'Chiến dịch Zalo cá nhân'),
          description: t('marketing:zalo.personalCampaigns.description', 'Quản lý chiến dịch Zalo cá nhân'),
          icon: 'send',
          linkTo: '/marketing/zalo/personal-campaigns',
        },
        {
          id: 'zalo-oa-campaigns',
          title: t('marketing:zalo.oaCampaigns.title', 'Chiến dịch OA'),
          description: t('marketing:zalo.oaCampaigns.description', 'Tài khoản OA'),
          icon: 'message-circle',
          linkTo: '/marketing/zalo/oa-campaigns',
        },
        {
          id: 'zns-campaigns',
          title: t('marketing:zalo.znsCampaigns.title', 'ZNS Campaigns'),
          description: t('marketing:zalo.znsCampaigns.description'),
          icon: 'marketplace',
          linkTo: '/marketing/zalo/zns-campaigns',
        },
        {
          id: 'zalo-groups',
          title: t('marketing:zalo.groups.title', 'Zalo Groups'),
          description: t('marketing:zalo.modules.groups.description'),
          icon: 'users',
          linkTo: '/marketing/zalo/groups',
        },
        {
          id: 'zalo-articles',
          title: t('marketing:zalo.articles.title', 'Zalo Articles'),
          description: t('marketing:zalo.modules.articles.description', 'Quản lý bài viết Zalo OA'),
          icon: 'file-text',
          linkTo: '/marketing/zalo/articles',
        },
        {
          id: 'zns-templates',
          title: t('marketing:zalo.zns.title', 'ZNS Templates'),
          description: t('marketing:zalo.zns.description', 'Quản lý mẫu tin nhắn ZNS'),
          icon: 'message-circle',
          linkTo: '/marketing/zalo/zns-templates',
        },
        {
          id: 'media-resources',
          title: t('marketing:mediaResources.title', 'Tài nguyên Media'),
          description: t('marketing:mediaResources.description', 'Quản lý file media cho Zalo OA'),
          icon: 'image',
          linkTo: '/marketing/zalo/media-resources',
        },
        {
          id: 'zalo-chat',
          title: t('marketing:zalo.chat.title', 'Chat'),
          description: t('marketing:zalo.chat.description', 'Quản lý cuộc trò chuyện Zalo'),
          icon: 'message-circle',
          linkTo: '/marketing/zalo/chat',
        }
      ],
    },

    {
      id: 'google',
      title: t('marketing:google.title', 'Google'),
      description: t('marketing:google.description', 'Quản lý tất cả dịch vụ Google'),
      icon: 'google',
      linkTo: '/marketing/google',
      hasSubModules: true,
      subModules: [
        {
          id: 'google-ads',
          title: t('marketing:google.ads.title', 'Google Ads'),
          description: t('marketing:google.ads.description', 'Quản lý quảng cáo Google'),
          icon: 'megaphone',
          linkTo: '/marketing/google-ads',
        },
        {
          id: 'google-analytics',
          title: t('marketing:google.analytics.title', 'Google Analytics'),
          description: t('marketing:google.analytics.description', 'Phân tích website với Google Analytics'),
          icon: 'bar-chart',
          linkTo: '/marketing/google/analytics',
        },
        {
          id: 'google-search-console',
          title: t('marketing:google.searchConsole.title', 'Google Search Console'),
          description: t('marketing:google.searchConsole.description', 'Tối ưu SEO với Search Console'),
          icon: 'search',
          linkTo: '/marketing/google/search-console',
        },
        {
          id: 'google-tag-manager',
          title: t('marketing:google.tagManager.title', 'Google Tag Manager'),
          description: t('marketing:google.tagManager.description', 'Quản lý thẻ theo dõi'),
          icon: 'tag',
          linkTo: '/marketing/google/tag-manager',
        },
        {
          id: 'google-my-business',
          title: t('marketing:google.myBusiness.title', 'Google My Business'),
          description: t('marketing:google.myBusiness.description', 'Quản lý doanh nghiệp trên Google'),
          icon: 'map-pin',
          linkTo: '/marketing/google/my-business',
        },
        {
          id: 'youtube-ads',
          title: t('marketing:google.youtube.title', 'YouTube Ads'),
          description: t('marketing:google.youtube.description', 'Quảng cáo trên YouTube'),
          icon: 'play',
          linkTo: '/marketing/google/youtube',
        },
      ],
    },
    {
      id: 'facebook',
      title: t('marketing:facebook.title', 'Facebook'),
      description: t('marketing:facebook.description', 'Quản lý tất cả dịch vụ Facebook'),
      icon: 'facebook',
      linkTo: '/marketing/facebook',
      hasSubModules: true,
      subModules: [
        {
          id: 'facebook-ads',
          title: t('marketing:facebook.ads.title', 'Facebook Ads'),
          description: t('marketing:facebook.ads.description', 'Quản lý quảng cáo Facebook'),
          icon: 'campaign',
          linkTo: '/marketing/facebook-ads',
        },
        {
          id: 'facebook-pages',
          title: t('marketing:facebook.pages.title', 'Facebook Pages'),
          description: t('marketing:facebook.pages.description', 'Quản lý trang Facebook'),
          icon: 'file-text',
          linkTo: '/marketing/facebook/pages',
        },
        {
          id: 'instagram-business',
          title: t('marketing:facebook.instagram.title', 'Instagram Business'),
          description: t('marketing:facebook.instagram.description', 'Quản lý Instagram doanh nghiệp'),
          icon: 'instagram',
          linkTo: '/marketing/facebook/instagram',
        },
        {
          id: 'facebook-pixel',
          title: t('marketing:facebook.pixel.title', 'Facebook Pixel'),
          description: t('marketing:facebook.pixel.description', 'Theo dõi và phân tích website'),
          icon: 'bar-chart',
          linkTo: '/marketing/facebook/pixel',
        },
        {
          id: 'facebook-messenger',
          title: t('marketing:facebook.messenger.title', 'Facebook Messenger'),
          description: t('marketing:facebook.messenger.description', 'Tin nhắn và chatbot'),
          icon: 'message-circle',
          linkTo: '/marketing/facebook/messenger',
        },
        {
          id: 'facebook-analytics',
          title: t('marketing:facebook.analytics.title', 'Facebook Analytics'),
          description: t('marketing:facebook.analytics.description', 'Phân tích tổng thể Facebook'),
          icon: 'bar-chart',
          linkTo: '/marketing/facebook/analytics',
        },
      ],
    },
  ];
};
