/**
 * Export all hooks
 */

export * from './useTagQuery';
export * from './useAudienceQuery';
export * from './useCustomFieldOperations';
export * from './useSegmentQuery';
export * from './useCampaignQuery';
export * from './useStatisticsQuery';
export * from './useTemplateEmailQuery';
export * from './useCustomFieldQuery';
export * from './useMarketingOverview';

// Email hooks
export * from './email/useEmailTemplates';
export * from './email/useEmailCampaigns';

// Gmail hooks
export * from './gmail';

// Google Ads hooks
export { default as useGoogleAdsAccounts } from './google-ads/useGoogleAdsAccounts';
export { default as useGoogleAdsCampaigns } from './google-ads/useGoogleAdsCampaigns';

// Facebook Ads hooks
export * from './facebook-ads/useFacebookAdsAccounts';
export * from './facebook-ads/useFacebookAdsCampaigns';
export * from './facebook-ads/useFacebookAuth';

// Zalo hooks
export * from './zalo/useZaloConnect';
export * from './useZaloGroups';
export * from './zalo/useZaloOACampaigns';
export {
  useZaloArticles as useZaloArticlesList,
  useCreateZaloArticle,
  useUpdateZaloArticle,
  useDeleteZaloArticle,
  useSyncZaloArticles,
  useZaloArticleDetail as useZaloArticleDetailOld
} from './useZaloArticles';
export {
  useZaloArticles,
  useZaloArticleDetail,
  useCreateZaloNormalArticle,
  useCreateZaloVideoArticle,
  useUpdateZaloNormalArticle,
  useUpdateZaloVideoArticle,
  useRemoveZaloArticle,
  useSyncAllZaloArticles,
  useToggleZaloArticleVisibility
} from './useZaloArticleQuery';

// System Template Email hooks
export * from './useSystemTemplateEmails';

// ZNS hooks
export * from './useZNSNotification';
export * from './useZNSTemplatesQuery';
export * from './useZNSCampaigns';

// Zalo Chat hooks
export * from './zalo/useZaloChat';
