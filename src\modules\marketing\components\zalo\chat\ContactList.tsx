/**
 * ContactList Component
 * Component hiển thị danh sách liên hệ với avatar, tên, tin nhắn cuối và thời gian
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Image, Icon, Loading } from '@/shared/components/common';
import type { Contact } from '../../../types/zalo-chat.types';
import { TIME_FORMATS } from '../../../constants/zalo-chat.constants';
import { format, isToday, isYesterday } from 'date-fns';
import { vi } from 'date-fns/locale';

interface ContactListProps {
  contacts: Contact[];
  selectedContactId?: string;
  onContactSelect: (contact: Contact) => void;
  isLoading?: boolean;
  searchQuery?: string;
  className?: string;
}

interface ContactItemProps {
  contact: Contact;
  isSelected: boolean;
  onClick: (contact: Contact) => void;
  searchQuery?: string;
}

/**
 * Component hiển thị một item contact
 */
const ContactItem: React.FC<ContactItemProps> = ({
  contact,
  isSelected,
  onClick,
  searchQuery,
}) => {
  const { t } = useTranslation(['marketing', 'common']);

  // Format thời gian tin nhắn cuối
  const formatLastMessageTime = (timestamp?: string): string => {
    if (!timestamp) return '';

    const messageDate = new Date(timestamp);
    const now = new Date();

    if (isToday(messageDate)) {
      return format(messageDate, TIME_FORMATS.MESSAGE_TIME);
    } else if (isYesterday(messageDate)) {
      return t('common:yesterday');
    } else {
      // Nếu trong tuần này thì hiển thị thứ, nếu không thì hiển thị ngày
      const daysDiff = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60 * 60 * 24));
      if (daysDiff <= 7) {
        return format(messageDate, 'EEEE', { locale: vi });
      } else {
        return format(messageDate, 'dd/MM', { locale: vi });
      }
    }
  };

  // Highlight text khi search
  const highlightText = (text: string, query?: string): React.ReactNode => {
    if (!query || !text) return text;

    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <span key={index} className="bg-yellow-200 dark:bg-yellow-800 font-medium">
          {part}
        </span>
      ) : (
        part
      )
    );
  };

  // Truncate tin nhắn cuối
  const truncateMessage = (message?: string, maxLength: number = 50): string => {
    if (!message) return '';
    return message.length > maxLength ? `${message.substring(0, maxLength)}...` : message;
  };

  return (
    <div
      onClick={() => onClick(contact)}
      className={`
        flex items-center p-3 cursor-pointer transition-all duration-200 border-b border-gray-100 dark:border-gray-700
        hover:bg-gray-50 dark:hover:bg-gray-700
        ${isSelected ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-l-blue-500' : ''}
      `}
    >
      {/* Avatar */}
      <div className="relative flex-shrink-0 mr-3">
        {contact.avatar ? (
          <Image
            src={contact.avatar}
            alt={contact.name}
            className="w-12 h-12 rounded-full object-cover"
          />
        ) : (
          <div className="w-12 h-12 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
            <Icon name="user" size="lg" className="text-gray-600 dark:text-gray-300" />
          </div>
        )}

        {/* Online status indicator */}
        {contact.isOnline && (
          <div className="absolute -bottom-0.5 -right-0.5 w-4 h-4 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
        )}

        {/* Unread count badge */}
        {contact.unreadCount > 0 && (
          <div className="absolute -top-1 -right-1 min-w-[18px] h-[18px] bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center px-1">
            {contact.unreadCount > 99 ? '99+' : contact.unreadCount}
          </div>
        )}
      </div>

      {/* Contact info */}
      <div className="flex-1 min-w-0">
        {/* Name and time */}
        <div className="flex items-center justify-between mb-1">
          <Typography
            variant="body2"
            className={`font-medium truncate ${
              contact.unreadCount > 0 ? 'text-gray-900 dark:text-gray-100' : 'text-gray-700 dark:text-gray-300'
            }`}
          >
            {highlightText(contact.name, searchQuery)}
          </Typography>
          
          {contact.lastMessageTime && (
            <Typography
              variant="caption"
              className="text-gray-500 flex-shrink-0 ml-2"
            >
              {formatLastMessageTime(contact.lastMessageTime)}
            </Typography>
          )}
        </div>

        {/* Last message */}
        <div className="flex items-center justify-between">
          <Typography
            variant="caption"
            className={`truncate ${
              contact.unreadCount > 0 
                ? 'text-gray-600 dark:text-gray-400 font-medium' 
                : 'text-gray-500'
            }`}
          >
            {contact.lastMessage ? (
              <>
                {contact.lastMessage.senderType === 'user' && (
                  <span className="text-blue-600 dark:text-blue-400 mr-1">
                    {t('marketing:zalo.chat.you')}:
                  </span>
                )}
                {contact.lastMessage.type === 'text' 
                  ? truncateMessage(contact.lastMessage.content)
                  : contact.lastMessage.type === 'image'
                  ? `📷 ${t('marketing:zalo.chat.image')}`
                  : contact.lastMessage.type === 'file'
                  ? `📎 ${t('marketing:zalo.chat.file')}`
                  : t('marketing:zalo.chat.message')
                }
              </>
            ) : (
              <span className="italic">
                {t('marketing:zalo.chat.noMessages')}
              </span>
            )}
          </Typography>

          {/* Message status indicator for last message from user */}
          {contact.lastMessage?.senderType === 'user' && (
            <div className="flex-shrink-0 ml-2">
              {contact.lastMessage.status === 'sending' && (
                <Icon name="clock" size="xs" className="text-gray-400" />
              )}
              {contact.lastMessage.status === 'sent' && (
                <Icon name="check" size="xs" className="text-gray-400" />
              )}
              {contact.lastMessage.status === 'delivered' && (
                <div className="flex">
                  <Icon name="check" size="xs" className="text-gray-400" />
                  <Icon name="check" size="xs" className="text-gray-400 -ml-1" />
                </div>
              )}
              {contact.lastMessage.status === 'read' && (
                <div className="flex">
                  <Icon name="check" size="xs" className="text-blue-500" />
                  <Icon name="check" size="xs" className="text-blue-500 -ml-1" />
                </div>
              )}
              {contact.lastMessage.status === 'failed' && (
                <Icon name="alert-circle" size="xs" className="text-red-500" />
              )}
            </div>
          )}
        </div>

        {/* Tags */}
        {contact.tags && contact.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {contact.tags.slice(0, 2).map((tag, index) => (
              <span
                key={index}
                className="inline-block px-2 py-0.5 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full"
              >
                {tag}
              </span>
            ))}
            {contact.tags.length > 2 && (
              <span className="inline-block px-2 py-0.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full">
                +{contact.tags.length - 2}
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Component ContactList chính
 */
const ContactList: React.FC<ContactListProps> = ({
  contacts,
  selectedContactId,
  onContactSelect,
  isLoading = false,
  searchQuery,
  className = '',
}) => {
  const { t } = useTranslation(['marketing', 'common']);

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center py-8 ${className}`}>
        <Loading />
      </div>
    );
  }

  if (contacts.length === 0) {
    return (
      <div className={`flex flex-col items-center justify-center py-8 px-4 ${className}`}>
        <Icon name="users" size="2xl" className="text-gray-400 mb-4" />
        <Typography variant="body2" className="text-gray-500 text-center">
          {searchQuery 
            ? t('marketing:zalo.chat.noContactsFound')
            : t('marketing:zalo.chat.noContacts')
          }
        </Typography>
        {searchQuery && (
          <Typography variant="caption" className="text-gray-400 text-center mt-1">
            {t('marketing:zalo.chat.tryDifferentSearch')}
          </Typography>
        )}
      </div>
    );
  }

  return (
    <div className={`overflow-y-auto ${className}`}>
      {contacts.map((contact) => (
        <ContactItem
          key={contact.id}
          contact={contact}
          isSelected={contact.id === selectedContactId}
          onClick={onContactSelect}
          searchQuery={searchQuery}
        />
      ))}
    </div>
  );
};

export default ContactList;
