import React from 'react';
import { useTranslation } from 'react-i18next';
import { IconCard } from '@/shared/components/common';
import DashboardTabManager from './DashboardTabManager';
import { DashboardTab } from '../types';

interface DashboardCollapsibleSidebarProps {
  onAddWidget: () => void;
  onCreateTab: () => void;
  canEdit: boolean;
  tabs: DashboardTab[];
  currentTabId: string;
  currentTabMode?: 'view' | 'edit';
  onSwitchTab: (tabId: string) => void;
  onDeleteTab: (tabId: string) => void;
  onRenameTab: (tabId: string, newName: string) => void;
  onChangeMode: (mode: 'view' | 'edit') => void;
  onSave: () => void;
  displayMode?: 'normal' | 'minimal' | 'ultra-minimal';
  onToggleDisplayMode?: () => void;
  onExportPDF: () => void;
  isExportingPDF: boolean;
  smartLayoutMode?: boolean;
  onToggleSmartLayout?: () => void;
  autoHeightMode?: boolean;
  onToggleAutoHeight?: () => void;
}

const DashboardCollapsibleSidebar: React.FC<DashboardCollapsibleSidebarProps> = ({
  onAddWidget,
  onCreateTab,
  canEdit,
  tabs,
  currentTabId,
  currentTabMode,
  onSwitchTab,
  onDeleteTab,
  onRenameTab,
  onChangeMode,
  onSave,
  displayMode = 'normal',
  onToggleDisplayMode,
  onExportPDF,
  isExportingPDF,
  smartLayoutMode = false,
  onToggleSmartLayout,
  autoHeightMode = true,
  onToggleAutoHeight,
}) => {
  const { t } = useTranslation(['dashboard']);

  return (
    <div className="flex flex-col gap-2 p-2">
      {/* Action IconCards và Mode Controls */}
      <div className="flex items-center gap-2">
        {/* Smart Layout Toggle - Đặt đầu tiên */}
        {onToggleSmartLayout && (
          <IconCard
            icon={smartLayoutMode ? "grid" : "layout"}
            variant={smartLayoutMode ? "primary" : "ghost"}
            onClick={onToggleSmartLayout}
            title={smartLayoutMode ? t('dashboard:smartLayout.disable', 'Tắt sắp xếp thông minh') : t('dashboard:smartLayout.enable', 'Bật sắp xếp thông minh')}
          />
        )}

        <IconCard
          icon="plus"
          variant="primary"
          onClick={onAddWidget}
          disabled={!canEdit}
          title={t('dashboard:addWidget.button')}
        />
        <IconCard
          icon="file-plus"
          variant="secondary"
          onClick={onCreateTab}
          title={t('dashboard:tabs.createNew')}
        />
        <IconCard
          icon="download"
          variant="ghost"
          onClick={onExportPDF}
          disabled={isExportingPDF}
          title={t('dashboard:export.pdf.button', 'Xuất báo cáo')}
        />
        {onToggleDisplayMode && (
          <>
            <IconCard
              icon={
                displayMode === 'normal'
                  ? 'eye-off'
                  : displayMode === 'minimal'
                    ? 'zoom-out'
                    : 'layers'
              }
              variant="ghost"
              onClick={onToggleDisplayMode}
              title={
                displayMode === 'normal'
                  ? t('dashboard:hideCardTitles')
                  : displayMode === 'minimal'
                    ? t('dashboard:ultraMinimalMode')
                    : t('dashboard:showCardTitles')
              }
            />
          </>
        )}

        {/* Auto Height Mode Toggle */}
        {onToggleAutoHeight && (
          <IconCard
            icon="maximize"
            variant={autoHeightMode ? 'primary' : 'ghost'}
            onClick={onToggleAutoHeight}
            title={
              autoHeightMode
                ? t('dashboard:autoHeight.disable', 'Tắt tự động điều chỉnh chiều cao')
                : t('dashboard:autoHeight.enable', 'Bật tự động điều chỉnh chiều cao')
            }
          />
        )}

        {/* Mode Controls */}
        <IconCard
          icon="eye"
          variant={currentTabMode === 'view' ? 'primary' : 'ghost'}
          onClick={() => onChangeMode('view')}
          title={t('dashboard:viewMode')}
        />
        <IconCard
          icon="edit"
          variant={currentTabMode === 'edit' ? 'primary' : 'ghost'}
          onClick={() => onChangeMode('edit')}
          title={t('dashboard:editMode')}
        />
        <IconCard icon="save" variant="secondary" onClick={onSave} title={t('dashboard:save')} />
      </div>

      {/* Tab Management */}
      <DashboardTabManager
        tabs={tabs}
        currentTabId={currentTabId}
        onSwitchTab={onSwitchTab}
        onDeleteTab={onDeleteTab}
        onRenameTab={onRenameTab}
      />
    </div>
  );
};

export default DashboardCollapsibleSidebar;
