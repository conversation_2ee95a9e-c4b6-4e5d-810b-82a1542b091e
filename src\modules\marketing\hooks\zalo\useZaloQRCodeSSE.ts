import { useCallback, useEffect } from 'react';
import { useSSE } from '@/shared/hooks/common/useSSE';
import { SSEConnectionState } from '@/shared/types/sse.types';

interface ZaloQRCodeSSEData {
  type: 'qr_scanned' | 'login_success' | 'login_failed' | 'expired' | 'error';
  sessionId: string;
  message?: string;
  data?: {
    profile?: {
      id: string;
      name: string;
      avatar?: string;
    };
    integrationId?: string;
  };
}

interface UseZaloQRCodeSSEOptions {
  /**
   * Session ID của QR code
   */
  sessionId: string;
  
  /**
   * Có tự động kết nối không
   */
  autoConnect?: boolean;
  
  /**
   * Callback khi QR code được quét
   */
  onQRScanned?: (data: ZaloQRCodeSSEData) => void;
  
  /**
   * Callback khi đăng nhập thành công
   */
  onLoginSuccess?: (data: ZaloQRCodeSSEData) => void;
  
  /**
   * Callback khi đăng nhập thất bại
   */
  onLoginFailed?: (data: ZaloQRCodeSSEData) => void;
  
  /**
   * Callback khi QR code hết hạn
   */
  onExpired?: (data: ZaloQRCodeSSEData) => void;
  
  /**
   * Callback khi có lỗi
   */
  onError?: (data: ZaloQRCodeSSEData) => void;
}

interface UseZaloQRCodeSSEReturn {
  /**
   * Trạng thái kết nối SSE
   */
  isConnected: boolean;
  
  /**
   * Có đang kết nối không
   */
  isConnecting: boolean;
  
  /**
   * Dữ liệu nhận được gần nhất
   */
  lastData: ZaloQRCodeSSEData | null;
  
  /**
   * Kết nối SSE
   */
  connect: () => void;
  
  /**
   * Ngắt kết nối SSE
   */
  disconnect: () => void;
  
  /**
   * Lỗi nếu có
   */
  error: any;
}

/**
 * Hook để theo dõi trạng thái QR code Zalo qua SSE
 */
export const useZaloQRCodeSSE = (options: UseZaloQRCodeSSEOptions): UseZaloQRCodeSSEReturn => {
  const {
    sessionId,
    autoConnect = true,
    onQRScanned,
    onLoginSuccess,
    onLoginFailed,
    onExpired,
    onError,
  } = options;

  // Tạo URL SSE
  const sseUrl = `/v1/marketing/zalo/qr-code/session/${sessionId}/stream`;

  // Sử dụng hook SSE
  const {
    connectionInfo,
    lastEvent,
    connect,
    disconnect,
    subscribe,
    unsubscribe,
  } = useSSE(sseUrl, {
    autoConnect: autoConnect && !!sessionId,
    debug: true,
    onOpen: (event) => {
      console.log('Zalo QR Code SSE connected:', event);
    },
    onError: (error) => {
      console.error('Zalo QR Code SSE error:', error);
    },
    onClose: (event) => {
      console.log('Zalo QR Code SSE closed:', event);
    },
  });

  // Xử lý message từ SSE
  const handleSSEMessage = useCallback((data: ZaloQRCodeSSEData) => {
    console.log('Zalo QR Code SSE message:', data);

    switch (data.type) {
      case 'qr_scanned':
        onQRScanned?.(data);
        break;
      case 'login_success':
        onLoginSuccess?.(data);
        break;
      case 'login_failed':
        onLoginFailed?.(data);
        break;
      case 'expired':
        onExpired?.(data);
        break;
      case 'error':
        onError?.(data);
        break;
      default:
        console.warn('Unknown SSE message type:', data.type);
    }
  }, [onQRScanned, onLoginSuccess, onLoginFailed, onExpired, onError]);

  // Subscribe vào message events
  useEffect(() => {
    if (!sessionId) return;

    const subscriptionId = subscribe('message', (event) => {
      try {
        const data = event.data as ZaloQRCodeSSEData;
        handleSSEMessage(data);
      } catch (error) {
        console.error('Error parsing SSE message:', error);
      }
    });

    return () => {
      unsubscribe(subscriptionId);
    };
  }, [sessionId, subscribe, unsubscribe, handleSSEMessage]);

  // Ngắt kết nối khi sessionId thay đổi hoặc component unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [sessionId, disconnect]);

  const isConnected = connectionInfo.state === SSEConnectionState.CONNECTED;
  const isConnecting = connectionInfo.state === SSEConnectionState.CONNECTING;
  const lastData = lastEvent?.data as ZaloQRCodeSSEData | null;

  return {
    isConnected,
    isConnecting,
    lastData,
    connect,
    disconnect,
    error: connectionInfo.lastError,
  };
};

export default useZaloQRCodeSSE;
