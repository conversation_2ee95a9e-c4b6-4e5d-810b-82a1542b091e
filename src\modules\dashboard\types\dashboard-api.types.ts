/**
 * Dashboard API Types
 * Đồng bộ với backend DTOs và entities
 */

import { DashboardTabsState } from './index';

// ==================== ENUMS ====================

export enum DashboardPageType {
  USER_CUSTOM = 'USER_CUSTOM',
  ADMIN_CUSTOM = 'ADMIN_CUSTOM',
  SYSTEM_USER_TEMPLATE = 'SYSTEM_USER_TEMPLATE',
  SYSTEM_EMPLOYEE_TEMPLATE = 'SYSTEM_EMPLOYEE_TEMPLATE',
  USER_TEMPLATE = 'USER_TEMPLATE',
  ADMIN_TEMPLATE = 'ADMIN_TEMPLATE',
}

export type OwnerType = 'USER' | 'EMPLOYEE';
export type AccessLevel = 'PRIVATE' | 'SHARED' | 'PUBLIC';

// ==================== REQUEST DTOs ====================

export interface CreateDashboardPageDto {
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  sortOrder?: number;
  isActive?: boolean;
  isDefault?: boolean;
  layoutConfig?: Record<string, unknown>;
  tabsConfig?: DashboardTabsState;
  metadata?: Record<string, unknown>;
  pageType?: DashboardPageType;
  ownerType?: OwnerType;
  accessLevel?: AccessLevel;
  sharedWith?: {
    users?: number[];
    employees?: number[];
    roles?: string[];
  };
}

export interface UpdateDashboardPageDto {
  name?: string;
  slug?: string;
  description?: string;
  icon?: string;
  sortOrder?: number;
  isActive?: boolean;
  isDefault?: boolean;
  layoutConfig?: Record<string, unknown>;
  tabsConfig?: DashboardTabsState;
  metadata?: Record<string, unknown>;
  pageType?: DashboardPageType;
  ownerType?: OwnerType;
  accessLevel?: AccessLevel;
  sharedWith?: {
    users?: number[];
    employees?: number[];
    roles?: string[];
  };
}

export interface QueryDashboardPageDto {
  page?: number;
  limit?: number;
  search?: string;
  pageType?: DashboardPageType;
  ownerType?: OwnerType;
  accessLevel?: AccessLevel;
  isActive?: boolean;
  isDefault?: boolean;
  sortBy?: 'name' | 'createdAt' | 'updatedAt' | 'sortOrder';
  sortOrder?: 'ASC' | 'DESC';
}

// ==================== RESPONSE DTOs ====================

export interface DashboardPageResponseDto {
  id: string;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  sortOrder: number;
  isActive: boolean;
  isDefault: boolean;
  layoutConfig?: Record<string, unknown>;
  tabsConfig?: DashboardTabsState;
  metadata?: Record<string, unknown>;
  userId?: number;
  employeeId?: number;
  pageType: DashboardPageType;
  ownerType: OwnerType;
  accessLevel: AccessLevel;
  sharedWith?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;
}

// ==================== API RESPONSE WRAPPERS ====================

export interface ApiResponseDto<T> {
  success: boolean;
  result: T;
  message?: string;
  statusCode: number;
}

export interface PageResult<T> {
  items: T[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

export type DashboardPagesResponse = ApiResponseDto<PageResult<DashboardPageResponseDto>>;
export type DashboardPageResponse = ApiResponseDto<DashboardPageResponseDto>;
export type CreateDashboardResponse = ApiResponseDto<DashboardPageResponseDto>;
export type UpdateDashboardResponse = ApiResponseDto<DashboardPageResponseDto>;
export type DeleteDashboardResponse = ApiResponseDto<{ success: boolean }>;

// ==================== BUSINESS LOGIC TYPES ====================

export interface DashboardTemplate {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  preview?: string;
  category: string;
  tags: string[];
  tabsConfig: DashboardTabsState;
  metadata?: Record<string, unknown>;
  isSystem: boolean;
  usageCount: number;
  rating: number;
  createdAt: Date;
}

export interface DashboardSaveOptions {
  saveToServer?: boolean;
  saveToLocal?: boolean;
  setAsDefault?: boolean;
  createBackup?: boolean;
}

export interface DashboardLoadOptions {
  preferServer?: boolean;
  fallbackToLocal?: boolean;
  includeWidgets?: boolean;
}

// ==================== ERROR TYPES ====================

export interface DashboardApiError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  statusCode?: number;
}

// ==================== UTILITY TYPES ====================

export interface DashboardSyncStatus {
  isLoading: boolean;
  isSyncing: boolean;
  lastSyncAt?: Date;
  hasUnsavedChanges: boolean;
  syncError?: DashboardApiError;
}

export interface DashboardMigrationData {
  localData?: DashboardTabsState;
  serverData?: DashboardPageResponseDto;
  conflicts?: string[];
  resolution: 'use_local' | 'use_server' | 'merge' | 'manual';
}
