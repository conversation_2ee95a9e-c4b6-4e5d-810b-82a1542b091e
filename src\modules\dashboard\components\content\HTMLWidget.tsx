import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Textarea } from '@/shared/components/common';
import { BaseWidgetProps } from '../../types';

interface HTMLWidgetProps extends BaseWidgetProps {
  initialHtml?: string;
  editable?: boolean;
  allowScripts?: boolean;
  sanitize?: boolean;
}

/**
 * Widget hiển thị HTML tùy chỉnh
 */
const HTMLWidget: React.FC<HTMLWidgetProps> = ({
  className,
  initialHtml = '<div style="text-align: center; padding: 20px;"><h2>HTML Widget</h2><p>Thêm HTML tùy chỉnh của bạn ở đây</p></div>',
  editable = true,
  allowScripts = false,
  sanitize = true,
}) => {
  const { t } = useTranslation(['dashboard', 'common']);
  const [html, setHtml] = useState(initialHtml);
  const [isEditing, setIsEditing] = useState(false);
  const [tempHtml, setTempHtml] = useState(html);
  const [error, setError] = useState<string | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const handleEdit = useCallback(() => {
    setTempHtml(html);
    setIsEditing(true);
    setError(null);
  }, [html]);

  const handleSave = useCallback(() => {
    try {
      // Basic validation
      if (tempHtml.trim() === '') {
        setError(t('dashboard:widgets.html.emptyError', 'HTML không được để trống'));
        return;
      }

      // Check for potentially dangerous content if sanitize is enabled
      if (sanitize && !allowScripts) {
        const scriptRegex = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi;
        if (scriptRegex.test(tempHtml)) {
          setError(t('dashboard:widgets.html.scriptError', 'Script tags không được phép'));
          return;
        }
      }

      setHtml(tempHtml);
      setError(null);
      setIsEditing(false);
    } catch (err) {
      setError(t('dashboard:widgets.html.invalidError', 'HTML không hợp lệ'));
    }
  }, [tempHtml, sanitize, allowScripts, t]);

  const handleCancel = useCallback(() => {
    setTempHtml(html);
    setError(null);
    setIsEditing(false);
  }, [html]);

  // Update iframe content when html changes
  useEffect(() => {
    if (iframeRef.current && !isEditing) {
      const iframe = iframeRef.current;
      const doc = iframe.contentDocument || iframe.contentWindow?.document;
      
      if (doc) {
        doc.open();
        doc.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <style>
                body {
                  margin: 0;
                  padding: 0;
                  font-family: system-ui, -apple-system, sans-serif;
                  background: transparent;
                  color: inherit;
                }
                * {
                  box-sizing: border-box;
                }
              </style>
            </head>
            <body>
              ${html}
            </body>
          </html>
        `);
        doc.close();
      }
    }
  }, [html, isEditing]);

  if (isEditing) {
    return (
      <div className={`w-full h-full p-4 ${className || ''}`}>
        <div className="h-full flex flex-col">
          <div className="mb-3">
            <Typography variant="body2" className="mb-2">
              {t('dashboard:widgets.html.label', 'HTML Code')}
            </Typography>
            <Textarea
              value={tempHtml}
              onChange={(e) => setTempHtml(e.target.value)}
              placeholder={t('dashboard:widgets.html.placeholder', 'Nhập HTML code...')}
              className="flex-1 resize-none font-mono text-sm"
              rows={8}
            />
            {error && (
              <Typography variant="caption" className="text-destructive mt-1">
                {error}
              </Typography>
            )}
          </div>

          <div className="mb-3 flex-1 min-h-0">
            <Typography variant="body2" className="mb-2">
              {t('dashboard:widgets.html.preview', 'Xem trước')}
            </Typography>
            <div className="w-full h-full border border-border rounded-md overflow-hidden">
              <iframe
                ref={iframeRef}
                className="w-full h-full"
                sandbox={allowScripts ? "allow-scripts allow-same-origin" : "allow-same-origin"}
                title="HTML Preview"
              />
            </div>
          </div>

          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <label className="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  checked={!sanitize}
                  onChange={(e) => {
                    // This would need to be passed as prop or managed by parent
                    console.log('Sanitize toggle:', !e.target.checked);
                  }}
                  className="rounded"
                  disabled
                />
                <Typography variant="caption" className="text-muted-foreground">
                  {t('dashboard:widgets.html.allowUnsafe', 'Cho phép HTML không an toàn')}
                </Typography>
              </label>
            </div>
            
            <div className="flex gap-2">
              <Button variant="ghost" size="sm" onClick={handleCancel}>
                {t('common:cancel')}
              </Button>
              <Button variant="primary" size="sm" onClick={handleSave}>
                {t('common:save')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`w-full h-full relative group ${className || ''}`}
    >
      <iframe
        ref={iframeRef}
        className="w-full h-full border-0"
        sandbox={allowScripts ? "allow-scripts allow-same-origin" : "allow-same-origin"}
        title="HTML Widget Content"
      />
      
      {editable && (
        <div className="absolute inset-0 bg-transparent opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="absolute top-2 right-2">
            <Button variant="secondary" size="sm" onClick={handleEdit}>
              {t('common:edit')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default HTMLWidget;
