/**
 * ZaloChatPage
 * Trang chat Zalo với layout 2 cột (30% sidebar, 70% chat area) và responsive design
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography } from '@/shared/components/common';
import ChatSidebar from '../../components/zalo/chat/ChatSidebar';
import ChatArea from '../../components/zalo/chat/ChatArea';
import { useZaloChat } from '../../hooks/zalo/useZaloChat';
import type { ZaloAccount, Contact } from '../../types/zalo-chat.types';

/**
 * Component mobile header - không cần thiết nữa vì sẽ tích hợp vào ChatArea
 */

/**
 * Component ZaloChatPage chính
 */
const ZaloChatPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  
  // State management
  const {
    selectedAccount,
    selectedContact,
    selectAccount,
    selectContact,
    clearSelection,
  } = useZaloChat();

  // Mobile responsive state
  const [isMobile, setIsMobile] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024); // lg breakpoint
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Auto hide sidebar on mobile when contact is selected
  useEffect(() => {
    if (isMobile && selectedContact) {
      setShowSidebar(false);
    }
  }, [isMobile, selectedContact]);

  // Handle account selection
  const handleAccountSelect = (account: ZaloAccount) => {
    selectAccount(account);
    // Clear contact selection when account changes
    clearSelection();
  };

  // Handle contact selection
  const handleContactSelect = (contact: Contact) => {
    selectContact(contact);
    // TODO: Load or create conversation for this contact
    // For now, we'll use a mock conversation ID
    // In real implementation, this should come from the conversation API
  };

  // Handle add tag
  const handleAddTag = (contact: Contact) => {
    // TODO: Implement add tag functionality
    console.log('Add tag for contact:', contact);
  };

  // Handle back to sidebar (mobile)
  const handleBackToSidebar = () => {
    setShowSidebar(true);
    clearSelection();
  };

  // Handle sidebar collapsed change
  const handleSidebarCollapsedChange = (isCollapsed: boolean) => {
    setSidebarCollapsed(isCollapsed);
  };

  // Generate mock conversation ID for demo
  const conversationId = selectedContact && selectedAccount 
    ? `${selectedAccount.id}-${selectedContact.id}`
    : undefined;

  return (
    <div className="w-full bg-background text-foreground">
      {/* Main layout */}
      <div className="flex h-screen">
        {/* Sidebar */}
        <div className={`
          ${isMobile ? 'fixed inset-y-0 left-0 z-50' : 'relative'}
          ${isMobile && !showSidebar ? '-translate-x-full' : 'translate-x-0'}
          ${isMobile
            ? 'w-full'
            : sidebarCollapsed
              ? 'w-16'
              : 'w-[30%] min-w-[320px] max-w-[400px]'
          }
          transition-all duration-300 ease-in-out
          bg-white dark:bg-gray-800
        `}>
          <ChatSidebar
            selectedAccount={selectedAccount}
            selectedContact={selectedContact}
            onAccountSelect={handleAccountSelect}
            onContactSelect={handleContactSelect}
            isMobile={isMobile}
            onCollapsedChange={handleSidebarCollapsedChange}
            className="h-full"
          />
        </div>

        {/* Chat area */}
        <div className={`
          flex-1
          ${isMobile && showSidebar ? 'hidden' : 'flex'}
          ${isMobile ? 'w-full' : 'w-[70%]'}
        `}>
          <ChatArea
            selectedAccount={selectedAccount}
            selectedContact={selectedContact}
            conversationId={conversationId}
            onAddTag={handleAddTag}
            isMobile={isMobile}
            onBackToSidebar={handleBackToSidebar}
            className="w-full"
          />
        </div>

        {/* Mobile overlay */}
        {isMobile && showSidebar && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setShowSidebar(false)}
          />
        )}
      </div>



      {/* Connection status indicator */}
      <div className="fixed top-4 right-4 z-30">
        <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-3 py-2 shadow-sm">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <Typography variant="caption" className="text-gray-600 dark:text-gray-400">
            {t('marketing:zalo.chat.connected')}
          </Typography>
        </div>
      </div>

      {/* Keyboard shortcuts help (hidden by default) */}
      <div className="hidden">
        <div className="fixed bottom-4 right-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-lg max-w-xs">
          <Typography variant="body2" className="font-medium text-gray-900 dark:text-gray-100 mb-2">
            {t('marketing:zalo.chat.keyboardShortcuts')}
          </Typography>
          <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex justify-between">
              <span>Enter</span>
              <span>{t('marketing:zalo.chat.sendMessage')}</span>
            </div>
            <div className="flex justify-between">
              <span>Shift + Enter</span>
              <span>{t('marketing:zalo.chat.newLine')}</span>
            </div>
            <div className="flex justify-between">
              <span>Ctrl + K</span>
              <span>{t('marketing:zalo.chat.searchContacts')}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ZaloChatPage;
