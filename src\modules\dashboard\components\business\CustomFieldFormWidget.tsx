import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import CustomFieldForm from '@/modules/business/components/forms/CustomFieldForm';
import { type BaseWidgetProps } from '../../types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

/**
 * Widget hiển thị form tạo trường tùy chỉnh trong dashboard
 * Sử dụng CustomFieldForm component từ business module
 */
const CustomFieldFormWidget: React.FC<BaseWidgetProps> = ({
  className,
}) => {
  const { t } = useTranslation(['dashboard', 'business']);
  const { success } = useSmartNotification();
  const [formKey, setFormKey] = useState(0); // Key để reset form

  // Xử lý khi submit thành công
  const handleSubmitSuccess = useCallback(() => {
    success({
      message: t('dashboard:widgets.customFieldForm.submitSuccess', 'Tạo trường tùy chỉnh thành công!')
    });

    // Reset form bằng cách thay đổi key
    setFormKey(prev => prev + 1);
  }, [success, t]);

  // Xử lý khi cancel (không cần làm gì trong widget)
  const handleCancel = useCallback(() => {
    // Trong widget, cancel chỉ reset form
    setFormKey(prev => prev + 1);
  }, []);

  return (
    <div className={`h-full flex flex-col ${className || ''}`}>
      <div className="flex-1 min-h-0 overflow-auto">
        <div className="p-2">
          <CustomFieldForm
            key={formKey} // Reset form khi key thay đổi
            onSubmit={handleSubmitSuccess}
            onCancel={handleCancel}
          />
        </div>
      </div>
    </div>
  );
};

export default CustomFieldFormWidget;
